<template>
  <div class="renewable-energy-admin page-content">
    <!-- 查询条件 -->
    <el-card class="search-card">
      <el-form :model="queryParams" ref="queryFormRef" inline>
        <el-form-item label="项目名称">
          <el-select
            v-model="queryParams.projectId"
            placeholder="请选择项目"
            clearable
            style="width: 200px"
            @change="handleProjectChange"
          >
            <el-option
              v-for="project in projectList"
              :key="project.id"
              :label="project.name"
              :value="project.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :icon="Search">查询</el-button>
          <el-button @click="resetForm" :icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <el-card class="operation-card">
      <el-button type="primary" @click="handleAdd" :icon="Plus">新增</el-button>
    </el-card>

    <!-- 表格 -->
    <el-card class="table-card">
      <ArtTable
        :data="tableData"
        :loading="loading"
        row-key="id"
        :border="false"
        :highlight-current-row="false"
        :total="totalCount"
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :max-height="500"
        index
        class="renewable-energy-table"
      >
        <el-table-column prop="projectId" label="项目名称" align="center">
          <template #default="scope">
            <span>{{ getProjectNameById(scope.row.projectId) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="tyngf" label="太阳能光伏装机容量" align="center">
          <template #default="scope">
            <span>{{ scope.row.tyngf ? scope.row.tyngf + ' kWp' : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="tynrsjrq" label="太阳能热水集热器面积" align="center">
          <template #default="scope">
            <span>{{ scope.row.tynrsjrq ? scope.row.tynrsjrq + ' m²' : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="dyrb" label="地源热泵制冷/热量" align="center">
          <template #default="scope">
            <span>{{ scope.row.dyrb ? scope.row.dyrb + ' kW' : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="kqyrb" label="空气源热泵制热量" align="center">
          <template #default="scope">
            <span>{{ scope.row.kqyrb ? scope.row.kqyrb + ' kW' : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" align="center" width="180">
          <template #default="scope">
            <span>{{ formatDate(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="150" fixed="right">
          <template #default="scope">
            <div class="operation-btns">
              <el-button
                link
                size="small"
                @click="handleEdit(scope.row)"
                title="编辑"
                class="operation-btn edit-btn"
              >
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button
                link
                size="small"
                @click="handleDelete(scope.row)"
                title="删除"
                class="operation-btn delete-btn"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
      </ArtTable>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogType === 'add' ? '新增可再生能源配置' : '编辑可再生能源配置'"
      v-model="dialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="configForm" :rules="configRules" ref="configFormRef" label-width="160px">
        <el-form-item label="项目名称" prop="projectId">
          <el-select
            v-model="configForm.projectId"
            placeholder="请选择项目"
            style="width: 100%"
            :disabled="dialogType === 'edit'"
          >
            <el-option
              v-for="project in projectList"
              :key="project.id"
              :label="project.name"
              :value="project.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="太阳能光伏装机容量" prop="tyngf">
          <el-input
            v-model="configForm.tyngf"
            placeholder="请输入太阳能光伏装机容量"
            style="width: 100%"
            type="number"
            :min="0"
            step="0.01"
          >
            <template #append>kW</template>
          </el-input>
        </el-form-item>
        <el-form-item label="太阳能热水集热器面积" prop="tynrsjrq">
          <el-input
            v-model="configForm.tynrsjrq"
            placeholder="请输入太阳能热水集热器面积"
            style="width: 100%"
            type="number"
            :min="0"
            step="0.01"
          >
            <template #append>m²</template>
          </el-input>
        </el-form-item>
        <el-form-item label="地源热泵制冷/热量" prop="dyrb">
          <el-input
            v-model="configForm.dyrb"
            placeholder="请输入地源热泵制冷/热量"
            style="width: 100%"
            type="number"
            :min="0"
            step="0.01"
          >
            <template #append>kW</template>
          </el-input>
        </el-form-item>
        <el-form-item label="空气源热泵制热量" prop="kqyrb">
          <el-input
            v-model="configForm.kqyrb"
            placeholder="请输入空气源热泵制热量"
            style="width: 100%"
            type="number"
            :min="0"
            step="0.01"
          >
            <template #append>kW</template>
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
  import { ElMessage, ElMessageBox, FormInstance } from 'element-plus'
  import { Search, Edit, Delete, Plus, Refresh } from '@element-plus/icons-vue'
  import {
    getRenewableEnergyConfigList,
    createRenewableEnergyConfig,
    updateRenewableEnergyConfig,
    deleteRenewableEnergyConfig,
    type RenewableEnergyConfig
  } from '@/api/renewableEnergyApi'
  import { getProjectSelectList, type ProjectOption } from '@/api/projectApi'

  // 项目列表
  const projectList = ref<ProjectOption[]>([])

  // 查询参数
  const queryParams = reactive({
    projectId: undefined as number | undefined,
    pageNum: 1,
    pageSize: 10
  })

  // 表格数据
  const tableData = ref<RenewableEnergyConfig[]>([])
  const loading = ref(false)
  const totalCount = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(10)

  // 表单引用
  const queryFormRef = ref<FormInstance>()
  const configFormRef = ref<FormInstance>()

  // 对话框相关
  const dialogVisible = ref(false)
  const dialogType = ref<'add' | 'edit'>('add')
  const submitLoading = ref(false)

  // 配置表单
  const configForm = reactive({
    id: undefined as number | undefined,
    projectId: undefined as number | undefined,
    tyngf: '',
    tynrsjrq: '',
    dyrb: '',
    kqyrb: ''
  })

  // 表单验证规则
  const configRules = {
    projectId: [{ required: true, message: '请选择项目', trigger: 'change' }],
    tyngf: [
      { required: true, message: '请输入太阳能光伏装机容量', trigger: 'blur' },
      { pattern: /^\d+(\.\d+)?$/, message: '请输入有效的数字', trigger: 'blur' }
    ],
    tynrsjrq: [
      { required: true, message: '请输入太阳能热水集热器面积', trigger: 'blur' },
      { pattern: /^\d+(\.\d+)?$/, message: '请输入有效的数字', trigger: 'blur' }
    ],
    dyrb: [
      { required: true, message: '请输入地源热泵制冷/热量', trigger: 'blur' },
      { pattern: /^\d+(\.\d+)?$/, message: '请输入有效的数字', trigger: 'blur' }
    ],
    kqyrb: [
      { required: true, message: '请输入空气源热泵制热量', trigger: 'blur' },
      { pattern: /^\d+(\.\d+)?$/, message: '请输入有效的数字', trigger: 'blur' }
    ]
  }

  // 格式化日期
  const formatDate = (dateStr: string) => {
    if (!dateStr) return '-'
    return new Date(dateStr).toLocaleString('zh-CN')
  }

  // 根据项目ID获取项目名称
  const getProjectNameById = (projectId: number | string) => {
    if (!projectId) return '-'

    // 确保projectId是数字类型
    const id = typeof projectId === 'string' ? parseInt(projectId) : projectId
    if (isNaN(id)) return '-'

    // 查找对应的项目
    const project = projectList.value.find((p) => p.id === id)

    // 调试信息（可以在开发时查看）
    if (!project) {
      console.log('未找到项目:', { projectId, id, projectListLength: projectList.value.length })
    }

    return project ? project.name : '-'
  }

  // 获取项目列表
  const getProjectList = async () => {
    try {
      const response = await getProjectSelectList()
      if (response.code === 200) {
        projectList.value = response.data
        // 默认选择第一个项目
        if (response.data && response.data.length > 0 && !queryParams.projectId) {
          queryParams.projectId = response.data[0].id
        }
        // 项目列表加载完成后，获取表格数据（无论是否有默认选择）
        if (queryParams.projectId) {
          getTableData()
        }
      }
    } catch (error) {
      console.error('获取项目列表失败:', error)
      ElMessage.error('获取项目列表失败')
    }
  }

  // 获取表格数据
  const getTableData = async () => {
    loading.value = true
    try {
      const response = await getRenewableEnergyConfigList({
        projectId: queryParams.projectId,
        pageNum: queryParams.pageNum,
        pageSize: queryParams.pageSize
      })
      if (response.code === 200) {
        tableData.value = response.rows
        totalCount.value = response.total
      } else {
        ElMessage.error(response.msg || '获取数据失败')
      }
    } catch (error) {
      console.error('获取数据失败:', error)
      ElMessage.error('获取数据失败')
    } finally {
      loading.value = false
    }
  }

  // 项目变化处理
  const handleProjectChange = () => {
    queryParams.pageNum = 1
    getTableData()
  }

  // 查询处理
  const handleSearch = () => {
    queryParams.pageNum = 1
    getTableData()
  }

  // 重置表单
  const resetForm = () => {
    queryParams.projectId = undefined
    queryParams.pageNum = 1
    getTableData()
  }

  // 分页大小变化
  const handleSizeChange = (size: number) => {
    pageSize.value = size
    queryParams.pageSize = size
    queryParams.pageNum = 1
    getTableData()
  }

  // 当前页变化
  const handleCurrentChange = (page: number) => {
    currentPage.value = page
    queryParams.pageNum = page
    getTableData()
  }

  // 新增处理
  const handleAdd = () => {
    dialogType.value = 'add'
    resetConfigForm()
    dialogVisible.value = true
  }

  // 编辑处理
  const handleEdit = (row: RenewableEnergyConfig) => {
    dialogType.value = 'edit'
    configForm.id = row.id
    configForm.projectId = row.projectId
    configForm.tyngf = row.tyngf
    configForm.tynrsjrq = row.tynrsjrq
    configForm.dyrb = row.dyrb
    configForm.kqyrb = row.kqyrb
    dialogVisible.value = true
  }

  // 删除处理
  const handleDelete = async (row: RenewableEnergyConfig) => {
    if (!row.id) return

    try {
      await ElMessageBox.confirm(
        `确定要删除项目"${row.projectName}"的可再生能源配置吗？`,
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      const response = await deleteRenewableEnergyConfig(row.id)
      if (response.code === 200) {
        ElMessage.success('删除成功')
        // 如果当前页没有数据了，回到上一页
        if (tableData.value.length === 1 && queryParams.pageNum > 1) {
          queryParams.pageNum--
        }
        getTableData()
      } else {
        ElMessage.error(response.msg || '删除失败')
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除失败:', error)
        ElMessage.error('删除失败')
      }
    }
  }

  // 重置配置表单
  const resetConfigForm = () => {
    configForm.id = undefined
    configForm.projectId = undefined
    configForm.tyngf = ''
    configForm.tynrsjrq = ''
    configForm.dyrb = ''
    configForm.kqyrb = ''
    nextTick(() => {
      configFormRef.value?.clearValidate()
    })
  }

  // 提交处理
  const handleSubmit = async () => {
    if (!configFormRef.value) return

    try {
      await configFormRef.value.validate()
      submitLoading.value = true

      let response
      if (dialogType.value === 'add') {
        response = await createRenewableEnergyConfig({
          projectId: configForm.projectId!,
          tyngf: configForm.tyngf,
          tynrsjrq: configForm.tynrsjrq,
          dyrb: configForm.dyrb,
          kqyrb: configForm.kqyrb
        })
      } else {
        response = await updateRenewableEnergyConfig({
          id: configForm.id!,
          projectId: configForm.projectId!,
          tyngf: configForm.tyngf,
          tynrsjrq: configForm.tynrsjrq,
          dyrb: configForm.dyrb,
          kqyrb: configForm.kqyrb
        })
      }

      if (response.code === 200) {
        ElMessage.success(dialogType.value === 'add' ? '新增成功' : '修改成功')
        dialogVisible.value = false
        getTableData()
      } else {
        ElMessage.error(response.msg || '操作失败')
      }
    } catch (error) {
      console.error('提交失败:', error)
      ElMessage.error('操作失败')
    } finally {
      submitLoading.value = false
    }
  }

  // 恢复页面滚动
  const restoreScroll = () => {
    document.body.style.overflow = ''
    document.documentElement.style.overflow = ''
  }

  // 初始化
  onMounted(() => {
    // 先获取项目列表，项目列表加载完成后会自动获取表格数据
    getProjectList()

    // 防止异常滚动
    nextTick(() => {
      window.scrollTo(0, 0)
    })
  })

  // 组件卸载时恢复滚动
  onUnmounted(() => {
    restoreScroll()
  })
</script>

<style scoped lang="scss">
  .renewable-energy-admin {
    /* 确保页面容器有固定高度，防止无限滚动 */
    position: relative;
    height: auto;
    min-height: 600px;
    max-height: none;
    overflow: visible;

    .search-card {
      margin-bottom: 16px;

      .el-form {
        .el-form-item {
          margin-bottom: 0;
        }
      }
    }

    .operation-card {
      margin-bottom: 16px;
    }

    .table-card {
      /* 固定表格卡片高度，防止无限增长 */
      min-height: 500px;

      .renewable-energy-table {
        /* 确保ArtTable组件有固定的显示空间 */
        :deep(.art-table) {
          height: auto !important;
          min-height: 400px;

          .table-container {
            height: auto !important;
            max-height: 500px;
            overflow-y: auto;
          }
        }

        /* 修复el-table__empty-block高度不断增长问题 */
        :deep(.el-table__empty-block) {
          height: 60px !important;
          min-height: 60px !important;
          max-height: 60px !important;
          overflow: hidden;
        }

        /* 确保空表格状态下的布局稳定 */
        :deep(.el-table__body-wrapper) {
          height: auto !important;
          min-height: 200px;
          max-height: 400px;
          overflow-y: auto;
        }

        :deep(.el-table) {
          .el-table__header-wrapper {
            th {
              font-weight: 600;
              color: #606266;
              background-color: transparent;
            }
          }

          .el-table__body-wrapper {
            .el-table__row {
              &:hover {
                background-color: #f5f7fa;
              }
            }
          }
        }

        // 自定义操作按钮样式
        .operation-btns {
          display: flex;
          gap: 12px;
          justify-content: center;
        }

        .operation-btn {
          width: 32px !important;
          height: 32px !important;
          padding: 6px !important;
          margin: 0 !important;
          line-height: 1 !important; /* 确保图标垂直居中 */
          border: none !important;
          border-radius: 4px !important; /* 方形边框 */
          box-shadow: 0 2px 4px rgb(0 0 0 / 10%) !important; /* 添加阴影效果 */
          transition: all 0.3s ease !important; /* 添加过渡效果 */
        }

        .operation-btn:hover {
          box-shadow: 0 4px 8px rgb(0 0 0 / 15%) !important; /* 悬停时增强阴影 */
          transform: translateY(-2px) !important; /* 悬停时上移效果 */
        }

        .edit-btn {
          background-color: #e6f7ff !important; /* 浅蓝色背景 */
        }

        .edit-btn .el-icon {
          font-size: 16px;
          color: #409eff !important; /* 蓝色图标 */
        }

        .delete-btn {
          background-color: #fff1f0 !important; /* 浅红色背景 */
        }

        .delete-btn .el-icon {
          font-size: 16px;
          color: #f56c6c !important; /* 红色图标 */
        }
      }
    }

    .dialog-footer {
      display: flex;
      gap: 12px;
      justify-content: flex-end;
    }
  }
</style>
