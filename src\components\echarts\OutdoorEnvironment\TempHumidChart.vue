<template>
  <div ref="chartContainer" style="width: 100%; height: 300px"></div>
</template>

<script setup>
  import { ref, onMounted, watch, inject } from 'vue'
  import * as echarts from 'echarts'

  // 从父组件接收时间范围参数
  const props = defineProps({
    timeRange: {
      type: String,
      default: 'day'
    }
  })

  // 引用父组件的airQualityData和trendData
  const airQualityData = inject('airQualityData')
  const trendData = inject('trendData')

  const chartContainer = ref(null)
  let chart = null

  // 获取图表数据
  const getChartData = () => {
    // 如果有真实趋势数据，使用真实数据
    if (trendData && trendData.value && trendData.value.timeList && trendData.value.dataList) {
      console.log('使用真实趋势数据:', trendData.value)
      const times = trendData.value.timeList
      const temperatureValues = trendData.value.dataList.map((item) => item.wd || 0)
      const humidityValues = trendData.value.dataList.map((item) => item.sd || 0)

      return { times, temperatureValues, humidityValues }
    }

    // 否则使用模拟数据
    console.log('使用模拟数据，timeRange:', props.timeRange)
    return generateData(props.timeRange)
  }

  // 模拟不同时间范围的数据（作为备用）
  const generateData = (range) => {
    const times = []
    const temperatureValues = []
    const humidityValues = []

    // 根据选择的时间范围生成数据
    if (range === 'day') {
      for (let i = 0; i < 24; i++) {
        times.push(`${i}:00`)
        // 使用airQualityData中的温度和湿度值作为基准，生成波动数据
        const tempBase = parseFloat(airQualityData.find((item) => item.name === '空气温度').value)
        const humidityBase = parseFloat(
          airQualityData.find((item) => item.name === '空气湿度').value
        )

        // 生成随机波动，温度波动幅度较小
        const tempRandom = parseFloat((tempBase + (Math.random() * 4 - 2)).toFixed(1))
        const humidityRandom = Math.floor(humidityBase * (0.9 + Math.random() * 0.2))

        temperatureValues.push(tempRandom)
        humidityValues.push(humidityRandom)
      }
    } else if (range === 'week') {
      const days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
      for (let i = 0; i < 7; i++) {
        times.push(days[i])
        const tempBase = parseFloat(airQualityData.find((item) => item.name === '空气温度').value)
        const humidityBase = parseFloat(
          airQualityData.find((item) => item.name === '空气湿度').value
        )

        // 周数据有更大的波动
        const tempRandom = parseFloat((tempBase + (Math.random() * 6 - 3)).toFixed(1))
        const humidityRandom = Math.floor(humidityBase * (0.85 + Math.random() * 0.3))

        temperatureValues.push(tempRandom)
        humidityValues.push(humidityRandom)
      }
    } else if (range === 'month') {
      for (let i = 1; i <= 30; i++) {
        times.push(`${i}日`)
        const tempBase = parseFloat(airQualityData.find((item) => item.name === '空气温度').value)
        const humidityBase = parseFloat(
          airQualityData.find((item) => item.name === '空气湿度').value
        )

        // 月数据体现更明显的季节性变化
        const seasonalOffset = Math.sin((i / 30) * Math.PI) * 3
        const tempRandom = parseFloat(
          (tempBase + seasonalOffset + (Math.random() * 2 - 1)).toFixed(1)
        )
        const humidityRandom = Math.floor(
          humidityBase * (0.8 + Math.random() * 0.4) + seasonalOffset
        )

        temperatureValues.push(tempRandom)
        humidityValues.push(humidityRandom)
      }
    }

    return { times, temperatureValues, humidityValues }
  }

  // 初始化图表
  const initChart = () => {
    if (chart) {
      chart.dispose()
    }

    chart = echarts.init(chartContainer.value)
    updateChart()
  }

  // 更新图表数据
  const updateChart = () => {
    const { times, temperatureValues, humidityValues } = getChartData()

    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: ['温度', '湿度'],
        bottom: 0,
        left: 'center'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: times,
        axisLabel: {
          rotate: props.timeRange === 'month' ? 45 : 0
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '温度(°C)',
          position: 'left',
          axisLine: {
            lineStyle: {
              color: '#000'
            }
          }
        },
        {
          type: 'value',
          name: '湿度(%)',
          position: 'right',
          axisLine: {
            lineStyle: {
              color: '#000'
            }
          },
          splitLine: {
            show: false
          }
        }
      ],
      series: [
        {
          name: '温度',
          type: 'line',
          data: temperatureValues,
          smooth: true,
          lineStyle: {
            width: 3,
            color: '#EE6666'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(238, 102, 102, 0.5)' },
              { offset: 1, color: 'rgba(238, 102, 102, 0.1)' }
            ])
          }
        },
        {
          name: '湿度',
          type: 'line',
          yAxisIndex: 1,
          data: humidityValues,
          smooth: true,
          lineStyle: {
            width: 3,
            color: '#5470C6'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(84, 112, 198, 0.5)' },
              { offset: 1, color: 'rgba(84, 112, 198, 0.1)' }
            ])
          }
        }
      ]
    }

    chart.setOption(option)
  }

  // 监听时间范围变化
  watch(
    () => props.timeRange,
    () => {
      updateChart()
    }
  )

  // 监听趋势数据变化
  watch(
    () => trendData?.value,
    () => {
      if (chart) {
        updateChart()
      }
    },
    { deep: true }
  )

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    if (chart) {
      chart.resize()
    }
  })

  onMounted(() => {
    initChart()
  })
</script>

<style scoped></style>
