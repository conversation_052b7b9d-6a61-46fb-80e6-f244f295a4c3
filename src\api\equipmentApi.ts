import api from '@/utils/http'

// ========== 表计管理相关接口 ==========

// 表计信息接口
export interface EquipmentInfo {
  id?: number
  sourceId?: string | null
  projectId: number
  buildingId: number
  floorId?: number | null
  code: string
  name: string
  equipmentStatus?: string
  energyType?: string
  equipmentType: string
  installTime?: string
  maintainTime?: string
  power?: string
  status?: string | null
  equipmentCategory?: string | null
  equipmentCategorytype?: string | null
  delFlag?: string
  createBy?: string
  createTime?: string
  updateBy?: string
  updateTime?: string
  remark?: string | null
  beginTime?: string | null
  endTime?: string | null
}

// 表计列表查询参数
export interface EquipmentListParams {
  projectId?: number | string
  buildingId?: number | string
  floorId?: number | string
  code?: string
  name?: string
  equipmentStatus?: string
  equipmentType?: string
  equipmentCategory?: string
  equipmentCategorytype?: string
  pageSize: number
  pageNum: number
  beginTime?: string
  endTime?: string
}

// 表计列表响应接口
export interface EquipmentListResponse {
  total: number
  rows: EquipmentInfo[]
  code: number
  msg: string
}

// 表计详情响应接口
export interface EquipmentDetailResponse {
  msg: string
  code: number
  data: EquipmentInfo
}

// 新增表计请求参数
export interface CreateEquipmentRequest {
  projectId: number
  buildingId: number
  floorId?: number
  code: string
  name: string
  equipmentStatus?: string
  equipmentType: string
  equipmentCategory: string
  equipmentCategorytype?: string
  installTime?: string
  maintainTime?: string
  power?: string
  energyType: string
}

// 修改表计请求参数
export interface UpdateEquipmentRequest {
  id: number
  projectId: number
  buildingId: number
  floorId?: number
  code: string
  name: string
  equipmentStatus?: string
  equipmentType: string
  equipmentCategory: string
  equipmentCategorytype?: string
  installTime?: string
  maintainTime?: string
  power?: string
  energyType?: string
}

// 通用响应接口
export interface CommonResponse {
  msg: string
  code: number
}

// 监测对象选项
export interface EquipmentTypeOption {
  key: string
  label: string
}

/**
 * 获取表计管理列表
 * @param params 查询参数
 * @returns 表计列表
 */
export function getEquipmentList(params: EquipmentListParams) {
  return api.get<EquipmentListResponse>({
    url: '/basedata/equipment/list',
    params
  })
}

/**
 * 获取表计管理详细信息
 * @param id 表计ID
 * @returns 表计详情
 */
export function getEquipmentDetail(id: number) {
  return api.get<EquipmentDetailResponse>({
    url: `/basedata/equipment/${id}`
  })
}

/**
 * 新增表计管理
 * @param data 表计数据
 * @returns 新增结果
 */
export function createEquipment(data: CreateEquipmentRequest) {
  return api.post<CommonResponse>({
    url: '/basedata/equipment/',
    data
  })
}

/**
 * 修改表计管理
 * @param data 表计数据
 * @returns 修改结果
 */
export function updateEquipment(data: UpdateEquipmentRequest) {
  return api.put<CommonResponse>({
    url: '/basedata/equipment/',
    data
  })
}

/**
 * 删除表计管理
 * @param id 表计ID
 * @returns 删除结果
 */
export function deleteEquipment(id: number) {
  return api.del<CommonResponse>({
    url: `/basedata/equipment/${id}`
  })
}

/**
 * 获取监测对象字典数据
 * @returns 监测对象列表
 */
export function getEquipmentTypeDict() {
  return api.get<{
    msg: string
    code: number
    data: EquipmentTypeOption[]
  }>({
    url: '/search/getDictDataList',
    params: { dictType: 'yw_sblx' }
  })
}
