<template>
  <div ref="chartRef" class="carbon-sankey-chart"></div>
</template>

<script setup lang="ts">
  import { ref, onMounted, nextTick, onUnmounted, computed, watch } from 'vue'
  import * as echarts from 'echarts/core'
  import { SankeyChart } from 'echarts/charts'
  import { TooltipComponent, TitleComponent } from 'echarts/components'
  import { CanvasRenderer } from 'echarts/renderers'
  import { useSettingStore } from '@/store/modules/setting'
  import { SystemThemeEnum } from '@/enums/appEnum'
  import type { CarbonFootprintEnergyType } from '@/api/carbon-emission'

  // 组件名称定义
  defineOptions({
    name: 'CarbonSankeyChart'
  })

  // 组件属性定义
  interface Props {
    carbonFootprintData?: CarbonFootprintEnergyType[]
    loading?: boolean
  }

  const props = withDefaults(defineProps<Props>(), {
    carbonFootprintData: () => [],
    loading: false
  })

  // 注册 ECharts 组件
  echarts.use([Sankey<PERSON>hart, TooltipComponent, TitleComponent, CanvasRenderer])

  const chartRef = ref<HTMLElement | null>(null)
  let chartInstance: echarts.ECharts | null = null

  // 获取主题状态
  const settingStore = useSettingStore()
  const isDark = computed(() => settingStore.systemThemeType === SystemThemeEnum.DARK)

  // 能源类型颜色映射
  const energyTypeColors: Record<string, string> = {
    耗水: '#4ECB73',
    耗电: '#FFB444',
    耗气: '#FF7043',
    光伏: '#7A7FFF'
  }

  // 设备颜色映射（根据能源类型生成不同深浅的颜色）
  const getDeviceColor = (energyType: string, index: number) => {
    const baseColors: Record<string, string[]> = {
      耗水: ['#66BB6A', '#81C784', '#A5D6A7'],
      耗电: ['#FF7043', '#FFCA28', '#FFEE58', '#FFA726', '#FF8A65'],
      耗气: ['#FF8A65', '#FFAB91', '#FFCCBC'],
      光伏: ['#9C27B0', '#BA68C8', '#CE93D8']
    }
    const colors = baseColors[energyType] || ['#BDBDBD', '#E0E0E0', '#F5F5F5']
    return colors[index % colors.length] || colors[0]
  }

  // 数据转换函数：将API数据转换为桑基图格式
  const transformCarbonFootprintData = (apiData: CarbonFootprintEnergyType[]) => {
    if (!apiData || apiData.length === 0) {
      return { nodes: [], links: [] }
    }

    const nodes = [{ name: '建筑碳排放', itemStyle: { color: '#3AA1FF' } }]
    const links: any[] = []

    apiData.forEach((energyType) => {
      // 添加能源类型节点
      nodes.push({
        name: energyType.name,
        itemStyle: { color: energyTypeColors[energyType.name] || '#BDBDBD' }
      })

      // 添加从根节点到能源类型的连接
      links.push({
        source: '建筑碳排放',
        target: energyType.name,
        value: energyType.totalQuantity
      })

      // 为每个设备创建节点和连接
      energyType.dataList.forEach((device, index) => {
        const deviceNodeName = `${energyType.name}-${device.itemName}`

        nodes.push({
          name: deviceNodeName,
          itemStyle: { color: getDeviceColor(energyType.name, index) }
        })

        links.push({
          source: energyType.name,
          target: deviceNodeName,
          value: device.quantity
        })
      })
    })

    return { nodes, links }
  }

  // 计算当前使用的节点和连接数据
  const chartData = computed(() => {
    if (props.carbonFootprintData && props.carbonFootprintData.length > 0) {
      return transformCarbonFootprintData(props.carbonFootprintData)
    }

    // 默认数据（当没有传入数据时显示）
    return {
      nodes: [
        { name: '建筑碳排放', itemStyle: { color: '#3AA1FF' } },
        { name: '暂无数据', itemStyle: { color: '#BDBDBD' } }
      ],
      links: [{ source: '建筑碳排放', target: '暂无数据', value: 0 }]
    }
  })

  // 图表配置
  const getChartOption = () => {
    const { nodes, links } = chartData.value

    return {
      title: {
        text: '建筑碳排放构成分析',
        left: 'center',
        top: 10,
        textStyle: {
          color: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#333',
          fontSize: 16,
          fontWeight: 'normal'
        }
      },
      tooltip: {
        trigger: 'item',
        triggerOn: 'mousemove',
        backgroundColor: isDark.value ? 'rgba(50, 50, 50, 0.9)' : 'rgba(255, 255, 255, 0.9)',
        borderColor: isDark.value ? '#555' : '#eee',
        borderWidth: 1,
        padding: [10, 15],
        textStyle: {
          color: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#666',
          fontSize: 13
        },
        formatter: (params: any) => {
          if (params.dataType === 'edge') {
            const displaySource = params.data.source
            const displayTarget = params.data.target.includes('-')
              ? params.data.target.split('-')[1]
              : params.data.target
            return `<div style="font-weight:bold;margin-bottom:3px;">${displaySource} → ${displayTarget}</div>
                  <div>数值: ${params.data.value.toFixed(2)}</div>`
          }
          const displayName = params.name.includes('-') ? params.name.split('-')[1] : params.name
          return `<div style="font-weight:bold;margin-bottom:3px;">${displayName}</div>
                <div>数值: ${params.value ? params.value.toFixed(2) : '-'}</div>`
        }
      },
      series: [
        {
          type: 'sankey',
          left: '5%',
          right: '5%',
          top: 50,
          bottom: 20,
          data: nodes,
          links: links,
          emphasis: {
            focus: 'adjacency' // 高亮相邻的节点和边
          },
          lineStyle: {
            color: 'source', // 使用源节点的颜色
            curveness: 0.5, // 边的弯曲度
            opacity: 0.5 // 边的透明度
          },
          label: {
            show: true,
            color: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#333', // 节点文字颜色
            fontSize: 12,
            position: 'right', // 文字位置
            formatter: (params: any) => {
              // 对于设备节点，只显示设备名称（去掉能源类型前缀）
              return params.name.includes('-') ? params.name.split('-')[1] : params.name
            }
          },
          nodeAlign: 'left', // 节点对齐方式改为左对齐
          nodeGap: 12, // 减小节点间距
          layoutIterations: 64, // 增加布局迭代次数，使布局更稳定
          itemStyle: {
            // 节点默认样式
            borderWidth: 0, // 去除边框
            borderColor: isDark.value ? '#333' : '#fff',
            borderRadius: 4 // 添加圆角
          }
        }
      ]
    }
  }

  // 初始化图表方法
  const initChart = () => {
    if (!chartRef.value) return

    chartInstance = echarts.init(chartRef.value)
    chartInstance.setOption(getChartOption())

    // 响应式调整图表大小
    window.addEventListener('resize', handleResize)
  }

  // 处理窗口大小变化
  const handleResize = () => {
    if (chartInstance) {
      chartInstance.resize()
    }
  }

  onMounted(() => {
    nextTick(() => {
      initChart()
    })
  })

  // 监听主题变化，更新图表
  watch(
    () => isDark.value,
    () => {
      if (chartInstance) {
        chartInstance.setOption(getChartOption())
      }
    }
  )

  // 监听数据变化，更新图表
  watch(
    () => props.carbonFootprintData,
    () => {
      if (chartInstance) {
        chartInstance.setOption(getChartOption())
      }
    },
    { deep: true }
  )

  onUnmounted(() => {
    // 清理图表实例和事件监听
    if (chartInstance) {
      chartInstance.dispose()
      chartInstance = null
    }
    window.removeEventListener('resize', handleResize)
  })
</script>

<style scoped>
  .carbon-sankey-chart {
    width: 100%;
    height: 100%;
    background-color: var(--art-main-bg-color);
    border-radius: 4px;
  }
</style>
