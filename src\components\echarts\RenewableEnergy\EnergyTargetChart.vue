<template>
  <div class="chart-container" ref="chartContainer"></div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted } from 'vue'
  import * as echarts from 'echarts'

  const chartContainer = ref(null)
  let chart = null

  // 可再生能源发展目标数据
  const energyTargetData = {
    categories: ['太阳能', '风能', '水能', '生物质能', '其他'],
    // 2023年实际值（单位：吉瓦）
    actual: [609, 420, 415, 43, 16],
    // 2025年目标（单位：吉瓦）
    target2025: [900, 650, 430, 60, 25],
    // 2030年目标（单位：吉瓦）
    target2030: [1500, 1000, 450, 80, 40]
  }

  // 初始化图表
  const initChart = () => {
    if (chartContainer.value) {
      chart = echarts.init(chartContainer.value)
      const option = {
        title: {
          text: '中国可再生能源发展目标与进度',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['2023年实际', '2025年目标', '2030年目标'],
          bottom: 10
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          name: '装机容量 (吉瓦)',
          nameLocation: 'middle',
          nameGap: 30
        },
        yAxis: {
          type: 'category',
          data: energyTargetData.categories
        },
        series: [
          {
            name: '2023年实际',
            type: 'bar',
            data: energyTargetData.actual,
            itemStyle: {
              color: '#4CAF50'
            }
          },
          {
            name: '2025年目标',
            type: 'bar',
            data: energyTargetData.target2025,
            itemStyle: {
              color: '#FF9800',
              opacity: 0.6
            }
          },
          {
            name: '2030年目标',
            type: 'bar',
            data: energyTargetData.target2030,
            itemStyle: {
              color: '#F44336',
              opacity: 0.6
            }
          }
        ]
      }

      chart.setOption(option)

      // 监听窗口大小变化，调整图表大小
      window.addEventListener('resize', () => {
        chart.resize()
      })
    }
  }

  // 组件挂载时初始化图表
  onMounted(() => {
    initChart()
  })

  // 组件卸载时销毁图表，释放资源
  onUnmounted(() => {
    if (chart) {
      chart.dispose()
      chart = null
    }
    window.removeEventListener('resize', () => {
      if (chart) {
        chart.resize()
      }
    })
  })
</script>

<style scoped lang="scss">
  .chart-container {
    width: 100%;
    height: 400px;
  }
</style>
