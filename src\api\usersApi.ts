import api from '@/utils/http'

/**
 * 用户登录
 * @param data 登录参数
 * @returns 登录结果
 */
export function login(data: { username: string; password: string }) {
  return api.post({
    url: '/login',
    data
  })
}

/**
 * 获取用户信息
 * @returns 用户信息
 */
export function getUserInfo() {
  return api.get({
    url: '/getInfo'
  })
}

/**
 * 刷新token
 * @returns 新的token
 */
export function refreshToken() {
  return api.post({
    url: '/auth/refresh-token'
  })
}

// 个人信息相关接口

// 个人信息数据结构
export interface UserProfile {
  userId: number
  userName: string
  nickName: string
  sex: string
  email: string
  phonenumber: string
  addr: string
  remark: string
}

// 个人信息更新请求参数
export interface UpdateUserProfileRequest {
  nickName: string
  sex: string
  email: string
  phonenumber: string
  addr: string
  remark: string
}

// 个人信息接口响应
export interface UserProfileResponse {
  msg: string
  code: number
  data: UserProfile
}

/**
 * 获取个人信息
 * @returns 个人信息
 */
export function getUserProfile(): Promise<UserProfileResponse> {
  return api.get({
    url: '/system/user/profile/'
  })
}

/**
 * 更新个人信息
 * @param data 个人信息数据
 * @returns 更新结果
 */
export function updateUserProfile(data: UpdateUserProfileRequest): Promise<{
  msg: string
  code: number
}> {
  return api.put({
    url: '/system/user/profile/',
    data
  })
}

// 用户管理相关接口

/**
 * 获取用户列表
 * @param params 查询参数
 * @returns 用户列表
 */
export function getUserList(params: {
  pageSize: number
  pageNum: number
  userName?: string
  roleId?: string
}) {
  return api.get({
    url: '/system/user/getUserList',
    params
  })
}

/**
 * 添加用户
 * @param data 用户数据
 * @returns 添加结果
 */
export function addUser(data: {
  userName: string
  nickName: string
  password: string
  roleId: number
  projectId?: string
}) {
  return api.post({
    url: '/system/user/addUser',
    data
  })
}

/**
 * 修改用户
 * @param data 用户数据
 * @returns 修改结果
 */
export function updateUser(data: {
  userId: string
  userName?: string
  nickName?: string
  password?: string
  roleId?: number
  projectId?: string
}) {
  return api.post({
    url: '/system/user/updateUser',
    data
  })
}

/**
 * 删除用户
 * @param userId 用户ID
 * @returns 删除结果
 */
export function deleteUser(userId: string) {
  return api.del({
    url: `/system/user/deleteUser/${userId}`
  })
}

/**
 * 获取所有角色
 * @param params 查询参数
 * @returns 角色列表
 */
export function getRoleList(params: { pageSize: number; pageNum: number }) {
  return api.get({
    url: '/system/role/list',
    params
  })
}

/**
 * 新增角色
 * @param data 角色数据
 * @returns 新增结果
 */
export function addRole(data: { roleName: string; roleKey: string; remark?: string }) {
  return api.post({
    url: '/system/role/',
    data
  })
}

/**
 * 根据角色编号获取详细信息
 * @param roleId 角色ID
 * @returns 角色详情
 */
export function getRoleDetail(roleId: number) {
  return api.get({
    url: `/system/role/${roleId}`
  })
}

/**
 * 修改保存角色
 * @param data 角色数据
 * @returns 修改结果
 */
export function updateRole(data: {
  roleId: number
  roleName: string
  roleKey: string
  remark?: string
}) {
  return api.put({
    url: '/system/role/',
    data
  })
}

/**
 * 删除角色
 * @param roleId 角色ID
 * @returns 删除结果
 */
export function deleteRole(roleId: number) {
  return api.del({
    url: `/system/role/${roleId}`
  })
}

/**
 * 加载对应角色菜单列表树
 * @param roleId 角色ID
 * @returns 菜单列表树
 */
export function getRoleMenuTreeselect(roleId: number) {
  return api.get({
    url: `/system/menu/roleMenuTreeselect/${roleId}`
  })
}

/**
 * 设置角色菜单权限
 * @param data 角色权限数据 - 简化版本，只需要三个字段
 * @returns 设置结果
 */
export function setRoleMenu(data: { roleId: number; roleName: string; menuIds: number[] }) {
  return api.post({
    url: '/system/role/setRoleMenu',
    data
  })
}
