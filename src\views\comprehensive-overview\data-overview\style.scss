@use '@/assets/styles/variables' as *;

.data-overview {
  .breadcrumb {
    margin-bottom: 20px;

    :deep(.el-breadcrumb__item) {
      .el-breadcrumb__inner {
        font-weight: normal;
        color: var(--el-text-color-regular);
      }

      &:last-child .el-breadcrumb__inner {
        font-weight: 500;
        color: var(--el-color-primary);
      }
    }
  }

  .action-bar {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 24px;

    .action-buttons {
      display: flex;
      gap: 12px;
    }
  }

  .metrics-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 24px;

    .metric-card {
      padding: 24px;
      background: var(--art-main-bg-color);
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 12px;
      box-shadow: 0 2px 8px rgb(0 0 0 / 6%);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 16px rgb(0 0 0 / 10%);
        transform: translateY(-2px);
      }

      .metric-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;

        .metric-title {
          font-size: 14px;
          font-weight: 500;
          color: var(--el-text-color-regular);
        }

        .info-icon {
          color: var(--el-text-color-placeholder);
          cursor: pointer;

          &:hover {
            color: var(--el-color-primary);
          }
        }
      }

      .metric-value {
        .value {
          font-size: 32px;
          font-weight: 600;
          line-height: 1;
          color: var(--el-text-color-primary);
        }

        .unit {
          margin-left: 8px;
          font-size: 14px;
          color: var(--el-text-color-regular);
        }

        .progress-container {
          .el-progress {
            margin-bottom: 8px;
          }

          .progress-text {
            font-size: 24px;
            font-weight: 600;
            color: var(--el-color-primary);
          }
        }
      }
    }
  }

  .charts-container {
    .chart-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .chart-card {
        padding: 24px;
        background: var(--art-main-bg-color);
        border: 1px solid var(--el-border-color-lighter);
        border-radius: 12px;
        box-shadow: 0 2px 8px rgb(0 0 0 / 6%);

        &.full-width {
          grid-column: 1 / -1;
        }

        .chart-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 20px;

          h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
          }

          .chart-legend {
            display: flex;
            gap: 16px;

            .legend-item {
              display: flex;
              gap: 6px;
              align-items: center;
              font-size: 12px;
              color: var(--el-text-color-regular);

              .legend-color {
                width: 12px;
                height: 12px;
                border-radius: 2px;
              }
            }
          }
        }

        .chart-content {
          .chart-wrapper {
            width: 100%;
            height: 350px;
          }
        }
      }
    }
  }

  // 响应式设计
  @media only screen and (max-width: $device-ipad) {
    .metrics-cards {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    .charts-container {
      .chart-row {
        grid-template-columns: 1fr;
        gap: 16px;

        .chart-card {
          padding: 20px;

          .chart-content {
            .chart-wrapper {
              height: 300px;
            }
          }
        }
      }
    }
  }

  @media only screen and (max-width: $device-phone) {
    .action-bar {
      .action-buttons {
        flex-direction: column;
        width: 100%;

        .el-button {
          width: 100%;
        }
      }
    }

    .metrics-cards {
      .metric-card {
        padding: 20px;

        .metric-value {
          .value {
            font-size: 28px;
          }
        }
      }
    }

    .charts-container {
      .chart-row {
        .chart-card {
          padding: 16px;

          .chart-header {
            flex-direction: column;
            gap: 12px;
            align-items: flex-start;

            .chart-legend {
              flex-wrap: wrap;
              gap: 12px;
            }
          }

          .chart-content {
            .chart-wrapper {
              height: 250px;
            }
          }
        }
      }
    }
  }

  // 暗色主题适配
  .dark & {
    .metric-card,
    .chart-card {
      background: var(--art-main-bg-color);
      border-color: var(--el-border-color);
      box-shadow: 0 2px 8px rgb(0 0 0 / 20%);

      &:hover {
        box-shadow: 0 4px 16px rgb(0 0 0 / 30%);
      }
    }
  }
}

// 对话框样式优化
.el-dialog {
  .dialog-footer {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
  }
}
