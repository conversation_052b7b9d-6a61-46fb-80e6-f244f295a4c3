import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { Message } from '@/api/deepseekApi'
import { getSysStorage, setSysStorage } from '@/utils/storage'

// 聊天消息接口
export interface ChatMessage {
  id: string
  role: 'system' | 'user' | 'assistant'
  content: string
  timestamp: number
  isThinking?: boolean
  reasoningContent?: string // 添加思考过程内容
}

// 添加消息参数接口
export interface AddMessageParams {
  role: 'system' | 'user' | 'assistant'
  content: string
  isThinking?: boolean
  reasoningContent?: string // 添加思考过程内容
}

// 聊天会话接口P
export interface Conversation {
  id: string
  title: string
  messages: ChatMessage[]
  createdAt: number
  updatedAt: number
}

// 存储键
const STORAGE_KEY = 'chat_conversations'

// 创建聊天 store
export const useChatStore = defineStore('chat', () => {
  // 所有会话
  const conversations = ref<Conversation[]>(getSysStorage(STORAGE_KEY) || [])

  // 当前会话 ID
  const currentConversationId = ref<string | null>(null)

  // 加载状态
  const isLoading = ref(false)

  // 当前会话
  const currentConversation = computed(() => {
    return conversations.value.find((conv) => conv.id === currentConversationId.value) || null
  })

  // 保存会话到本地存储
  const saveConversations = () => {
    setSysStorage(STORAGE_KEY, conversations.value)
  }

  // 创建新会话
  const createConversation = (title: string = '新对话') => {
    const id = `conv_${Date.now()}`
    const newConversation: Conversation = {
      id,
      title,
      messages: [],
      createdAt: Date.now(),
      updatedAt: Date.now()
    }

    conversations.value.unshift(newConversation)
    currentConversationId.value = id
    saveConversations()

    return id
  }

  // 删除会话
  const deleteConversation = (id: string) => {
    const index = conversations.value.findIndex((conv) => conv.id === id)
    if (index !== -1) {
      conversations.value.splice(index, 1)

      // 如果删除的是当前会话，切换到第一个会话或创建新会话
      if (currentConversationId.value === id) {
        if (conversations.value.length > 0) {
          currentConversationId.value = conversations.value[0].id
        } else {
          createConversation()
        }
      }

      saveConversations()
    }
  }

  // 重命名会话
  const renameConversation = (id: string, newTitle: string) => {
    const conversation = conversations.value.find((conv) => conv.id === id)
    if (conversation) {
      conversation.title = newTitle
      conversation.updatedAt = Date.now()
      saveConversations()
    }
  }

  // 切换会话
  const switchConversation = (id: string) => {
    if (conversations.value.some((conv) => conv.id === id)) {
      currentConversationId.value = id
    }
  }

  // 添加消息到当前会话
  const addMessage = (message: AddMessageParams) => {
    if (!currentConversation.value) {
      createConversation()
    }

    const newMessage: ChatMessage = {
      id: `msg_${Date.now()}`,
      ...message,
      timestamp: Date.now()
    }

    currentConversation.value!.messages.push(newMessage)
    currentConversation.value!.updatedAt = Date.now()

    // 如果是第一条用户消息，使用它作为会话标题
    if (
      message.role === 'user' &&
      currentConversation.value!.messages.filter((m) => m.role === 'user').length === 1
    ) {
      const title = message.content.slice(0, 30) + (message.content.length > 30 ? '...' : '')
      currentConversation.value!.title = title
    }

    saveConversations()
    return newMessage
  }

  // 更新消息
  const updateMessage = (messageId: string, updates: Partial<ChatMessage>) => {
    if (!currentConversation.value) return

    const message = currentConversation.value.messages.find((msg) => msg.id === messageId)
    if (message) {
      Object.assign(message, updates)
      currentConversation.value.updatedAt = Date.now()
      saveConversations()
    }
  }

  // 删除消息
  const deleteMessage = (messageId: string) => {
    if (!currentConversation.value) return

    const index = currentConversation.value.messages.findIndex((msg) => msg.id === messageId)
    if (index !== -1) {
      currentConversation.value.messages.splice(index, 1)
      currentConversation.value.updatedAt = Date.now()
      saveConversations()
    }
  }

  // 清空当前会话
  const clearCurrentConversation = () => {
    if (currentConversation.value) {
      currentConversation.value.messages = []
      currentConversation.value.updatedAt = Date.now()
      saveConversations()
    }
  }

  // 设置加载状态
  const setLoading = (loading: boolean) => {
    isLoading.value = loading
  }

  // 获取会话的消息历史（用于 API 请求）
  const getConversationMessages = (conversationId: string): Message[] => {
    const conversation = conversations.value.find((conv) => conv.id === conversationId)
    if (!conversation) return []

    return conversation.messages
      .filter((msg) => !msg.isThinking) // 过滤掉思考中的消息
      .map((msg) => ({
        role: msg.role,
        content: msg.content
      }))
  }

  // 初始化：如果没有会话，创建一个新会话
  if (conversations.value.length === 0) {
    createConversation()
  } else if (!currentConversationId.value) {
    // 如果有会话但没有选中的会话，选择第一个
    currentConversationId.value = conversations.value[0].id
  }

  return {
    conversations,
    currentConversationId,
    currentConversation,
    isLoading,
    createConversation,
    deleteConversation,
    renameConversation,
    switchConversation,
    addMessage,
    updateMessage,
    deleteMessage,
    clearCurrentConversation,
    setLoading,
    getConversationMessages
  }
})
