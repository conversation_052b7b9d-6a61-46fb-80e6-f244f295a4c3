<template>
  <div class="chat-container" :class="{ 'dark-mode': isDark }">
    <!-- 左侧对话列表 -->
    <div class="conversation-list">
      <div class="list-header">
        <h2>对话列表</h2>
        <el-button type="primary" size="small" @click="createNewConversation">
          <el-icon><Plus /></el-icon>
          新对话
        </el-button>
      </div>

      <el-input
        v-model="searchQuery"
        placeholder="搜索对话"
        prefix-icon="Search"
        clearable
        class="search-input"
      />

      <el-scrollbar class="conversation-scrollbar">
        <div
          v-for="conversation in filteredConversations"
          :key="conversation.id"
          class="conversation-item"
          :class="{ active: conversation.id === currentConversationId }"
          @click="switchConversation(conversation.id)"
        >
          <div class="conversation-info">
            <el-icon><ChatDotRound /></el-icon>
            <div class="title">{{ conversation.title }}</div>
            <div class="time">{{ formatTime(conversation.updatedAt) }}</div>
          </div>

          <div class="conversation-actions">
            <el-popconfirm
              title="确定要删除这个对话吗？"
              @confirm="deleteConversation(conversation.id)"
              width="200"
            >
              <template #reference>
                <el-button type="danger" size="small" circle @click.stop>
                  <el-icon><Delete /></el-icon>
                </el-button>
              </template>
            </el-popconfirm>
          </div>
        </div>
      </el-scrollbar>
    </div>

    <!-- 右侧聊天区域 -->
    <div class="chat-main">
      <!-- 聊天头部 -->
      <div class="chat-header">
        <div class="header-info">
          <h2>{{ currentConversation?.title || '新对话' }}</h2>
          <div class="model-info">模型: Deepseek r1</div>
        </div>

        <div class="header-actions">
          <el-button type="danger" size="small" plain @click="clearCurrentConversation">
            <el-icon><Delete /></el-icon>
            清空对话
          </el-button>
        </div>
      </div>

      <!-- 聊天消息区域 -->
      <div class="chat-messages" ref="messagesContainer">
        <template v-if="currentConversation && currentConversation.messages.length > 0">
          <div
            v-for="message in currentConversation.messages"
            :key="message.id"
            class="message-container"
          >
            <!-- AI消息 -->
            <div v-if="message.role === 'assistant'" class="message message-ai">
              <div class="message-avatar">
                <div class="message-time">{{ formatMessageTime(message.timestamp) }}</div>
                <el-avatar :size="40" :src="aiAvatar" />
              </div>
              <div class="message-content">
                <div class="message-header">
                  <span class="sender-name">AI助手</span>
                </div>
                <div class="message-body" :class="{ thinking: message.isThinking }">
                  <template
                    v-if="message.isThinking && !message.reasoningContent && !message.content"
                  >
                    <div class="thinking-indicator">
                      <span></span>
                      <span></span>
                      <span></span>
                    </div>
                  </template>
                  <template v-else>
                    <!-- 思考内容 (reasoning_content) -->
                    <div v-if="message.reasoningContent" class="reasoning-content">
                      <div class="reasoning-header">思考过程:</div>
                      <MarkdownRenderer :content="message.reasoningContent" />
                    </div>
                    <!-- 最终回答 (content) -->
                    <div v-if="message.content" class="final-content">
                      <div v-if="message.reasoningContent" class="final-header">最终回答:</div>
                      <MarkdownRenderer :content="message.content" />
                    </div>
                  </template>
                </div>
              </div>
            </div>

            <!-- 用户消息 -->
            <div v-else class="message message-user">
              <div class="message-content">
                <div class="message-header">
                  <span class="sender-name">你</span>
                </div>
                <div class="message-body">
                  <MarkdownRenderer :content="message.content" />
                </div>
              </div>
              <div class="message-avatar">
                <div class="message-time">{{ formatMessageTime(message.timestamp) }}</div>
                <el-avatar :size="40" :src="userAvatar" />
              </div>
            </div>
          </div>
        </template>

        <div v-else class="empty-chat">
          <el-empty description="开始一个新的对话吧">
            <el-button type="primary" @click="focusInput">开始对话</el-button>
          </el-empty>
        </div>
      </div>

      <!-- 聊天输入区域 -->
      <div class="chat-input-area">
        <el-input
          v-model="messageInput"
          type="textarea"
          :rows="3"
          placeholder="输入消息，按 Enter 发送，Shift + Enter 换行"
          resize="none"
          @keydown.enter.prevent="handleEnterKey"
          ref="inputRef"
        />

        <div class="input-actions">
          <el-button
            type="primary"
            :loading="isLoading"
            :disabled="!messageInput.trim()"
            @click="sendMessage"
          >
            发送
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, nextTick, watch } from 'vue'
  import { storeToRefs } from 'pinia'
  import { useSettingStore } from '@/store/modules/setting'
  import { useChatStore, ChatMessage } from '@/store/modules/chat'
  import { deepseekService, Message } from '@/api/deepseekApi'
  // @ts-ignore
  import MarkdownRenderer from '@/components/chat/MarkdownRenderer.vue'
  import { Plus, Delete, ChatDotRound } from '@element-plus/icons-vue'
  import { ElMessage } from 'element-plus'

  // 导入头像 - 统一使用logo
  import userAvatar from '@/assets/img/login/logo.png'
  // 使用自定义机器人图标作为AI头像
  const aiAvatar = ref(
    'data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231890ff"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-9.5c1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3 1.34 3 3 3zm0 1.5c-2 0-6 1-6 3v1h12v-1c0-2-4-3-6-3z"/></svg>'
  )

  // 获取主题设置
  const settingStore = useSettingStore()
  const { isDark } = storeToRefs(settingStore)

  // 获取聊天 store
  const chatStore = useChatStore()
  const { conversations, currentConversationId, currentConversation, isLoading } =
    storeToRefs(chatStore)

  // 搜索查询
  const searchQuery = ref('')

  // 过滤后的对话列表
  const filteredConversations = computed(() => {
    if (!searchQuery.value) return conversations.value

    const query = searchQuery.value.toLowerCase()
    return conversations.value.filter((conv) => conv.title.toLowerCase().includes(query))
  })

  // 消息输入
  const messageInput = ref('')
  const inputRef = ref<HTMLTextAreaElement | null>(null)
  const messagesContainer = ref<HTMLElement | null>(null)

  // 创建新对话
  const createNewConversation = () => {
    chatStore.createConversation()
    focusInput()
  }

  // 切换对话
  const switchConversation = (id: string) => {
    chatStore.switchConversation(id)
  }

  // 删除对话
  const deleteConversation = (id: string) => {
    chatStore.deleteConversation(id)
  }

  // 清空当前对话
  const clearCurrentConversation = () => {
    chatStore.clearCurrentConversation()
  }

  // 聚焦输入框
  const focusInput = () => {
    nextTick(() => {
      inputRef.value?.focus()
    })
  }

  // 处理 Enter 键
  const handleEnterKey = (e: Event) => {
    const keyEvent = e as KeyboardEvent
    if (keyEvent.shiftKey) return // Shift + Enter 换行
    sendMessage()
  }

  // 发送消息
  const sendMessage = async () => {
    const content = messageInput.value.trim()
    if (!content || isLoading.value) return

    // 清空输入框
    messageInput.value = ''

    // 添加用户消息
    chatStore.addMessage({
      role: 'user',
      content
    })

    // 添加 AI 思考中消息
    const thinkingMessage = chatStore.addMessage({
      role: 'assistant',
      content: '',
      isThinking: true,
      reasoningContent: '' // 初始化思考内容为空
    })

    // 滚动到底部
    scrollToBottom()

    try {
      // 设置加载状态
      chatStore.setLoading(true)

      // 获取对话历史
      const messages = chatStore.getConversationMessages(
        currentConversationId.value!
      ) as ChatMessage[]

      // 使用流式 API 发送请求到 Deepseek
      await deepseekService.sendStreamMessage(
        messages,
        // 思考内容更新回调
        (reasoning) => {
          if (thinkingMessage) {
            chatStore.updateMessage(thinkingMessage.id, {
              reasoningContent: reasoning,
              isThinking: true
            })
            scrollToBottom()
          }
        },
        // 最终回答更新回调
        (content) => {
          if (thinkingMessage) {
            chatStore.updateMessage(thinkingMessage.id, {
              content: content,
              isThinking: false
            })
            scrollToBottom()
          }
        }
      )
    } catch (error) {
      console.error('发送消息失败:', error)

      if (thinkingMessage) {
        // 更新为错误消息
        chatStore.updateMessage(thinkingMessage.id, {
          content: '抱歉，发生了错误，请稍后再试。',
          isThinking: false,
          reasoningContent: ''
        })
      }

      ElMessage.error('发送消息失败，请稍后再试')
    } finally {
      // 清除加载状态
      chatStore.setLoading(false)

      // 滚动到底部
      scrollToBottom()
    }
  }

  // 滚动到底部
  const scrollToBottom = () => {
    nextTick(() => {
      if (messagesContainer.value) {
        messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
      }
    })
  }

  // 格式化时间
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp)
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)

    if (date.toDateString() === today.toDateString()) {
      return '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
    } else if (date.toDateString() === yesterday.toDateString()) {
      return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
    } else {
      return date.toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' })
    }
  }

  // 格式化消息时间
  const formatMessageTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  }

  // 监听消息变化，滚动到底部
  watch(
    () => currentConversation.value?.messages.length,
    () => {
      scrollToBottom()
    }
  )

  // 组件挂载后
  onMounted(() => {
    // 如果没有对话，创建一个新对话
    if (conversations.value.length === 0) {
      createNewConversation()
    }

    // 滚动到底部
    scrollToBottom()

    // 聚焦输入框
    focusInput()
  })
</script>

<style lang="scss" scoped>
  .chat-container {
    display: flex;
    height: calc(100vh - 180px);
    min-height: 600px;
    overflow: hidden;
    background-color: var(--art-main-bg-color);
    border: 1px solid var(--art-border-color);
    border-radius: 10px;

    &.dark-mode {
      border-color: var(--art-border-dashed-color);

      .message-ai .message-content .message-body {
        background-color: var(--art-gray-800);
      }

      .message-avatar .message-time {
        color: var(--art-gray-400);
      }
    }
  }

  // 左侧对话列表
  .conversation-list {
    display: flex;
    flex-direction: column;
    width: 280px;
    border-right: 1px solid var(--art-border-color);

    .list-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      border-bottom: 1px solid var(--art-border-color);

      h2 {
        margin: 0;
        font-size: 18px;
        font-weight: 500;
      }
    }

    .search-input {
      width: calc(100% - 24px);
      margin: 12px;
    }

    .conversation-scrollbar {
      flex: 1;
      overflow-y: auto;
    }

    .conversation-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      cursor: pointer;
      border-bottom: 1px solid var(--art-border-color);
      transition: background-color 0.2s;

      &:hover {
        background-color: var(--art-color);
      }

      &.active {
        background-color: var(--el-color-primary-light-9);
      }

      .conversation-info {
        display: flex;
        gap: 8px;
        align-items: center;
        width: 100%;
        overflow: hidden;

        .title {
          max-width: 120px;
          overflow: hidden;
          font-weight: 500;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .time {
          min-width: 60px;
          margin-left: auto;
          font-size: 12px;
          color: var(--el-text-color-secondary);
          text-align: right;
        }
      }

      .conversation-actions {
        opacity: 0;
        transition: opacity 0.2s;
      }

      &:hover .conversation-actions {
        opacity: 1;
      }
    }
  }

  // 右侧聊天区域
  .chat-main {
    display: flex;
    flex: 1;
    flex-direction: column;

    .chat-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      border-bottom: 1px solid var(--art-border-color);

      .header-info {
        h2 {
          margin: 0;
          font-size: 18px;
          font-weight: 500;
        }

        .model-info {
          margin-top: 4px;
          font-size: 12px;
          color: var(--el-text-color-secondary);
        }
      }
    }

    .chat-messages {
      flex: 1;
      padding: 16px;
      overflow-y: auto;

      .message-container {
        width: 100%;
        margin-bottom: 24px;
      }

      .message {
        display: flex;
        align-items: flex-start;
        margin-bottom: 4px;
      }

      // AI消息样式 - 左侧显示
      .message-ai {
        .message-avatar {
          position: relative;
          margin-right: 12px;

          .message-time {
            position: absolute;
            top: -18px;
            left: 0;
            padding: 0 4px;
            font-size: 12px;
            color: var(--el-text-color-secondary);
            white-space: nowrap;
            background-color: rgb(255 255 255 / 70%);
            border-radius: 4px;
          }
        }

        .message-content {
          max-width: 80%;

          .message-header {
            margin-bottom: 4px;
          }

          .message-body {
            display: inline-block;
            min-width: 60px;
            padding: 10px 14px;
            word-break: break-word;
            background-color: #f4f6f8;
            border-radius: 2px 12px 12px;
            box-shadow: 0 2px 4px rgb(0 0 0 / 5%);
            transition: all 0.3s ease;

            &.thinking {
              min-height: 24px;
            }

            .dark-mode & {
              background-color: var(--art-gray-800);
              box-shadow: 0 2px 4px rgb(0 0 0 / 15%);
            }

            // 思考内容样式
            .reasoning-content {
              padding: 10px;
              margin-bottom: 16px;
              font-size: 0.9em;
              color: #666;
              background-color: #f8f8f8;
              border-left: 3px solid #ddd;
              border-radius: 6px;
              transition: all 0.3s ease;
              animation: fadeIn 0.3s ease;

              .reasoning-header {
                margin-bottom: 6px;
                font-size: 0.85em;
                font-weight: 600;
                color: #888;
              }

              .dark-mode & {
                color: #aaa;
                background-color: #2d2d2d;
                border-left-color: #555;
              }
            }

            // 最终回答样式
            .final-content {
              font-size: 1em;
              color: var(--el-text-color-primary);
              transition: all 0.3s ease;
              animation: fadeIn 0.3s ease;

              .final-header {
                margin-bottom: 6px;
                font-size: 0.9em;
                font-weight: 600;
                color: var(--el-color-primary);
              }
            }
          }
        }
      }

      // 用户消息样式 - 右侧显示
      .message-user {
        justify-content: flex-end;

        .message-avatar {
          position: relative;
          margin-left: 12px;

          .message-time {
            position: absolute;
            top: -18px;
            right: 0;
            padding: 0 4px;
            font-size: 12px;
            color: var(--el-text-color-secondary);
            white-space: nowrap;
            background-color: rgb(255 255 255 / 70%);
            border-radius: 4px;
          }
        }

        .message-content {
          max-width: 80%;

          .message-header {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 4px;
          }

          .message-body {
            display: inline-block;
            padding: 10px 14px;
            word-break: break-word;
            background-color: var(--el-color-primary-light-9);
            border-radius: 12px 2px 12px 12px;
            box-shadow: 0 2px 4px rgb(0 0 0 / 5%);
          }
        }
      }

      .empty-chat {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
      }
    }

    .chat-input-area {
      padding: 16px;
      border-top: 1px solid var(--art-border-color);

      .input-actions {
        display: flex;
        justify-content: flex-end;
        margin-top: 12px;
      }
    }
  }

  // 思考中动画
  .thinking-indicator {
    display: flex;
    gap: 4px;
    align-items: center;

    span {
      display: inline-block;
      width: 8px;
      height: 8px;
      background-color: var(--el-color-primary);
      border-radius: 50%;
      animation: thinking 1.4s infinite ease-in-out both;

      &:nth-child(1) {
        animation-delay: -0.32s;
      }

      &:nth-child(2) {
        animation-delay: -0.16s;
      }
    }
  }

  @keyframes thinking {
    0%,
    80%,
    100% {
      transform: scale(0);
    }

    40% {
      transform: scale(1);
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(5px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  // 响应式设计
  @media screen and (width <= 768px) {
    .chat-container {
      flex-direction: column;
      height: calc(100vh - 120px);
    }

    .conversation-list {
      width: 100%;
      height: 200px;
      border-right: none;
      border-bottom: 1px solid var(--art-border-color);
    }
  }
</style>
