<template>
  <div class="container page-content">
    <!-- 顶部卡片 -->
    <el-card class="overview-card">
      <template #header>
        <div class="card-header">
          <div class="header-title">
            <el-icon class="header-icon"><DataAnalysis /></el-icon>
            <span>可再生能源监测数据</span>
          </div>
          <div class="filter-controls">
            <ProjectSelector
              ref="projectSelectorRef"
              v-model="selectedProjectId"
              placeholder="请选择项目"
              style="width: 400px"
              @change="handleProjectChange"
              @loaded="handleProjectListLoaded"
            />

            <el-button
              type="primary"
              @click="handleSearch"
              :loading="loading"
              :disabled="queryButtonDisabled"
              >查询</el-button
            >
            <el-button @click="handleReset">重置</el-button>
          </div>
        </div>
      </template>

      <div class="data-grid">
        <div class="data-item" v-for="(card, index) in cardData" :key="index">
          <div class="item-value">
            {{ card.value }}<span class="unit">{{ card.unit }}</span>
          </div>
          <div class="item-name">{{ card.title }}</div>
        </div>
      </div>
    </el-card>

    <!-- 监控面板 -->
    <div class="monitor-panels">
      <el-card class="panel-item pv-params">
        <div class="panel-header">
          <div class="panel-icon">
            <el-icon><Monitor /></el-icon>
          </div>
          <div class="panel-title">光伏发电量统计</div>
        </div>
        <div class="panel-grid photovoltaic-params">
          <div v-for="(param, index) in pvParameters" :key="index" class="param-item">
            <div class="param-icon">
              <el-icon><component :is="param.icon" /></el-icon>
            </div>
            <div class="param-content">
              <div class="param-label">{{ param.label }}</div>
              <div class="param-value">{{ param.value }}</div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 数据筛选及图表 -->
    <el-card class="chart-container">
      <div class="filter-container">
        <div class="filter-row">
          <div class="filter-item">
            <span class="filter-label">时间</span>
            <el-radio-group v-model="queryParams.timeGranularity" class="time-granularity-buttons">
              <el-radio-button
                v-for="item in timeGranularityOptions"
                :key="item.value"
                :label="item.value"
              >
                {{ item.label }}
              </el-radio-button>
            </el-radio-group>
          </div>
          <div class="filter-item date-filter">
            <span class="filter-label">日期选择：</span>
            <div class="date-picker-group">
              <!-- 日期和月份使用范围选择器 -->
              <el-date-picker
                v-if="queryParams.timeGranularity !== 'year'"
                v-model="queryParams.dateRange"
                :type="datePickerType"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :shortcuts="dateShortcuts"
                :disabled-date="disabledDate"
                class="date-picker"
              />
              <!-- 年份使用范围选择器 -->
              <el-date-picker
                v-else
                v-model="queryParams.yearRange"
                type="yearrange"
                range-separator="至"
                start-placeholder="开始年份"
                end-placeholder="结束年份"
                :disabled-date="disabledDate"
                class="date-picker"
                value-format="YYYY"
              />
            </div>
          </div>
          <div class="filter-actions">
            <el-button
              type="primary"
              @click="handleChartSearch"
              :loading="chartLoading"
              :disabled="chartQueryButtonDisabled"
            >
              查询
            </el-button>
            <el-button @click="resetChartForm"> 重置 </el-button>
            <el-button type="success" @click="handleChartExport" :loading="chartExporting">
              导出数据
            </el-button>
          </div>
        </div>
      </div>

      <PowerGenerationChart
        :timeGranularity="queryParams.timeGranularity"
        :dateRange="queryParams.dateRange"
        :chartData="photovoltaicData"
      />
    </el-card>
  </div>
</template>

<script setup>
  import { ref, reactive, computed, nextTick, watch } from 'vue'
  import { Histogram, Calendar, TrendCharts, DataAnalysis, Monitor } from '@element-plus/icons-vue'
  import { ElMessage } from 'element-plus'
  import PowerGenerationChart from '@/components/echarts/RenewableEnergy/PowerGenerationChart.vue'
  import ProjectSelector from '@/components/ProjectSelector.vue'
  import {
    getRenewableEnergyData,
    getPhotovoltaicData
  } from '@/api/energy-monitoring/renewableEnergyApi'

  // 项目选择相关数据
  const selectedProjectId = ref(null) // 初始为空，等待获取项目列表后设置第一个
  const selectedProjectInfo = ref(null)
  const projectSelectorRef = ref()

  // 保留原有的项目和点位选择（用于兼容）
  const selectedProject = ref('')
  const selectedPoint = ref('')

  // 数据加载状态
  const loading = ref(false)
  const chartLoading = ref(false)
  const chartExporting = ref(false)

  // API数据存储
  const renewableEnergyData = ref(null)
  const photovoltaicData = ref(null)

  // 顶部卡片数据 - 将从API获取后更新
  const cardData = ref([
    { title: '太阳能光伏装机容量', value: '0', unit: 'kWp' },
    { title: '太阳能热水集热器面积', value: '0', unit: 'm²' },
    { title: '地源热泵制冷/热量', value: '0', unit: 'kW' },
    { title: '空气源热泵制热量', value: '0', unit: 'kW' }
  ])

  // 处理项目变化
  const handleProjectChange = (projectInfo) => {
    selectedProjectInfo.value = projectInfo
    selectedPoint.value = ''
    // 清空查询缓存，允许重新查询
    lastQueryParams.value = null
    queryButtonDisabled.value = false
    lastChartQueryParams.value = null
    chartQueryButtonDisabled.value = false
    // 当项目选择变化时，自动发送查询请求
    if (projectInfo && projectInfo.id) {
      // 自动执行查询
      nextTick(() => {
        handleSearch()
      })
    }
  }

  // 监听项目选择器组件的项目列表加载完成
  const handleProjectListLoaded = (projectList) => {
    // 当项目列表加载完成后，自动选择第一个项目并发请求
    if (projectList && projectList.length > 0) {
      const firstProject = projectList[0]
      selectedProjectId.value = firstProject.id
      selectedProjectInfo.value = firstProject
      // 自动发请求获取数据
      nextTick(() => {
        handleSearch()
      })
    }
  }

  // 查询状态管理 - 防重复查询
  const lastQueryParams = ref(null)
  const queryButtonDisabled = ref(false) // 查询按钮状态

  // 比较查询参数是否相同
  const isSameQueryParams = (params1, params2) => {
    if (!params1 || !params2) return false
    return params1.projectId === params2.projectId
  }

  const handleSearch = async () => {
    // 如果没有选择项目，不发请求
    if (!selectedProjectId.value) {
      return
    }

    // 如果查询按钮已禁用，不执行查询
    if (queryButtonDisabled.value) {
      ElMessage.info('查询条件未变化，无需重复查询')
      return
    }

    // 获取当前查询参数
    const currentParams = {
      projectId: selectedProjectId.value
    }

    // 检查查询参数是否与上次相同
    if (isSameQueryParams(currentParams, lastQueryParams.value)) {
      ElMessage.info('查询条件未变化')
      queryButtonDisabled.value = true
      return
    }

    loading.value = true

    try {
      // 调用可再生能源监测数据接口
      await fetchRenewableEnergyData()

      // 调用光伏发电量统计接口
      await fetchPhotovoltaicData()

      // 保存当前查询参数
      lastQueryParams.value = { ...currentParams }
      // 禁用查询按钮（因为已经查询过相同条件）
      queryButtonDisabled.value = true
    } catch (error) {
    } finally {
      loading.value = false
    }
  }

  // 获取可再生能源监测数据
  const fetchRenewableEnergyData = async () => {
    try {
      const params = {
        projectId: selectedProjectId.value
      }

      const response = await getRenewableEnergyData(params)

      if (response.code === 200) {
        renewableEnergyData.value = response.data
        // 根据返回数据更新顶部卡片数据
        updateCardData(response.data)
      }
    } catch (error) {
      console.error('获取可再生能源监测数据失败:', error)
      throw error
    }
  }

  // 获取光伏发电量统计数据
  const fetchPhotovoltaicData = async () => {
    try {
      // 构建时间参数 - 使用当前图表查询的时间范围
      const endDate = new Date()
      const startDate = new Date()
      startDate.setTime(startDate.getTime() - 3600 * 1000 * 24 * 7) // 默认最近一周

      const params = {
        projectId: selectedProjectId.value,
        beginTime: formatDateForApi(startDate),
        endTime: formatDateForApi(endDate),
        timeType: 0 // 默认按日查询
      }

      const response = await getPhotovoltaicData(params)

      if (response.code === 200) {
        photovoltaicData.value = response.data
        // 更新光伏发电量统计显示
        updatePvParameters(response.data)
      }
    } catch (error) {
      console.error('获取光伏发电量统计数据失败:', error)
      throw error
    }
  }

  // 格式化日期为API需要的格式，根据时间粒度决定格式
  const formatDateForApi = (date, timeGranularity = 'day') => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')

    switch (timeGranularity) {
      case 'year':
        return String(year)
      case 'month':
        return `${year}-${month}`
      case 'day':
      default:
        return `${year}-${month}-${day}`
    }
  }

  // 根据可再生能源监测数据更新顶部卡片
  const updateCardData = (data) => {
    if (data && typeof data === 'object') {
      // 根据实际API返回的数据结构更新卡片数据
      // tyngf: 太阳能光伏装机容量
      if (data.tyngf !== undefined) {
        cardData.value[0].value = data.tyngf.toString()
      }

      // tynrsjrq: 太阳能热水集热器面积
      if (data.tynrsjrq !== undefined) {
        cardData.value[1].value = data.tynrsjrq.toString()
      }

      // dyrb: 地源热泵制冷/热量
      if (data.dyrb !== undefined) {
        cardData.value[2].value = data.dyrb.toString()
      }

      // kqyrb: 空气源热泵制热量
      if (data.kqyrb !== undefined) {
        cardData.value[3].value = data.kqyrb.toString()
      }
    }
  }

  // 根据光伏发电量统计数据更新显示
  const updatePvParameters = (data) => {
    if (data) {
      pvParameters.value = [
        { label: '本日', value: `${data.todayData || 0} kWh`, icon: Calendar },
        { label: '本月', value: `${data.monthData || 0} kWh`, icon: Histogram },
        { label: '本年', value: `${data.yearData || 0} kWh`, icon: TrendCharts }
      ]
    }
  }

  const handleReset = () => {
    // 重置为第一个项目
    if (projectSelectorRef.value && projectSelectorRef.value.projectList?.length > 0) {
      const firstProject = projectSelectorRef.value.projectList[0]
      selectedProjectId.value = firstProject.id
      selectedProjectInfo.value = firstProject
    } else {
      selectedProjectId.value = null
      selectedProjectInfo.value = null
    }

    selectedProject.value = ''
    selectedPoint.value = ''
    // 清空查询缓存，允许重新查询
    lastQueryParams.value = null
    queryButtonDisabled.value = false
    lastChartQueryParams.value = null
    chartQueryButtonDisabled.value = false
    handleSearch()
  }

  // 光伏发电量统计 - 初始为0，将从API获取后更新
  const pvParameters = ref([
    { label: '本日', value: '0 kWh', icon: Calendar },
    { label: '本月', value: '0 kWh', icon: Histogram },
    { label: '本年', value: '0 kWh', icon: TrendCharts }
  ])

  // --- 图表筛选区域逻辑开始 ---

  // 查询参数
  const queryParams = reactive({
    timeGranularity: 'day', // 默认按日查询
    dateRange: [],
    yearRange: [] // 年份范围选择值
  })

  // 时间粒度选项
  const timeGranularityOptions = [
    { value: 'day', label: '日' },
    { value: 'month', label: '月' },
    { value: 'year', label: '年' }
  ]

  // 根据选择的时间粒度动态设置日期选择器类型
  const datePickerType = computed(() => {
    switch (queryParams.timeGranularity) {
      case 'day':
        return 'daterange'
      case 'month':
        return 'monthrange'
      case 'year':
        return 'year' // 年份使用范围选择
      default:
        return 'daterange'
    }
  })

  // 日期快捷选项 (与 EnergyMonitoring.vue 保持一致)
  const dateShortcuts = [
    {
      text: '最近一周',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
        return [start, end]
      }
    },
    {
      text: '最近一个月',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        return [start, end]
      }
    },
    {
      text: '最近三个月',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
        return [start, end]
      }
    }
  ]

  // 禁用日期选择 (与 EnergyMonitoring.vue 保持一致)
  const disabledDate = (time) => {
    return time.getTime() > Date.now()
  }

  // 图表查询状态管理 - 防重复查询
  const lastChartQueryParams = ref(null)
  const chartQueryButtonDisabled = ref(false) // 图表查询按钮状态

  // 比较图表查询参数是否相同
  const isSameChartQueryParams = (params1, params2) => {
    if (!params1 || !params2) return false
    return (
      params1.projectId === params2.projectId &&
      params1.timeGranularity === params2.timeGranularity &&
      JSON.stringify(params1.dateRange) === JSON.stringify(params2.dateRange) &&
      JSON.stringify(params1.yearRange) === JSON.stringify(params2.yearRange)
    )
  }

  // 图表查询按钮点击事件
  const handleChartSearch = async () => {
    if (!selectedProjectId.value) {
      ElMessage.warning('请先选择项目')
      return
    }

    // 验证时间选择
    if (queryParams.timeGranularity === 'year') {
      if (!queryParams.yearRange || queryParams.yearRange.length !== 2) {
        ElMessage.warning('请选择年份范围')
        return
      }
    } else {
      if (!queryParams.dateRange || queryParams.dateRange.length !== 2) {
        ElMessage.warning('请选择日期范围')
        return
      }
    }

    // 如果图表查询按钮已禁用，不执行查询
    if (chartQueryButtonDisabled.value) {
      ElMessage.info('查询条件未变化，无需重复查询')
      return
    }

    // 获取当前图表查询参数
    const currentParams = {
      projectId: selectedProjectId.value,
      timeGranularity: queryParams.timeGranularity,
      dateRange: queryParams.dateRange,
      yearRange: queryParams.yearRange
    }

    // 检查查询参数是否与上次相同
    if (isSameChartQueryParams(currentParams, lastChartQueryParams.value)) {
      ElMessage.info('查询条件未变化')
      chartQueryButtonDisabled.value = true
      return
    }

    chartLoading.value = true

    try {
      // 构建光伏发电量统计接口参数
      let beginTime, endTime

      if (
        queryParams.timeGranularity === 'year' &&
        queryParams.yearRange &&
        queryParams.yearRange.length === 2
      ) {
        // 年份范围选择：如果开始和结束年份相同，只发送一个年份值
        if (queryParams.yearRange[0] === queryParams.yearRange[1]) {
          // 相同年份时，只发送一个值
          beginTime = queryParams.yearRange[0]
          endTime = queryParams.yearRange[0]
        } else {
          // 不同年份时，发送范围
          beginTime = queryParams.yearRange[0]
          endTime = queryParams.yearRange[1]
        }
      } else if (queryParams.dateRange) {
        beginTime = formatDateForApi(queryParams.dateRange[0], queryParams.timeGranularity)
        endTime = formatDateForApi(queryParams.dateRange[1], queryParams.timeGranularity)
      }

      const params = {
        projectId: selectedProjectId.value,
        beginTime,
        endTime,
        timeType: getTimeTypeFromGranularity(queryParams.timeGranularity)
      }

      const response = await getPhotovoltaicData(params)

      if (response.code === 200) {
        photovoltaicData.value = response.data
        // 更新光伏发电量统计显示
        updatePvParameters(response.data)
        ElMessage.success('图表数据获取成功')

        // 保存当前查询参数
        lastChartQueryParams.value = { ...currentParams }
        // 禁用查询按钮（因为已经查询过相同条件）
        chartQueryButtonDisabled.value = true
      } else {
        ElMessage.error(response.msg || '图表数据获取失败')
      }
    } catch (error) {
      console.error('获取图表数据失败:', error)
      ElMessage.error('图表数据获取失败，请稍后重试')
    } finally {
      chartLoading.value = false
    }
  }

  // 将时间粒度转换为API需要的timeType
  const getTimeTypeFromGranularity = (granularity) => {
    switch (granularity) {
      case 'day':
        return 0
      case 'month':
        return 1
      case 'year':
        return 2
      default:
        return 0
    }
  }

  // 图表重置按钮点击事件
  const resetChartForm = () => {
    queryParams.timeGranularity = 'day'
    queryParams.yearRange = []
    // 设置默认时间范围为最近一周
    const end = new Date()
    const start = new Date()
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
    queryParams.dateRange = [start, end]
    // 清空图表查询缓存，允许重新查询
    lastChartQueryParams.value = null
    chartQueryButtonDisabled.value = false
    // 可能需要重新触发查询
    handleChartSearch()
  }

  // 图表导出按钮点击事件
  const handleChartExport = () => {
    chartExporting.value = true
    // 添加导出图表数据的逻辑
    setTimeout(() => {
      chartExporting.value = false
    }, 1500) // 模拟导出
  }

  // 设置默认日期范围 (与 EnergyMonitoring.vue 保持一致)
  const setDefaultDateRange = () => {
    const end = new Date()
    const start = new Date()
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
    queryParams.dateRange = [start, end]
  }

  // 初始化时设置默认日期范围
  setDefaultDateRange()

  // --- 监听器部分开始 ---

  // 监听时间粒度变化，自动触发查询
  watch(
    () => queryParams.timeGranularity,
    (newVal, oldVal) => {
      if (newVal !== oldVal && selectedProjectId.value) {
        console.log('时间粒度变化:', newVal)
        // 清空图表查询缓存，允许重新查询
        lastChartQueryParams.value = null
        chartQueryButtonDisabled.value = false
        // 自动触发查询
        nextTick(() => {
          handleChartSearch()
        })
      }
    }
  )

  // 监听日期范围变化，自动触发查询
  watch(
    () => queryParams.dateRange,
    (newVal, oldVal) => {
      if (JSON.stringify(newVal) !== JSON.stringify(oldVal) && selectedProjectId.value) {
        console.log('日期范围变化:', newVal)
        // 清空图表查询缓存，允许重新查询
        lastChartQueryParams.value = null
        chartQueryButtonDisabled.value = false
        // 自动触发查询
        nextTick(() => {
          handleChartSearch()
        })
      }
    },
    { deep: true }
  )

  // 监听年份范围选择变化，自动触发查询
  watch(
    () => queryParams.yearRange,
    (newVal, oldVal) => {
      if (JSON.stringify(newVal) !== JSON.stringify(oldVal) && selectedProjectId.value) {
        console.log('年份范围选择变化:', newVal)
        // 清空图表查询缓存，允许重新查询
        lastChartQueryParams.value = null
        chartQueryButtonDisabled.value = false
        // 自动触发查询
        nextTick(() => {
          handleChartSearch()
        })
      }
    },
    { deep: true }
  )

  // --- 监听器部分结束 ---

  // --- 图表筛选区域逻辑结束 ---
</script>

<style lang="scss" scoped>
  .container {
    padding: 2rem 1rem;
    margin: 0 auto;

    // 顶部卡片
    .overview-card {
      margin-bottom: 2rem;

      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .header-title {
          display: flex;
          align-items: center;

          .header-icon {
            margin-right: 8px;
            font-size: 18px;
            color: rgb(var(--art-success)); /* 修改：使用成功主题色变量 */
          }

          span {
            font-size: 16px;
            font-weight: 500;
            color: var(--art-text-gray-800); /* 修改：使用文本颜色变量 */
          }
        }

        .filter-controls {
          display: flex;
          gap: 10px;
          align-items: center;
        }
      }

      .data-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        padding: 1rem 0;

        .data-item {
          flex: 1;
          min-width: 120px;
          padding: 16px 10px;
          text-align: center;
          background-color: var(--art-gray-100); /* 修改：使用灰色背景变量 */
          border-radius: 8px;
          box-shadow: var(--art-box-shadow-xs); /* 修改：使用阴影变量 */

          .item-value {
            margin-bottom: 8px;
            font-size: 22px;
            font-weight: 600;
            color: rgb(var(--art-success)); /* 修改：使用成功主题色变量 */

            .unit {
              margin-left: 2px;
              font-size: 14px;
              color: rgb(var(--art-success)); /* 修改：使用成功主题色变量 */
            }
          }

          .item-name {
            font-size: 14px;
            color: var(--art-text-gray-600); /* 修改：使用文本颜色变量 */
          }
        }
      }
    }

    // 监控面板
    .monitor-panels {
      grid-template-columns: repeat(2, 1fr);
      gap: 1.5rem;
      margin-bottom: 2rem;

      .panel-item {
        position: relative;
        overflow: hidden;
        border-radius: 10px;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 10px 15px rgb(0 0 0 / 10%);
          transform: translateY(-5px);
        }

        &.pv-params {
          .panel-header,
          .param-icon {
            color: rgb(var(--art-info)); /* 修改：使用信息主题色变量 */
          }

          .param-value {
            color: rgb(var(--art-info)); /* 修改：使用信息主题色变量 */
          }
        }

        &.pv-stats {
          .panel-header,
          .stat-icon {
            color: rgb(var(--art-success)); /* 修改：使用成功主题色变量 */
          }

          .stat-value {
            color: rgb(var(--art-success)); /* 修改：使用成功主题色变量 */
          }
        }

        .panel-header {
          display: flex;
          align-items: center;
          padding-bottom: 1rem;
          margin-bottom: 1.5rem;
          border-bottom: 1px solid var(--art-border-color); /* 修改：使用边框颜色变量 */

          .panel-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.5rem;
            font-size: 1.5rem;
          }

          .panel-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--art-text-gray-800); /* 修改：使用文本颜色变量 */
          }
        }

        .panel-grid {
          display: grid;
          gap: 1.5rem;

          &.photovoltaic-params {
            grid-template-columns: repeat(3, 1fr);
          }

          &.photovoltaic-stats {
            grid-template-columns: repeat(3, 1fr);
          }

          .param-item,
          .stat-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            background-color: var(--art-gray-100); /* 修改：使用灰色背景变量 */
            border-radius: 8px;
            transition: all 0.2s ease;

            &:hover {
              background-color: rgb(var(--art-bg-info)); /* 修改：使用信息背景色变量 */
              transform: scale(1.02);
            }

            .param-icon {
              /* 分开 param-icon 和 stat-icon 以便应用不同背景 */
              display: flex;
              align-items: center;
              justify-content: center;
              width: 40px;
              height: 40px;
              margin-right: 0.75rem;
              font-size: 1.25rem;
              background-color: rgba(var(--art-info), 0.1); /* 修改：使用信息主题色RGB变量 */
              border-radius: 50%;
            }

            .stat-icon {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 40px;
              height: 40px;
              margin-right: 0.75rem;
              font-size: 1.25rem;
              background-color: rgba(var(--art-success), 0.1); /* 修改：使用成功主题色RGB变量 */
              border-radius: 50%;
            }

            .param-content,
            .stat-content {
              flex: 1;
            }

            .param-label,
            .stat-label {
              margin-bottom: 0.25rem;
              font-size: 0.875rem;
              color: var(--art-text-gray-600); /* 修改：使用文本颜色变量 */
            }

            .param-value,
            .stat-value {
              font-size: 1.25rem;
              font-weight: 600;
            }
          }

          /* .stat-item .stat-icon 已在上方 .stat-icon 中定义 */
        }
      }
    }

    // 数据筛选及图表
    .chart-container {
      padding: 16px;
      margin-bottom: 2rem;
      background-color: var(--art-main-bg-color); /* 修改：使用主背景色变量 */
      border-radius: 8px;
      box-shadow: var(--art-root-card-box-shadow); /* 修改：使用卡片阴影变量 */

      .filter-container {
        margin-bottom: 16px;

        .filter-row {
          display: flex;
          flex-wrap: wrap;
          gap: 16px;
          align-items: center;

          &:last-child {
            margin-bottom: 0;
          }
        }

        .filter-item {
          display: flex;
          align-items: center;
          margin-right: 0;

          .filter-label {
            margin-right: 8px;
            font-size: 14px;
            color: var(--art-text-gray-600); /* 修改：使用文本颜色变量 */
            white-space: nowrap;
          }

          .filter-select {
            width: 200px;
          }

          .time-granularity-buttons {
            margin-left: 8px;
          }
        }

        .date-filter {
          .date-picker-group {
            display: flex;

            .date-picker {
              width: 100%;
              max-width: 300px;
            }
          }
        }

        .filter-actions {
          display: flex;
          gap: 10px;
          justify-content: flex-end;
          margin-left: auto;
        }
      }
    }
  }

  // 响应式布局
  @media (width <= 1200px) {
    .container {
      .data-grid {
        grid-template-columns: repeat(2, 1fr);
      }

      .monitor-panels {
        grid-template-columns: 1fr;
      }
    }
  }

  @media (width <= 768px) {
    .container {
      .card-header {
        flex-direction: column;
        align-items: flex-start;

        .filter-controls {
          flex-wrap: wrap;
          margin-top: 10px;
        }
      }

      .filter-container .filter-row {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;

        .filter-item {
          margin-right: 0;
        }

        .date-filter .date-picker-group .date-picker {
          max-width: none;
        }

        .filter-actions {
          justify-content: flex-start;
          margin-top: 10px;
          margin-left: 0;
        }
      }
    }
  }
</style>
