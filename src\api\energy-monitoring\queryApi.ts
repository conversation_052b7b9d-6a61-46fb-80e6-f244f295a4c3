import request from '@/utils/http'

// 能耗数据查询相关接口

/**
 * 能耗数据查询参数
 */
export interface EnergySearchParams {
  projectId?: string | number
  buildingId?: string | number
  beginTime?: string
  endTime?: string
  equipmentType?: string
  pageSize: number
  pageNum: number
}

/**
 * 能耗数据项
 */
export interface EnergySearchItem {
  id?: number
  projectName: string
  buildingName: string
  reportTime: string
  wQuantity: number // 耗水量
  eQuantity: number // 耗电量
  gQuantity: number // 耗气量
  cQuantity: number // 折标煤
  co2Quantity: number // 碳排放量
}

/**
 * 能耗数据查询响应
 */
export interface EnergySearchResponse {
  total: number
  rows: EnergySearchItem[]
  code: number
  msg: string
}

/**
 * 获取能耗数据查询列表
 */
export function getEnergySearch(params: EnergySearchParams): Promise<EnergySearchResponse> {
  return request.get({
    url: '/energy/energyconsume/getEnergySearch',
    params
  })
}

/**
 * 设备类型选项
 */
export interface EquipmentTypeOption {
  key: string
  label: string
}

/**
 * 获取设备类型字典数据
 * @returns 设备类型列表
 */
export function getEquipmentTypeDict() {
  return request.get<{
    msg: string
    code: number
    data: EquipmentTypeOption[]
  }>({
    url: '/search/getDictDataList',
    params: { dictType: 'yw_sblx' }
  })
}

/**
 * 能耗数据查询参数
 */
export interface EnergyDataParams {
  projectId?: string | number
  buildingId?: string | number
  beginTime?: string
  endTime?: string
  timeType?: string | number
  equipmentType?: string
}

/**
 * 能耗数据响应
 */
export interface EnergyDataResponse {
  msg: string
  code: number
  data: {
    timeList: string[]
    dataList: number[]
    unit?: string
  }
}

/**
 * 获取能耗数据
 */
export function getEnergyData(params: EnergyDataParams): Promise<EnergyDataResponse> {
  return request.get({
    url: '/energy/energyconsume/getEnergyData',
    params
  })
}
