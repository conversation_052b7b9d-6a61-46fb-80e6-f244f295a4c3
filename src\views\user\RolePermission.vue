<template>
  <div class="role-permission-container">
    <!-- 页面头部 -->
    <div class="permission-header">
      <div class="header-content">
        <div class="header-left"> </div>
        <div class="header-actions">
          <el-button type="primary" @click="handleRefreshPermissions" :loading="refreshLoading">
            <el-icon><Refresh /></el-icon>
            刷新权限
          </el-button>
          <el-button type="success" @click="handleBatchOperation">
            <el-icon><Operation /></el-icon>
            批量操作
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="permission-main">
      <!-- 左侧角色列表 -->
      <div class="role-sidebar">
        <div class="sidebar-header">
          <h3>角色列表</h3>
          <el-input
            v-model="roleSearchKeyword"
            placeholder="搜索角色..."
            size="small"
            clearable
            @input="handleRoleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <div class="role-list" v-loading="loading">
          <div
            v-for="role in filteredRoles"
            :key="role.roleId"
            :class="['role-card', { active: selectedRole?.roleId === role.roleId }]"
            @click="selectRole(role)"
          >
            <div class="role-info">
              <div class="role-name">{{ role.roleName }}</div>
              <div class="role-key">{{ role.roleKey }}</div>
              <div class="role-status">
                <el-tag :type="role.status === '0' ? 'success' : 'danger'" size="small">
                  {{ role.status === '0' ? '正常' : '停用' }}
                </el-tag>
              </div>
            </div>
            <div class="role-actions" v-if="!role.admin">
              <el-icon class="action-icon"><Setting /></el-icon>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="filteredRoles.length === 0" class="empty-state">
            <el-empty description="暂无角色数据" :image-size="80" />
          </div>
        </div>
      </div>

      <!-- 右侧权限配置区域 -->
      <div class="permission-content">
        <!-- 未选择角色时的提示 -->
        <div v-if="!selectedRole" class="no-selection">
          <div class="no-selection-content">
            <el-icon class="no-selection-icon"><UserFilled /></el-icon>
            <h3>请选择一个角色</h3>
            <p>从左侧角色列表中选择一个角色来配置其权限</p>
          </div>
        </div>

        <!-- 权限配置内容 -->
        <div v-else class="permission-config">
          <!-- 角色信息卡片 -->
          <div class="role-info-card">
            <div class="role-avatar">
              <el-icon><Avatar /></el-icon>
            </div>
            <div class="role-details">
              <h2>{{ selectedRole.roleName }}</h2>
              <p class="role-description">{{ selectedRole.roleKey }}</p>
              <div class="role-meta">
                <span class="meta-item">
                  <el-icon><Calendar /></el-icon>
                  创建时间：{{ formatDate(selectedRole.createTime) }}
                </span>
                <span class="meta-item">
                  <el-icon><Document /></el-icon>
                  备注：{{ selectedRole.remark || '暂无备注' }}
                </span>
              </div>
            </div>
            <div class="role-status-badge">
              <el-tag :type="selectedRole.status === '0' ? 'success' : 'danger'" size="large">
                {{ selectedRole.status === '0' ? '正常' : '停用' }}
              </el-tag>
            </div>
          </div>

          <!-- 菜单权限配置 -->
          <div class="permission-content-main">
            <div class="tab-header">
              <div class="tab-title">
                <el-icon><Menu /></el-icon>
                <span>菜单访问权限</span>
              </div>
              <div class="tab-actions">
                <el-button size="small" @click="handleExpandAll">
                  <el-icon><Plus /></el-icon>
                  展开全部
                </el-button>
                <el-button size="small" @click="handleCollapseAll">
                  <el-icon><Minus /></el-icon>
                  折叠全部
                </el-button>
                <el-button type="primary" size="small" @click="handleSelectAll">
                  <el-icon><Check /></el-icon>
                  全选
                </el-button>
                <el-button size="small" @click="handleUnselectAll">
                  <el-icon><Close /></el-icon>
                  取消全选
                </el-button>
              </div>
            </div>

            <div class="menu-tree-container" v-loading="menuTreeLoading">
              <el-tree
                ref="menuTreeRef"
                :data="menuTreeData"
                show-checkbox
                node-key="id"
                :check-strictly="false"
                :default-expanded-keys="expandedKeys"
                :default-checked-keys="checkedKeys"
                :props="treeProps"
                @check="handleMenuCheck"
                class="permission-tree"
              >
                <template #default="{ node, data }">
                  <div class="tree-node">
                    <div class="node-content">
                      <el-icon class="node-icon">
                        <component :is="getMenuIcon(data)" />
                      </el-icon>
                      <span class="node-label">{{ node.label }}</span>
                    </div>
                    <div class="node-meta">
                      <el-tag size="small" type="info">{{ data.path || 'N/A' }}</el-tag>
                    </div>
                  </div>
                </template>
              </el-tree>
            </div>
          </div>

          <!-- 底部操作按钮 -->
          <div class="permission-footer">
            <el-button @click="handleResetPermissions">
              <el-icon><RefreshLeft /></el-icon>
              重置权限
            </el-button>
            <el-button type="primary" @click="handleSavePermissions" :loading="permissionLoading">
              <el-icon><Check /></el-icon>
              {{ permissionLoading ? '保存中...' : '保存权限' }}
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 批量操作对话框 -->
    <el-dialog
      v-model="batchDialogVisible"
      title="批量权限操作"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="batch-operation-content">
        <el-alert
          title="批量操作提示"
          description="您可以选择多个角色进行批量权限配置，操作将应用到所有选中的角色。"
          type="info"
          show-icon
          :closable="false"
        />

        <el-form :model="batchForm" label-width="100px" style="margin-top: 20px">
          <el-form-item label="操作类型">
            <el-radio-group v-model="batchForm.operationType">
              <el-radio value="copy">复制权限</el-radio>
              <el-radio value="clear">清空权限</el-radio>
              <el-radio value="template">应用模板</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="源角色" v-if="batchForm.operationType === 'copy'">
            <el-select v-model="batchForm.sourceRoleId" placeholder="请选择源角色">
              <el-option
                v-for="role in tableData"
                :key="role.roleId"
                :label="role.roleName"
                :value="role.roleId"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="权限模板" v-if="batchForm.operationType === 'template'">
            <el-select v-model="batchForm.templateId" placeholder="请选择权限模板">
              <el-option label="管理员模板" value="admin" />
              <el-option label="普通用户模板" value="user" />
              <el-option label="只读用户模板" value="readonly" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleBatchSubmit" :loading="batchLoading">
            {{ batchLoading ? '执行中...' : '确定' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, nextTick, computed } from 'vue'
  import { ElMessage, ElMessageBox, ElTree, FormInstance, FormRules } from 'element-plus'
  import {
    Search,
    Refresh,
    Plus,
    Lock,
    Setting,
    UserFilled,
    Avatar,
    Calendar,
    Document,
    Menu,
    Check,
    Close,
    Minus,
    RefreshLeft,
    Folder
  } from '@element-plus/icons-vue'
  import { getRoleList, getRoleMenuTreeselect, setRoleMenu } from '@/api/usersApi'

  // 类型定义
  interface ApiResponse {
    code: number
    msg?: string
    rows?: any[]
    total?: number
    data?: any
  }

  interface RoleItem {
    roleId: number
    roleName: string
    roleKey: string
    roleSort?: number
    status: string
    remark?: string
    createTime?: string
    admin?: boolean
  }

  interface MenuTreeItem {
    id: number
    label: string
    path?: string
    children?: MenuTreeItem[]
  }

  // 响应式数据
  const loading = ref(false)
  const menuTreeLoading = ref(false)
  const permissionLoading = ref(false)
  const refreshLoading = ref(false)
  const batchLoading = ref(false)

  // 对话框状态
  const batchDialogVisible = ref(false)

  // 角色相关数据
  const tableData = ref<RoleItem[]>([])
  const selectedRole = ref<RoleItem | null>(null)
  const roleSearchKeyword = ref('')

  // 分页数据
  const pagination = reactive({
    pageNum: 1,
    pageSize: 10,
    total: 0
  })

  // 菜单权限相关数据
  const menuTreeRef = ref<InstanceType<typeof ElTree>>()
  const menuTreeData = ref<MenuTreeItem[]>([])
  const checkedKeys = ref<number[]>([])
  const expandedKeys = ref<number[]>([])
  const treeProps = {
    children: 'children',
    label: 'label'
  }

  // 批量操作相关数据
  const batchForm = reactive({
    operationType: 'copy',
    sourceRoleId: undefined as number | undefined,
    templateId: undefined as string | undefined
  })

  // 计算属性
  const filteredRoles = computed(() => {
    if (!roleSearchKeyword.value) {
      return tableData.value
    }
    return tableData.value.filter(
      (role) =>
        role.roleName.toLowerCase().includes(roleSearchKeyword.value.toLowerCase()) ||
        role.roleKey.toLowerCase().includes(roleSearchKeyword.value.toLowerCase())
    )
  })

  // 工具方法
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return '-'
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getMenuIcon = (data: MenuTreeItem) => {
    // 根据菜单数据返回对应的图标组件
    if (data.children && data.children.length > 0) {
      return 'Folder'
    }
    return 'Document'
  }

  // 获取角色列表
  const fetchRoleList = async () => {
    try {
      loading.value = true
      const params = {
        pageSize: pagination.pageSize,
        pageNum: pagination.pageNum
      }

      const response = (await getRoleList(params)) as any

      if (response.code === 200 || response.code === 1) {
        tableData.value = response.rows || []
        pagination.total = response.total || 0
      } else {
        ElMessage.error(response.msg || '获取角色列表失败')
      }
    } catch (error) {
      console.error('获取角色列表失败:', error)
      ElMessage.error('获取角色列表失败')
    } finally {
      loading.value = false
    }
  }

  // 角色相关方法
  const selectRole = async (role: RoleItem) => {
    selectedRole.value = role
    await loadRolePermissions(role)
  }

  const handleRoleSearch = () => {
    // 搜索逻辑已通过计算属性实现
  }

  // 权限加载方法
  const loadRolePermissions = async (role: RoleItem) => {
    try {
      menuTreeLoading.value = true
      console.log('获取角色菜单权限，角色ID:', role.roleId)
      const response = (await getRoleMenuTreeselect(role.roleId)) as any
      console.log('获取菜单权限响应:', response)

      if (response.code === 200) {
        menuTreeData.value = response.data?.menus || response.menus || []
        const rawCheckedKeys = response.data?.checkedKeys || response.checkedKeys || []

        // 过滤出叶子节点（没有子节点的节点）进行回显
        // 这样可以避免父节点被错误回显，让父节点状态由子节点自动决定
        const getLeafNodeIds = (nodes: any[], allCheckedIds: number[]): number[] => {
          let leafIds: number[] = []
          nodes.forEach((node) => {
            if (allCheckedIds.includes(node.id)) {
              if (!node.children || node.children.length === 0) {
                // 叶子节点
                leafIds.push(node.id)
              } else {
                // 有子节点，递归处理子节点
                leafIds = leafIds.concat(getLeafNodeIds(node.children, allCheckedIds))
              }
            }
          })
          return leafIds
        }

        checkedKeys.value = getLeafNodeIds(menuTreeData.value, rawCheckedKeys)
        console.log('原始选中节点:', rawCheckedKeys)
        console.log('过滤后的叶子节点:', checkedKeys.value)

        // 设置默认展开的节点
        if (menuTreeData.value.length > 0) {
          const getAllIds = (nodes: any[]): number[] => {
            let ids: number[] = []
            nodes.forEach((node) => {
              ids.push(node.id)
              if (node.children && node.children.length > 0) {
                ids = ids.concat(getAllIds(node.children))
              }
            })
            return ids
          }
          expandedKeys.value = getAllIds(menuTreeData.value)
        }
      } else {
        ElMessage.error(response.msg || '获取菜单权限失败')
      }
    } catch (error) {
      console.error('获取菜单权限失败:', error)
      ElMessage.error('获取菜单权限失败')
    } finally {
      menuTreeLoading.value = false
    }
  }

  // 头部操作方法
  const handleRefreshPermissions = async () => {
    refreshLoading.value = true
    try {
      await fetchRoleList()
      if (selectedRole.value) {
        await loadRolePermissions(selectedRole.value)
      }
      ElMessage.success('权限数据已刷新')
    } catch (error) {
      ElMessage.error('刷新失败')
    } finally {
      refreshLoading.value = false
    }
  }

  const handleBatchOperation = () => {
    batchDialogVisible.value = true
  }

  const handleBatchSubmit = async () => {
    try {
      batchLoading.value = true
      // 这里实现批量操作逻辑
      await new Promise((resolve) => setTimeout(resolve, 1000)) // 模拟API调用
      ElMessage.success('批量操作成功')
      batchDialogVisible.value = false
    } catch (error) {
      ElMessage.error('批量操作失败')
    } finally {
      batchLoading.value = false
    }
  }

  const handleResetPermissions = () => {
    ElMessageBox.confirm('确定要重置当前角色的所有权限吗？', '重置确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      if (menuTreeRef.value) {
        menuTreeRef.value.setCheckedKeys([])
      }
      ElMessage.success('权限已重置')
    })
  }

  const handleSavePermissions = async () => {
    if (!selectedRole.value || !menuTreeRef.value) return

    try {
      permissionLoading.value = true

      // 获取选中的菜单ID（包括完全选中和半选中的节点）
      const checkedNodes = menuTreeRef.value.getCheckedNodes()
      const halfCheckedNodes = menuTreeRef.value.getHalfCheckedNodes()
      const menuIds = [
        ...checkedNodes.map((node: any) => node.id),
        ...halfCheckedNodes.map((node: any) => node.id)
      ]

      // 构建请求数据
      const data = {
        roleId: selectedRole.value.roleId,
        roleName: selectedRole.value.roleName || '',
        menuIds: menuIds
      }

      console.log('发送权限设置请求:', data)
      const response = (await setRoleMenu(data)) as any

      if (response.code === 200) {
        ElMessage.success('权限保存成功')
      } else {
        ElMessage.error(response.msg || '权限保存失败')
      }
    } catch (error) {
      console.error('权限保存失败:', error)
      ElMessage.error('权限保存失败')
    } finally {
      permissionLoading.value = false
    }
  }

  // 菜单树操作方法
  const handleMenuCheck = (data: any, checked: any) => {
    if (!menuTreeRef.value) return
    console.log('菜单选择变化:', {
      nodeId: data.id,
      nodeLabel: data.label,
      isChecked: checked.checkedKeys.includes(data.id),
      checkedKeys: checked.checkedKeys,
      checkedNodes: checked.checkedNodes.map((node: any) => ({ id: node.id, label: node.label }))
    })
  }

  const handleSelectAll = () => {
    if (!menuTreeRef.value || !menuTreeData.value.length) return
    const getAllNodeIds = (nodes: any[]): number[] => {
      let ids: number[] = []
      nodes.forEach((node) => {
        ids.push(node.id)
        if (node.children && node.children.length > 0) {
          ids = ids.concat(getAllNodeIds(node.children))
        }
      })
      return ids
    }
    const allNodeIds = getAllNodeIds(menuTreeData.value)
    menuTreeRef.value.setCheckedKeys(allNodeIds)
  }

  const handleUnselectAll = () => {
    if (!menuTreeRef.value) return
    menuTreeRef.value.setCheckedKeys([])
  }

  const handleExpandAll = () => {
    if (!menuTreeRef.value || !menuTreeData.value.length) return
    const getAllNodeIds = (nodes: any[]): number[] => {
      let ids: number[] = []
      nodes.forEach((node) => {
        ids.push(node.id)
        if (node.children && node.children.length > 0) {
          ids = ids.concat(getAllNodeIds(node.children))
        }
      })
      return ids
    }
    const allNodeIds = getAllNodeIds(menuTreeData.value)
    expandedKeys.value = allNodeIds
    allNodeIds.forEach((nodeId) => {
      const node = menuTreeRef.value!.getNode(nodeId)
      if (node && !node.expanded) {
        node.expand()
      }
    })
  }

  const handleCollapseAll = () => {
    if (!menuTreeRef.value) return
    expandedKeys.value = []
    const collapseAllNodes = (nodes: any[]) => {
      nodes.forEach((node) => {
        const treeNode = menuTreeRef.value!.getNode(node.id)
        if (treeNode && treeNode.expanded) {
          treeNode.collapse()
        }
        if (node.children && node.children.length > 0) {
          collapseAllNodes(node.children)
        }
      })
    }
    if (menuTreeData.value.length > 0) {
      collapseAllNodes(menuTreeData.value)
    }
  }

  // 组件挂载时获取数据
  onMounted(() => {
    fetchRoleList()
  })
</script>

<style lang="scss" scoped>
  @use '@/assets/styles/variables' as *;

  .role-permission-container {
    min-height: 100vh;
    background: var(--art-bg-color);

    .permission-header {
      margin-bottom: 20px;
      background: var(--art-main-bg-color);
      border-radius: 12px;
      box-shadow: var(--art-card-shadow);

      .header-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 24px 32px;

        .header-left {
          .page-title {
            display: flex;
            gap: 12px;
            align-items: center;
            margin-bottom: 8px;

            .title-icon {
              font-size: 28px;
              color: var(--el-color-primary);
            }

            h1 {
              margin: 0;
              font-size: 24px;
              font-weight: 600;
              color: var(--art-text-gray-900);
            }
          }

          .page-subtitle {
            margin: 0;
            font-size: 14px;
            color: var(--art-text-gray-600);
          }
        }

        .header-actions {
          display: flex;
          gap: 12px;
        }
      }
    }

    .permission-main {
      display: flex;
      gap: 20px;
      height: calc(100vh - 200px);

      .role-sidebar {
        display: flex;
        flex-direction: column;
        width: 320px;
        background: var(--art-main-bg-color);
        border-radius: 12px;
        box-shadow: var(--art-card-shadow);

        .sidebar-header {
          padding: 20px;
          border-bottom: 1px solid var(--art-border-color);

          h3 {
            margin: 0 0 16px;
            font-size: 16px;
            font-weight: 600;
            color: var(--art-text-gray-900);
          }
        }

        .role-list {
          flex: 1;
          padding: 8px;
          overflow-y: auto;

          // 自定义滚动条样式
          &::-webkit-scrollbar {
            width: 6px;
          }

          &::-webkit-scrollbar-track {
            background: var(--art-bg-primary);
            border-radius: 3px;
          }

          &::-webkit-scrollbar-thumb {
            background: var(--el-color-primary);
            border-radius: 3px;

            &:hover {
              background: var(--el-color-primary-dark-2);
            }
          }

          .role-card {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            margin-bottom: 8px;
            cursor: pointer;
            border: 2px solid transparent;
            border-radius: 8px;
            transition: all 0.2s ease;

            &:hover {
              background: var(--art-bg-primary);
              transform: translateY(-1px);
            }

            &.active {
              background: var(--art-bg-primary);
              border-color: var(--el-color-primary);
              box-shadow: 0 4px 12px rgba(var(--art-primary), 0.15);
            }

            .role-info {
              flex: 1;

              .role-name {
                margin-bottom: 4px;
                font-weight: 600;
                color: var(--art-text-gray-900);
              }

              .role-key {
                margin-bottom: 8px;
                font-size: 12px;
                color: var(--art-text-gray-600);
              }
            }

            .role-actions {
              .action-icon {
                font-size: 16px;
                color: var(--art-text-gray-400);
              }
            }
          }

          .empty-state {
            padding: 40px 20px;
            text-align: center;
          }
        }
      }

      .permission-content {
        display: flex;
        flex: 1;
        flex-direction: column;
        background: var(--art-main-bg-color);
        border-radius: 12px;
        box-shadow: var(--art-card-shadow);

        .no-selection {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100%;

          .no-selection-content {
            text-align: center;

            .no-selection-icon {
              margin-bottom: 16px;
              font-size: 64px;
              color: var(--art-text-gray-300);
            }

            h3 {
              margin: 0 0 8px;
              color: var(--art-text-gray-600);
            }

            p {
              margin: 0;
              font-size: 14px;
              color: var(--art-text-gray-400);
            }
          }
        }

        .permission-config {
          display: flex;
          flex-direction: column;
          height: 100%;

          .role-info-card {
            display: flex;
            align-items: center;
            padding: 24px 32px;
            border-bottom: 1px solid var(--art-border-color);

            .role-avatar {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 60px;
              height: 60px;
              margin-right: 20px;
              background: var(--art-bg-primary);
              border-radius: 50%;

              .el-icon {
                font-size: 28px;
                color: var(--el-color-primary);
              }
            }

            .role-details {
              flex: 1;

              h2 {
                margin: 0 0 4px;
                font-size: 20px;
                font-weight: 600;
                color: var(--art-text-gray-900);
              }

              .role-description {
                margin: 0 0 12px;
                font-size: 14px;
                color: var(--art-text-gray-600);
              }

              .role-meta {
                display: flex;
                gap: 24px;

                .meta-item {
                  display: flex;
                  gap: 6px;
                  align-items: center;
                  font-size: 12px;
                  color: var(--art-text-gray-500);

                  .el-icon {
                    font-size: 14px;
                  }
                }
              }
            }

            .role-status-badge {
              margin-left: 20px;
            }
          }

          .permission-content-main {
            display: flex;
            flex-direction: column;

            .tab-header {
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding: 20px 32px;
              border-bottom: 1px solid var(--art-border-color);

              .tab-title {
                display: flex;
                gap: 8px;
                align-items: center;
                font-weight: 600;
                color: var(--art-text-gray-900);

                .el-icon {
                  font-size: 18px;
                  color: var(--el-color-primary);
                }
              }

              .tab-actions {
                display: flex;
                gap: 8px;
              }
            }

            .menu-tree-container {
              flex: 1;
              max-height: 450px;
              padding: 16px;
              margin: 0 32px 20px;
              overflow-y: auto;
              background: var(--art-bg-color);
              border: 1px solid var(--art-border-color);
              border-radius: 8px;

              &::-webkit-scrollbar {
                width: 6px;
              }

              &::-webkit-scrollbar-track {
                background: var(--art-bg-primary);
                border-radius: 3px;
              }

              &::-webkit-scrollbar-thumb {
                background: var(--el-color-primary);
                border-radius: 3px;

                &:hover {
                  background: var(--el-color-primary-dark-2);
                }
              }

              .permission-tree {
                min-height: 200px;

                :deep(.el-tree-node__content) {
                  height: 40px;
                  margin-bottom: 4px;
                  border-radius: 6px;

                  &:hover {
                    background: var(--art-bg-primary);
                  }
                }

                .tree-node {
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  width: 100%;

                  .node-content {
                    display: flex;
                    gap: 8px;
                    align-items: center;

                    .node-icon {
                      font-size: 16px;
                      color: var(--art-text-gray-500);
                    }

                    .node-label {
                      font-weight: 500;
                      color: var(--art-text-gray-800);
                    }
                  }

                  .node-meta {
                    .el-tag {
                      font-size: 11px;
                    }
                  }
                }
              }
            }
          }

          .permission-footer {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            padding: 20px 32px;
            background: var(--art-bg-primary);
            border-top: 1px solid var(--art-border-color);
            border-radius: 0 0 12px 12px;
          }
        }
      }
    }

    .batch-operation-content {
      .el-alert {
        margin-bottom: 20px;
      }
    }
  }

  // 响应式设计
  @media (max-width: $device-ipad) {
    .role-permission-container {
      .permission-main {
        flex-direction: column;
        height: auto;

        .role-sidebar {
          width: 100%;
          height: 300px;

          .role-list {
            .role-card {
              padding: 12px;

              .role-info {
                .role-name {
                  font-size: 14px;
                }

                .role-key {
                  font-size: 11px;
                }
              }
            }
          }
        }

        .permission-content {
          height: 600px;

          .permission-config {
            .role-info-card {
              padding: 16px 20px;

              .role-avatar {
                width: 50px;
                height: 50px;
                margin-right: 16px;

                .el-icon {
                  font-size: 24px;
                }
              }

              .role-details {
                h2 {
                  font-size: 18px;
                }

                .role-meta {
                  flex-direction: column;
                  gap: 8px;
                }
              }
            }

            .permission-content-main {
              .tab-header {
                flex-direction: column;
                gap: 12px;
                align-items: flex-start;
                padding: 16px 20px;

                .tab-actions {
                  flex-wrap: wrap;
                  justify-content: flex-start;
                  width: 100%;
                }
              }

              .menu-tree-container {
                padding: 16px 20px;
              }
            }

            .permission-footer {
              padding: 16px 20px;
            }
          }
        }
      }
    }
  }
</style>
