<template>
  <div class="page-content">
    <div class="ai-assistant-container">
      <iframe
        src="https://udify.app/chatbot/G3nyeXcIrkO6U5Wi"
        class="iframe-content"
        frameborder="0"
        allow="microphone"
      >
      </iframe>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { defineComponent } from 'vue'

  defineComponent({
    name: 'AIAssistant'
  })
</script>

<style lang="scss" scoped>
  @use '@/assets/styles/variables' as *;

  .page-content {
    width: 100%;
    height: 100%;
    padding: 0 !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
  }

  .ai-assistant-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .iframe-content {
    width: 100%;
    height: 100%;
    min-height: calc(100vh - 120px);
    border: none;
  }
</style>
