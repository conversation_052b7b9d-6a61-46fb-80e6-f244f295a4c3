<template>
  <div class="chart-container" ref="chartContainer"></div>
</template>

<script setup>
  import { ref, onMounted, watch, inject } from 'vue'
  import * as echarts from 'echarts'

  // 接收父组件传递的时间范围参数
  const props = defineProps({
    timeRange: {
      type: String,
      default: 'day'
    }
  })

  // 注入数据
  const waterQualityData = inject('waterQualityData')
  const phTrendData = inject('phTrendData')

  // 图表容器引用
  const chartContainer = ref(null)
  let chart = null

  // 处理真实API数据
  const processApiData = () => {
    if (!phTrendData.value || !phTrendData.value.timeList || !phTrendData.value.dataList) {
      return {
        xAxis: [],
        ph: [],
        do: []
      }
    }

    const phData = []
    const doData = []
    const xAxisData = phTrendData.value.timeList

    // 从API数据中提取pH和溶解氧数据
    phTrendData.value.dataList.forEach((item) => {
      phData.push(item.ph || 0)
      doData.push(item.rjy || 0) // rjy是溶解氧
    })

    return {
      xAxis: xAxisData,
      ph: phData,
      do: doData
    }
  }

  // 生成示例数据（作为备用）
  const generateChartData = (range) => {
    // 如果有真实数据，优先使用真实数据
    if (phTrendData.value && phTrendData.value.timeList && phTrendData.value.timeList.length > 0) {
      return processApiData()
    }

    // 否则使用模拟数据
    const phData = []
    const doData = []
    const xAxisData = []

    const phBase = parseFloat(waterQualityData.find((item) => item.name === '酸碱度').value)
    const doBase = parseFloat(waterQualityData.find((item) => item.name === '溶解氧').value)

    let pointCount = 24 // 默认为日视图24小时

    if (range === 'day') {
      // 生成24小时数据
      pointCount = 24

      for (let i = 0; i < pointCount; i++) {
        const hour = i
        xAxisData.push(`${hour}:00`)

        // 小时pH值变化（考虑日变化规律）
        const timeEffect = Math.sin((i / 24) * Math.PI * 2) * 0.1 + 1
        const ph = Math.max(
          6.5,
          Math.min(8.5, phBase * timeEffect * (0.98 + Math.random() * 0.04))
        ).toFixed(1)
        phData.push(ph)

        // 小时溶解氧波动（早晚较高，中午较低）
        const dayNightEffect = i >= 6 && i <= 18 ? 0.95 : 1.05 // 白天溶解氧略低
        const doHourFactor = 0.97 + Math.random() * 0.06
        const do_value = Math.max(
          5.0,
          Math.min(9.0, doBase * doHourFactor * dayNightEffect)
        ).toFixed(1)
        doData.push(do_value)
      }
    } else if (range === 'week') {
      // 生成7天数据
      pointCount = 7
      const today = new Date()

      for (let i = 0; i < pointCount; i++) {
        const date = new Date(today)
        date.setDate(today.getDate() - (pointCount - 1) + i)
        xAxisData.push(`${date.getMonth() + 1}-${date.getDate()}`)

        // 日pH值变化
        const phRandomFactor = 0.95 + Math.random() * 0.1
        const ph = Math.max(6.5, Math.min(8.5, phBase * phRandomFactor)).toFixed(1)
        phData.push(ph)

        // 日溶解氧波动
        const doRandomFactor = 0.9 + Math.random() * 0.2
        const doPeriodFactor = Math.sin((i / pointCount) * Math.PI * 2) * 0.5 + 1 // 周期性波动
        const do_value = Math.max(
          5.0,
          Math.min(9.0, doBase * doRandomFactor * doPeriodFactor)
        ).toFixed(1)
        doData.push(do_value)
      }
    } else if (range === 'month') {
      // 生成30天数据
      pointCount = 30
      const today = new Date()

      for (let i = 0; i < pointCount; i++) {
        const date = new Date(today)
        date.setDate(today.getDate() - (pointCount - 1) + i)
        xAxisData.push(`${date.getMonth() + 1}-${date.getDate()}`)

        // 日pH值变化
        const phRandomFactor = 0.95 + Math.random() * 0.1
        const ph = Math.max(6.5, Math.min(8.5, phBase * phRandomFactor)).toFixed(1)
        phData.push(ph)

        // 日溶解氧波动
        const doRandomFactor = 0.9 + Math.random() * 0.2
        const doPeriodFactor = Math.sin((i / pointCount) * Math.PI * 2) * 0.5 + 1 // 周期性波动
        const do_value = Math.max(
          5.0,
          Math.min(9.0, doBase * doRandomFactor * doPeriodFactor)
        ).toFixed(1)
        doData.push(do_value)
      }
    }

    return {
      xAxis: xAxisData,
      ph: phData,
      do: doData
    }
  }

  // 初始化图表
  const initChart = () => {
    if (chart) {
      chart.dispose()
    }

    // 创建图表实例
    chart = echarts.init(chartContainer.value)

    // 更新图表数据
    updateChart()

    // 响应窗口大小变化
    window.addEventListener('resize', () => {
      chart.resize()
    })
  }

  // 计算Y轴范围
  const calculateYAxisRange = (data, padding = 0.1) => {
    if (!data || data.length === 0) return { min: 0, max: 10 }

    const values = data.map((v) => parseFloat(v)).filter((v) => !isNaN(v))
    if (values.length === 0) return { min: 0, max: 10 }

    const min = Math.min(...values)
    const max = Math.max(...values)
    const range = max - min
    const paddingValue = range * padding

    return {
      min: Math.max(0, min - paddingValue),
      max: max + paddingValue
    }
  }

  // 更新图表数据
  const updateChart = () => {
    const data = generateChartData(props.timeRange)

    // 动态计算Y轴范围
    const phRange = calculateYAxisRange(data.ph)
    const doRange = calculateYAxisRange(data.do)

    // 设置图表配置
    const option = {
      tooltip: {
        trigger: 'axis',
        formatter: function (params) {
          return params
            .map((param) => {
              if (param.seriesName === 'pH值') {
                return `${param.seriesName}: ${param.value}`
              } else {
                return `${param.seriesName}: ${param.value} mg/l`
              }
            })
            .join('<br>')
        }
      },
      legend: {
        data: ['pH值', '溶解氧'],
        bottom: '0%'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: data.xAxis
      },
      yAxis: [
        {
          type: 'value',
          name: 'pH值',
          min: phRange.min,
          max: phRange.max,
          axisLabel: {
            formatter: '{value}'
          }
        },
        {
          type: 'value',
          name: '溶解氧(mg/l)',
          min: doRange.min,
          max: doRange.max,
          axisLabel: {
            formatter: '{value}'
          }
        }
      ],
      series: [
        {
          name: 'pH值',
          type: 'line',
          smooth: true,
          yAxisIndex: 0,
          data: data.ph,
          lineStyle: {
            width: 3,
            color: '#3498db'
          },
          itemStyle: {
            color: '#3498db'
          }
        },
        {
          name: '溶解氧',
          type: 'line',
          smooth: true,
          yAxisIndex: 1,
          data: data.do,
          lineStyle: {
            width: 3,
            color: '#2ecc71'
          },
          itemStyle: {
            color: '#2ecc71'
          }
        }
      ]
    }

    // 应用配置
    chart.setOption(option)
  }

  // 监听时间范围变化
  watch(
    () => props.timeRange,
    () => {
      updateChart()
    }
  )

  // 监听趋势数据变化
  watch(
    () => phTrendData.value,
    () => {
      if (chart) {
        updateChart()
      }
    },
    { deep: true }
  )

  // 组件挂载时初始化图表
  onMounted(() => {
    initChart()
  })
</script>

<style lang="scss" scoped>
  .chart-container {
    width: 100%;
    height: 350px;
  }
</style>
