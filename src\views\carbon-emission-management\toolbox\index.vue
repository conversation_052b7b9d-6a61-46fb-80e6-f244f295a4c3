<template>
  <div class="carbon-toolbox-page page-content">
    <el-tabs v-model="activeTabName" class="toolbox-tabs">
      <el-tab-pane label="碳排放因子库管理" name="factorManagement">
        <div class="carbon-factors-management">
          <!-- 2. 操作与筛选区域 -->
          <el-card shadow="never" class="actions-and-filters-card">
            <!-- 2.1 搜索与过滤表单 -->
            <el-form
              :inline="true"
              :model="searchForm"
              class="search-form"
              @submit.prevent="handleSearch"
            >
              <el-form-item label="因子名称">
                <el-input
                  v-model="searchForm.factorName"
                  placeholder="请输入因子名称或关键词"
                  clearable
                />
              </el-form-item>
              <el-form-item label="活动大类">
                <el-select v-model="searchForm.category" placeholder="请选择活动大类" clearable>
                  <el-option
                    v-for="item in dictData.categories"
                    :key="item.key"
                    :label="item.label"
                    :value="item.key"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="能耗类型">
                <el-select v-model="searchForm.activityType" placeholder="请选择能耗类型" clearable>
                  <el-option
                    v-for="item in dictData.energyTypes"
                    :key="item.key"
                    :label="item.label"
                    :value="item.key"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="适用区域">
                <el-select v-model="searchForm.region" placeholder="请选择适用区域" clearable>
                  <el-option
                    v-for="item in dictData.areas"
                    :key="item.key"
                    :label="item.label"
                    :value="item.key"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="状态">
                <el-select v-model="searchForm.isActive" placeholder="请选择状态" clearable>
                  <el-option label="选用" :value="true" />
                  <el-option label="非选用" :value="false" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" native-type="submit">
                  <el-icon><Search /></el-icon> 查询
                </el-button>
                <el-button @click="resetSearchForm">
                  <el-icon><Refresh /></el-icon> 重置
                </el-button>
              </el-form-item>
            </el-form>

            <!-- 2.2 操作按钮组 -->
            <div class="action-buttons">
              <el-button type="success" @click="handleAddFactor" :disabled="!isAdminUser">
                <el-icon><Plus /></el-icon> 新增因子
              </el-button>
              <el-button type="primary" @click="handleBatchImport" :disabled="!isAdminUser">
                <el-icon><Upload /></el-icon> 批量导入
              </el-button>
              <el-button
                type="warning"
                @click="handleBatchExport"
                :disabled="selectedFactors.length === 0"
              >
                <el-icon><Download /></el-icon> 批量导出
              </el-button>
            </div>
          </el-card>

          <!-- 3. 因子列表展示区域 -->
          <el-card shadow="never" class="factors-table-card">
            <!-- 使用 ArtTable 替代 el-table 和 el-pagination -->
            <ArtTable
              :data="factorsData"
              v-loading="loading"
              row-key="id"
              :border="false"
              :highlight-current-row="false"
              :total="totalFactors"
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              @selection-change="handleSelectionChange"
              class="factors-table"
            >
              <el-table-column
                type="selection"
                width="55"
                align="center"
                :reserve-selection="true"
              />
              <el-table-column
                prop="factorName"
                label="因子名称"
                show-overflow-tooltip
                sortable
                align="center"
              />
              <el-table-column prop="category" label="活动大类" sortable align="center" />
              <el-table-column prop="activityType" label="能耗类型" sortable align="center" />
              <el-table-column
                prop="activitySubType"
                label="活动子类"
                show-overflow-tooltip
                align="center"
              />
              <el-table-column prop="factorValue" label="因子数值" align="center" sortable />
              <el-table-column prop="factorUnit" label="因子单位" align="center" />
              <el-table-column
                prop="dataSource"
                label="数据来源"
                show-overflow-tooltip
                align="center"
              />
              <el-table-column prop="region" label="适用区域" align="center" />
              <el-table-column prop="isActive" label="状态" align="center" sortable>
                <template #default="scope">
                  <el-tag :type="scope.row.isActive ? 'success' : 'danger'">
                    {{ scope.row.isActive ? '选用' : '非选用' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="180" align="center" fixed="right">
                <template #default="scope">
                  <!-- 将操作按钮修改为图标按钮 -->
                  <el-button
                    link
                    type="primary"
                    size="small"
                    @click="handleViewFactor(scope.row)"
                    title="详情"
                    class="action-btn-view"
                  >
                    <i class="iconfont-sys iconsys-fangda"></i>
                  </el-button>
                  <el-button
                    link
                    type="primary"
                    size="small"
                    @click="handleEditFactor(scope.row)"
                    title="编辑"
                    class="action-btn-edit"
                    :disabled="!isAdminUser"
                  >
                    <i class="iconfont-sys iconsys-edit-pencil-01"></i>
                  </el-button>
                  <el-button
                    link
                    type="danger"
                    size="small"
                    @click="handleDeleteFactor(scope.row)"
                    title="删除"
                    class="action-btn-delete"
                    :disabled="!isAdminUser"
                  >
                    <i class="iconfont-sys iconsys-lajitong"></i>
                  </el-button>
                </template>
              </el-table-column>
            </ArtTable>
          </el-card>

          <!-- 4. 新增/编辑因子弹窗 -->
          <el-dialog
            :title="dialogTitle"
            v-model="factorDialogVisible"
            width="70%"
            :close-on-click-modal="false"
            @close="resetFactorForm"
            top="5vh"
            append-to-body
            draggable
          >
            <el-form
              :model="factorForm"
              :rules="factorFormRules"
              ref="factorFormRef"
              label-width="120px"
            >
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="因子名称" prop="factorName">
                    <el-input v-model="factorForm.factorName" placeholder="请输入因子名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="活动大类" prop="category">
                    <el-select
                      v-model="factorForm.category"
                      placeholder="请选择活动大类"
                      style="width: 100%"
                      @change="handleCategoryChange"
                    >
                      <el-option
                        v-for="item in dictData.categories"
                        :key="item.key"
                        :label="item.label"
                        :value="item.key"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="能耗类型" prop="activityType">
                    <el-select
                      v-model="factorForm.activityType"
                      placeholder="请选择能耗类型"
                      style="width: 100%"
                      @change="handleEnergyTypeChange"
                    >
                      <el-option
                        v-for="item in dictData.energyTypes"
                        :key="item.key"
                        :label="item.label"
                        :value="item.key"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="活动子类" prop="activitySubType">
                    <el-input
                      v-model="factorForm.activitySubType"
                      placeholder="请输入活动子类 (如: 汽油, 柴油)"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="因子数值" prop="factorValue">
                    <el-input-number
                      v-model="factorForm.factorValue"
                      :precision="6"
                      controls-position="right"
                      style="width: 100%"
                      placeholder="请输入因子数值"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="因子单位" prop="factorUnit">
                    <el-input
                      v-model="factorForm.factorUnit"
                      placeholder="例如: kgCO2e/kWh, tCO2/tkm"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="数据来源" prop="dataSource">
                    <el-input
                      v-model="factorForm.dataSource"
                      placeholder="例如: IPCC 2006, 中国区域电网2022"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="来源年份" prop="dataSourceYear">
                    <el-input-number
                      v-model="factorForm.dataSourceYear"
                      :min="1900"
                      :max="2100"
                      controls-position="right"
                      style="width: 100%"
                      placeholder="来源年份"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="适用区域" prop="region">
                    <el-select
                      v-model="factorForm.region"
                      placeholder="请选择适用区域"
                      style="width: 100%"
                      @change="handleAreaChange"
                    >
                      <el-option
                        v-for="item in dictData.areas"
                        :key="item.key"
                        :label="item.label"
                        :value="item.key"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="遵循标准" prop="standard">
                    <el-input
                      v-model="factorForm.standard"
                      placeholder="例如: GHG Protocol, ISO 14064"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="因子版本" prop="version">
                    <el-input v-model="factorForm.version" placeholder="例如: 1.0, 2023版" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="状态" prop="isActive">
                    <el-switch
                      v-model="factorForm.isActive"
                      active-text="选用"
                      inactive-text="非选用"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="备注" prop="notes">
                    <el-input
                      type="textarea"
                      :rows="3"
                      v-model="factorForm.notes"
                      placeholder="请输入备注信息"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
            <template #footer>
              <span class="dialog-footer">
                <el-button @click="factorDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitFactorForm" :loading="formSubmitting"
                  >确定</el-button
                >
              </span>
            </template>
          </el-dialog>

          <!-- 5. 查看因子详情弹窗 -->
          <el-dialog
            title="因子详情"
            v-model="factorDetailDialogVisible"
            width="60%"
            append-to-body
            draggable
          >
            <el-descriptions :column="2" border v-if="currentFactorDetail.id">
              <el-descriptions-item label="因子ID">{{
                currentFactorDetail.id
              }}</el-descriptions-item>
              <el-descriptions-item label="因子名称">{{
                currentFactorDetail.factorName
              }}</el-descriptions-item>
              <el-descriptions-item label="活动大类">{{
                currentFactorDetail.category
              }}</el-descriptions-item>
              <el-descriptions-item label="能耗类型">{{
                currentFactorDetail.activityType
              }}</el-descriptions-item>
              <el-descriptions-item label="活动子类">{{
                currentFactorDetail.activitySubType
              }}</el-descriptions-item>
              <el-descriptions-item label="因子数值">{{
                currentFactorDetail.factorValue
              }}</el-descriptions-item>
              <el-descriptions-item label="因子单位">{{
                currentFactorDetail.factorUnit
              }}</el-descriptions-item>
              <el-descriptions-item label="数据来源">{{
                currentFactorDetail.dataSource
              }}</el-descriptions-item>
              <el-descriptions-item label="来源年份">{{
                currentFactorDetail.dataSourceYear
              }}</el-descriptions-item>
              <el-descriptions-item label="适用区域">{{
                currentFactorDetail.region
              }}</el-descriptions-item>
              <el-descriptions-item label="遵循标准">{{
                currentFactorDetail.standard
              }}</el-descriptions-item>
              <el-descriptions-item label="因子版本">{{
                currentFactorDetail.version
              }}</el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="currentFactorDetail.isActive ? 'success' : 'danger'">
                  {{ currentFactorDetail.isActive ? '选用' : '非选用' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="备注" :span="2">{{
                currentFactorDetail.notes
              }}</el-descriptions-item>
            </el-descriptions>
            <template #footer>
              <span class="dialog-footer">
                <el-button @click="factorDetailDialogVisible = false">关闭</el-button>
              </span>
            </template>
          </el-dialog>

          <!-- 6. 批量导入弹窗 -->
          <el-dialog
            title="批量导入因子"
            v-model="batchImportDialogVisible"
            width="500px"
            append-to-body
            draggable
          >
            <p>请下载模板，按照模板格式填写数据后上传。</p>
            <el-button
              type="primary"
              link
              @click="downloadImportTemplate"
              style="margin-bottom: 15px"
            >
              <el-icon><Download /></el-icon> 下载导入模板.xlsx
            </el-button>
            <el-upload
              class="upload-importer"
              drag
              action="/api/carbon-factors/batch-import"
              :on-success="handleImportSuccess"
              :on-error="handleImportError"
              :before-upload="beforeImportUpload"
              :limit="1"
              accept=".xlsx, .xls"
            >
              <!-- 替换为实际后端上传接口 (注释移到这里或完全删除) -->
              <el-icon class="el-icon--upload"><upload-filled /></el-icon>
              <div class="el-upload__text"> 将文件拖到此处，或<em>点击上传</em> </div>
              <template #tip>
                <div class="el-upload__tip"> 只能上传 xls/xlsx 文件，且不超过10MB。 </div>
              </template>
            </el-upload>
            <template #footer>
              <span class="dialog-footer">
                <el-button @click="batchImportDialogVisible = false">取消</el-button>
              </span>
            </template>
          </el-dialog>
        </div>
      </el-tab-pane>

      <el-tab-pane label="活动数据与碳排放计算" name="activityAndCalculation">
        <div class="activity-data-entry">
          <!-- 1. 核算周期与范围选择 (简化版) -->
          <el-card shadow="never" style="margin-bottom: 20px">
            <el-form :inline="true" :model="calculationConfig">
              <el-form-item label="核算年度/周期">
                <el-date-picker
                  v-model="calculationConfig.period"
                  type="year"
                  placeholder="选择年度"
                  value-format="YYYY"
                />
              </el-form-item>
              <el-form-item label="核算主体/边界 (可选)">
                <el-input v-model="calculationConfig.entity" placeholder="例如：XX公司整体运营" />
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 2. 活动数据输入区域 -->
          <el-collapse v-model="activeCollapsePanels" accordion>
            <el-collapse-item title="范畴一：直接排放 (Scope 1)" name="scope1">
              <div class="activity-group">
                <h4>2.1 固定燃烧源</h4>
                <el-table :data="scope1Data.fixedCombustion" style="width: 100%" border>
                  <el-table-column label="序号" type="index" width="60" align="center" />
                  <el-table-column label="燃料类型" prop="fuelType" min-width="150" align="center">
                    <template #default="scope">
                      <el-select
                        v-model="scope.row.fuelType"
                        placeholder="选择燃料"
                        clearable
                        @change="updateMatchedFactorDisplay(scope.row, 'fixedCombustion')"
                      >
                        <el-option label="原煤" value="原煤" />
                        <el-option label="天然气" value="天然气" />
                        <el-option label="柴油" value="柴油" />
                        <el-option label="燃料油" value="燃料油" />
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column label="消耗量" prop="consumption" min-width="120" align="center">
                    <template #default="scope">
                      <el-input-number
                        v-model="scope.row.consumption"
                        :precision="2"
                        controls-position="right"
                        style="width: 100%"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column label="单位" prop="unit" min-width="120" align="center">
                    <template #default="scope">
                      <el-select v-model="scope.row.unit" placeholder="选择单位">
                        <el-option label="吨" value="t" />
                        <el-option label="万立方米" value="万m³" />
                        <el-option label="升" value="L" />
                        <el-option label="千克" value="kg" />
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="排放因子 (自动匹配)"
                    prop="matchedFactorDisplay"
                    min-width="200"
                    show-overflow-tooltip
                    align="center"
                  >
                    <template #default="scope">
                      <el-input
                        v-model="scope.row.matchedFactorDisplay"
                        readonly
                        placeholder="根据燃料自动匹配"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" align="center">
                    <template #default="scope">
                      <el-button
                        type="danger"
                        link
                        @click="removeActivityItem('scope1Data', 'fixedCombustion', scope.$index)"
                        >删除</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>
                <el-button
                  type="primary"
                  plain
                  @click="addActivityItem('scope1Data', 'fixedCombustion')"
                  style="margin-top: 10px"
                >
                  <el-icon><Plus /></el-icon> 添加固定燃烧活动
                </el-button>
              </div>

              <div class="activity-group">
                <h4>2.2 移动燃烧源</h4>
                <el-table :data="scope1Data.mobileCombustion" style="width: 100%" border>
                  <el-table-column label="序号" type="index" width="60" align="center" />
                  <el-table-column
                    label="车辆/设备类型"
                    prop="vehicleType"
                    min-width="150"
                    align="center"
                  >
                    <template #default="scope"
                      ><el-input v-model="scope.row.vehicleType" placeholder="如：运输卡车"
                    /></template>
                  </el-table-column>
                  <el-table-column label="燃料类型" prop="fuelType" min-width="150" align="center">
                    <template #default="scope">
                      <el-select
                        v-model="scope.row.fuelType"
                        placeholder="选择燃料"
                        clearable
                        @change="updateMatchedFactorDisplay(scope.row, 'mobileCombustion')"
                      >
                        <el-option label="汽油" value="汽油" />
                        <el-option label="柴油" value="柴油" />
                        <el-option label="天然气" value="天然气" />
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="消耗量/里程"
                    prop="consumption"
                    min-width="120"
                    align="center"
                  >
                    <template #default="scope"
                      ><el-input-number
                        v-model="scope.row.consumption"
                        :precision="2"
                        controls-position="right"
                        style="width: 100%"
                    /></template>
                  </el-table-column>
                  <el-table-column label="单位" prop="unit" min-width="120" align="center">
                    <template #default="scope">
                      <el-select v-model="scope.row.unit" placeholder="选择单位">
                        <el-option label="升 (L)" value="L" />
                        <el-option label="千克 (kg)" value="kg" />
                        <el-option label="公里 (km)" value="km" />
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="排放因子 (自动匹配)"
                    prop="matchedFactorDisplay"
                    min-width="200"
                    show-overflow-tooltip
                    align="center"
                  >
                    <template #default="scope"
                      ><el-input
                        v-model="scope.row.matchedFactorDisplay"
                        readonly
                        placeholder="根据燃料/里程匹配"
                    /></template>
                  </el-table-column>
                  <el-table-column label="操作" align="center">
                    <template #default="scope"
                      ><el-button
                        type="danger"
                        link
                        @click="removeActivityItem('scope1Data', 'mobileCombustion', scope.$index)"
                        >删除</el-button
                      ></template
                    >
                  </el-table-column>
                </el-table>
                <el-button
                  type="primary"
                  plain
                  @click="addActivityItem('scope1Data', 'mobileCombustion')"
                  style="margin-top: 10px"
                >
                  <el-icon><Plus /></el-icon> 添加移动燃烧活动
                </el-button>
              </div>
            </el-collapse-item>

            <el-collapse-item title="范畴二：间接排放 - 外购能源 (Scope 2)" name="scope2">
              <div class="activity-group">
                <h4>外购电力/热力/蒸汽</h4>
                <el-table :data="scope2Data.purchasedEnergy" style="width: 100%" border>
                  <el-table-column label="序号" type="index" width="60" align="center" />
                  <el-table-column
                    label="能源类型"
                    prop="energyType"
                    min-width="150"
                    align="center"
                  >
                    <template #default="scope">
                      <el-select
                        v-model="scope.row.energyType"
                        placeholder="选择能源类型"
                        @change="updateMatchedFactorDisplay(scope.row, 'purchasedEnergy')"
                      >
                        <el-option label="外购电力" value="electricity" />
                        <el-option label="外购热力" value="heat" />
                        <el-option label="外购蒸汽" value="steam" />
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column label="消耗量" prop="consumption" min-width="120" align="center">
                    <template #default="scope">
                      <el-input-number
                        v-model="scope.row.consumption"
                        :precision="2"
                        controls-position="right"
                        style="width: 100%"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column label="单位" prop="unit" min-width="120" align="center">
                    <template #default="scope">
                      <el-select v-model="scope.row.unit" placeholder="选择单位">
                        <el-option label="kWh" value="kWh" />
                        <el-option label="MWh" value="MWh" />
                        <el-option label="GJ" value="GJ" />
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="数据来源/地区 (用于匹配因子)"
                    prop="region"
                    min-width="180"
                    align="center"
                  >
                    <template #default="scope">
                      <el-input
                        v-model="scope.row.region"
                        placeholder="例如：华北电网，某热力公司"
                        @change="updateMatchedFactorDisplay(scope.row, 'purchasedEnergy')"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="排放因子 (自动匹配)"
                    prop="matchedFactorDisplay"
                    min-width="200"
                    show-overflow-tooltip
                    align="center"
                  >
                    <template #default="scope">
                      <el-input
                        v-model="scope.row.matchedFactorDisplay"
                        readonly
                        placeholder="根据能源类型和地区匹配"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" align="center">
                    <template #default="scope">
                      <el-button
                        type="danger"
                        link
                        @click="removeActivityItem('scope2Data', 'purchasedEnergy', scope.$index)"
                        >删除</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>
                <el-button
                  type="primary"
                  plain
                  @click="addActivityItem('scope2Data', 'purchasedEnergy')"
                  style="margin-top: 10px"
                >
                  <el-icon><Plus /></el-icon> 添加外购能源项
                </el-button>
              </div>
            </el-collapse-item>

            <el-collapse-item title="范畴三：其他间接排放 (Scope 3)" name="scope3">
              <div class="activity-group">
                <h4>类别5: 运营中产生的废弃物</h4>
                <el-table :data="scope3Data.wasteGenerated" style="width: 100%" border>
                  <el-table-column label="序号" type="index" width="60" align="center" />
                  <el-table-column
                    label="废弃物类型"
                    prop="wasteType"
                    min-width="150"
                    align="center"
                  >
                    <template #default="scope">
                      <el-input
                        v-model="scope.row.wasteType"
                        placeholder="例如：生活垃圾、危险废弃物"
                        @change="updateMatchedFactorDisplay(scope.row, 'wasteGenerated')"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="处理方式"
                    prop="disposalMethod"
                    min-width="150"
                    align="center"
                  >
                    <template #default="scope">
                      <el-select
                        v-model="scope.row.disposalMethod"
                        placeholder="选择处理方式"
                        @change="updateMatchedFactorDisplay(scope.row, 'wasteGenerated')"
                      >
                        <el-option label="填埋" value="landfill" />
                        <el-option label="焚烧" value="incineration" />
                        <el-option label="回收" value="recycling" />
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column label="重量" prop="weight" min-width="120" align="center">
                    <template #default="scope">
                      <el-input-number
                        v-model="scope.row.weight"
                        :precision="3"
                        controls-position="right"
                        style="width: 100%"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column label="单位" prop="unit" min-width="100" align="center">
                    <template #default="scope">
                      <el-select v-model="scope.row.unit" placeholder="单位">
                        <el-option label="吨 (t)" value="t" />
                        <el-option label="千克 (kg)" value="kg" />
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="排放因子 (自动匹配)"
                    prop="matchedFactorDisplay"
                    min-width="200"
                    show-overflow-tooltip
                    align="center"
                  >
                    <template #default="scope">
                      <el-input
                        v-model="scope.row.matchedFactorDisplay"
                        readonly
                        placeholder="根据废弃物类型和处理方式匹配"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" align="center">
                    <template #default="scope">
                      <el-button
                        type="danger"
                        link
                        @click="removeActivityItem('scope3Data', 'wasteGenerated', scope.$index)"
                        >删除</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>
                <el-button
                  type="primary"
                  plain
                  @click="addActivityItem('scope3Data', 'wasteGenerated')"
                  style="margin-top: 10px"
                >
                  <el-icon><Plus /></el-icon> 添加废弃物处理项
                </el-button>
              </div>
            </el-collapse-item>
          </el-collapse>

          <div style="margin-top: 30px; text-align: center">
            <el-button
              type="success"
              size="large"
              @click="handleCalculateEmissions"
              :loading="isCalculating"
            >
              <el-icon><Cpu /></el-icon> 计算碳排放量
            </el-button>
          </div>
        </div>
      </el-tab-pane>

      <el-tab-pane label="碳排放报告与分析" name="reportAndAnalysis">
        <div class="emission-report-analysis">
          <el-card shadow="never">
            <template #header>
              <div class="card-header">
                <span>碳排放报告 (核算年度: {{ calculationConfig.period || '未指定' }})</span>
                <el-button
                  type="primary"
                  @click="exportReport"
                  :disabled="!emissionResults.totalEmissions"
                >
                  <el-icon><Download /></el-icon> 导出报告 (模拟)
                </el-button>
              </div>
            </template>

            <div
              v-if="!emissionResults.totalEmissions && !isCalculating"
              class="no-data-placeholder"
            >
              <el-empty description="暂无计算结果，请先在活动数据录入标签页输入数据并进行计算。" />
            </div>

            <div
              v-if="isCalculating"
              v-loading="isCalculating"
              element-loading-text="正在计算中..."
              style="min-height: 200px"
            >
            </div>

            <div v-if="emissionResults.totalEmissions && !isCalculating">
              <el-descriptions title="总体排放概览" :column="2" border style="margin-bottom: 20px">
                <el-descriptions-item label="碳排放总量 (tCO2e)">
                  <span style="font-weight: bold; color: #e6a23c">
                    {{
                      emissionResults.totalEmissions
                        ? emissionResults.totalEmissions.toFixed(2)
                        : 'N/A'
                    }}
                  </span>
                </el-descriptions-item>
              </el-descriptions>

              <el-card shadow="hover" style="margin-bottom: 20px">
                <template #header><span>按范畴排放明细 (tCO2e)</span></template>
                <el-row :gutter="16">
                  <el-col :span="16">
                    <el-table :data="emissionResults.byScope" style="width: 100%" border>
                      <el-table-column prop="scope" label="排放范畴" align="center" />
                      <el-table-column prop="emissions" label="排放量 (tCO2e)" align="center">
                        <template #default="scope">{{
                          scope.row.emissions !== null ? scope.row.emissions.toFixed(2) : 'N/A'
                        }}</template>
                      </el-table-column>
                      <el-table-column prop="percentage" label="占比 (%)" align="center">
                        <template #default="scope"
                          >{{
                            scope.row.percentage !== null ? scope.row.percentage.toFixed(1) : 'N/A'
                          }}%</template
                        >
                      </el-table-column>
                    </el-table>
                  </el-col>
                  <el-col :span="8">
                    <div
                      ref="scopePieChartRef"
                      style="
                        width: 100%;
                        height: 250px;
                        line-height: 250px;
                        text-align: center;
                        border: 1px solid #eee;
                      "
                      >饼图占位符</div
                    >
                  </el-col>
                </el-row>
              </el-card>

              <el-card shadow="hover" style="margin-bottom: 20px">
                <template #header><span>总排放量汇总 (tCO2e)</span></template>
                <el-table :data="emissionResults.byGas" style="width: 100%" border>
                  <el-table-column prop="gasType" label="排放类型" align="center" />
                  <el-table-column
                    prop="emissionsCO2e"
                    label="CO2当量排放量 (tCO2e)"
                    align="center"
                  >
                    <template #default="scope">{{
                      scope.row.emissionsCO2e !== null ? scope.row.emissionsCO2e.toFixed(2) : 'N/A'
                    }}</template>
                  </el-table-column>
                </el-table>
              </el-card>

              <el-card shadow="hover">
                <template #header><span>按主要活动源排放明细 (tCO2e)</span></template>
                <el-table :data="emissionResults.bySource" style="width: 100%" border>
                  <el-table-column prop="sourceName" label="活动源/类别" align="center" />
                  <el-table-column prop="emissions" label="排放量 (tCO2e)" align="center">
                    <template #default="scope">{{
                      scope.row.emissions !== null ? scope.row.emissions.toFixed(2) : 'N/A'
                    }}</template>
                  </el-table-column>
                </el-table>
              </el-card>
            </div>
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, watch, nextTick, computed } from 'vue' // Added nextTick and computed
  import { ElMessage, ElMessageBox, FormInstance, FormRules, UploadProps } from 'element-plus'
  import {
    Plus,
    Upload,
    Download,
    UploadFilled,
    Search,
    Refresh,
    Cpu // Added Cpu icon
  } from '@element-plus/icons-vue'
  import { useUserStore } from '@/store/modules/user'

  // --- 类型定义 ---
  // (Factor interface remains the same)
  interface ActivityItemBase {
    id?: string // for unique key in v-for if needed
    consumption?: number | null | undefined // 改为可选，以适应所有活动类型
    unit: string
    matchedFactorDisplay?: string
    // internal fields for calculation
    _factor?: Factor | null
    _co2e?: number | null
  }

  interface FixedCombustionItem extends ActivityItemBase {
    fuelType: string
  }

  interface MobileCombustionItem extends ActivityItemBase {
    vehicleType: string
    fuelType: string
  }

  interface PurchasedEnergyItem extends ActivityItemBase {
    energyType: string // 'electricity', 'heat', 'steam'
    region: string // For matching regional grid factors
  }

  interface WasteGeneratedItem extends ActivityItemBase {
    consumption?: number | null | undefined // 明确标记为可选，因为 weight 是主要字段
    wasteType: string
    disposalMethod: string // 'landfill', 'incineration', 'recycling'
    weight: number | null | undefined // Overrides consumption for clarity
  }

  interface Factor {
    id: number | null
    factorName: string
    category?: string
    activityType: string
    activitySubType?: string
    factorValue: number | undefined // 修改: null -> undefined
    factorUnit: string
    dataSource: string
    dataSourceYear?: number | undefined // 修改: null -> undefined
    region?: string
    standard?: string
    version?: string
    isActive: boolean
    notes?: string
    // 保存原始的字典key值用于API调用
    _originalCategory?: string
    _originalEnergyType?: string
    _originalArea?: string
  }

  // --- 响应式数据 ---
  const searchForm = reactive({
    factorName: '',
    category: '',
    activityType: '',
    region: '',
    isActive: true as boolean | undefined // 默认查询选用状态的因子
  })

  // --- 用户权限控制 ---
  const userStore = useUserStore()

  // 计算属性：判断当前用户是否为admin
  const isAdminUser = computed(() => {
    const userInfo = userStore.getUserInfo
    return userInfo.userName === 'admin'
  })

  const factorsData = ref<Factor[]>([])
  const loading = ref(false)
  const currentPage = ref(1)
  const pageSize = ref(10)
  const totalFactors = ref(0)
  const selectedFactors = ref<Factor[]>([])

  const factorDialogVisible = ref(false)
  const dialogTitle = ref('')
  const factorFormRef = ref<FormInstance>()
  const formSubmitting = ref(false)

  const initialFactorFormState: Factor = {
    id: null,
    factorName: '',
    category: '',
    activityType: '',
    activitySubType: '',
    factorValue: undefined, // 修改: null -> undefined
    factorUnit: '',
    dataSource: '',
    dataSourceYear: undefined, // 修改: null -> undefined
    region: '',
    standard: '',
    version: '1.0',
    isActive: false, // 修改: 默认为false（非选用状态）
    notes: ''
  }
  const factorForm = reactive<Factor>({ ...initialFactorFormState })

  const factorFormRules = reactive<FormRules>({
    factorName: [{ required: true, message: '请输入因子名称', trigger: 'blur' }],
    activityType: [{ required: true, message: '请选择活动大类', trigger: 'change' }],
    factorValue: [
      { required: true, message: '请输入因子数值', trigger: 'blur' },
      { type: 'number', message: '因子数值必须为数字' }
    ],
    factorUnit: [{ required: true, message: '请输入因子单位', trigger: 'blur' }],
    dataSource: [{ required: true, message: '请输入数据来源', trigger: 'blur' }],
    dataSourceYear: [{ type: 'number', message: '来源年份必须为数字' }]
  })

  const factorDetailDialogVisible = ref(false)
  const currentFactorDetail = ref<Partial<Factor>>({})

  const batchImportDialogVisible = ref(false)

  // --- 字典数据 ---
  const dictData = reactive({
    categories: [] as Array<{ key: string; label: string }>, // 活动大类
    energyTypes: [] as Array<{ key: string; label: string }>, // 能耗类型
    areas: [] as Array<{ key: string; label: string }> // 适用区域
  })

  // --- Tabs Control ---
  const activeTabName = ref('factorManagement') // Default to factor management

  // --- 活动数据录入与计算模块的数据 ---
  const calculationConfig = reactive({
    period: new Date().getFullYear().toString(),
    entity: '示例公司整体运营'
  })
  const activeCollapsePanels = ref(['scope1']) // Default open panels

  // 活动数据模型
  const scope1Data = reactive<{
    fixedCombustion: FixedCombustionItem[]
    mobileCombustion: MobileCombustionItem[]
    // fugitiveEmissions: FugitiveEmissionItem[]; // Example for future
  }>({
    fixedCombustion: [
      {
        id: Date.now().toString(),
        fuelType: '',
        consumption: undefined,
        unit: '',
        matchedFactorDisplay: ''
      }
    ],
    mobileCombustion: [
      {
        id: Date.now().toString(),
        vehicleType: '',
        fuelType: '',
        consumption: undefined,
        unit: '',
        matchedFactorDisplay: ''
      }
    ]
  })

  const scope2Data = reactive<{
    purchasedEnergy: PurchasedEnergyItem[]
  }>({
    purchasedEnergy: [
      {
        id: Date.now().toString(),
        energyType: '',
        consumption: undefined,
        unit: '',
        region: '',
        matchedFactorDisplay: ''
      }
    ]
  })

  const scope3Data = reactive<{
    wasteGenerated: WasteGeneratedItem[]
    // businessTravel: BusinessTravelItem[]; // Example for future
  }>({
    wasteGenerated: [
      {
        id: Date.now().toString(),
        wasteType: '',
        disposalMethod: '',
        weight: undefined,
        unit: 't',
        matchedFactorDisplay: ''
      }
    ]
  })

  const isCalculating = ref(false)

  // --- 碳排放报告与分析模块的数据 ---
  interface EmissionResultDetail {
    emissions: number | null
    percentage?: number | null // Optional for byScope
  }
  interface EmissionByGasDetail {
    gasType: string
    emissionsCO2e: number | null
  }

  const emissionResults = reactive<{
    totalEmissions: number | null
    byScope: Array<{ scope: string } & EmissionResultDetail>
    byGas: EmissionByGasDetail[]
    bySource: Array<{ sourceName: string; emissions: number | null }>
  }>({
    totalEmissions: null,
    byScope: [
      { scope: '范畴一', emissions: 0, percentage: 0 },
      { scope: '范畴二', emissions: 0, percentage: 0 },
      { scope: '范畴三', emissions: 0, percentage: 0 }
    ],
    byGas: [],
    bySource: []
  })
  const scopePieChartRef = ref<HTMLElement | null>(null) // For ECharts if used

  // --- 方法 ---

  // (因子库管理的方法 fetchFactors, handleSearch, etc. remain here)
  const fetchFactors = async () => {
    loading.value = true
    console.log('正在获取因子列表，参数:', {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      name: searchForm.factorName,
      category: searchForm.category,
      energyType: searchForm.activityType,
      area: searchForm.region,
      status: searchForm.isActive !== undefined ? (searchForm.isActive ? '0' : '1') : ''
    })

    try {
      const { getCarbonFactorList } = await import('@/api/carbon-emission-management/toolboxApi')
      const response = await getCarbonFactorList({
        pageNum: currentPage.value,
        pageSize: pageSize.value,
        name: searchForm.factorName || '',
        category: searchForm.category || '',
        energyType: searchForm.activityType || '',
        area: searchForm.region || '',
        status: searchForm.isActive !== undefined ? (searchForm.isActive ? '0' : '1') : ''
      })

      if (response.code === 200) {
        // 转换API数据格式到组件使用的格式
        factorsData.value = response.rows.map((item) => ({
          id: item.id,
          factorName: item.name,
          category: getCategoryLabel(item.category || ''), // 使用字典转换显示名称
          activityType: getEnergyTypeLabel(item.energyType), // 使用字典转换显示名称
          activitySubType: item.energyType2 || '',
          factorValue: item.factorValue,
          factorUnit: item.factorUnit,
          dataSource: item.dataSource,
          dataSourceYear: item.sourceYear ? parseInt(item.sourceYear) : undefined,
          region: getAreaLabel(item.area || ''), // 使用字典转换显示名称
          standard: item.level,
          version: item.factorVersion,
          isActive: item.status === '0',
          notes: item.remark || '',
          // 保存原始的key值用于编辑
          _originalCategory: item.category,
          _originalEnergyType: item.energyType,
          _originalArea: item.area
        }))
        totalFactors.value = response.total
        console.log('获取到的因子数据:', factorsData.value)
      } else {
        throw new Error(response.msg || '获取数据失败')
      }
    } catch (error) {
      console.error('获取因子列表失败:', error)
      ElMessage.error('获取因子列表失败，请稍后重试')
      // 发生错误时使用空数据
      factorsData.value = []
      totalFactors.value = 0
    } finally {
      loading.value = false
    }
  }

  // 加载字典数据
  const loadDictData = async () => {
    try {
      const { getDictData, DICT_TYPES } = await import(
        '@/api/carbon-emission-management/toolboxApi'
      )

      // 加载活动大类字典
      const categoriesResponse = await getDictData(DICT_TYPES.CO2_CATEGORY)
      if (categoriesResponse.code === 200) {
        dictData.categories = categoriesResponse.data
      }

      // 加载能耗类型字典
      const energyTypesResponse = await getDictData(DICT_TYPES.ENERGY_TYPE)
      if (energyTypesResponse.code === 200) {
        dictData.energyTypes = energyTypesResponse.data
      }

      // 加载适用区域字典
      const areasResponse = await getDictData(DICT_TYPES.AREA)
      if (areasResponse.code === 200) {
        dictData.areas = areasResponse.data
      }

      console.log('字典数据加载完成:', dictData)
    } catch (error) {
      console.error('加载字典数据失败:', error)
      // 字典数据加载失败时使用默认数据
      dictData.categories = [
        { key: '1', label: '固定燃烧' },
        { key: '2', label: '移动燃烧' },
        { key: '3', label: '外购电力' },
        { key: '4', label: '外购热力' },
        { key: '5', label: '其他' }
      ]
      dictData.energyTypes = [
        { key: '0', label: '电' },
        { key: '1', label: '水' },
        { key: '2', label: '气' },
        { key: '3', label: '热' },
        { key: '4', label: '其他' }
      ]
      dictData.areas = [
        { key: '华北', label: '华北' },
        { key: '华东', label: '华东' },
        { key: '华南', label: '华南' },
        { key: '全国', label: '全国' }
      ]
    }
  }

  // 根据字典key获取显示标签
  const getCategoryLabel = (key: string) => {
    const item = dictData.categories.find((item) => item.key === key)
    return item ? item.label : key
  }

  const getEnergyTypeLabel = (key: string) => {
    const item = dictData.energyTypes.find((item) => item.key === key)
    return item ? item.label : key
  }

  const getAreaLabel = (key: string) => {
    const item = dictData.areas.find((item) => item.key === key)
    return item ? item.label : key
  }

  const handleSearch = () => {
    currentPage.value = 1
    fetchFactors()
  }

  const resetSearchForm = () => {
    searchForm.factorName = ''
    searchForm.category = ''
    searchForm.activityType = ''
    searchForm.region = ''
    searchForm.isActive = true // 重置时默认查询选用状态的因子
    handleSearch()
  }

  const handleSelectionChange = (selection: Factor[]) => {
    selectedFactors.value = selection
  }

  const handleSizeChange = (val: number) => {
    pageSize.value = val
    currentPage.value = 1 // 页大小改变时通常回到第一页
    fetchFactors()
  }

  const handleCurrentChange = (val: number) => {
    currentPage.value = val
    fetchFactors()
  }

  const resetFactorForm = () => {
    Object.assign(factorForm, initialFactorFormState) // 重置为初始状态
    factorForm.id = null // 确保id也被重置
    if (factorFormRef.value) {
      factorFormRef.value.clearValidate() // 清除校验状态
    }
  }

  const handleAddFactor = () => {
    // 权限检查
    if (!isAdminUser.value) {
      ElMessage.warning('您没有权限执行此操作')
      return
    }

    resetFactorForm()
    dialogTitle.value = '新增碳排放因子'
    factorDialogVisible.value = true
  }

  const handleEditFactor = (row: Factor) => {
    // 权限检查
    if (!isAdminUser.value) {
      ElMessage.warning('您没有权限执行此操作')
      return
    }

    resetFactorForm()
    dialogTitle.value = '编辑碳排放因子'
    Object.assign(factorForm, JSON.parse(JSON.stringify(row))) // 深拷贝填充表单
    // 设置表单显示值为字典key
    if (row._originalCategory) {
      factorForm.category = row._originalCategory
    }
    if (row._originalEnergyType) {
      factorForm.activityType = row._originalEnergyType
    }
    if (row._originalArea) {
      factorForm.region = row._originalArea
    }
    factorDialogVisible.value = true
  }

  // 处理活动大类变化
  const handleCategoryChange = (value: string) => {
    factorForm._originalCategory = value
  }

  // 处理能耗类型变化
  const handleEnergyTypeChange = (value: string) => {
    factorForm._originalEnergyType = value
  }

  // 处理适用区域变化
  const handleAreaChange = (value: string) => {
    factorForm._originalArea = value
  }

  const submitFactorForm = async () => {
    // 权限检查
    if (!isAdminUser.value) {
      ElMessage.warning('您没有权限执行此操作')
      return
    }

    if (!factorFormRef.value) return
    formSubmitting.value = true
    try {
      await factorFormRef.value.validate()

      // 准备API请求数据，使用原始的字典key值
      const formData = {
        name: factorForm.factorName,
        category: factorForm._originalCategory || factorForm.category || '',
        energyType: factorForm._originalEnergyType || factorForm.activityType,
        energyType2: factorForm.activitySubType || '',
        factorValue: factorForm.factorValue!,
        factorUnit: factorForm.factorUnit,
        dataSource: factorForm.dataSource,
        sourceYear: factorForm.dataSourceYear?.toString() || '',
        area: factorForm._originalArea || factorForm.region || '',
        level: factorForm.standard || '',
        factorVersion: factorForm.version || '',
        status: factorForm.isActive ? '0' : '1'
      }

      console.log('提交因子表单:', formData)

      if (factorForm.id) {
        // 编辑现有因子
        const { updateCarbonFactor } = await import('@/api/carbon-emission-management/toolboxApi')
        const response = await updateCarbonFactor({
          id: factorForm.id,
          ...formData
        })

        if (response.code === 200) {
          ElMessage.success('因子更新成功！')
          factorDialogVisible.value = false
          fetchFactors() // 重新加载列表
        } else {
          throw new Error(response.msg || '更新失败')
        }
      } else {
        // 创建新因子
        const { createCarbonFactor } = await import('@/api/carbon-emission-management/toolboxApi')
        const response = await createCarbonFactor(formData)

        if (response.code === 200) {
          ElMessage.success('因子新增成功！')
          factorDialogVisible.value = false
          fetchFactors() // 重新加载列表
        } else {
          throw new Error(response.msg || '新增失败')
        }
      }
    } catch (error) {
      console.error('表单校验或提交失败:', error)
      ElMessage.error(error instanceof Error ? error.message : '表单信息有误，请检查！')
    } finally {
      formSubmitting.value = false
    }
  }

  const handleViewFactor = (row: Factor) => {
    currentFactorDetail.value = { ...row }
    factorDetailDialogVisible.value = true
  }

  const handleDeleteFactor = (row: Factor) => {
    // 权限检查
    if (!isAdminUser.value) {
      ElMessage.warning('您没有权限执行此操作')
      return
    }

    ElMessageBox.confirm(`确定要删除因子 "${row.factorName}" 吗？此操作不可恢复。`, '警告', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        try {
          console.log('正在删除因子:', row.id)
          const { deleteCarbonFactor } = await import('@/api/carbon-emission-management/toolboxApi')
          const response = await deleteCarbonFactor(row.id!)

          if (response.code === 200) {
            ElMessage.success('因子删除成功！')
            fetchFactors() // 重新加载列表
          } else {
            throw new Error(response.msg || '删除失败')
          }
        } catch (error) {
          console.error('删除因子失败:', error)
          ElMessage.error(error instanceof Error ? error.message : '删除失败，请稍后重试')
        }
      })
      .catch(() => {
        ElMessage.info('已取消删除')
      })
  }

  const handleBatchImport = () => {
    // 权限检查
    if (!isAdminUser.value) {
      ElMessage.warning('您没有权限执行此操作')
      return
    }

    batchImportDialogVisible.value = true
  }

  const downloadImportTemplate = () => {
    ElMessage.info('正在准备下载导入模板...')
    // 实际应是后端接口或静态文件链接
    // window.open('/api/carbon-factors/template', '_blank');
    const link = document.createElement('a')
    link.href =
      'data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,UEsDBBQABgAIAAAAIQC7%2B%2BqiTQEAAGYCAAATAAgCW0NvbnRlbnRfVHlwZXNdLnhtbCCiBAIooAAC' // 这是一个极简的空xlsx文件base64示例
    link.download = '碳排放因子导入模板.xlsx'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success('模板下载（模拟）完成!')
  }

  const beforeImportUpload: UploadProps['beforeUpload'] = (rawFile) => {
    // 权限检查
    if (!isAdminUser.value) {
      ElMessage.warning('您没有权限执行此操作')
      return false
    }

    const isExcel =
      rawFile.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      rawFile.type === 'application/vnd.ms-excel'
    const isLt10M = rawFile.size / 1024 / 1024 < 10
    if (!isExcel) {
      ElMessage.error('上传文件只能是 XLS 或 XLSX 格式!')
      return false
    }
    if (!isLt10M) {
      ElMessage.error('上传文件大小不能超过 10MB!')
      return false
    }
    return true
  }

  const handleImportSuccess: UploadProps['onSuccess'] = (response, uploadFile) => {
    // 权限检查
    if (!isAdminUser.value) {
      ElMessage.warning('您没有权限执行此操作')
      return
    }

    ElMessage.success(`文件 ${uploadFile.name} 上传成功，正在处理导入...`)
    // 假设后端处理后返回成功信息，然后刷新列表
    // batchImportDialogVisible.value = false; // 可根据后端是否同步处理来决定是否关闭
    fetchFactors()
  }

  const handleImportError: UploadProps['onError'] = (error, uploadFile) => {
    ElMessage.error(`文件 ${uploadFile.name} 上传失败或导入过程中发生错误！`)
    console.error('导入错误:', error)
  }

  const handleBatchExport = () => {
    if (selectedFactors.value.length === 0) {
      ElMessage.warning('请至少选择一个因子进行导出！')
      return
    }
    ElMessageBox.confirm(`确定要导出选中的 ${selectedFactors.value.length} 个因子吗？`, '提示', {
      confirmButtonText: '确定导出',
      cancelButtonText: '取消',
      type: 'info'
    })
      .then(() => {
        console.log(
          '正在导出选中的因子:',
          selectedFactors.value.map((f) => f.id)
        )
        // 模拟后端导出文件
        const dataToExport = JSON.stringify(selectedFactors.value, null, 2)
        const blob = new Blob([dataToExport], { type: 'application/json' })
        const link = document.createElement('a')
        link.href = URL.createObjectURL(blob)
        link.download = '导出的碳排放因子.json'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(link.href)
        ElMessage.success('已开始导出任务（模拟JSON导出）。')
      })
      .catch(() => {
        ElMessage.info('已取消导出')
      })
  }

  onMounted(async () => {
    // 先加载字典数据，再加载因子数据
    await loadDictData()
    fetchFactors()

    // 预先设置一些固定燃料类型的数据，方便用户直接选择
    scope1Data.fixedCombustion[0].fuelType = '天然气'
    scope1Data.fixedCombustion[0].unit = '万m³'

    scope1Data.mobileCombustion[0].fuelType = '汽油'
    scope1Data.mobileCombustion[0].vehicleType = '公务车'
    scope1Data.mobileCombustion[0].unit = 'L'

    scope2Data.purchasedEnergy[0].energyType = 'electricity'
    scope2Data.purchasedEnergy[0].region = '华北'
    scope2Data.purchasedEnergy[0].unit = 'kWh'

    scope3Data.wasteGenerated[0].wasteType = '生活垃圾'
    scope3Data.wasteGenerated[0].disposalMethod = 'landfill'

    // 触发一次匹配更新，让用户可以立即看到匹配的因子
    updateMatchedFactorDisplay(scope1Data.fixedCombustion[0], 'fixedCombustion')
    updateMatchedFactorDisplay(scope1Data.mobileCombustion[0], 'mobileCombustion')
    updateMatchedFactorDisplay(scope2Data.purchasedEnergy[0], 'purchasedEnergy')
    updateMatchedFactorDisplay(scope3Data.wasteGenerated[0], 'wasteGenerated')
  })

  // --- 活动数据录入与计算模块的方法 ---

  const getNewActivityItemId = () =>
    Date.now().toString() + Math.random().toString(36).substring(2, 7)

  const addActivityItem = (
    scopeKey: 'scope1Data' | 'scope2Data' | 'scope3Data',
    activityKey: string
  ) => {
    let newItem: any
    const baseItem = {
      id: getNewActivityItemId(),
      consumption: undefined,
      unit: '',
      matchedFactorDisplay: ''
    }

    if (scopeKey === 'scope1Data') {
      if (activityKey === 'fixedCombustion') {
        newItem = { ...baseItem, fuelType: '' }
        scope1Data.fixedCombustion.push(newItem as FixedCombustionItem)
      } else if (activityKey === 'mobileCombustion') {
        newItem = { ...baseItem, vehicleType: '', fuelType: '' }
        scope1Data.mobileCombustion.push(newItem as MobileCombustionItem)
      }
    } else if (scopeKey === 'scope2Data') {
      if (activityKey === 'purchasedEnergy') {
        newItem = { ...baseItem, energyType: '', region: '' }
        scope2Data.purchasedEnergy.push(newItem as PurchasedEnergyItem)
      }
    } else if (scopeKey === 'scope3Data') {
      if (activityKey === 'wasteGenerated') {
        newItem = {
          id: getNewActivityItemId(),
          wasteType: '',
          disposalMethod: '',
          weight: undefined,
          unit: 't',
          matchedFactorDisplay: ''
        }
        scope3Data.wasteGenerated.push(newItem as WasteGeneratedItem)
      }
    }
    if (newItem) console.log(`Added new item to ${scopeKey}.${activityKey}:`, newItem)
  }

  const removeActivityItem = (
    scopeKey: 'scope1Data' | 'scope2Data' | 'scope3Data',
    activityKey: string,
    index: number
  ) => {
    if (scopeKey === 'scope1Data') {
      if (activityKey === 'fixedCombustion') scope1Data.fixedCombustion.splice(index, 1)
      else if (activityKey === 'mobileCombustion') scope1Data.mobileCombustion.splice(index, 1)
    } else if (scopeKey === 'scope2Data') {
      if (activityKey === 'purchasedEnergy') scope2Data.purchasedEnergy.splice(index, 1)
    } else if (scopeKey === 'scope3Data') {
      if (activityKey === 'wasteGenerated') scope3Data.wasteGenerated.splice(index, 1)
    }
  }

  // 优化findMatchingFactor函数，使其能够更准确地匹配到预设的因子
  const findMatchingFactor = (item: any, activityTypeKey: string): Factor | null => {
    // 改进的匹配逻辑，更准确地匹配到预设的默认因子
    let matchedFactor: Factor | undefined = undefined

    if (activityTypeKey === 'fixedCombustion') {
      const fcItem = item as FixedCombustionItem
      // 优先匹配精确的燃料类型
      matchedFactor = factorsData.value.find(
        (f) => f.isActive && f.activityType === '能源' && f.activitySubType === fcItem.fuelType
      )

      // 如果没有精确匹配，尝试模糊匹配
      if (!matchedFactor && fcItem.fuelType) {
        matchedFactor = factorsData.value.find(
          (f) =>
            f.isActive &&
            f.activityType === '能源' &&
            f.activitySubType?.toLowerCase().includes(fcItem.fuelType.toLowerCase())
        )
      }
    } else if (activityTypeKey === 'mobileCombustion') {
      const mcItem = item as MobileCombustionItem
      // 优先匹配精确的燃料类型
      matchedFactor = factorsData.value.find(
        (f) => f.isActive && f.activityType === '交通' && f.activitySubType === mcItem.fuelType
      )

      // 如果没有精确匹配，尝试模糊匹配
      if (!matchedFactor && mcItem.fuelType) {
        matchedFactor = factorsData.value.find(
          (f) =>
            f.isActive &&
            f.activityType === '交通' &&
            f.activitySubType?.toLowerCase().includes(mcItem.fuelType.toLowerCase())
        )
      }
    } else if (activityTypeKey === 'purchasedEnergy') {
      const peItem = item as PurchasedEnergyItem
      if (peItem.energyType === 'electricity') {
        // 先尝试根据地区匹配
        if (peItem.region) {
          matchedFactor = factorsData.value.find(
            (f) =>
              f.isActive &&
              f.activityType === '能源' &&
              f.activitySubType === '外购电力' &&
              f.region?.includes(peItem.region)
          )
        }

        // 如果没有匹配到区域特定因子，使用默认电力因子
        if (!matchedFactor) {
          matchedFactor = factorsData.value.find(
            (f) => f.isActive && f.activityType === '能源' && f.activitySubType === '外购电力'
          )
        }
      } else if (peItem.energyType === 'heat' || peItem.energyType === 'steam') {
        // 这里可以添加热力和蒸汽的匹配逻辑
        matchedFactor = factorsData.value.find(
          (f) =>
            f.isActive &&
            f.activityType === '能源' &&
            f.activitySubType?.toLowerCase().includes(peItem.energyType.toLowerCase())
        )
      }
    } else if (activityTypeKey === 'wasteGenerated') {
      const wgItem = item as WasteGeneratedItem

      // 匹配废弃物类型和处理方式
      if (wgItem.wasteType && wgItem.disposalMethod) {
        // 先尝试精确匹配
        matchedFactor = factorsData.value.find(
          (f) =>
            f.isActive &&
            f.activityType === '废弃物' &&
            f.activitySubType?.includes(wgItem.wasteType) &&
            f.notes?.toLowerCase().includes(wgItem.disposalMethod.toLowerCase())
        )

        // 如果没有精确匹配，尝试只匹配处理方式
        if (!matchedFactor) {
          matchedFactor = factorsData.value.find(
            (f) =>
              f.isActive &&
              f.activityType === '废弃物' &&
              f.notes?.toLowerCase().includes(wgItem.disposalMethod.toLowerCase())
          )
        }
      }
    }

    // 如果还是没有匹配到，尝试用更宽松的条件匹配
    if (!matchedFactor) {
      if (activityTypeKey === 'fixedCombustion' || activityTypeKey === 'mobileCombustion') {
        const fuelType =
          activityTypeKey === 'fixedCombustion'
            ? (item as FixedCombustionItem).fuelType
            : (item as MobileCombustionItem).fuelType

        if (fuelType) {
          matchedFactor = factorsData.value.find(
            (f) =>
              f.isActive &&
              (f.factorName.includes(fuelType) || f.activitySubType?.includes(fuelType))
          )
        }
      }
    }

    return matchedFactor || null
  }

  const updateMatchedFactorDisplay = (item: any, activityTypeKey: string) => {
    const factor = findMatchingFactor(item, activityTypeKey)
    item._factor = factor // 存储匹配到的因子对象用于计算

    if (factor) {
      // 显示因子信息，所有因子都已经是CO2e单位
      item.matchedFactorDisplay = `${factor.factorName} (${factor.factorValue} ${factor.factorUnit})`
    } else {
      item.matchedFactorDisplay = '未匹配到因子'
    }
  }

  const calculateSingleActivityEmission = (item: ActivityItemBase): number => {
    const factor = item._factor
    // 确保因子和因子值存在，并且活动数据（consumption 或 weight）有效
    if (
      factor &&
      typeof factor.factorValue === 'number' // 明确检查 factorValue 是数字
    ) {
      let activityValue: number | null | undefined = null
      if ((item as WasteGeneratedItem).weight !== undefined) {
        activityValue = (item as WasteGeneratedItem).weight
      } else if (item.consumption !== undefined && item.consumption !== null) {
        activityValue = item.consumption
      }

      if (typeof activityValue === 'number') {
        // 确保活动值也是数字，因子已经是CO2e单位，直接计算
        const co2e = activityValue * factor.factorValue
        item._co2e = co2e // Store calculated CO2e on the item

        // For byGas aggregation - 简化为统一的CO2e类型
        if (!emissionResults.byGas.find((g) => g.gasType === 'CO2e')) {
          emissionResults.byGas.push({
            gasType: 'CO2e',
            emissionsCO2e: 0
          })
        }
        const gasEntry = emissionResults.byGas.find((g) => g.gasType === 'CO2e')!
        gasEntry.emissionsCO2e = (gasEntry.emissionsCO2e || 0) + co2e

        return co2e
      }
    }
    item._co2e = 0
    return 0
  }

  const handleCalculateEmissions = async () => {
    isCalculating.value = true

    try {
      // Reset previous results
      emissionResults.totalEmissions = 0
      emissionResults.byScope = [
        { scope: '范畴一', emissions: 0, percentage: 0 },
        { scope: '范畴二', emissions: 0, percentage: 0 },
        { scope: '范畴三', emissions: 0, percentage: 0 }
      ]
      emissionResults.byGas = []
      emissionResults.bySource = []

      let currentTotalCO2e = 0

      // Scope 1 Calculations
      scope1Data.fixedCombustion.forEach((item) => {
        if (!item._factor) updateMatchedFactorDisplay(item, 'fixedCombustion') // Ensure factor is matched
        const co2e = calculateSingleActivityEmission(item)
        currentTotalCO2e += co2e
        emissionResults.byScope[0].emissions! += co2e
        if (co2e > 0)
          emissionResults.bySource.push({
            sourceName: `固定燃烧 - ${item.fuelType}`,
            emissions: co2e
          })
      })
      scope1Data.mobileCombustion.forEach((item) => {
        if (!item._factor) updateMatchedFactorDisplay(item, 'mobileCombustion')
        const co2e = calculateSingleActivityEmission(item)
        currentTotalCO2e += co2e
        emissionResults.byScope[0].emissions! += co2e
        if (co2e > 0)
          emissionResults.bySource.push({
            sourceName: `移动燃烧 - ${item.vehicleType} (${item.fuelType})`,
            emissions: co2e
          })
      })

      // Scope 2 Calculations
      scope2Data.purchasedEnergy.forEach((item) => {
        if (!item._factor) updateMatchedFactorDisplay(item, 'purchasedEnergy')
        const co2e = calculateSingleActivityEmission(item)
        currentTotalCO2e += co2e
        emissionResults.byScope[1].emissions! += co2e
        if (co2e > 0)
          emissionResults.bySource.push({
            sourceName: `外购能源 - ${
              item.energyType === 'electricity'
                ? '电力'
                : item.energyType === 'heat'
                  ? '热力'
                  : '蒸汽'
            } (${item.region})`,
            emissions: co2e
          })
      })

      // Scope 3 Calculations
      scope3Data.wasteGenerated.forEach((item) => {
        if (!item._factor) updateMatchedFactorDisplay(item, 'wasteGenerated')
        const co2e = calculateSingleActivityEmission(item)
        currentTotalCO2e += co2e
        emissionResults.byScope[2].emissions! += co2e
        if (co2e > 0)
          emissionResults.bySource.push({
            sourceName: `废弃物 - ${item.wasteType} (${item.disposalMethod})`,
            emissions: co2e
          })
      })

      emissionResults.totalEmissions = currentTotalCO2e

      // Calculate percentages for byScope
      if (currentTotalCO2e > 0) {
        emissionResults.byScope.forEach((s) => {
          s.percentage = (s.emissions! / currentTotalCO2e) * 100
        })
      }

      // Sort bySource by emissions desc
      emissionResults.bySource.sort((a, b) => (b.emissions || 0) - (a.emissions || 0))

      console.log('计算结果:', JSON.stringify(emissionResults))

      // 模拟计算时间
      await new Promise((resolve) => setTimeout(resolve, 1000))

      isCalculating.value = false
      ElMessage.success('碳排放计算完成！')
      activeTabName.value = 'reportAndAnalysis'

      // 确保切换到报表页后再渲染图表
      setTimeout(() => {
        renderScopePieChart()
      }, 300)
    } catch (error) {
      console.error('计算过程中出错:', error)
      isCalculating.value = false
      ElMessage.error('计算过程中发生错误，请检查数据')
    }
  }

  const exportReport = () => {
    ElMessage.info('正在准备导出报告...')

    // 准备导出数据
    const reportData = {
      title: `碳排放报告 (核算年度: ${calculationConfig.period})`,
      entity: calculationConfig.entity,
      generateTime: new Date().toLocaleString(),
      totalEmissions: emissionResults.totalEmissions?.toFixed(2) + ' tCO2e',
      byScopeData: emissionResults.byScope.map((item) => ({
        scope: item.scope,
        emissions: item.emissions?.toFixed(2) + ' tCO2e',
        percentage: item.percentage?.toFixed(1) + '%'
      })),
      byGasData: emissionResults.byGas.map((item) => ({
        gasType: item.gasType,
        emissionsCO2e: item.emissionsCO2e?.toFixed(2) + ' tCO2e'
      })),
      bySourceData: emissionResults.bySource.map((item) => ({
        sourceName: item.sourceName,
        emissions: item.emissions?.toFixed(2) + ' tCO2e'
      }))
    }

    // 将数据转换为CSV格式
    let csvContent = `# ${reportData.title}\n`
    csvContent += `# 核算主体: ${reportData.entity}\n`
    csvContent += `# 生成时间: ${reportData.generateTime}\n\n`

    csvContent += `# 总排放量: ${reportData.totalEmissions}\n\n`

    csvContent += '# 按范畴排放明细\n'
    csvContent += '排放范畴,排放量 (tCO2e),占比 (%)\n'
    reportData.byScopeData.forEach((row) => {
      csvContent += `${row.scope},${row.emissions},${row.percentage}\n`
    })
    csvContent += '\n'

    csvContent += '# 按温室气体类型排放明细\n'
    csvContent += '气体类型,CO2当量排放量 (tCO2e)\n'
    reportData.byGasData.forEach((row) => {
      csvContent += `${row.gasType},${row.emissionsCO2e}\n`
    })
    csvContent += '\n'

    csvContent += '# 按主要活动源排放明细\n'
    csvContent += '活动源/类别,排放量 (tCO2e)\n'
    reportData.bySourceData.forEach((row) => {
      csvContent += `${row.sourceName},${row.emissions}\n`
    })

    // 创建下载链接
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `碳排放报告_${calculationConfig.period}_${new Date().getTime()}.csv`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success('报告导出成功!')
  }

  // Watchers for auto-updating matched factor display (optional, can be triggered manually)
  watch(
    () => scope1Data.fixedCombustion,
    (items) => items.forEach((item) => updateMatchedFactorDisplay(item, 'fixedCombustion')),
    { deep: true }
  )
  watch(
    () => scope1Data.mobileCombustion,
    (items) => items.forEach((item) => updateMatchedFactorDisplay(item, 'mobileCombustion')),
    { deep: true }
  )
  watch(
    () => scope2Data.purchasedEnergy,
    (items) => items.forEach((item) => updateMatchedFactorDisplay(item, 'purchasedEnergy')),
    { deep: true }
  )
  watch(
    () => scope3Data.wasteGenerated,
    (items) => items.forEach((item) => updateMatchedFactorDisplay(item, 'wasteGenerated')),
    { deep: true }
  )

  // 修复类型错误的renderScopePieChart函数
  const renderScopePieChart = () => {
    if (!scopePieChartRef.value) {
      console.error('饼图容器元素不存在')
      return
    }

    console.log('正在渲染饼图...')

    // 动态导入echarts以减少初始加载时间
    import('echarts')
      .then((echarts) => {
        try {
          // 尝试销毁已存在的图表实例
          // 修复类型错误：添加非空断言
          const element = scopePieChartRef.value!
          const existingChart = echarts.getInstanceByDom(element)
          if (existingChart) {
            existingChart.dispose()
          }

          // 初始化图表
          const chart = echarts.init(element)

          // 准备数据
          const data = emissionResults.byScope
            .filter((item) => item.emissions && item.emissions > 0)
            .map((item) => ({
              name: item.scope,
              value: item.emissions
            }))

          console.log('饼图数据:', data)

          if (data.length === 0) {
            // 如果没有数据，显示空状态
            // 修复类型错误：添加非空检查
            if (element) {
              element.innerHTML =
                '<div style="display:flex; justify-content:center; align-items:center; height:100%;">暂无数据</div>'
            }
            return
          }

          // 设置图表选项
          const option = {
            tooltip: {
              trigger: 'item',
              formatter: '{a} <br/>{b}: {c} tCO2e ({d}%)'
            },
            legend: {
              orient: 'vertical',
              left: 10,
              data: data.map((item) => item.name)
            },
            series: [
              {
                name: '排放范畴',
                type: 'pie',
                radius: ['40%', '70%'],
                avoidLabelOverlap: false,
                itemStyle: {
                  borderRadius: 10,
                  borderColor: '#fff',
                  borderWidth: 2
                },
                label: {
                  show: true,
                  formatter: '{b}: {d}%'
                },
                emphasis: {
                  label: {
                    show: true,
                    fontSize: '14',
                    fontWeight: 'bold'
                  }
                },
                labelLine: {
                  show: true
                },
                data: data
              }
            ],
            color: ['#67C23A', '#409EFF', '#E6A23C']
          }

          // 设置图表
          chart.setOption(option)
          console.log('饼图渲染完成')

          // 窗口大小变化时重新调整图表大小
          window.addEventListener('resize', () => {
            chart.resize()
          })
        } catch (error) {
          console.error('渲染饼图时发生错误:', error)
          // 显示错误信息
          if (scopePieChartRef.value) {
            scopePieChartRef.value.innerHTML =
              '<div style="color:red; text-align:center;">图表渲染失败</div>'
          }
        }
      })
      .catch((error) => {
        console.error('加载ECharts失败:', error)
      })
  }
</script>

<style lang="scss" scoped>
  .carbon-factors-management {
    padding: 20px;
    background-color: var(--art-bg-color); // 使用主题背景色
  }

  .page-header {
    margin-bottom: 20px;
    background-color: var(--art-main-bg-color); // 卡片背景色
    border-color: var(--art-root-card-border-color); // 卡片边框色
    .el-card__body {
      padding-bottom: 10px;
    }

    h2 {
      margin-top: 0;
      margin-bottom: 8px;
      font-size: 20px;
      color: var(--art-text-gray-900); // 主要文字颜色
    }

    p {
      margin-bottom: 0;
      font-size: 14px;
      color: var(--art-text-gray-600); // 次要文字颜色
    }
  }

  .actions-and-filters-card {
    margin-bottom: 20px;
    background-color: var(--art-main-bg-color); // 卡片背景色
    border-color: var(--art-root-card-border-color); // 卡片边框色
    .search-form {
      .el-form-item {
        margin-bottom: 12px;
      }

      .el-select {
        width: 180px;
      }
    }

    .action-buttons {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-top: 10px;
    }
  }

  .factors-table-card {
    min-height: 400px;
    background-color: var(--art-main-bg-color); // 卡片背景色
    border-color: var(--art-root-card-border-color); // 卡片边框色

    /* 确保ArtTable组件有足够的显示空间 */
    :deep(.art-table) {
      height: auto;
      min-height: 400px;
    }

    /* 修复el-table__empty-block高度不断增长问题 */
    .factors-table :deep(.el-table__empty-block) {
      height: auto !important;
      min-height: 60px;
      max-height: 400px;
      overflow: hidden;
    }

    /* 确保空表格状态下的布局稳定 */
    .factors-table :deep(.el-table__body-wrapper) {
      height: auto !important;
      min-height: 200px;
    }

    /* 自定义操作按钮样式 */
    .action-btn-view,
    .action-btn-edit,
    .action-btn-delete {
      padding: 6px !important; /* 使用 !important 确保覆盖 element-plus 的默认 padding */
      line-height: 1; /* 确保图标垂直居中 */
      border-radius: 4px !important;
    }

    .action-btn-view,
    .action-btn-edit {
      background-color: #e6f7ff !important; /* 浅蓝色背景 */
    }

    .action-btn-view .iconfont-sys,
    .action-btn-edit .iconfont-sys {
      margin: 0; /* 移除可能的默认边距 */
      font-size: 14px; /* 可根据需要调整图标大小 */
      color: #409eff !important; /* Element Plus Primary Color */
    }

    .action-btn-delete {
      background-color: #fff1f0 !important; /* 浅红色背景 */
    }

    .action-btn-delete .iconfont-sys {
      margin: 0; /* 移除可能的默认边距 */
      font-size: 14px; /* 可根据需要调整图标大小 */
      color: #f56c6c !important; /* Element Plus Danger Color */
    }
  }

  .pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }

  .upload-importer {
    margin-top: 15px;
    text-align: center;
  }

  // el-dialog 相关的颜色通常由 Element Plus 自身的主题控制，此处一般无需额外修改
  // .el-dialog__body {
  //   padding-bottom: 10px;
  // }

  // .el-dialog__footer {
  //   padding-top: 10px;
  // }

  // 针对 el-icon 的一些微调
  .el-button .el-icon {
    margin-right: 5px;
  }

  .el-button.is-link .el-icon {
    margin-right: 3px;
  }

  /* Styles for new tabs */
  .carbon-toolbox-page {
    .toolbox-tabs {
      margin-top: 0; /* 确保没有顶部边距 */
    }

    .el-tab-pane {
      padding: 10px 0;
    }
  }

  .activity-data-entry,
  .emission-report-analysis {
    padding: 0; /* 确保没有额外的内边距 */
  }

  .activity-group {
    padding: 15px;
    margin-bottom: 25px;
    background-color: var(--art-main-bg-color); // 使用主背景色或稍浅的灰色
    border: 1px solid var(--art-border-color); // 使用主题边框颜色
    border-radius: 4px;

    h4 {
      padding-bottom: 8px;
      margin-top: 0;
      margin-bottom: 15px;
      font-size: 16px;
      color: var(--art-text-gray-900); // 主要文字颜色
      border-bottom: 1px solid var(--art-border-color); // 使用主题边框颜色
    }

    .el-table {
      font-size: 13px;
    }
  }

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
    font-weight: bold;
    color: var(--art-text-gray-900); // 卡片头部文字颜色
  }

  .no-data-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    color: var(--art-text-muted); // 使用柔和的提示文字颜色
  }

  // 确保 el-card 的背景和边框也使用主题变量
  .el-card {
    background-color: var(--art-main-bg-color) !important; // 强制覆盖 Element Plus 默认样式
    border: 1px solid var(--art-root-card-border-color) !important; // 强制覆盖 Element Plus 默认样式
  }

  // 饼图占位符的边框颜色
  div[ref='scopePieChartRef'] {
    border: 1px solid var(--art-border-color) !important; // 使用主题边框颜色
  }
</style>
