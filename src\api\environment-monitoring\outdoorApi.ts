import api from '@/utils/http'

// 室外环境监测相关接口

/**
 * 设备列表查询参数
 */
export interface EquipmentListParams {
  projectId: number | string
  buildingId?: number | string
  floorId?: number | string
  equipmentCategory?: number | string
  equipmentCategorytype?: number | string
}

/**
 * 设备信息
 */
export interface EquipmentInfo {
  id: number
  name: string
  code: string
  equipmentType: string
  // 其他设备字段...
}

/**
 * 设备列表响应
 */
export interface EquipmentListResponse {
  msg: string
  code: number
  data: EquipmentInfo[]
}

/**
 * 空气质量监测数据
 */
export interface AirQualityData {
  createBy: string | null
  createTime: string
  updateBy: string | null
  updateTime: string | null
  remark: string | null
  id: number
  projectId: number | null
  equipmentId: number | null
  outdoorId: number
  kqwd: number // 空气温度
  kqsd: number // 空气湿度
  fx: string // 风向
  fs: number // 风速
  zs: number // 噪声
  zfs: number // 总辐射
  pm25: number // PM2.5
  pm10: number // PM10
  js: number // 降水
  monitorTimestamp: number // 监测时间戳
  outDoorIdList: any[] | null
  hourTime: string | null
}

/**
 * 空气质量监测响应
 */
export interface AirQualityResponse {
  msg: string
  code: number
  data: AirQualityData
}

/**
 * 温度湿度趋势数据项
 */
export interface TrendDataItem {
  wd: number // 温度
  sd: number // 湿度
  ph2_5: number // PM2.5
  ph10: number // PM10
}

/**
 * 温度湿度趋势数据
 */
export interface TrendData {
  timeList: string[]
  dataList: TrendDataItem[]
}

/**
 * 温度湿度趋势响应
 */
export interface TrendDataResponse {
  msg: string
  code: number
  data: TrendData
}

/**
 * 历史数据查询参数
 */
export interface HistoryDataParams {
  projectId: number | string
  equipmentId?: number | string
  beginTime?: string
  endTime?: string
  pageSize: number
  pageNum: number
}

/**
 * 历史数据响应
 */
export interface HistoryDataResponse {
  total: number
  rows: AirQualityData[]
  code: number
  msg: string
}

/**
 * 获取设备列表
 * @param params 查询参数
 * @returns 设备列表
 */
export function getEquipmentList(params: EquipmentListParams): Promise<EquipmentListResponse> {
  return api.get({
    url: '/search/getEquipmentList',
    params
  })
}

/**
 * 获取空气质量监测数据
 * @param params 查询参数
 * @returns 空气质量数据
 */
export function getAirQualityMonitoring(params: {
  projectId: number | string
  equipmentId?: number | string
}): Promise<AirQualityResponse> {
  return api.get({
    url: '/envmonitor/outdoor/getAirQualityMonitoring',
    params
  })
}

/**
 * 获取温度湿度趋势数据
 * @param params 查询参数
 * @returns 趋势数据
 */
export function getTemperatureAndTemperatureTrend(params: {
  projectId: number | string
  equipmentId?: number | string
  timeType: number // 0:日, 1:周, 2:月
}): Promise<TrendDataResponse> {
  return api.get({
    url: '/envmonitor/outdoor/getTemperatureAndTemperatureTrend',
    params
  })
}

/**
 * 获取历史监测数据
 * @param params 查询参数
 * @returns 历史数据
 */
export function getHistoryList(params: HistoryDataParams): Promise<HistoryDataResponse> {
  return api.get({
    url: '/envmonitor/outdoor/getHistoryList',
    params
  })
}

/**
 * 导出参数
 */
export interface ExportParams {
  projectId: number | string
  equipmentId?: number | string
  beginTime?: string
  endTime?: string
}

/**
 * 导出室外环境监测数据
 * @param params 导出参数
 * @returns 文件流
 */
export function exportOutdoorData(params: ExportParams): Promise<Blob> {
  return api.post({
    url: '/envmonitor/outdoor/export',
    data: params,
    responseType: 'blob'
  })
}
