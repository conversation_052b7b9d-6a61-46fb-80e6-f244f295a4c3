<template>
  <div class="air-quality-container page-content">
    <div class="content-section">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card class="overview-card">
            <template #header>
              <div class="card-header">
                <span>空气质量监测数据</span>
                <div class="filter-controls">
                  <ProjectSelector
                    ref="projectSelectorRef"
                    v-model="selectedProjectId"
                    placeholder="请选择项目"
                    style="width: 250px"
                    @change="handleProjectChange"
                    @loaded="handleProjectListLoaded"
                  />
                  <el-select
                    v-model="selectedPoint"
                    placeholder="选择点位"
                    style="width: 200px"
                    :disabled="!selectedProjectId"
                    @change="handlePointChange"
                  >
                    <el-option
                      v-for="point in pointOptions"
                      :key="point.value"
                      :label="point.label"
                      :value="point.value"
                    ></el-option>
                  </el-select>
                </div>
              </div>
            </template>

            <div class="data-grid">
              <div class="data-item" v-for="(item, index) in airQualityData" :key="index">
                <div class="item-value" :class="item.status"> {{ item.value }}{{ item.unit }} </div>
                <div class="item-name">{{ item.name }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="chart-row">
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>温度湿度趋势</span>
                <el-radio-group v-model="tempHumidTimeRange" size="small">
                  <el-radio-button label="day">日</el-radio-button>
                  <el-radio-button label="week">周</el-radio-button>
                  <el-radio-button label="month">月</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <TempHumidChart :timeRange="tempHumidTimeRange" />
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>颗粒物污染趋势</span>
                <el-radio-group v-model="pm25TimeRange" size="small">
                  <el-radio-button label="day">日</el-radio-button>
                  <el-radio-button label="week">周</el-radio-button>
                  <el-radio-button label="month">月</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <PM25Chart :timeRange="pm25TimeRange" />
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-card class="history-card">
            <template #header>
              <div class="card-header">
                <span>历史监测数据</span>
                <div>
                  <el-date-picker
                    v-model="dateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    format="YYYY/MM/DD"
                    value-format="YYYY-MM-DD"
                    style="width: 320px"
                    @change="handleDateRangeChange"
                  ></el-date-picker>
                  <el-button
                    type="primary"
                    size="small"
                    style="margin-left: 10px"
                    @click="searchData"
                    :disabled="historyQueryButtonDisabled"
                    >查询</el-button
                  >
                  <el-button type="primary" size="small" @click="exportData">导出</el-button>
                </div>
              </div>
            </template>

            <el-table :data="historyData" border style="width: 100%">
              <el-table-column prop="序号" label="序号" align="center" width="80"></el-table-column>
              <el-table-column
                prop="监测时间"
                label="监测时间"
                align="center"
                width="180"
              ></el-table-column>
              <el-table-column
                prop="空气温度"
                label="空气温度(°C)"
                align="center"
              ></el-table-column>
              <el-table-column prop="空气湿度" label="空气湿度(%)" align="center"></el-table-column>
              <el-table-column prop="风向" label="风向(°)" align="center"></el-table-column>
              <el-table-column prop="风速" label="风速(m/s)" align="center"></el-table-column>
              <el-table-column prop="噪声" label="噪声(dB)" align="center"></el-table-column>
              <el-table-column prop="总辐射" label="总辐射(W/m²)" align="center"></el-table-column>
              <el-table-column prop="PM25" label="PM2.5(μg/m³)" align="center"></el-table-column>
              <el-table-column prop="PM10" label="PM10(μg/m³)" align="center"></el-table-column>
              <el-table-column prop="降水" label="降水(mm/h)" align="center"></el-table-column>
            </el-table>

            <div class="pagination-container">
              <el-pagination
                background
                layout="total, sizes, prev, pager, next, jumper"
                :total="totalItems"
                :page-sizes="[10, 20, 50, 100]"
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              ></el-pagination>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive, provide, computed, watch, nextTick, onMounted } from 'vue'
  import { ElMessage } from 'element-plus'
  import PM25Chart from '@/components/echarts/OutdoorEnvironment/PM25Chart.vue'
  import TempHumidChart from '@/components/echarts/OutdoorEnvironment/TempHumidChart.vue'
  import ProjectSelector from '@/components/ProjectSelector.vue'
  import {
    getEquipmentList,
    getAirQualityMonitoring,
    getTemperatureAndTemperatureTrend,
    getHistoryList,
    exportOutdoorData
  } from '@/api/environment-monitoring/outdoorApi'

  // 项目选择相关数据
  const selectedProjectId = ref(null) // 初始为空，等待获取项目列表后设置第一个
  const selectedProjectInfo = ref(null)
  const projectSelectorRef = ref()

  // 点位选择相关数据
  const selectedPoint = ref(null) // 当前选中的点位值
  const equipmentList = ref([]) // 设备列表
  const equipmentLoading = ref(false) // 设备列表加载状态

  // 查询状态管理 - 历史数据查询
  const lastHistoryQueryParams = ref(null)
  const historyQueryButtonDisabled = ref(true) // 初始状态为禁用

  // 日期范围选择
  const dateRange = ref([])

  // 比较历史数据查询参数是否相同
  const isSameHistoryQueryParams = (params1, params2) => {
    if (!params1 || !params2) return false
    return (
      params1.projectId === params2.projectId &&
      params1.selectedPoint === params2.selectedPoint &&
      params1.beginTime === params2.beginTime &&
      params1.endTime === params2.endTime &&
      params1.pageSize === params2.pageSize &&
      params1.pageNum === params2.pageNum
    )
  }

  // 计算当前选中项目的点位选项
  const pointOptions = computed(() => {
    if (!equipmentList.value || equipmentList.value.length === 0) {
      return []
    }
    // 第一个选项是平均值
    const points = [{ label: '平均值', value: 'average' }]
    // 添加具体设备点位
    equipmentList.value.forEach((equipment) => {
      points.push({
        label: equipment.name || `设备${equipment.id}`,
        value: equipment.id
      })
    })
    return points
  })

  // 获取设备列表
  const fetchEquipmentList = async (projectId) => {
    if (!projectId) return

    try {
      equipmentLoading.value = true
      const response = await getEquipmentList({
        projectId: projectId,
        buildingId: '', // 可以为空
        floorId: '', // 可以为空
        equipmentCategory: 1, // 环境监测设备
        equipmentCategorytype: 0 // 室外气象站
      })

      if (response.code === 200) {
        equipmentList.value = response.data || []
        console.log('设备列表获取成功:', response.data)
      } else {
        ElMessage.error(response.msg || '获取设备列表失败')
        equipmentList.value = []
      }
    } catch (error) {
      console.error('获取设备列表失败:', error)
      ElMessage.error('获取设备列表失败，请稍后重试')
      equipmentList.value = []
    } finally {
      equipmentLoading.value = false
    }
  }

  // 处理项目变化
  const handleProjectChange = (projectInfo) => {
    selectedProjectInfo.value = projectInfo
    selectedPoint.value = null // 项目变化时清空点位选择
    console.log('选中的项目:', projectInfo)
    // 获取该项目的设备列表
    if (projectInfo && projectInfo.id) {
      fetchEquipmentList(projectInfo.id)
    }
    // 项目变化时自动发送请求（包含历史数据）
    nextTick(() => {
      autoSearch()
    })
  }

  // 处理点位变化
  const handlePointChange = (pointValue) => {
    console.log('选中的点位:', pointValue)
    // 点位变化时自动发送请求（包含历史数据）
    autoSearch()
  }

  // 处理日期范围变化
  const handleDateRangeChange = (dateRangeValue) => {
    console.log('日期范围变化:', dateRangeValue)
    // 日期范围变化时不自动发送请求，只启用查询按钮
    historyQueryButtonDisabled.value = false
  }

  // 监听项目选择器组件的项目列表加载完成
  const handleProjectListLoaded = (projectList) => {
    // 当项目列表加载完成后，自动选择第一个项目并发请求
    if (projectList && projectList.length > 0) {
      const firstProject = projectList[0]
      selectedProjectId.value = firstProject.id
      selectedProjectInfo.value = firstProject
      console.log('自动选择第一个项目:', firstProject)
      // 获取设备列表
      fetchEquipmentList(firstProject.id)
      // 启用历史数据查询按钮
      historyQueryButtonDisabled.value = false
      // 自动发请求获取数据（包含历史数据）
      nextTick(() => {
        autoSearch()
      })
    }
  }

  // 自动查询方法（包含历史数据查询）
  const autoSearch = async () => {
    // 自动查询时不需要检查项目选择，使用固定参数
    console.log('自动查询触发，使用固定参数:', {
      projectId: 17,
      equipmentId: 1,
      beginTime: getTodayDateString(),
      endTime: getTodayDateString()
    })

    // 获取实时空气质量数据（如果有选择项目的话）
    if (selectedProjectId.value) {
      await fetchAirQualityData()
      // 获取趋势数据
      await fetchTrendData()
    }

    // 获取历史数据（使用自动参数）
    await fetchHistoryData(true)
  }

  // 监听项目选择变化
  watch(selectedProjectId, (newVal, oldVal) => {
    if (newVal !== oldVal) {
      selectedPoint.value = null // 项目改变时清空点位选择
      // 可以在这里触发数据加载等操作
    }
  })

  // 监听日期范围变化，只控制查询按钮状态，不自动发送请求
  watch(
    dateRange,
    (newVal, oldVal) => {
      // 日期范围变化时启用查询按钮
      if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
        console.log('日期范围变化:', newVal)
        historyQueryButtonDisabled.value = false
        // 清空上次查询参数，允许重新查询
        lastHistoryQueryParams.value = null
      }
    },
    { deep: true }
  )

  // 空气质量数据
  const airQualityData = reactive([
    {
      name: '空气温度',
      value: '26.5',
      unit: '°C',
      status: 'good',
      range: '-40°C~+120°C'
    },
    {
      name: '空气湿度',
      value: '52',
      unit: '%RH',
      status: 'good',
      range: '0%RH-100%RH'
    },
    { name: '风向', value: '45', unit: '°', status: 'good', range: '0~360°' },
    { name: '风速', value: '3.2', unit: 'm/s', status: 'good', range: '0~70m/s' },
    {
      name: '噪声',
      value: '58',
      unit: 'dB',
      status: 'good',
      range: '30dB-120dB'
    },
    {
      name: '总辐射',
      value: '420',
      unit: 'W/m²',
      status: 'good',
      range: '0-1800W/m²'
    },
    {
      name: 'PM2.5',
      value: '28',
      unit: 'μg/m³',
      status: 'good'
    },
    {
      name: 'PM10',
      value: '56',
      unit: 'μg/m³',
      status: 'good'
    },
    {
      name: '降水',
      value: '0',
      unit: 'mm/h',
      status: 'good',
      range: '0-200mm/h'
    }
  ])

  // 获取实时空气质量数据
  const fetchAirQualityData = async () => {
    try {
      const params = {
        projectId: selectedProjectId.value
      }

      // 如果选择了具体设备，添加设备ID参数
      if (selectedPoint.value && selectedPoint.value !== 'average') {
        params.equipmentId = selectedPoint.value
      }

      const response = await getAirQualityMonitoring(params)

      if (response.code === 200 && response.data) {
        const data = response.data
        // 更新空气质量数据
        updateAirQualityDisplay(data)
        console.log('空气质量数据获取成功:', data)
      } else {
        ElMessage.error(response.msg || '获取空气质量数据失败')
      }
    } catch (error) {
      console.error('获取空气质量数据失败:', error)
      ElMessage.error('获取空气质量数据失败，请稍后重试')
    }
  }

  // 更新空气质量显示数据
  const updateAirQualityDisplay = (data) => {
    // 根据API返回的字段更新显示数据
    const updates = [
      { name: '空气温度', value: data.kqwd?.toString() || '0', unit: '°C' },
      { name: '空气湿度', value: data.kqsd?.toString() || '0', unit: '%RH' },
      { name: '风向', value: data.fx?.toString() || '0', unit: '°' },
      { name: '风速', value: data.fs?.toString() || '0', unit: 'm/s' },
      { name: '噪声', value: data.zs?.toString() || '0', unit: 'dB' },
      { name: '总辐射', value: data.zfs?.toString() || '0', unit: 'W/m²' },
      { name: 'PM2.5', value: data.pm25?.toString() || '0', unit: 'μg/m³' },
      { name: 'PM10', value: data.pm10?.toString() || '0', unit: 'μg/m³' },
      { name: '降水', value: data.js?.toString() || '0', unit: 'mm/h' }
    ]

    updates.forEach((update) => {
      const item = airQualityData.find((item) => item.name === update.name)
      if (item) {
        item.value = update.value
        item.unit = update.unit
        // 可以根据数值范围设置状态
        item.status = 'good' // 简化处理，实际可以根据阈值判断
      }
    })
  }

  // 图表时间范围
  const pm25TimeRange = ref('day')
  const tempHumidTimeRange = ref('day')

  // 趋势数据
  const trendData = ref(null)

  // 提供数据给子组件
  provide('airQualityData', airQualityData)
  provide('trendData', trendData)

  // 获取趋势数据
  const fetchTrendData = async () => {
    try {
      const params = {
        projectId: selectedProjectId.value,
        timeType: getTimeTypeFromRange(tempHumidTimeRange.value) // 使用温度湿度的时间范围
      }

      // 如果选择了具体设备，添加设备ID参数
      if (selectedPoint.value && selectedPoint.value !== 'average') {
        params.equipmentId = selectedPoint.value
      }

      const response = await getTemperatureAndTemperatureTrend(params)

      if (response.code === 200 && response.data) {
        trendData.value = response.data
        console.log('趋势数据获取成功:', response.data)
      } else {
        ElMessage.error(response.msg || '获取趋势数据失败')
      }
    } catch (error) {
      console.error('获取趋势数据失败:', error)
      ElMessage.error('获取趋势数据失败，请稍后重试')
    }
  }

  // 将时间范围转换为API需要的timeType
  const getTimeTypeFromRange = (range) => {
    switch (range) {
      case 'day':
        return 0
      case 'week':
        return 1
      case 'month':
        return 2
      default:
        return 0
    }
  }

  // 监听时间范围变化，重新获取趋势数据
  watch([tempHumidTimeRange, pm25TimeRange], () => {
    if (selectedProjectId.value) {
      fetchTrendData()
    }
  })

  // 分页
  const currentPage = ref(1)
  const pageSize = ref(10)
  const totalItems = ref(0)

  // 历史数据
  const historyData = ref([])
  const historyLoading = ref(false)

  // 获取今天的日期字符串（YYYY-MM-DD格式）
  const getTodayDateString = () => {
    const today = new Date()
    const year = today.getFullYear()
    const month = String(today.getMonth() + 1).padStart(2, '0')
    const day = String(today.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  // 获取历史数据
  const fetchHistoryData = async (useAutoParams = false) => {
    try {
      historyLoading.value = true

      // 基础参数配置
      const params = {
        projectId: useAutoParams ? 17 : selectedProjectId.value, // 自动查询时使用固定项目ID
        equipmentId: useAutoParams
          ? 1
          : selectedPoint.value && selectedPoint.value !== 'average'
            ? selectedPoint.value
            : undefined, // 自动查询时使用固定设备ID
        pageSize: pageSize.value,
        pageNum: currentPage.value
      }

      // 如果是自动查询或没有选择日期范围，使用当天日期
      if (useAutoParams || !dateRange.value || dateRange.value.length !== 2) {
        const todayDate = getTodayDateString()
        params.beginTime = todayDate
        params.endTime = todayDate
      } else {
        // 使用用户选择的日期范围
        params.beginTime = dateRange.value[0]
        params.endTime = dateRange.value[1]
      }

      console.log('历史数据查询参数:', params)
      const response = await getHistoryList(params)

      if (response.code === 200) {
        // 转换数据格式以适应表格显示
        historyData.value = response.rows.map((item, index) => ({
          id: item.id,
          序号: (currentPage.value - 1) * pageSize.value + index + 1,
          监测时间: item.createTime || formatMonitorTime(item.monitorTimestamp) || '',
          空气温度: item.kqwd?.toString() || '0',
          空气湿度: item.kqsd?.toString() || '0',
          风向: item.fx?.toString() || '0',
          风速: item.fs?.toString() || '0',
          噪声: item.zs?.toString() || '0',
          总辐射: item.zfs?.toString() || '0',
          PM25: item.pm25?.toString() || '0',
          PM10: item.pm10?.toString() || '0',
          降水: item.js?.toString() || '0'
        }))
        totalItems.value = response.total
        console.log('历史数据获取成功:', response)
      } else {
        ElMessage.error(response.msg || '获取历史数据失败')
        historyData.value = []
        totalItems.value = 0
      }
    } catch (error) {
      console.error('获取历史数据失败:', error)
      ElMessage.error('获取历史数据失败，请稍后重试')
      historyData.value = []
      totalItems.value = 0
    } finally {
      historyLoading.value = false
    }
  }

  // 格式化监测时间戳
  const formatMonitorTime = (monitorTimestamp) => {
    if (!monitorTimestamp) return ''
    const timeStr = monitorTimestamp.toString()
    if (timeStr.length === 14) {
      // 格式：20250620091932 -> 2025-06-20 09:19:32
      return `${timeStr.substring(0, 4)}-${timeStr.substring(4, 6)}-${timeStr.substring(6, 8)} ${timeStr.substring(8, 10)}:${timeStr.substring(10, 12)}:${timeStr.substring(12, 14)}`
    }
    return timeStr
  }

  // 查询数据
  const searchData = () => {
    // 如果查询按钮已禁用，不执行查询
    if (historyQueryButtonDisabled.value) {
      ElMessage.info('查询条件未变化，无需重复查询')
      return
    }

    // 获取当前查询参数
    const currentParams = {
      projectId: selectedProjectId.value,
      selectedPoint: selectedPoint.value,
      beginTime: dateRange.value && dateRange.value.length === 2 ? dateRange.value[0] : undefined,
      endTime: dateRange.value && dateRange.value.length === 2 ? dateRange.value[1] : undefined,
      pageSize: pageSize.value,
      pageNum: 1 // 查询时重置为第一页
    }

    // 检查查询参数是否与上次相同
    if (isSameHistoryQueryParams(currentParams, lastHistoryQueryParams.value)) {
      ElMessage.info('查询条件未变化')
      historyQueryButtonDisabled.value = true
      return
    }

    console.log('查询日期范围：', dateRange.value)

    // 保存当前查询参数
    lastHistoryQueryParams.value = { ...currentParams }

    // 禁用查询按钮
    historyQueryButtonDisabled.value = true

    // 重置到第一页并重新获取数据
    currentPage.value = 1
    fetchHistoryData()
  }

  // 分页大小变化
  const handleSizeChange = (size) => {
    pageSize.value = size
    currentPage.value = 1
    fetchHistoryData()
  }

  // 页码变化
  const handleCurrentChange = (page) => {
    currentPage.value = page
    fetchHistoryData()
  }

  // 导出数据
  const exportData = async () => {
    // 检查是否选择了项目
    if (!selectedProjectId.value) {
      ElMessage.warning('请先选择项目')
      return
    }

    try {
      // 准备导出参数
      const exportParams = {
        projectId: selectedProjectId.value
      }

      // 如果选择了具体设备，添加设备ID参数
      if (selectedPoint.value && selectedPoint.value !== 'average') {
        exportParams.equipmentId = selectedPoint.value
      }

      // 如果有选择日期范围，添加时间参数
      if (dateRange.value && dateRange.value.length === 2) {
        const [startDate, endDate] = dateRange.value
        exportParams.beginTime = startDate
        exportParams.endTime = endDate
      }

      ElMessage.info('正在导出数据，请稍候...')

      // 调用导出API
      const blob = await exportOutdoorData(exportParams)

      // 创建下载链接
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')

      // 设置文件名称
      const fileName = `室外环境监测数据_${new Date().toISOString().split('T')[0]}.xlsx`

      // 下载文件
      link.href = url
      link.setAttribute('download', fileName)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // 清理URL对象
      URL.revokeObjectURL(url)

      ElMessage.success('数据导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('导出失败，请稍后重试')
    }
  }

  // 组件挂载时触发初始化自动查询
  onMounted(() => {
    console.log('室外环境监测页面已挂载，触发初始化自动查询')
    // 页面加载时立即触发自动查询
    autoSearch()
  })
</script>

<style lang="scss" scoped>
  .air-quality-container {
    padding: 20px;

    .page-header {
      margin-bottom: 20px;

      h1 {
        margin: 0;
        font-size: 24px;
        color: var(--art-text-gray-900); // 修改标题颜色为主题变量
      }
    }

    .content-section {
      margin-bottom: 20px;

      .chart-row {
        margin-top: 20px;
        margin-bottom: 20px;
      }

      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .filter-controls {
        display: flex;
        gap: 10px;
        align-items: center;
      }

      .data-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;

        .data-item {
          flex: 1;
          min-width: 140px;
          padding: 15px;
          text-align: center;
          background-color: var(--art-gray-100); // 修改背景色为主题变量
          border-radius: 8px;

          .item-value {
            margin-bottom: 8px;
            font-size: 24px;
            font-weight: bold;

            &.good {
              color: rgb(var(--art-success)); // 修改颜色为主题变量
            }

            &.normal {
              color: rgb(var(--art-warning)); // 修改颜色为主题变量
            }

            &.bad {
              color: rgb(var(--art-danger)); // 修改颜色为主题变量
            }
          }

          .item-name {
            font-size: 14px;
            color: var(--art-text-gray-600); // 修改文字颜色为主题变量
          }
        }
      }

      .chart-container {
        height: 300px;
      }

      .pagination-container {
        display: flex;
        justify-content: center;
        margin-top: 20px;
      }

      .chart-card,
      .history-card,
      .overview-card {
        margin-bottom: 0;
      }
    }
  }
</style>
