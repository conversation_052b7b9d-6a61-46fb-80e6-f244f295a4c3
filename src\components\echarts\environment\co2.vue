<template>
  <div class="chart-container" ref="chartContainer"></div>
</template>

<script setup>
  import { ref, onMounted, watch, nextTick } from 'vue'
  import * as echarts from 'echarts'

  const props = defineProps({
    timeRange: {
      type: String,
      default: 'day'
    },
    chartData: {
      type: Object,
      default: () => ({
        xAxisData: [],
        co2Data: [],
        illuminanceData: [],
        noiseData: []
      })
    },
    // 指定要显示的指标，可选值有 'co2', 'illuminance', 'noise'
    indicators: {
      type: Array,
      default: () => ['co2']
    }
  })

  const chartContainer = ref(null)
  let chart = null

  // 指标配置
  const indicatorConfig = {
    co2: {
      name: '二氧化碳',
      unit: 'ppm',
      color: '#409EFF',
      yAxisIndex: 1 // 使用右侧Y轴
    },
    formaldehyde: {
      name: '甲醛',
      unit: 'μg/m³',
      color: '#909399',
      yAxisIndex: 0 // 使用左侧Y轴
    },
    tvoc: {
      name: 'TVOC',
      unit: 'μg/m³',
      color: '#67C23A',
      yAxisIndex: 0 // 使用左侧Y轴
    },
    illuminance: {
      name: '照度',
      unit: 'lux',
      color: '#E6A23C'
    },
    noise: {
      name: '噪声',
      unit: 'dB',
      color: '#909399'
    }
  }

  // 根据颜色字符串获取rgba颜色
  const getColorWithOpacity = (color, opacity) => {
    // 简单的颜色映射
    const colorMap = {
      '#409EFF': `rgba(64, 158, 255, ${opacity})`,
      '#E6A23C': `rgba(230, 162, 60, ${opacity})`,
      '#909399': `rgba(144, 147, 153, ${opacity})`
    }

    return colorMap[color] || `rgba(64, 158, 255, ${opacity})`
  }

  // 初始化图表
  const initChart = () => {
    if (!chartContainer.value) return

    // 如果图表已经存在，销毁它以避免重复创建
    if (chart) {
      chart.dispose()
    }

    // 获取要显示的指标
    const indicators = props.indicators || ['co2']
    const xAxisData = props.chartData.xAxisData || []

    // 创建新的图表实例
    chart = echarts.init(chartContainer.value)

    // 准备图例数据和系列数据
    const legendData = []
    const series = []

    // 准备Y轴配置
    indicators.some((ind) => indicatorConfig[ind]?.yAxisIndex === 1)

    // 准备Y轴配置
    let yAxis = []

    // 如果有指标使用左侧Y轴（甲醛/TVOC）
    if (indicators.some((ind) => indicatorConfig[ind]?.yAxisIndex === 0)) {
      yAxis.push({
        type: 'value',
        name: '甲醛/TVOC(μg/m³)',
        nameTextStyle: {
          padding: [0, 30, 0, 0],
          color: '#666',
          fontWeight: 'bold'
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#67C23A'
          }
        },
        axisLabel: {
          formatter: '{value}',
          color: '#666'
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(224, 230, 241, 0.8)'
          }
        }
      })
    }

    // 如果有指标使用右侧Y轴（CO2）
    if (indicators.some((ind) => indicatorConfig[ind]?.yAxisIndex === 1)) {
      yAxis.push({
        type: 'value',
        name: '二氧化碳(ppm)',
        nameTextStyle: {
          padding: [0, 0, 0, 30],
          color: '#666',
          fontWeight: 'bold'
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#409EFF'
          }
        },
        axisLabel: {
          formatter: '{value}',
          color: '#666'
        },
        splitLine: {
          show: false
        },
        position: 'right'
      })
    }

    // 如果没有配置Y轴，使用默认配置
    if (yAxis.length === 0) {
      const defaultIndicator = indicators[0] || 'co2'
      const config = indicatorConfig[defaultIndicator]

      yAxis = {
        type: 'value',
        name: `${config.name}(${config.unit})`,
        nameTextStyle: {
          padding: [0, 30, 0, 0],
          color: '#666',
          fontWeight: 'bold'
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: config.color
          }
        },
        axisLabel: {
          formatter: '{value}',
          color: '#666'
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(224, 230, 241, 0.8)'
          }
        }
      }
    }

    // 为每个指标创建系列
    indicators.forEach((indicator) => {
      const config = indicatorConfig[indicator]
      if (!config) return

      const data = props.chartData[`${indicator}Data`] || []
      legendData.push(config.name)

      series.push({
        name: config.name,
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        showSymbol: false,
        yAxisIndex: config.yAxisIndex || 0,
        data: data,
        itemStyle: {
          color: config.color
        },
        lineStyle: {
          width: 2
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: getColorWithOpacity(config.color, 0.3)
            },
            {
              offset: 1,
              color: getColorWithOpacity(config.color, 0.1)
            }
          ])
        }
      })
    })

    // 设置图表选项
    const option = {
      tooltip: {
        trigger: 'axis',
        formatter: function (params) {
          let result = params[0].axisValue + '<br/>'
          params.forEach((param) => {
            const indicator = indicators.find(
              (ind) => indicatorConfig[ind]?.name === param.seriesName
            )
            const config = indicatorConfig[indicator]

            let marker = `<span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${param.color};"></span>`
            let value = parseFloat(param.value).toFixed(2) + (config ? config.unit : '')
            result += marker + param.seriesName + ': ' + value + '<br/>'
          })
          return result
        }
      },
      legend: {
        data: legendData,
        bottom: 0
      },
      grid: {
        left: '5%',
        right: '5%',
        bottom: '15%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: xAxisData,
        axisLine: {
          lineStyle: {
            color: '#999'
          }
        },
        axisLabel: {
          color: '#666'
        }
      },
      yAxis: yAxis,
      series: series
    }

    // 应用选项
    chart.setOption(option)

    // 添加响应式调整
    window.addEventListener('resize', () => {
      if (chart) {
        chart.resize()
      }
    })
  }

  // 更新图表
  const updateChart = () => {
    if (!chart) {
      initChart()
      return
    }

    const indicators = props.indicators || ['co2']
    const seriesData = []

    // 为每个指标准备数据更新
    indicators.forEach((indicator) => {
      if (!indicatorConfig[indicator]) return

      const data = props.chartData[`${indicator}Data`] || []
      seriesData.push({
        data: data
      })
    })

    // 更新图表数据
    chart.setOption({
      xAxis: {
        data: props.chartData.xAxisData || []
      },
      series: seriesData
    })
  }

  // 监听图表数据变化
  watch(
    () => props.chartData,
    () => {
      nextTick(() => {
        updateChart()
      })
    },
    { deep: true }
  )

  // 监听时间范围变化
  watch(
    () => props.timeRange,
    () => {
      nextTick(() => {
        updateChart()
      })
    }
  )

  // 监听指标变化
  watch(
    () => props.indicators,
    () => {
      nextTick(() => {
        initChart() // 指标变化需要重新初始化
      })
    },
    { deep: true }
  )

  // 组件挂载时初始化图表
  onMounted(() => {
    initChart()
  })
</script>

<style scoped lang="scss">
  .chart-container {
    width: 100%;
    height: 350px;
  }
</style>
