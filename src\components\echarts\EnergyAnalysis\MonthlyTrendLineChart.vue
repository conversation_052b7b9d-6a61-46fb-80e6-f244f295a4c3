<template>
  <div ref="chartRef" :style="{ height: height, width: width }" class="chart-container"></div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted, watch, nextTick, computed } from 'vue'
  import * as echarts from 'echarts/core'
  import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'echarts/charts'
  import {
    TitleComponent,
    TooltipComponent,
    GridComponent,
    LegendComponent,
    MarkLineComponent
  } from 'echarts/components'
  import { CanvasRenderer } from 'echarts/renderers'
  import { ElMessage } from 'element-plus'
  import { useSettingStore } from '@/store/modules/setting'
  import { SystemThemeEnum } from '@/enums/appEnum'

  echarts.use([
    TitleComponent,
    TooltipComponent,
    GridComponent,
    LegendComponent,
    LineChart,
    BarChart,
    MarkLineComponent,
    CanvasRenderer
  ])

  const props = defineProps({
    height: {
      type: String,
      default: '400px'
    },
    width: {
      type: String,
      default: '100%'
    },
    xAxisData: {
      type: Array,
      required: true,
      default: () => [
        '1月',
        '2月',
        '3月',
        '4月',
        '5月',
        '6月',
        '7月',
        '8月',
        '9月',
        '10月',
        '11月',
        '12月'
      ]
    },
    seriesData: {
      type: Array,
      required: true,
      default: () => [
        {
          name: '电力能耗',
          type: 'line',
          yAxisIndex: 0,
          data: [25, 30, 28, 36, 32, 40, 35, 38, 42, 36, 38, 36],
          unit: 'mg/l',
          color: '#6236FF'
        },
        {
          name: '水资源消耗',
          type: 'line',
          yAxisIndex: 0,
          data: [18, 20, 22, 24, 26, 24, 25, 26, 28, 24, 25, 22],
          unit: 'mg/l',
          color: '#4F7CFF'
        },
        {
          name: '燃气能耗',
          type: 'line',
          yAxisIndex: 0,
          data: [10, 12, 15, 18, 20, 22, 24, 22, 20, 18, 16, 14],
          unit: 'mg/l',
          color: '#00C292'
        }
      ]
    },
    warningLines: {
      type: Array,
      default: () => [{ name: '警戒线', value: 40, yAxisIndex: 0 }]
    }
  })

  const chartRef = ref(null)
  let chartInstance = null

  // 获取主题状态
  const settingStore = useSettingStore()
  const isDark = computed(() => settingStore.systemThemeType === SystemThemeEnum.DARK)

  const initChart = () => {
    if (!chartRef.value) {
      console.warn('图表容器未找到')
      return
    }

    try {
      // 先确保容器显示
      chartRef.value.style.width = props.width
      chartRef.value.style.height = props.height

      // 如果已存在实例，先销毁
      if (chartInstance) {
        chartInstance.dispose()
      }

      // 重新创建实例
      chartInstance = echarts.init(chartRef.value)
      console.log('图表容器尺寸:', chartRef.value.clientWidth, chartRef.value.clientHeight)

      // 显示加载状态
      chartInstance.showLoading()

      // 设置配置
      setOptions()

      // 隐藏加载状态
      chartInstance.hideLoading()

      console.log('多折线图初始化完成')
    } catch (error) {
      console.error('初始化图表失败:', error)
      ElMessage.error('图表初始化失败')
    }
  }

  const setOptions = () => {
    if (!chartInstance) return

    // 创建完整数据
    let series = []

    // 处理传入的系列数据
    if (props.seriesData && props.seriesData.length > 0) {
      series = props.seriesData.map((item, index) => {
        const baseColor = item.color || getDefaultColor(index)

        return {
          name: item.name,
          type: item.type || 'line',
          yAxisIndex: item.yAxisIndex || 0,
          data: item.data || [],
          unit: item.unit || '',
          smooth: true,
          showSymbol: true,
          symbolSize: 6,
          // 确保所有需要的样式设置都存在
          itemStyle: {
            color: baseColor
          },
          lineStyle: {
            width: 2,
            color: baseColor
          },
          emphasis: {
            focus: 'series'
          },
          // 区域着色
          areaStyle:
            item.areaStyle !== false
              ? {
                  opacity: 0.1,
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: baseColor },
                    { offset: 1, color: 'rgba(255,255,255,0)' }
                  ])
                }
              : undefined
        }
      })
    } else {
      // 如果没有数据，使用默认数据
      console.warn('没有提供系列数据，使用默认数据')
      series = [
        {
          name: '示例数据',
          type: 'line',
          data: [10, 15, 20, 25, 30, 35, 40, 45, 40, 35, 30, 25],
          smooth: true,
          showSymbol: true,
          symbolSize: 6,
          itemStyle: { color: '#6236FF' },
          lineStyle: { width: 2, color: '#6236FF' },
          emphasis: { focus: 'series' },
          areaStyle: {
            opacity: 0.1,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#6236FF' },
              { offset: 1, color: 'rgba(255,255,255,0)' }
            ])
          }
        }
      ]
    }

    // 添加警戒线
    props.warningLines.forEach((line) => {
      const index = line.yAxisIndex || 0
      if (series[index]) {
        if (!series[index].markLine) {
          series[index].markLine = {
            silent: true,
            lineStyle: {
              color: '#FF4757',
              type: 'dashed',
              width: 1
            },
            label: {
              show: true,
              position: 'end',
              formatter: `{c} ${series[index].unit || ''}`
            },
            data: []
          }
        }
        series[index].markLine.data.push([
          {
            name: line.name,
            yAxis: line.value,
            label: {
              formatter: line.name + ': {c}'
            }
          },
          {
            yAxis: line.value
          }
        ])
      } else if (series.length > 0) {
        // 如果没有对应索引的系列，添加到第一个系列
        if (!series[0].markLine) {
          series[0].markLine = {
            silent: true,
            lineStyle: {
              color: '#FF4757',
              type: 'dashed',
              width: 1
            },
            label: {
              show: true,
              position: 'end',
              formatter: `{c} ${series[0].unit || ''}`
            },
            data: []
          }
        }
        series[0].markLine.data.push([
          {
            name: line.name,
            yAxis: line.value,
            label: {
              formatter: line.name + ': {c}'
            }
          },
          {
            yAxis: line.value
          }
        ])
      }
    })

    // 设置图表配置
    const option = {
      title: {
        text: '标煤 kgce',
        left: '10px',
        top: '0px',
        textStyle: {
          fontSize: 14,
          fontWeight: 'normal',
          color: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#333'
        },
        padding: [10, 0, 0, 10]
      },
      color: series.map((item) => item.itemStyle?.color || '#6236FF'),
      tooltip: {
        trigger: 'axis',
        backgroundColor: isDark.value ? 'rgba(50, 50, 50, 0.9)' : 'rgba(255, 255, 255, 0.9)',
        borderColor: isDark.value ? '#555' : '#eee',
        borderWidth: 1,
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: isDark.value ? 'rgba(255, 255, 255, 0.5)' : '#999'
          }
        },
        formatter: function (params) {
          const textColor = isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#666'
          const titleColor = isDark.value ? 'rgba(255, 255, 255, 0.95)' : '#333'

          let tooltipHtml = `<div style="font-size: 14px; color: ${textColor}; line-height: 22px;">`
          tooltipHtml += `<div style="font-weight: bold; color: ${titleColor}; margin-bottom: 8px;">${params[0].axisValueLabel}</div>`

          params.forEach((param) => {
            const seriesItem = props.seriesData.find((s) => s.name === param.seriesName) || {}
            const unit = seriesItem.unit || ''
            tooltipHtml += `
            <div style="display: flex; align-items: center; margin-bottom: 4px;">
              ${param.marker}
              <span style="flex: 1; margin-left: 8px;">${param.seriesName}</span>
              <span style="font-weight: bold; margin-left: 16px;">${
                param.value !== null && param.value !== undefined ? param.value + ' ' + unit : '-'
              }</span>
            </div>
          `
          })

          tooltipHtml += `</div>`
          return tooltipHtml
        }
      },
      legend: {
        data: series.map((s) => s.name),
        bottom: 10,
        itemWidth: 14,
        itemHeight: 8,
        textStyle: {
          fontSize: 12,
          color: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#333'
        }
      },
      grid: {
        left: '2%',
        right: '3%',
        bottom: '15%',
        top: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: props.xAxisData || [
          '1月',
          '2月',
          '3月',
          '4月',
          '5月',
          '6月',
          '7月',
          '8月',
          '9月',
          '10月',
          '11月',
          '12月'
        ],
        boundaryGap: true,
        axisLine: {
          lineStyle: {
            color: isDark.value ? 'rgba(255, 255, 255, 0.2)' : '#E0E6F1'
          }
        },
        axisTick: {
          alignWithLabel: true,
          lineStyle: {
            color: isDark.value ? 'rgba(255, 255, 255, 0.2)' : '#E0E6F1'
          }
        },
        axisPointer: {
          type: 'shadow'
        },
        axisLabel: {
          fontSize: 12,
          color: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#333',
          interval: 0,
          rotate: props.xAxisData && props.xAxisData.length > 12 ? 45 : 0
        }
      },
      yAxis: {
        type: 'value',
        name: '',
        nameGap: 25,
        min: 0,
        axisLine: {
          show: true,
          lineStyle: {
            color: isDark.value ? 'rgba(255, 255, 255, 0.2)' : '#E0E6F1'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          formatter: '{value}',
          fontSize: 12,
          color: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#333'
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            color: isDark.value ? 'rgba(255, 255, 255, 0.1)' : '#E0E6F1'
          }
        }
      },
      series: series
    }

    // 应用配置并显示图表
    chartInstance.setOption(option, true)
  }

  // 获取默认颜色
  const getDefaultColor = (index) => {
    const colors = ['#6236FF', '#4F7CFF', '#00C292', '#FF9F43', '#FF4757', '#7367F0']
    return colors[index % colors.length]
  }

  const resizeChart = () => {
    if (chartInstance && chartRef.value) {
      chartInstance.resize()
    }
  }

  // 监听数据变化
  watch(
    () => [props.xAxisData, props.seriesData, props.warningLines],
    () => {
      nextTick(() => {
        if (chartRef.value) {
          initChart()
        }
      })
    },
    { deep: true }
  )

  // 监听主题变化，更新图表
  watch(
    () => isDark.value,
    () => {
      nextTick(() => {
        if (chartRef.value && chartInstance) {
          setOptions()
        }
      })
    }
  )

  // 监听容器尺寸变化
  watch(
    () => [props.width, props.height],
    () => {
      nextTick(() => {
        if (chartRef.value) {
          chartRef.value.style.width = props.width
          chartRef.value.style.height = props.height
          resizeChart()
        }
      })
    }
  )

  onMounted(() => {
    // 确保DOM已完全渲染
    nextTick(() => {
      setTimeout(() => {
        initChart()
      }, 300)
    })

    window.addEventListener('resize', resizeChart)
  })

  onUnmounted(() => {
    if (chartInstance) {
      chartInstance.dispose()
    }
    window.removeEventListener('resize', resizeChart)
    console.log('图表实例已清理')
  })

  // 暴露方法
  defineExpose({
    resizeChart,
    initChart
  })
</script>

<style lang="scss" scoped>
  .chart-container {
    position: relative;
    min-height: 300px;
  }
</style>
