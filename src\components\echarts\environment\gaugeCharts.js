/**
 * 环境监测页面仪表盘配置
 */
import * as echarts from 'echarts'

// 创建基础仪表盘配置
export const createGaugeOption = (value, name) => {
  return {
    tooltip: {
      formatter: '{a} <br/>{b} : {c}%'
    },
    series: [
      {
        name: '环境指标',
        type: 'gauge',
        radius: '80%',
        center: ['50%', '50%'],
        detail: {
          formatter: '{value}',
          fontSize: 20
        },
        title: {
          fontSize: 16
        },
        data: [
          {
            value: value,
            name: name
          }
        ]
      }
    ]
  }
}

// 舒适度监测仪表盘配置
export const createComfortGaugeOption = (value, name) => {
  return createGaugeOption(value, name)
}

// 有害气体监测仪表盘配置
export const createGasGaugeOption = (value, name) => {
  return createGaugeOption(value, name)
}

// 颗粒物监测仪表盘配置
export const createParticleGaugeOption = (value, name) => {
  return createGaugeOption(value, name)
}

// 噪声监测仪表盘配置
export const createNoiseGaugeOption = (value, name) => {
  return createGaugeOption(value, name)
}

// 水质监测仪表盘配置
export const createWaterGaugeOption = (value, name) => {
  return createGaugeOption(value, name)
}

// 室外环境监测仪表盘配置
export const createOutdoorGaugeOption = (value, name) => {
  return createGaugeOption(value, name)
}

// 初始化图表并添加自适应调整
export const initGaugeChart = (element, option) => {
  if (!element) return null

  // 先销毁已有的实例防止重复初始化
  const existingInstance = echarts.getInstanceByDom(element)
  if (existingInstance) {
    existingInstance.dispose()
  }

  console.log('初始化图表容器尺寸:', element.offsetWidth, 'x', element.offsetHeight)

  // 创建新实例
  const chart = echarts.init(element)
  chart.setOption(option)

  // 添加窗口大小变化时的自适应
  window.addEventListener('resize', () => {
    chart.resize()
  })

  return chart
}
