<template>
  <div class="air-quality-container page-content">
    <div class="content-section">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card class="overview-card">
            <template #header>
              <div class="card-header">
                <span>空气质量概览</span>
                <div class="filter-controls">
                  <ProjectSelector
                    ref="projectSelectorRef"
                    v-model="selectedProjectId"
                    placeholder="请选择项目"
                    style="width: 250px"
                    @change="handleProjectChange"
                    @loaded="handleProjectListLoaded"
                  />
                  <el-select
                    v-model="selectedPoint"
                    placeholder="选择点位"
                    style="width: 200px"
                    :disabled="!selectedProjectId"
                    :loading="loading"
                    @change="handlePointChange"
                  >
                    <el-option
                      v-for="point in pointOptions"
                      :key="point.value"
                      :label="point.label"
                      :value="point.value"
                    ></el-option>
                  </el-select>
                </div>
              </div>
            </template>

            <div class="data-grid">
              <div class="data-item" v-for="(item, index) in airQualityData" :key="index">
                <div class="item-value" :class="item.status"> {{ item.value }}{{ item.unit }} </div>
                <div class="item-name">{{ item.name }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 温湿度趋势图 -->
      <el-row :gutter="20" class="chart-row">
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>温湿度趋势</span>
                <el-radio-group v-model="tempHumidTimeRange" size="small">
                  <el-radio-button label="day">日</el-radio-button>
                  <el-radio-button label="week">周</el-radio-button>
                  <el-radio-button label="month">月</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <TemperatureHumidityChart
              ref="tempHumidChartRef"
              :time-range="tempHumidTimeRange"
              :chart-data="tempHumidChartData"
            />
          </el-card>
        </el-col>

        <!-- 污染物趋势图 -->
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>颗粒物趋势</span>
                <el-radio-group v-model="pollutantTimeRange" size="small">
                  <el-radio-button label="day">日</el-radio-button>
                  <el-radio-button label="week">周</el-radio-button>
                  <el-radio-button label="month">月</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <PollutantChart
              ref="pollutantChartRef"
              :time-range="pollutantTimeRange"
              :chart-data="pollutantChartData"
            />
          </el-card>
        </el-col>
      </el-row>

      <!-- 二氧化碳/甲醛和照度/噪声趋势图 -->
      <el-row :gutter="20" class="chart-row">
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>有害气体</span>
                <el-radio-group v-model="co2FormaldehydeTimeRange" size="small">
                  <el-radio-button label="day">日</el-radio-button>
                  <el-radio-button label="week">周</el-radio-button>
                  <el-radio-button label="month">月</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <co2
              ref="co2FormaldehydeChartRef"
              :time-range="co2FormaldehydeTimeRange"
              :chart-data="co2FormaldehydeChartData"
              :indicators="['co2', 'formaldehyde', 'tvoc']"
            />
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>照度/噪声趋势</span>
                <el-radio-group v-model="illuminanceNoiseTimeRange" size="small">
                  <el-radio-button label="day">日</el-radio-button>
                  <el-radio-button label="week">周</el-radio-button>
                  <el-radio-button label="month">月</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <OtherIndexChart
              ref="illuminanceNoiseChartRef"
              :time-range="illuminanceNoiseTimeRange"
              :chart-data="illuminanceNoiseChartData"
              :indicators="['illuminance', 'noise']"
            />
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-card class="history-card">
            <template #header>
              <div class="card-header">
                <span>历史监测数据</span>
                <div>
                  <el-date-picker
                    v-model="dateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    format="YYYY/MM/DD"
                    value-format="YYYY-MM-DD"
                    style="width: 320px"
                  ></el-date-picker>
                  <el-button
                    type="primary"
                    size="small"
                    style="margin-left: 10px"
                    @click="searchData"
                    :disabled="historyQueryButtonDisabled"
                    >查询</el-button
                  >
                  <!-- 导出 -->
                  <el-button type="primary" size="small" @click="exportData">导出</el-button>
                </div>
              </div>
            </template>

            <el-table :data="historyData" border style="width: 100%">
              <!-- 序号 -->
              <el-table-column label="序号" align="center" width="80">
                <template #default="scope">
                  {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
                </template>
              </el-table-column>
              <el-table-column
                prop="date"
                label="监测时间"
                align="center"
                width="200"
              ></el-table-column>
              <el-table-column prop="temperature" label="温度(°C)" align="center"></el-table-column>
              <el-table-column prop="humidity" label="湿度(%RH)" align="center"></el-table-column>
              <el-table-column prop="pm25" label="PM2.5(μg/m³)" align="center"></el-table-column>
              <el-table-column prop="pm10" label="PM10(μg/m³)" align="center"></el-table-column>
              <el-table-column prop="tvoc" label="TVOC(μg/m³)" align="center"></el-table-column>
              <el-table-column prop="hcho" label="甲醛(μg/m³)" align="center"></el-table-column>
              <el-table-column prop="co2" label="二氧化碳(ppm)" align="center"></el-table-column>
              <el-table-column
                prop="illuminance"
                label="照度(lux)"
                align="center"
              ></el-table-column>
              <el-table-column prop="noise" label="噪声(dB)" align="center"></el-table-column>
            </el-table>

            <div class="pagination-container">
              <el-pagination
                background
                layout="total, sizes, prev, pager, next, jumper"
                :total="totalItems"
                :page-sizes="[10, 20, 50, 100]"
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              ></el-pagination>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted, reactive, computed, watch, nextTick } from 'vue'
  import TemperatureHumidityChart from '@/components/echarts/environment/TemperatureHumidityChart.vue'
  import PollutantChart from '@/components/echarts/environment/PollutantChart.vue'
  import OtherIndexChart from '@/components/echarts/environment/OtherIndexChart.vue'
  import co2 from '@/components/echarts/environment/co2.vue'

  import { ElMessage } from 'element-plus'
  import ProjectSelector from '@/components/ProjectSelector.vue'
  import {
    getEquipmentList,
    getIndoorAirQualityMonitoring,
    getIndoorTemperatureAndTemperatureTrend,
    getIndoorHistoryList,
    exportIndoorData
  } from '@/api/environment-monitoring/indoorApi'

  // 项目选择相关数据
  const selectedProjectId = ref(null) // 初始为空，等待获取项目列表后设置第一个
  const selectedProjectInfo = ref(null)
  const projectSelectorRef = ref()

  // --- 项目与点位选择 ---
  const selectedPoint = ref(null) // 当前选中的点位值
  const equipmentList = ref([]) // 设备列表
  const loading = ref(false) // 加载状态

  // 查询状态管理 - 历史数据查询
  const lastHistoryQueryParams = ref(null)
  const historyQueryButtonDisabled = ref(false)

  // 比较历史数据查询参数是否相同
  const isSameHistoryQueryParams = (params1, params2) => {
    if (!params1 || !params2) return false
    return (
      params1.projectId === params2.projectId &&
      params1.selectedPoint === params2.selectedPoint &&
      params1.beginTime === params2.beginTime &&
      params1.endTime === params2.endTime &&
      params1.pageSize === params2.pageSize &&
      params1.pageNum === params2.pageNum
    )
  }

  // 计算当前选中项目的点位选项
  const pointOptions = computed(() => {
    if (!equipmentList.value || equipmentList.value.length === 0) {
      return []
    }
    // 第一个选项是平均值
    const points = [{ label: '平均值', value: 'average' }]
    // 添加具体设备
    equipmentList.value.forEach((equipment) => {
      points.push({
        label: equipment.name,
        value: equipment.id
      })
    })
    return points
  })

  // 获取设备列表
  const fetchEquipmentList = async (projectId) => {
    try {
      loading.value = true
      const params = {
        projectId: projectId,
        buildingId: '', // 可选参数
        floorId: '', // 可选参数
        equipmentCategory: 1, // 环境监测设备
        equipmentCategorytype: 1 // 室内七合一
      }

      const response = await getEquipmentList(params)

      if (response.code === 200 && response.data) {
        equipmentList.value = response.data || []
        console.log('设备列表获取成功:', equipmentList.value)
      } else {
        ElMessage.error(response.msg || '获取设备列表失败')
        equipmentList.value = []
      }
    } catch (error) {
      console.error('获取设备列表失败:', error)
      ElMessage.error('获取设备列表失败，请稍后重试')
      equipmentList.value = []
    } finally {
      loading.value = false
    }
  }

  // 处理项目变化
  const handleProjectChange = (projectInfo) => {
    selectedProjectInfo.value = projectInfo
    selectedPoint.value = null // 项目变化时清空点位选择
    console.log('选中的项目:', projectInfo)
    // 获取该项目的设备列表
    if (projectInfo && projectInfo.id) {
      fetchEquipmentList(projectInfo.id)
    }
    // 项目变化时自动发送请求（包含所有接口）
    nextTick(() => {
      autoSearch()
    })
  }

  // 处理点位变化
  const handlePointChange = (pointValue) => {
    console.log('选中的点位:', pointValue)
    // 点位变化时自动发送请求（包含所有接口）
    autoSearch()
  }

  // 监听项目选择器组件的项目列表加载完成
  const handleProjectListLoaded = (projectList) => {
    // 当项目列表加载完成后，自动选择第一个项目并发请求
    if (projectList && projectList.length > 0) {
      const firstProject = projectList[0]
      selectedProjectId.value = firstProject.id
      selectedProjectInfo.value = firstProject
      console.log('自动选择第一个项目:', firstProject)
      // 获取设备列表
      fetchEquipmentList(firstProject.id)
      // 自动发请求获取数据（包含所有接口）
      nextTick(() => {
        autoSearch()
      })
    }
  }

  // 获取今天的日期字符串（YYYY-MM-DD格式）
  const getTodayDateString = () => {
    const today = new Date()
    const year = today.getFullYear()
    const month = String(today.getMonth() + 1).padStart(2, '0')
    const day = String(today.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  // 自动查询方法（包含所有接口）
  const autoSearch = async () => {
    // 自动查询时不需要检查项目选择，使用固定参数
    console.log('室内空气质量自动查询触发，使用固定参数:', {
      projectId: 17,
      equipmentId: 1,
      beginTime: getTodayDateString(),
      endTime: getTodayDateString()
    })

    // 获取实时空气质量数据（如果有选择项目的话）
    if (selectedProjectId.value) {
      await fetchAirQualityData()
      // 获取趋势数据（默认使用"日"时间粒度）
      await fetchTrendData()
    }

    // 获取历史数据（使用自动参数）
    await fetchHistoryData(true)
  }

  // 监听项目选择变化，确保点位列表更新后重置点位选择
  watch(selectedProjectId, (newVal, oldVal) => {
    if (newVal !== oldVal) {
      selectedPoint.value = null // 项目改变时清空点位选择
      // 根据需要，可以在这里触发数据加载等操作
    }
  })
  // --- END 项目与点位选择 ---

  // 空气质量数据
  const airQualityData = reactive([
    { name: '温度', value: '24.5', unit: '°C', status: 'good' },
    { name: '湿度', value: '45', unit: '%RH', status: 'good' },
    { name: 'PM2.5', value: '35', unit: 'μg/m³', status: 'normal' },
    { name: 'PM10', value: '68', unit: 'μg/m³', status: 'normal' },
    { name: 'TVOC', value: '0.28', unit: 'μg/m³', status: 'good' },
    { name: '甲醛', value: '0.05', unit: 'μg/m³', status: 'good' },
    { name: '二氧化碳', value: '650', unit: 'ppm', status: 'good' },
    { name: '照度', value: '300', unit: 'lux', status: 'good' },
    { name: '噪声', value: '50', unit: 'dB', status: 'good' }
  ])

  // 图表时间范围
  const tempHumidTimeRange = ref('day')
  const pollutantTimeRange = ref('day')
  const co2FormaldehydeTimeRange = ref('day')
  const illuminanceNoiseTimeRange = ref('day')

  // 图表引用
  const tempHumidChartRef = ref(null)
  const pollutantChartRef = ref(null)
  const co2FormaldehydeChartRef = ref(null)
  const illuminanceNoiseChartRef = ref(null)

  // 基础时间轴数据（24小时）
  const baseTimeData = [
    '00:00',
    '02:00',
    '04:00',
    '06:00',
    '08:00',
    '10:00',
    '12:00',
    '14:00',
    '16:00',
    '18:00',
    '20:00',
    '22:00',
    '24:00'
  ]

  // 温湿度图表数据
  const tempHumidChartData = reactive({
    xAxisData: baseTimeData,
    temperatureData: [24.5, 25.0, 26.2, 25.8, 24.0, 23.5, 24.2, 25.0, 25.5, 24.8, 24.0, 24.5, 25.0],
    humidityData: [45, 48, 51, 52, 48, 45, 47, 49, 51, 50, 48, 47, 46]
  })

  // 污染物图表数据
  const pollutantChartData = reactive({
    xAxisData: baseTimeData,
    pm25Data: [35, 32, 30, 28, 25, 27, 29, 32, 35, 38, 36, 34, 32],
    pm10Data: [68, 65, 62, 60, 58, 60, 63, 65, 68, 70, 69, 67, 65]
  })

  // 有害气体图表数据
  const co2FormaldehydeChartData = reactive({
    xAxisData: baseTimeData,
    co2Data: [650, 640, 630, 620, 610, 620, 630, 640, 650, 660, 650, 640, 630],
    formaldehydeData: [
      0.05, 0.048, 0.047, 0.045, 0.046, 0.049, 0.051, 0.053, 0.052, 0.05, 0.048, 0.047, 0.046
    ],
    tvocData: [0.28, 0.26, 0.25, 0.24, 0.23, 0.25, 0.27, 0.29, 0.3, 0.29, 0.28, 0.27, 0.26]
  })

  // 照度和噪声图表数据
  const illuminanceNoiseChartData = reactive({
    xAxisData: baseTimeData,
    illuminanceData: [300, 320, 350, 380, 400, 390, 370, 360, 340, 320, 310, 300, 290],
    noiseData: [50, 48, 45, 47, 49, 52, 54, 53, 51, 49, 48, 47, 46]
  })

  // 日期范围选择 - 初始化时不设置默认日期
  const dateRange = ref(null) // 初始化为null，不显示默日期

  // 监听日期范围变化
  watch(
    dateRange,
    (newVal, oldVal) => {
      // 当日期范围发生变化时，启用查询按钮
      if (newVal !== oldVal) {
        historyQueryButtonDisabled.value = false
        console.log('日期范围变化，启用查询按钮:', newVal)
      }
    },
    { deep: true }
  )

  // 分页
  const currentPage = ref(1)
  const pageSize = ref(10)
  const totalItems = ref(0)

  // 模拟历史数据
  const historyData = ref([
    // 注意：这里是模拟数据，实际使用时应根据用户选择的日期范围、项目和点位从 API 获取历史数据
    {
      date: '2025-04-14',
      temperature: '24.5',
      humidity: '45',
      pm25: '35',
      pm10: '68',
      tvoc: '0.28',
      hcho: '0.050',
      co2: '650',
      illuminance: '300',
      noise: '50'
    },
    {
      date: '2025-04-13',
      temperature: '24.0',
      humidity: '46',
      pm25: '38',
      pm10: '70',
      tvoc: '0.26',
      hcho: '0.048',
      co2: '640',
      illuminance: '320',
      noise: '48'
    },
    {
      date: '2025-04-12',
      temperature: '23.8',
      humidity: '47',
      pm25: '42',
      pm10: '75',
      tvoc: '0.28',
      hcho: '0.052',
      co2: '650',
      illuminance: '310',
      noise: '52'
    },
    {
      date: '2025-04-11',
      temperature: '24.5',
      humidity: '43',
      pm25: '30',
      pm10: '62',
      tvoc: '0.23',
      hcho: '0.045',
      co2: '620',
      illuminance: '290',
      noise: '49'
    },
    {
      date: '2025-04-10',
      temperature: '25.0',
      humidity: '42',
      pm25: '28',
      pm10: '58',
      tvoc: '0.22',
      hcho: '0.043',
      co2: '600',
      illuminance: '330',
      noise: '47'
    },
    {
      date: '2025-04-09',
      temperature: '24.3',
      humidity: '46',
      pm25: '34',
      pm10: '67',
      tvoc: '0.27',
      hcho: '0.049',
      co2: '640',
      illuminance: '305',
      noise: '51'
    },
    {
      date: '2025-04-08',
      temperature: '23.5',
      humidity: '48',
      pm25: '45',
      pm10: '82',
      tvoc: '0.30',
      hcho: '0.055',
      co2: '670',
      illuminance: '280',
      noise: '53'
    },
    {
      date: '2025-04-07',
      temperature: '24.0',
      humidity: '45',
      pm25: '40',
      pm10: '72',
      tvoc: '0.28',
      hcho: '0.051',
      co2: '650',
      illuminance: '295',
      noise: '50'
    },
    {
      date: '2025-04-06',
      temperature: '24.2',
      humidity: '44',
      pm25: '36',
      pm10: '68',
      tvoc: '0.26',
      hcho: '0.047',
      co2: '630',
      illuminance: '310',
      noise: '49'
    },
    {
      date: '2025-04-05',
      temperature: '24.5',
      humidity: '43',
      pm25: '32',
      pm10: '65',
      tvoc: '0.24',
      hcho: '0.046',
      co2: '620',
      illuminance: '300',
      noise: '48'
    }
  ])

  // 获取实时空气质量数据
  const fetchAirQualityData = async () => {
    try {
      const params = {
        projectId: selectedProjectId.value
      }

      // 如果选择了具体设备，添加设备ID参数
      if (selectedPoint.value && selectedPoint.value !== 'average') {
        params.equipmentId = selectedPoint.value
      }

      const response = await getIndoorAirQualityMonitoring(params)

      if (response.code === 200 && response.data) {
        const data = response.data
        // 更新空气质量数据
        updateAirQualityDisplay(data)
        console.log('室内空气质量数据获取成功:', data)
      } else {
        ElMessage.error(response.msg || '获取空气质量数据失败')
      }
    } catch (error) {
      console.error('获取空气质量数据失败:', error)
      ElMessage.error('获取空气质量数据失败，请稍后重试')
    }
  }

  // 更新空气质量数据显示
  const updateAirQualityDisplay = (data) => {
    // 字段映射：wd→温度，sd→湿度，pm25→PM2.5，pm10→PM10，tvoc→TVOC，hcho→甲醛，co2→二氧化碳，zd→照度，zs→噪声
    airQualityData[0].value = data.wd?.toFixed(1) || '0.0'
    airQualityData[1].value = data.sd?.toString() || '0'
    airQualityData[2].value = data.pm25?.toString() || '0'
    airQualityData[3].value = data.pm10?.toString() || '0'
    airQualityData[4].value = data.tvoc?.toFixed(2) || '0.00'
    airQualityData[5].value = data.hcho?.toFixed(3) || '0.000'
    airQualityData[6].value = data.co2?.toString() || '0'
    airQualityData[7].value = data.zd?.toString() || '0'
    airQualityData[8].value = data.zs?.toString() || '0'
  }

  // 获取趋势数据
  const fetchTrendData = async () => {
    try {
      // 时间类型转换：day→0, week→1, month→2
      const timeTypeMap = { day: 0, week: 1, month: 2 }

      const params = {
        projectId: selectedProjectId.value,
        timeType: timeTypeMap[tempHumidTimeRange.value] || 0
      }

      // 如果选择了具体设备，添加设备ID参数
      if (selectedPoint.value && selectedPoint.value !== 'average') {
        params.equipmentId = selectedPoint.value
      }

      const response = await getIndoorTemperatureAndTemperatureTrend(params)

      if (response.code === 200 && response.data) {
        const data = response.data
        // 更新图表数据
        updateTrendChartData(data)
        console.log('室内趋势数据获取成功:', data)
      } else {
        ElMessage.error(response.msg || '获取趋势数据失败')
      }
    } catch (error) {
      console.error('获取趋势数据失败:', error)
      ElMessage.error('获取趋势数据失败，请稍后重试')
    }
  }

  // 更新趋势图表数据
  const updateTrendChartData = (data) => {
    if (!data.timeList || !data.dataList) return

    // 更新时间轴
    const timeList = data.timeList

    // 更新温湿度图表数据
    tempHumidChartData.xAxisData = timeList
    tempHumidChartData.temperatureData = data.dataList.map((item) => item.wd || 0)
    tempHumidChartData.humidityData = data.dataList.map((item) => item.sd || 0)

    // 更新污染物图表数据
    pollutantChartData.xAxisData = timeList
    pollutantChartData.pm25Data = data.dataList.map((item) => item.ph2_5 || 0)
    pollutantChartData.pm10Data = data.dataList.map((item) => item.ph10 || 0)

    // 更新有害气体图表数据
    co2FormaldehydeChartData.xAxisData = timeList
    co2FormaldehydeChartData.co2Data = data.dataList.map((item) => item.co2 || 0)
    co2FormaldehydeChartData.formaldehydeData = data.dataList.map((item) => item.hcho || 0)
    co2FormaldehydeChartData.tvocData = data.dataList.map((item) => item.tvoc || 0)

    // 更新照度噪声图表数据
    illuminanceNoiseChartData.xAxisData = timeList
    illuminanceNoiseChartData.illuminanceData = data.dataList.map((item) => item.zd || 0)
    illuminanceNoiseChartData.noiseData = data.dataList.map((item) => item.zs || 0)
  }

  // 获取历史数据
  const fetchHistoryData = async (useAutoParams = false) => {
    try {
      // 基础参数配置
      const params = {
        projectId: useAutoParams ? 17 : selectedProjectId.value, // 自动查询时使用固定项目ID
        equipmentId: useAutoParams
          ? 1
          : selectedPoint.value && selectedPoint.value !== 'average'
            ? selectedPoint.value
            : undefined, // 自动查询时使用固定设备ID
        pageSize: pageSize.value,
        pageNum: currentPage.value
      }

      // 如果是自动查询或没有选择日期范围，使用当天日期
      if (useAutoParams || !dateRange.value || dateRange.value.length !== 2) {
        const todayDate = getTodayDateString()
        params.beginTime = todayDate
        params.endTime = todayDate
      } else {
        // 使用用户选择的日期范围
        params.beginTime = dateRange.value[0]
        params.endTime = dateRange.value[1]
      }

      console.log('室内空气质量历史数据查询参数:', params)
      const response = await getIndoorHistoryList(params)

      if (response.code === 200) {
        // 更新历史数据
        updateHistoryData(response.rows || [])
        totalItems.value = response.total || 0
        console.log('室内历史数据获取成功:', response)
      } else {
        ElMessage.error(response.msg || '获取历史数据失败')
      }
    } catch (error) {
      console.error('获取历史数据失败:', error)
      ElMessage.error('获取历史数据失败，请稍后重试')
    }
  }

  // 更新历史数据显示
  const updateHistoryData = (data) => {
    historyData.value = data.map((item) => ({
      date: item.createTime || '',
      temperature: item.wd?.toFixed(1) || '0.0',
      humidity: item.sd?.toString() || '0',
      pm25: item.pm25?.toString() || '0',
      pm10: item.pm10?.toString() || '0',
      tvoc: item.tvoc?.toFixed(2) || '0.00',
      hcho: item.hcho?.toFixed(3) || '0.000',
      co2: item.co2?.toString() || '0',
      illuminance: item.zd?.toString() || '0',
      noise: item.zs?.toString() || '0'
    }))
  }

  // 初始化
  onMounted(() => {
    console.log('室内空气质量监测页面已挂载，触发初始化自动查询')

    // 组件自身会处理图表初始化
    totalItems.value = 120 // 模拟总数据量

    // 确保各图表数据初始化
    nextTick(() => {
      // 为每个图表设置随机生成的数据序列
      updateChartDataByRange('day', tempHumidChartData, ['temperatureData', 'humidityData'])
      updateChartDataByRange('day', pollutantChartData, ['pm25Data', 'pm10Data'])
      updateChartDataByRange('day', co2FormaldehydeChartData, [
        'co2Data',
        'formaldehydeData',
        'tvocData'
      ])
      updateChartDataByRange('day', illuminanceNoiseChartData, ['illuminanceData', 'noiseData'])

      console.log('图表数据已初始化：', {
        温湿度: tempHumidChartData,
        污染物: pollutantChartData,
        二氧化碳: co2FormaldehydeChartData,
        照度噪声: illuminanceNoiseChartData
      })
    })

    // 页面加载时立即触发自动查询
    autoSearch()
  })

  // 更新图表数据的工具函数
  const updateChartDataByRange = (timeRange, chartData, dataKeys) => {
    // 根据时间范围生成不同的 x 轴数据
    let xAxisData = []

    if (timeRange === 'day') {
      xAxisData = baseTimeData
    } else if (timeRange === 'week') {
      xAxisData = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    } else if (timeRange === 'month') {
      xAxisData = ['1日', '5日', '10日', '15日', '20日', '25日', '30日']
    }

    // 更新 x 轴数据
    chartData.xAxisData = xAxisData

    // 为每个数据系列生成模拟数据
    dataKeys.forEach((key) => {
      let baseLine, variation, scale

      // 根据不同类型的数据设置不同的基准值和变化范围
      if (key === 'temperatureData') {
        baseLine = 24 // 温度基线 24℃
        variation = 2 // 变化幅度 ±2℃
        scale = 1 // 保留1位小数
      } else if (key === 'humidityData') {
        baseLine = 45 // 湿度基线 45%
        variation = 7 // 变化幅度 ±7%
        scale = 0 // 整数
      } else if (key === 'pm25Data') {
        baseLine = 35 // PM2.5基线 35μg/m³
        variation = 8 // 变化幅度 ±8μg/m³
        scale = 0 // 整数
      } else if (key === 'pm10Data') {
        baseLine = 68 // PM10基线 68μg/m³
        variation = 10 // 变化幅度 ±10μg/m³
        scale = 0 // 整数
      } else if (key === 'tvocData') {
        baseLine = 0.28 // TVOC基线 0.28μg/m³
        variation = 0.05 // 变化幅度 ±0.05μg/m³
        scale = 2 // 保留2位小数
      } else if (key === 'formaldehydeData') {
        baseLine = 0.05 // 甲醛基线 0.05μg/m³
        variation = 0.01 // 变化幅度 ±0.01μg/m³
        scale = 3 // 保留3位小数
      } else if (key === 'co2Data') {
        baseLine = 650 // CO2基线 650ppm
        variation = 50 // 变化幅度 ±50ppm
        scale = 0 // 整数
      } else if (key === 'illuminanceData') {
        baseLine = 300 // 照度基线 300lux
        variation = 50 // 变化幅度 ±50lux
        scale = 0 // 整数
      } else if (key === 'noiseData') {
        baseLine = 50 // 噪声基线 50dB
        variation = 6 // 变化幅度 ±6dB
        scale = 0 // 整数
      } else {
        baseLine = 50 // 默认基线 50
        variation = 10 // 默认变化幅度 ±10
        scale = 1 // 默认保留1位小数
      }

      const dataPoints = timeRange === 'day' ? xAxisData.length : timeRange === 'week' ? 7 : 7
      chartData[key] = Array(dataPoints)
        .fill(0)
        .map(() => {
          const value = baseLine + (Math.random() * variation * 2 - variation)
          return parseFloat(value.toFixed(scale))
        })

      console.log(`生成${key}数据:`, chartData[key])
    })
  }

  // 监听温湿度图表时间范围变化
  watch(tempHumidTimeRange, (newRange) => {
    // 如果有选择项目，重新获取趋势数据
    if (selectedProjectId.value) {
      fetchTrendData()
    } else {
      // 否则使用模拟数据
      updateChartDataByRange(newRange, tempHumidChartData, ['temperatureData', 'humidityData'])
    }
  })

  // 监听污染物图表时间范围变化
  watch(pollutantTimeRange, (newRange) => {
    // 如果有选择项目，重新获取趋势数据
    if (selectedProjectId.value) {
      fetchTrendData()
    } else {
      // 否则使用模拟数据
      updateChartDataByRange(newRange, pollutantChartData, ['pm25Data', 'pm10Data'])
    }
  })

  // 监听有害气体图表时间范围变化
  watch(co2FormaldehydeTimeRange, (newRange) => {
    // 如果有选择项目，重新获取趋势数据
    if (selectedProjectId.value) {
      fetchTrendData()
    } else {
      // 否则使用模拟数据
      updateChartDataByRange(newRange, co2FormaldehydeChartData, [
        'co2Data',
        'formaldehydeData',
        'tvocData'
      ])
    }
  })

  // 监听照度和噪声图表时间范围变化
  watch(illuminanceNoiseTimeRange, (newRange) => {
    // 如果有选择项目，重新获取趋势数据
    if (selectedProjectId.value) {
      fetchTrendData()
    } else {
      // 否则使用模拟数据
      updateChartDataByRange(newRange, illuminanceNoiseChartData, ['illuminanceData', 'noiseData'])
    }
  })

  // 查询数据
  const searchData = () => {
    // 如果查询按钮已禁用，不执行查询
    if (historyQueryButtonDisabled.value) {
      ElMessage.info('查询条件未变化，无需重复查询')
      return
    }

    // 获取当前查询参数
    const currentParams = {
      projectId: selectedProjectId.value,
      selectedPoint: selectedPoint.value,
      beginTime: dateRange.value && dateRange.value.length === 2 ? dateRange.value[0] : undefined,
      endTime: dateRange.value && dateRange.value.length === 2 ? dateRange.value[1] : undefined,
      pageSize: pageSize.value,
      pageNum: currentPage.value
    }

    // 检查查询参数是否与上次相同
    if (isSameHistoryQueryParams(currentParams, lastHistoryQueryParams.value)) {
      ElMessage.info('查询条件未变化')
      historyQueryButtonDisabled.value = true
      return
    }

    console.log('查询日期范围：', dateRange.value)

    // 保存当前查询参数
    lastHistoryQueryParams.value = { ...currentParams }

    // 禁用查询按钮
    historyQueryButtonDisabled.value = true

    // 如果有选择项目，调用API获取历史数据
    if (selectedProjectId.value) {
      fetchHistoryData()
    }
  }

  // 导出数据
  const exportData = async () => {
    // 检查是否选择了项目
    if (!selectedProjectId.value) {
      ElMessage.warning('请先选择项目')
      return
    }

    try {
      // 准备导出参数
      const exportParams = {
        projectId: selectedProjectId.value,
        timeType: 1 // 默认按周导出
      }

      // 如果选择了具体设备，添加设备ID参数
      if (selectedPoint.value && selectedPoint.value !== 'average') {
        exportParams.equipmentId = selectedPoint.value
      }

      // 如果有选择日期范围，添加时间参数
      if (dateRange.value && dateRange.value.length === 2) {
        const [startDate, endDate] = dateRange.value
        exportParams.beginTime = startDate
        exportParams.endTime = endDate
      }

      ElMessage.info('正在导出数据，请稍候...')

      // 调用导出API
      const blob = await exportIndoorData(exportParams)

      // 创建下载链接
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')

      // 设置文件名称
      const fileName = `室内空气质量监测数据_${new Date().toISOString().split('T')[0]}.xlsx`

      // 下载文件
      link.href = url
      link.setAttribute('download', fileName)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // 清理URL对象
      URL.revokeObjectURL(url)

      ElMessage.success('数据导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('导出失败，请稍后重试')
    }
  }

  // 分页大小变化
  const handleSizeChange = (size) => {
    pageSize.value = size
    // 重新获取数据
    if (selectedProjectId.value) {
      fetchHistoryData()
    }
  }

  // 页码变化
  const handleCurrentChange = (page) => {
    currentPage.value = page
    // 重新获取数据
    if (selectedProjectId.value) {
      fetchHistoryData()
    }
  }
</script>

<style lang="scss" scoped>
  .air-quality-container {
    padding: 20px;

    .page-header {
      margin-bottom: 20px;

      h1 {
        margin: 0;
        font-size: 24px;
        color: var(--art-text-gray-900); // 修改标题颜色为主题变量
      }
    }

    .content-section {
      margin-bottom: 20px;

      .chart-row {
        margin-top: 20px;
        margin-bottom: 20px;
      }

      .card-header {
        display: flex;
        gap: 20px;
        align-items: center;
        justify-content: space-between;

        .filter-controls {
          display: flex;
          gap: 10px;
          align-items: center;
        }
      }

      .data-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;

        .data-item {
          flex: 1;
          min-width: 140px;
          padding: 15px;
          text-align: center;
          background-color: var(--art-gray-100); // 修改背景色为主题变量
          border-radius: 8px;

          .item-value {
            margin-bottom: 8px;
            font-size: 24px;
            font-weight: bold;

            &.good {
              color: rgb(var(--art-success)); // 修改颜色为主题变量
            }

            &.normal {
              color: rgb(var(--art-warning)); // 修改颜色为主题变量
            }

            &.bad {
              color: rgb(var(--art-danger)); // 修改颜色为主题变量
            }
          }

          .item-name {
            font-size: 14px;
            color: var(--art-text-gray-600); // 修改文字颜色为主题变量
          }
        }
      }

      .chart-container {
        height: 300px;
      }

      .pagination-container {
        display: flex;
        justify-content: center;
        margin-top: 20px;
      }

      .chart-card,
      .history-card,
      .overview-card {
        margin-bottom: 0;
      }

      .chart-card {
        height: 430px;
        margin-bottom: 0;

        :deep(.el-card__body) {
          height: calc(100% - 60px);
          padding: 10px;
        }
      }
    }
  }
</style>
