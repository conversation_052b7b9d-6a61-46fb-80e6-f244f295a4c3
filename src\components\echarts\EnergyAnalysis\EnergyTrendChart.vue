<template>
  <div class="chart-container" ref="chartRef"></div>
</template>

<script setup>
  import { ref, onMounted, watch, onUnmounted, nextTick, computed } from 'vue'
  import * as echarts from 'echarts'
  import { useSettingStore } from '@/store/modules/setting'
  import { SystemThemeEnum } from '@/enums/appEnum'

  const props = defineProps({
    energyData: {
      type: Array,
      required: true,
      default: () => []
    },
    timeType: {
      type: String,
      default: 'year'
    }
  })

  const chartRef = ref(null)
  let chart = null

  // 获取主题状态
  const settingStore = useSettingStore()
  const isDark = computed(() => settingStore.systemThemeType === SystemThemeEnum.DARK)

  // 初始化图表
  const initChart = () => {
    if (!chartRef.value) return

    // 如果图表已存在则销毁
    if (chart) {
      chart.dispose()
    }

    chart = echarts.init(chartRef.value)
    updateChart()

    // 添加窗口大小变化的自适应
    window.addEventListener('resize', handleResize)
  }

  // 更新图表数据
  const updateChart = () => {
    if (!chart || props.energyData.length === 0) return

    // 提取数据并准备饼图数据
    const pieData = props.energyData.map((item) => {
      return {
        name: item.type,
        value: parseFloat(item.value.replace(/,/g, '')),
        unit: item.unit
      }
    })

    // 计算总用电量和确定单位
    let totalValue = 0
    let commonUnit = ''
    if (pieData.length > 0) {
      commonUnit = pieData[0].unit // 默认使用第一个数据的单位
      pieData.forEach((item) => {
        totalValue += item.value
        // 如果需要更复杂的单位统一逻辑，可以在这里添加
      })
    }

    // 设置图表配置项
    const option = {
      tooltip: {
        trigger: 'item',
        backgroundColor: isDark.value ? 'rgba(50, 50, 50, 0.9)' : 'rgba(255, 255, 255, 0.9)',
        borderColor: isDark.value ? '#555' : '#eee',
        borderWidth: 1,
        formatter: function (params) {
          const textColor = isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#666'
          const titleColor = isDark.value ? 'rgba(255, 255, 255, 0.95)' : '#333'
          return `
          <div style="font-size:14px;color:${textColor};line-height:22px">
            <div style="font-weight:bold;color:${titleColor}">${params.name}</div>
            <div>用电量: ${params.value.toLocaleString()} ${params.data.unit}</div>
            <div>占比: ${params.percent}%</div>
          </div>
        `
        }
      },
      // 移除图例
      // legend: {
      //   orient: 'vertical',
      //   right: 10,
      //   top: 'center',
      //   data: types, // types 变量未在此处定义，原代码中提取了 types
      //   textStyle: {
      //     fontSize: 12
      //   }
      // },
      title: {
        text: '用电总量',
        subtext: `${totalValue.toLocaleString()} ${commonUnit}`,
        left: 'center',
        top: 'center', // 微调使其垂直居中
        textStyle: {
          fontSize: 20, // 根据图片调整
          fontWeight: 'bold',
          color: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#333'
        },
        subtextStyle: {
          fontSize: 16, // 根据图片调整
          color: isDark.value ? 'rgba(255, 255, 255, 0.75)' : '#333'
        },
        itemGap: 5 // 调整主标题和副标题之间的间距
      },
      color: ['#2E8B57', '#3CB371', '#66CDAA', '#8FBC8F', '#98FB98'], // 绿色系示例颜色
      series: [
        {
          name: '用电类型分布',
          type: 'pie',
          radius: ['55%', '75%'], // 调整内外半径，使环更像图片
          avoidLabelOverlap: true, // 开启防止标签重叠
          itemStyle: {
            borderRadius: 0, // 图片中似乎没有圆角
            borderColor: isDark.value ? '#333' : '#fff',
            borderWidth: 1 // 图片中扇区间隔不明显，可以设为0或1
          },
          label: {
            show: true,
            position: 'outside', // 标签显示在外部
            formatter: (params) => {
              // 格式：名称\n数值 单位\n百分比
              // return `${params.name}\n${params.value.toLocaleString()} ${params.data.unit}\n${params.percent}%`;
              // 为了更像图片样式，我们可以分行显示，并用 rich text 调整
              return `{a|${params.name}}\n{b|${params.value.toLocaleString()} ${
                params.data.unit
              }}\n{c|${params.percent}%}`
            },
            rich: {
              a: {
                color: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#333',
                fontSize: 12,
                lineHeight: 18,
                align: 'center' // 根据实际效果调整对齐
              },
              b: {
                color: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#333',
                fontSize: 12,
                lineHeight: 18,
                align: 'center'
              },
              c: {
                color: isDark.value ? 'rgba(255, 255, 255, 0.65)' : '#555', // 百分比颜色可以稍浅
                fontSize: 12,
                lineHeight: 18,
                align: 'center'
              }
            }
          },
          emphasis: {
            // label: { // 中间文本由 title 实现，这里不再需要强调label
            //   show: false,
            // },
            itemStyle: {
              // 保留悬浮时的阴影效果
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          labelLine: {
            show: true,
            length: 8, // 第一段线长
            length2: 10, // 第二段线长
            smooth: false // 可以尝试 true 或 false 看效果
          },
          data: pieData
        }
      ]
    }

    chart.setOption(option)
  }

  const getTimeTypeText = () => {
    switch (props.timeType) {
      case 'year':
        return '年度'
      case 'month':
        return '月度'
      case 'day':
        return '日度'
      default:
        return '年度'
    }
  }

  // 处理窗口大小变化
  const handleResize = () => {
    chart && chart.resize()
  }

  // 监听数据变化
  watch(() => props.energyData, updateChart, { deep: true })
  watch(() => props.timeType, updateChart)

  // 监听主题变化，更新图表
  watch(() => isDark.value, updateChart)

  // 组件挂载时初始化图表
  onMounted(() => {
    nextTick(() => {
      initChart()
    })
  })

  // 组件卸载时清理
  onUnmounted(() => {
    if (chart) {
      chart.dispose()
      chart = null
    }
    window.removeEventListener('resize', handleResize)
  })

  // 暴露 resize 方法
  defineExpose({
    resizeChart: handleResize
  })
</script>

<style lang="scss" scoped>
  .chart-container {
    width: 100%;
    height: 450px; /* 您可以根据需要调整图表高度 */

    /* 移除或注释掉不再需要的旧样式 */

    /*
  :deep(.echarts-tooltip-diy) {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 4px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    padding: 10px;

    .diy-title {
      font-weight: bold;
      margin-bottom: 5px;
    }

    .diy-content {
      color: #666;
    }
  }
  */
  }
</style>
