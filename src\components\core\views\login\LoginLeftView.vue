<template>
  <div class="left-view">
    <div class="logo">
      <img src="@imgs/login/logo.png" alt="logo" class="logo-img" />
      <h1 class="title">{{ AppConfig.systemInfo.name }}</h1>
    </div>
    <img class="left-bg" src="@imgs/login/background.png" />
    <img class="left-img" src="@imgs/login/lf_icon2.png" />
  </div>
</template>

<script setup lang="ts">
  import AppConfig from '@/config'
</script>

<style lang="scss" scoped>
  .left-view {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: var(--el-color-primary);

    .logo {
      position: absolute;
      top: 30px;
      left: 40px;
      z-index: 1;
      display: flex;
      align-items: center;

      .title {
        margin: 0;
        font-size: 22px;
        color: #fff;
      }
    }

    .logo-img {
      height: 40px;
      margin-right: 10px;
    }

    .left-bg {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .left-img {
      position: absolute;
      bottom: 0;
      left: 50%;
      z-index: 1;
      max-width: 90%;
      max-height: 80%;
      object-fit: contain;
      transform: translateX(-50%);
    }
  }
</style>
