<template>
  <div class="network-params-container page-content">
    <div class="content-box">
      <!-- 状态概览卡片 -->
      <el-card class="status-card" shadow="hover">
        <template #header>
          <div class="status-header">
            <span class="status-title">通讯状态概览</span>
            <el-button type="primary" size="small" @click="refreshStatus" :icon="Refresh"
              >刷新状态</el-button
            >
          </div>
        </template>
        <div class="status-content">
          <div class="status-item" v-for="(device, index) in deviceStatusList" :key="index">
            <div class="device-icon" :class="device.status">
              <el-icon :size="24">
                <component :is="getProtocolIcon(device.protocol)" />
              </el-icon>
            </div>
            <div class="device-info">
              <div class="device-name">{{ device.name }}</div>
              <div class="device-address">{{ device.address }}</div>
              <div class="device-status">
                <span class="status-dot" :class="device.status"></span>
                <span class="status-text" :class="device.status">{{
                  getStatusText(device.status)
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <el-card class="param-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span class="card-title">网络通讯参数配置</span>
            <el-button type="primary" size="small" @click="handleAddParameter" :icon="Plus"
              >添加参数</el-button
            >
          </div>
        </template>

        <!-- 搜索表单 -->
        <div class="search-form">
          <el-form :model="searchForm" inline>
            <el-form-item label="参数名称">
              <el-input
                v-model="searchForm.configName"
                placeholder="请输入参数名称"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="参数编码">
              <el-input
                v-model="searchForm.configKey"
                placeholder="请输入参数编码"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="参数类型">
              <el-select
                v-model="searchForm.configType"
                placeholder="请选择参数类型"
                clearable
                style="width: 150px"
              >
                <el-option label="设备参数" value="0" />
                <el-option label="网络通讯参数" value="1" />
                <el-option label="信息安全参数" value="2" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" :icon="Search">查询</el-button>
              <el-button @click="handleReset" :icon="Refresh">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 参数列表 -->
        <ArtTable
          :data="networkParamsList"
          :loading="loading"
          row-key="id"
          :border="false"
          :highlight-current-row="false"
          :total="total"
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          index
          class="param-table"
        >
          <el-table-column prop="configName" label="参数名称" />
          <el-table-column prop="configKey" label="参数编码" />
          <el-table-column prop="configValue" label="参数值" />
          <el-table-column label="参数类型" align="center">
            <template #default="scope">
              <el-tag
                :type="getConfigTypeTagType(scope.row.configType)"
                effect="light"
                size="small"
              >
                {{ getConfigTypeText(scope.row.configType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="ipAddr" label="IP地址" />
          <el-table-column prop="port" label="端口" />
          <el-table-column prop="userName" label="用户名" />
          <el-table-column prop="remark" label="描述" show-overflow-tooltip />
          <el-table-column prop="createTime" label="创建时间" width="180" />
          <el-table-column label="操作" width="180" align="center" fixed="right">
            <template #default="scope">
              <div class="operation-btns">
                <el-button
                  link
                  size="small"
                  @click="handleEdit(scope.row)"
                  title="编辑"
                  class="operation-btn edit-btn"
                >
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-button
                  link
                  size="small"
                  @click="handleDelete(scope.row)"
                  title="删除"
                  class="operation-btn delete-btn"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </template>
          </el-table-column>
        </ArtTable>

        <!-- 数据为空时显示提示 -->
        <el-empty v-if="networkParamsList.length === 0" description="暂无数据" />
      </el-card>
    </div>

    <!-- 添加/编辑参数对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEditing ? '编辑网络参数' : '添加网络参数'"
      width="650px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-form
        :model="paramForm"
        :rules="rules"
        ref="paramFormRef"
        label-width="120px"
        label-position="right"
        class="param-form"
      >
        <el-form-item label="参数名称" prop="configName">
          <el-input v-model="paramForm.configName" placeholder="请输入参数名称" />
        </el-form-item>
        <el-form-item label="参数编码" prop="configKey">
          <el-input v-model="paramForm.configKey" placeholder="请输入参数编码" />
        </el-form-item>
        <el-form-item label="参数值" prop="configValue">
          <el-input v-model="paramForm.configValue" placeholder="请输入参数值" />
        </el-form-item>
        <el-form-item label="参数类型" prop="configType">
          <el-select
            v-model="paramForm.configType"
            placeholder="请选择参数类型"
            style="width: 100%"
          >
            <el-option label="设备参数" value="0" />
            <el-option label="网络通讯参数" value="1" />
            <el-option label="信息安全参数" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="IP地址" prop="ipAddr">
          <el-input v-model="paramForm.ipAddr" placeholder="请输入IP地址" />
        </el-form-item>
        <el-form-item label="端口" prop="port">
          <el-input v-model="paramForm.port" placeholder="请输入端口" />
        </el-form-item>
        <el-form-item label="用户名" prop="userName">
          <el-input v-model="paramForm.userName" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="密码" prop="pwd">
          <el-input
            v-model="paramForm.pwd"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="QoS" prop="qos">
          <el-select v-model="paramForm.qos" placeholder="请选择QoS级别" style="width: 100%">
            <el-option label="0 - 最多一次" value="0" />
            <el-option label="1 - 至少一次" value="1" />
            <el-option label="2 - 只有一次" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="remark">
          <el-input
            v-model="paramForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入参数描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import {
    Edit,
    Delete,
    Refresh,
    Plus,
    Search,
    Connection,
    Monitor,
    Bell,
    Link,
    SetUp
  } from '@element-plus/icons-vue'
  import {
    getNetworkConfigList,
    createNetworkConfig,
    updateNetworkConfig,
    deleteNetworkConfig
  } from '@/api/safeguard/networkConfigApi'

  // 数据状态
  const loading = ref(false)
  const networkParamsList = ref([])
  const total = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(10)

  // 搜索表单
  const searchForm = reactive({
    configName: '',
    configKey: '',
    configValue: '',
    configType: ''
  })

  // 设备状态列表
  const deviceStatusList = ref([
    {
      id: 1,
      name: 'Modbus设备1',
      protocol: 'modbus',
      address: '*************:502',
      status: 'online'
    },
    {
      id: 2,
      name: 'MQTT服务器',
      protocol: 'mqtt',
      address: 'mqtt://broker.example.com:1883',
      status: 'online'
    },
    {
      id: 3,
      name: 'BACnet设备',
      protocol: 'bacnet',
      address: '*************',
      status: 'offline'
    },
    {
      id: 4,
      name: 'HTTP API服务',
      protocol: 'http',
      address: 'http://api.example.com',
      status: 'warning'
    }
  ])

  // 状态文本映射
  const getStatusText = (status) => {
    const statusMap = {
      online: '在线',
      offline: '离线',
      warning: '警告'
    }
    return statusMap[status] || '未知'
  }

  // 协议图标映射
  const getProtocolIcon = (protocol) => {
    const iconMap = {
      modbus: Connection,
      mqtt: Monitor,
      bacnet: Bell,
      http: Link,
      other: SetUp
    }
    return iconMap[protocol] || iconMap.other
  }

  // 刷新设备状态
  const refreshStatus = () => {
    // 这里可以调用API刷新设备状态
    ElMessage.success('设备状态已刷新')
  }

  // 配置类型映射
  const getConfigTypeText = (configType) => {
    const typeMap = {
      0: '设备参数',
      1: '网络通讯参数',
      2: '信息安全参数'
    }
    return typeMap[configType] || '未知'
  }

  // 配置类型标签类型
  const getConfigTypeTagType = (configType) => {
    const typeMap = {
      0: 'success',
      1: 'primary',
      2: 'warning'
    }
    return typeMap[configType] || ''
  }

  // 表单相关
  const dialogVisible = ref(false)
  const isEditing = ref(false)
  const paramFormRef = ref(null)
  const paramForm = reactive({
    id: null,
    configName: '',
    configKey: '',
    configValue: '',
    configType: '',
    ipAddr: '',
    port: '',
    userName: '',
    pwd: '',
    qos: '',
    remark: ''
  })

  // 表单验证规则
  const rules = {
    configName: [{ required: true, message: '请输入参数名称', trigger: 'blur' }],
    configKey: [{ required: true, message: '请输入参数编码', trigger: 'blur' }],
    configValue: [{ required: true, message: '请输入参数值', trigger: 'blur' }],
    configType: [{ required: true, message: '请选择参数类型', trigger: 'change' }]
  }

  // 获取网络配置列表
  const fetchNetworkConfigList = async () => {
    try {
      loading.value = true
      const params = {
        ...searchForm,
        pageSize: pageSize.value,
        pageNum: currentPage.value
      }
      const response = await getNetworkConfigList(params)
      if (response.code === 200) {
        networkParamsList.value = response.rows || []
        total.value = response.total || 0
      } else {
        ElMessage.error(response.msg || '获取数据失败')
      }
    } catch (error) {
      console.error('获取网络配置列表失败:', error)

      // 检查是否是404错误（接口不存在）
      if (error.response?.status === 404) {
        ElMessage.error('查询接口暂未实现，请联系管理员')
      } else if (error.response?.status === 500) {
        ElMessage.error('服务器内部错误，请稍后重试')
      } else {
        ElMessage.error('获取数据失败，请检查网络连接')
      }
    } finally {
      loading.value = false
    }
  }

  // 搜索
  const handleSearch = () => {
    currentPage.value = 1
    fetchNetworkConfigList()
  }

  // 重置搜索
  const handleReset = () => {
    Object.keys(searchForm).forEach((key) => {
      searchForm[key] = ''
    })
    currentPage.value = 1
    fetchNetworkConfigList()
  }

  // 生命周期钩子
  onMounted(() => {
    fetchNetworkConfigList()
  })

  // 分页方法
  const handleSizeChange = (size) => {
    pageSize.value = size
    fetchNetworkConfigList()
  }

  const handleCurrentChange = (page) => {
    currentPage.value = page
    fetchNetworkConfigList()
  }

  // 添加参数
  const handleAddParameter = () => {
    isEditing.value = false
    resetForm()
    dialogVisible.value = true
  }

  // 编辑参数
  const handleEdit = (row) => {
    isEditing.value = true
    resetForm()

    // 设置表单字段
    Object.keys(paramForm).forEach((key) => {
      if (key in row) {
        paramForm[key] = row[key]
      }
    })

    dialogVisible.value = true
  }

  // 删除参数
  const handleDelete = async (row) => {
    try {
      await ElMessageBox.confirm('确定要删除此参数吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      const response = await deleteNetworkConfig(row.id)
      if (response.code === 200) {
        ElMessage.success('删除成功')
        fetchNetworkConfigList()
      } else {
        ElMessage.error(response.msg || '删除失败')
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除网络配置失败:', error)

        // 检查是否是404错误（接口不存在）
        if (error.response?.status === 404) {
          ElMessage.error('删除接口暂未实现，请联系管理员')
        } else if (error.response?.status === 500) {
          ElMessage.error('服务器内部错误，请稍后重试')
        } else {
          ElMessage.error('删除失败，请检查网络连接')
        }
      }
    }
  }

  // 提交表单
  const submitForm = async () => {
    try {
      const valid = await paramFormRef.value.validate()
      if (!valid) return

      const formData = { ...paramForm }

      if (isEditing.value) {
        // 编辑模式
        const response = await updateNetworkConfig(formData)
        if (response.code === 200) {
          ElMessage.success('更新成功')
          dialogVisible.value = false
          fetchNetworkConfigList()
        } else {
          ElMessage.error(response.msg || '更新失败')
        }
      } else {
        // 添加模式
        delete formData.id // 新增时不需要id
        const response = await createNetworkConfig(formData)
        if (response.code === 200) {
          ElMessage.success('添加成功')
          dialogVisible.value = false
          fetchNetworkConfigList()
        } else {
          ElMessage.error(response.msg || '添加失败')
        }
      }
    } catch (error) {
      console.error('提交表单失败:', error)

      // 检查是否是404错误（接口不存在）
      if (error.response?.status === 404) {
        ElMessage.error(`${isEditing.value ? '修改' : '新增'}接口暂未实现，请联系管理员`)
      } else if (error.response?.status === 500) {
        ElMessage.error('服务器内部错误，请稍后重试')
      } else {
        ElMessage.error(`${isEditing.value ? '更新' : '添加'}失败，请检查网络连接`)
      }
    }
  }

  // 重置表单
  const resetForm = () => {
    Object.keys(paramForm).forEach((key) => {
      if (key === 'id') {
        paramForm[key] = null
      } else {
        paramForm[key] = ''
      }
    })
    if (paramFormRef.value) {
      paramFormRef.value.resetFields()
    }
  }
</script>

<style scoped>
  .network-params-container {
    min-height: calc(100vh - 60px);
    padding: 20px;
    background-color: var(--art-bg-color);
  }

  .page-title {
    margin-bottom: 20px;
    font-size: 24px;
    font-weight: 600;
    color: var(--art-text-gray-800);
  }

  .content-box {
    border-radius: 4px;
  }

  .status-card {
    margin-bottom: 20px;
    background-color: var(--art-main-bg-color);
    border: 1px solid var(--art-border-color);
    border-radius: 8px;
    box-shadow: var(--art-box-shadow-xs);
    transition: all 0.3s ease;
  }

  .status-card:hover {
    box-shadow: var(--art-box-shadow-sm);
  }

  .status-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .status-title,
  .card-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--art-text-gray-800);
  }

  .status-content {
    display: flex;
    gap: 15px;
    padding: 8px 0;
  }

  .status-item {
    display: flex;
    align-items: center;
    width: calc(25% - 12px);
    min-width: 250px;
    padding: 15px;
    background-color: var(--art-main-bg-color);
    border: 1px solid var(--art-border-color);
    border-radius: 8px;
    box-shadow: var(--art-box-shadow-xs);
    transition: all 0.3s ease;
  }

  .status-item:hover {
    box-shadow: var(--art-box-shadow-sm);
    transform: translateY(-3px);
  }

  .device-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 46px;
    height: 46px;
    margin-right: 15px;
    color: var(--art-text-gray-600);
    border-radius: 50%;
    transition: all 0.3s ease;
  }

  .device-icon.online {
    color: var(--el-color-success);
    background-color: var(--art-bg-success);
    border: 1px solid var(--el-color-success);
  }

  .device-icon.offline {
    color: var(--el-color-danger);
    background-color: var(--art-bg-danger);
    border: 1px solid var(--el-color-danger);
  }

  .device-icon.warning {
    color: var(--el-color-warning);
    background-color: var(--art-bg-warning);
    border: 1px solid var(--el-color-warning);
  }

  .device-info {
    flex: 1;
  }

  .device-name {
    margin-bottom: 8px;
    font-size: 15px;
    font-weight: 600;
    color: var(--art-text-gray-800);
  }

  .device-address {
    margin-bottom: 10px;
    font-size: 13px;
    color: var(--art-text-gray-600);
  }

  .device-status {
    display: flex;
    align-items: center;
  }

  .status-dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    margin-right: 6px;
    border-radius: 50%;
  }

  .status-dot.online {
    background-color: var(--el-color-success);
  }

  .status-dot.offline {
    background-color: var(--el-color-danger);
  }

  .status-dot.warning {
    background-color: var(--el-color-warning);
  }

  .status-text {
    font-size: 13px;
    font-weight: 500;
  }

  .status-text.online {
    color: var(--el-color-success);
  }

  .status-text.offline {
    color: var(--el-color-danger);
  }

  .status-text.warning {
    color: var(--el-color-warning);
  }

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .param-card {
    margin-bottom: 20px;
    background-color: var(--art-main-bg-color);
    border: 1px solid var(--art-border-color);
    border-radius: 8px;
    box-shadow: var(--art-box-shadow-xs);
    transition: all 0.3s ease;
  }

  .param-card:hover {
    box-shadow: var(--art-box-shadow-sm);
  }

  .search-form {
    padding: 20px;
    margin-bottom: 20px;
    background-color: var(--art-main-bg-color);
    border: 1px solid var(--art-border-color);
    border-radius: 8px;
  }

  /* 确保ArtTable组件有足够的显示空间 */
  .param-table {
    height: auto;
    min-height: 400px;
  }

  /* 修复el-table__empty-block高度不断增长问题 */
  .param-table :deep(.el-table__empty-block) {
    height: auto !important;
    min-height: 60px;
    max-height: 400px;
    overflow: hidden;
  }

  /* 确保空表格状态下的布局稳定 */
  .param-table :deep(.el-table__body-wrapper) {
    height: auto !important;
    min-height: 200px;
  }

  .icon-wrapper {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    margin-right: 8px;
    color: var(--art-text-gray-600);
  }

  .protocol-name-cell {
    display: flex;
    align-items: center;
  }

  .protocol-tag {
    padding: 4px 8px;
    font-weight: 500;
    letter-spacing: 0.5px;
    border-radius: 4px;
  }

  /* 自定义操作按钮样式 */
  .operation-btns {
    display: flex;
    gap: 12px;
    justify-content: center;
  }

  .operation-btn {
    width: 32px !important;
    height: 32px !important;
    padding: 6px !important;
    margin: 0 !important;
    line-height: 1 !important; /* 确保图标垂直居中 */
    border: none !important;
    border-radius: 4px !important; /* 方形边框 */
    box-shadow: 0 2px 4px rgb(0 0 0 / 10%) !important; /* 添加阴影效果 */
    transition: all 0.3s ease !important; /* 添加过渡效果 */
  }

  .operation-btn:hover {
    box-shadow: 0 4px 8px rgb(0 0 0 / 15%) !important; /* 悬停时增强阴影 */
    transform: translateY(-2px) !important; /* 悬停时上移效果 */
  }

  .edit-btn {
    background-color: #e6f7ff !important; /* 浅蓝色背景 */
  }

  .edit-btn .el-icon {
    font-size: 16px;
    color: #409eff !important; /* 蓝色图标 */
  }

  .delete-btn {
    background-color: #fff1f0 !important; /* 浅红色背景 */
  }

  .delete-btn .el-icon {
    font-size: 16px;
    color: #f56c6c !important; /* 红色图标 */
  }

  .pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 25px;
  }

  .protocol-option,
  .qos-option {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .qos-level {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    font-size: 12px;
    font-weight: bold;
    color: white;
    border-radius: 50%;
  }

  .qos-0 {
    background-color: #67c23a;
  }

  .qos-1 {
    background-color: #409eff;
  }

  .qos-2 {
    background-color: #f56c6c;
  }

  .param-form {
    padding: 0 20px;
  }

  /* 表格行样式 */
  :deep(.row-modbus) {
    background-color: rgba(var(--art-success), 0.05);
  }

  :deep(.row-modbus:hover) {
    background-color: rgba(var(--art-success), 0.1) !important;
  }

  :deep(.row-mqtt) {
    background-color: rgba(var(--art-primary), 0.05);
  }

  :deep(.row-mqtt:hover) {
    background-color: rgba(var(--art-primary), 0.1) !important;
  }

  :deep(.row-bacnet) {
    background-color: rgba(var(--art-warning), 0.05);
  }

  :deep(.row-bacnet:hover) {
    background-color: rgba(var(--art-warning), 0.1) !important;
  }

  :deep(.row-http) {
    background-color: rgba(var(--art-info), 0.05);
  }

  :deep(.row-http:hover) {
    background-color: rgba(var(--art-info), 0.1) !important;
  }

  /* 响应式设计 */
  @media screen and (width <= 1200px) {
    .status-item {
      width: calc(33.33% - 10px);
    }
  }

  @media screen and (width <= 992px) {
    .status-item {
      width: calc(50% - 10px);
    }
  }

  @media screen and (width <= 768px) {
    .status-item {
      width: 100%;
    }
  }
</style>
