<template>
  <div class="device-params-container">
    <!-- 页面头部 -->

    <!-- 查询条件 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="queryParams" :inline="true" label-width="80px">
        <el-form-item label="参数名称">
          <el-input
            v-model="queryParams.configName"
            placeholder="请输入参数名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="参数键名">
          <el-input
            v-model="queryParams.configKey"
            placeholder="请输入参数键名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="参数键值">
          <el-input
            v-model="queryParams.configValue"
            placeholder="请输入参数键值"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="设备类型">
          <el-select
            v-model="queryParams.businessType"
            placeholder="请选择设备类型"
            clearable
            style="width: 200px"
          >
            <el-option label="空调" value="0" />
            <el-option label="照明" value="1" />
            <el-option label="电梯" value="2" />
            <el-option label="供水" value="3" />
            <el-option label="其他" value="4" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleQuery">查询</el-button>
          <el-button :icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <div class="table-header">
        <div class="table-title"> 设备参数列表 </div>
        <div class="table-actions">
          <el-button type="primary" :icon="Plus" @click="handleAdd"> 添加参数 </el-button>
        </div>
      </div>

      <el-table v-loading="loading" :data="configList" row-key="configId" border stripe>
        <el-table-column type="index" label="序号" width="80" align="center" />
        <el-table-column prop="configName" label="参数名称" align="center" show-overflow-tooltip />
        <el-table-column prop="configKey" label="参数键名" align="center" show-overflow-tooltip />
        <el-table-column prop="configValue" label="参数键值" align="center" show-overflow-tooltip />
        <el-table-column label="设备类型" align="center">
          <template #default="scope">
            <el-tag :type="getBusinessTypeTag(scope.row.businessType)" size="small">
              {{ getBusinessTypeName(scope.row.businessType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="unitName" label="单位" align="center" />
        <el-table-column prop="remark" label="备注" align="center" show-overflow-tooltip />
        <el-table-column prop="createTime" label="创建时间" align="center" />
        <el-table-column label="操作" width="180" align="center" fixed="right">
          <template #default="scope">
            <div class="operation-btns">
              <el-button
                link
                size="small"
                @click="handleEdit(scope.row)"
                title="编辑"
                class="operation-btn edit-btn"
              >
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button
                link
                size="small"
                @click="handleDelete(scope.row)"
                title="删除"
                class="operation-btn delete-btn"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑参数对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEditing ? '编辑参数' : '添加参数'"
      width="600px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="参数名称" prop="configName">
          <el-input v-model="form.configName" placeholder="请输入参数名称" />
        </el-form-item>
        <el-form-item label="参数键名" prop="configKey">
          <el-input v-model="form.configKey" placeholder="请输入参数键名" />
        </el-form-item>
        <el-form-item label="参数键值" prop="configValue">
          <el-input v-model="form.configValue" placeholder="请输入参数键值" />
        </el-form-item>
        <el-form-item label="设备类型" prop="businessType">
          <el-select v-model="form.businessType" placeholder="请选择设备类型" style="width: 100%">
            <el-option label="空调" value="0" />
            <el-option label="照明" value="1" />
            <el-option label="电梯" value="2" />
            <el-option label="供水" value="3" />
            <el-option label="其他" value="4" />
          </el-select>
        </el-form-item>
        <el-form-item label="单位名称" prop="unitName">
          <el-input v-model="form.unitName" placeholder="请输入单位名称" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitLoading">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted, computed } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Edit, Delete, Refresh, Plus, Search, Setting } from '@element-plus/icons-vue'
  import {
    getDeviceConfigList,
    createDeviceConfig,
    updateDeviceConfig,
    deleteDeviceConfig,
    getDeviceConfigDetail
  } from '@/api/safeguard/deviceConfigApi'
  import { useSettingStore } from '@/store/modules/setting'
  import { SystemThemeEnum } from '@/enums/appEnum'

  // 获取主题设置
  const settingStore = useSettingStore()
  const isDark = computed(() => settingStore.systemThemeType === SystemThemeEnum.DARK)

  // 数据状态
  const loading = ref(false)
  const submitLoading = ref(false)
  const configList = ref([])
  const total = ref(0)

  // 查询参数
  const queryParams = reactive({
    configName: '',
    configKey: '',
    configValue: '',
    businessType: '',
    pageSize: 10,
    pageNum: 1
  })

  // 对话框状态
  const dialogVisible = ref(false)
  const isEditing = ref(false)
  const formRef = ref()

  // 表单数据
  const form = reactive({
    configId: null,
    configName: '',
    configKey: '',
    configValue: '',
    businessType: '',
    unitName: '',
    remark: ''
  })

  // 表单验证规则
  const rules = {
    configName: [{ required: true, message: '请输入参数名称', trigger: 'blur' }],
    configKey: [{ required: true, message: '请输入参数键名', trigger: 'blur' }],
    configValue: [{ required: true, message: '请输入参数键值', trigger: 'blur' }],
    businessType: [{ required: true, message: '请选择设备类型', trigger: 'change' }]
  }

  // 获取设备类型名称
  const getBusinessTypeName = (type) => {
    const typeMap = {
      0: '空调',
      1: '照明',
      2: '电梯',
      3: '供水',
      4: '其他'
    }
    return typeMap[type] || '未知'
  }

  // 获取设备类型标签颜色
  const getBusinessTypeTag = (type) => {
    const typeMap = {
      0: 'primary',
      1: 'success',
      2: 'warning',
      3: 'info',
      4: ''
    }
    return typeMap[type] || ''
  }

  // 获取配置列表
  const getList = async () => {
    loading.value = true
    try {
      const response = await getDeviceConfigList(queryParams)
      configList.value = response.rows || []
      total.value = response.total || 0
    } catch (error) {
      console.error('获取配置列表失败:', error)
      ElMessage.error('获取配置列表失败')
    } finally {
      loading.value = false
    }
  }

  // 查询
  const handleQuery = () => {
    queryParams.pageNum = 1
    getList()
  }

  // 重置查询
  const resetQuery = () => {
    Object.assign(queryParams, {
      configName: '',
      configKey: '',
      configValue: '',
      businessType: '',
      pageSize: 10,
      pageNum: 1
    })
    getList()
  }

  // 刷新数据
  const refreshData = () => {
    getList()
    ElMessage.success('数据已刷新')
  }

  // 分页处理
  const handleSizeChange = (size) => {
    queryParams.pageSize = size
    queryParams.pageNum = 1
    getList()
  }

  const handleCurrentChange = (page) => {
    queryParams.pageNum = page
    getList()
  }

  // 重置表单
  const resetForm = () => {
    Object.assign(form, {
      configId: null,
      configName: '',
      configKey: '',
      configValue: '',
      businessType: '',
      unitName: '',
      remark: ''
    })
    if (formRef.value) {
      formRef.value.clearValidate()
    }
  }

  // 添加
  const handleAdd = () => {
    resetForm()
    isEditing.value = false
    dialogVisible.value = true
  }

  // 编辑
  const handleEdit = async (row) => {
    resetForm()
    isEditing.value = true
    dialogVisible.value = true

    try {
      const response = await getDeviceConfigDetail(row.configId)
      Object.assign(form, response.data)
    } catch (error) {
      console.error('获取配置详情失败:', error)
      ElMessage.error('获取配置详情失败')
    }
  }

  // 删除
  const handleDelete = (row) => {
    ElMessageBox.confirm(`确定要删除参数"${row.configName}"吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        try {
          await deleteDeviceConfig(row.configId)
          ElMessage.success('删除成功')
          getList()
        } catch (error) {
          console.error('删除失败:', error)
          ElMessage.error('删除失败')
        }
      })
      .catch(() => {
        // 取消删除
      })
  }

  // 提交表单
  const submitForm = () => {
    if (!formRef.value) return

    formRef.value.validate(async (valid) => {
      if (!valid) return

      submitLoading.value = true
      try {
        if (isEditing.value) {
          await updateDeviceConfig(form)
          ElMessage.success('修改成功')
        } else {
          await createDeviceConfig(form)
          ElMessage.success('添加成功')
        }
        dialogVisible.value = false
        getList()
      } catch (error) {
        console.error('提交失败:', error)
        ElMessage.error('提交失败')
      } finally {
        submitLoading.value = false
      }
    })
  }

  // 生命周期钩子
  onMounted(() => {
    getList()
  })
</script>

<style scoped>
  .device-params-container {
    padding: 20px;
    color: var(--el-text-color-primary);
    background-color: var(--el-bg-color);
  }

  .page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: var(--el-box-shadow-light);
  }

  .header-left {
    flex: 1;
  }

  .page-title {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 24px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .title-icon {
    margin-right: 8px;
    color: var(--el-color-primary);
  }

  .page-description {
    font-size: 14px;
    color: var(--el-text-color-regular);
  }

  .header-right {
    display: flex;
    gap: 12px;
  }

  .search-card,
  .table-card {
    margin-bottom: 20px;

    /* background: var(--el-bg-color-page); */
    border-radius: 8px;
    box-shadow: var(--el-box-shadow-light);
  }

  .search-card {
    padding: 20px;
  }

  .table-card {
    padding: 20px;
  }

  .table-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
  }

  .table-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .table-actions {
    display: flex;
    gap: 8px;
  }

  .pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }

  /* 自定义操作按钮样式 */
  .operation-btns {
    display: flex;
    gap: 12px;
    justify-content: center;
  }

  .operation-btn {
    width: 32px !important;
    height: 32px !important;
    padding: 6px !important;
    margin: 0 !important;
    line-height: 1 !important; /* 确保图标垂直居中 */
    border: none !important;
    border-radius: 4px !important; /* 方形边框 */
    box-shadow: 0 2px 4px rgb(0 0 0 / 10%) !important; /* 添加阴影效果 */
    transition: all 0.3s ease !important; /* 添加过渡效果 */
  }

  .operation-btn:hover {
    box-shadow: var(--el-box-shadow) !important; /* 悬停时增强阴影 */
    transform: translateY(-2px) !important; /* 悬停时上移效果 */
  }

  .edit-btn {
    background-color: var(--el-color-primary-light-9) !important; /* 浅蓝色背景 */
  }

  .edit-btn .el-icon {
    font-size: 16px;
    color: var(--el-color-primary) !important; /* 蓝色图标 */
  }

  .delete-btn {
    background-color: var(--el-color-danger-light-9) !important; /* 浅红色背景 */
  }

  .delete-btn .el-icon {
    font-size: 16px;
    color: var(--el-color-danger) !important; /* 红色图标 */
  }

  /* 黑夜模式特殊样式 */
  :global(.dark) {
    .device-params-container {
      .search-card,
      .table-card {
        /* background: var(--el-bg-color-overlay); */
        border: 1px solid var(--el-border-color);
      }

      .operation-btn {
        box-shadow: var(--el-box-shadow-dark) !important;
      }
    }
  }
</style>
