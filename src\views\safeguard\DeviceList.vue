<template>
  <div class="query-container page-content">
    <!-- 查询条件区域 -->
    <div class="filter-section">
      <el-form :model="queryParams" inline>
        <!-- 项目和楼栋选择器 -->
        <ProjectBuildingSelector
          ref="projectBuildingSelectorRef"
          v-model="projectBuildingSelection"
          @change="handleProjectBuildingChange"
          @project-loaded="handleProjectListLoaded"
        />

        <el-form-item label="设备类型:">
          <el-select
            v-model="queryParams.equipmentType"
            placeholder="请选择设备类型"
            clearable
            :loading="equipmentTypeLoading"
          >
            <el-option
              v-for="item in equipmentTypeOptions"
              :key="item.key"
              :label="item.label"
              :value="item.key"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="表计编码:">
          <el-input v-model="queryParams.code" placeholder="请输入表计编码" clearable />
        </el-form-item>

        <el-form-item label="表计名称:">
          <el-input v-model="queryParams.name" placeholder="请输入表计名称" clearable />
        </el-form-item>

        <el-form-item label="设备类别:">
          <el-select
            v-model="queryParams.equipmentCategory"
            placeholder="请选择设备类别"
            clearable
            @change="handleQueryCategoryChange"
          >
            <el-option label="耗能监测设备" value="0" />
            <el-option label="环境监测设备" value="1" />
          </el-select>
        </el-form-item>

        <el-form-item label="类别类型:">
          <el-select
            v-model="queryParams.equipmentCategorytype"
            placeholder="请选择类别类型"
            clearable
            :disabled="queryParams.equipmentCategory !== '1'"
          >
            <el-option label="室外气象站" value="0" />
            <el-option label="室内七合一" value="1" />
            <el-option label="水质监测" value="2" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch" :icon="Search">查询</el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button type="success" @click="handleExport" :loading="exporting" :icon="Download"
            >导出数据</el-button
          >
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <div class="table-operation">
        <el-button type="primary" @click="handleAdd" :icon="Plus">新增表计</el-button>
      </div>

      <ArtTable
        :data="tableData"
        :loading="loading"
        row-key="id"
        :border="false"
        :highlight-current-row="false"
        :total="totalCount"
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        class="device-table"
      >
        <el-table-column prop="index" label="序号" align="center" width="70">
          <template #default="scope">
            <span>{{ (currentPage - 1) * pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="code" label="表计编码" align="center">
          <template #default="scope">
            <span>{{ scope.row.code }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="表计名称" align="center">
          <template #default="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="equipmentCategory" label="设备类别" align="center">
          <template #default="scope">
            <span>{{ getEquipmentCategoryLabel(scope.row.equipmentCategory) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="equipmentType" label="监测对象" align="center">
          <template #default="scope">
            <span>{{ getEquipmentTypeLabel(scope.row.equipmentType) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="energyType" label="能源类型" align="center">
          <template #default="scope">
            <span>{{ getEnergyTypeLabel(scope.row.energyType) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="equipmentCategorytype" label="类别类型" align="center">
          <template #default="scope">
            <span>{{
              getEquipmentCategorytypeLabel(
                scope.row.equipmentCategorytype,
                scope.row.equipmentCategory
              )
            }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="180" align="center">
          <template #default="scope">
            <div class="operation-btns">
              <el-button
                link
                size="small"
                @click="handleDetail(scope.row)"
                title="查看"
                class="operation-btn view-btn"
              >
                <el-icon><Search /></el-icon>
              </el-button>
              <el-button
                link
                size="small"
                @click="handleEdit(scope.row)"
                title="编辑"
                class="operation-btn edit-btn"
              >
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button
                link
                size="small"
                @click="handleDelete(scope.row)"
                title="删除"
                class="operation-btn delete-btn"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
      </ArtTable>
    </div>

    <!-- 新增/编辑设备对话框 -->
    <el-dialog
      :title="dialogType === 'add' ? '新增表计' : '编辑表计'"
      v-model="dialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="deviceForm"
        :rules="deviceRules"
        ref="deviceFormRef"
        label-width="100px"
        style="max-height: 500px; overflow-y: auto"
      >
        <el-form-item label="表计编码" prop="codeSuffix">
          <div class="code-input-wrapper">
            <span class="code-prefix">{{ getProjectCodePrefix() }}-</span>
            <el-input
              v-model="deviceForm.codeSuffix"
              placeholder="请输入表计编码"
              class="code-suffix-input"
              :disabled="dialogType === 'edit'"
            />
          </div>
        </el-form-item>
        <el-form-item label="表计名称" prop="name">
          <el-input v-model="deviceForm.name" placeholder="请输入表计名称" />
        </el-form-item>
        <el-form-item label="设备类别" prop="equipmentCategory">
          <el-select
            v-model="deviceForm.equipmentCategory"
            placeholder="请选择设备类别"
            style="width: 100%"
            @change="handleEquipmentCategoryChange"
          >
            <el-option label="耗能监测设备" value="0" />
            <el-option label="环境监测设备" value="1" />
          </el-select>
        </el-form-item>
        <!-- 耗能监测设备时显示监测对象和能源类型 -->
        <el-form-item
          v-if="deviceForm.equipmentCategory === '0'"
          label="监测对象"
          prop="equipmentType"
        >
          <el-select
            v-model="deviceForm.equipmentType"
            placeholder="请选择监测对象"
            style="width: 100%"
            :loading="equipmentTypeLoading"
          >
            <el-option
              v-for="item in equipmentTypeOptions"
              :key="item.key"
              :label="item.label"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="deviceForm.equipmentCategory === '0'"
          label="能源类型"
          prop="energyType"
        >
          <el-select
            v-model="deviceForm.energyType"
            placeholder="请选择能源类型"
            style="width: 100%"
          >
            <el-option label="耗水" value="0" />
            <el-option label="耗电" value="1" />
            <el-option label="耗气" value="2" />
            <el-option label="光伏" value="3" />
          </el-select>
        </el-form-item>
        <!-- 环境监测设备时显示类别类型 -->
        <el-form-item
          v-if="deviceForm.equipmentCategory === '1'"
          label="类别类型"
          prop="equipmentCategorytype"
        >
          <el-select
            v-model="deviceForm.equipmentCategorytype"
            placeholder="请选择类别类型"
            style="width: 100%"
          >
            <el-option label="室外气象站" value="0" />
            <el-option label="室内七合一" value="1" />
            <el-option label="水质监测" value="2" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitDeviceForm" :loading="submitLoading"
            >确定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, reactive, computed, onMounted, nextTick } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Search, Edit, Delete, Plus, Download } from '@element-plus/icons-vue'
  import ProjectBuildingSelector from '@/components/ProjectBuildingSelector.vue'
  import {
    getEquipmentList,
    createEquipment,
    updateEquipment,
    deleteEquipment,
    getEquipmentDetail,
    getEquipmentTypeDict
  } from '@/api/equipmentApi'
  // ArtTable组件已在全局注册，无需导入

  // 项目楼栋选择相关数据
  const projectBuildingSelectorRef = ref()
  const projectBuildingSelection = ref({
    projectId: null,
    buildingId: null
  })

  // 当前选中的项目信息（包含code字段）
  const selectedProjectInfo = ref(null)

  // 查询参数
  const queryParams = reactive({
    equipmentType: '',
    equipmentCategory: '',
    equipmentCategorytype: '',
    code: '',
    name: '',
    pageNum: 1,
    pageSize: 10
  })

  // 表格数据和分页相关
  const tableData = ref([])
  const loading = ref(false)
  const exporting = ref(false)
  const totalCount = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(10) // 修改默认分页大小为10，与ArtTable默认值保持一致

  // 下拉选项数据
  const equipmentTypeOptions = ref([])
  const equipmentTypeLoading = ref(false)

  // 获取监测对象选项
  const fetchEquipmentTypeOptions = async () => {
    try {
      equipmentTypeLoading.value = true
      const response = await getEquipmentTypeDict()

      if (response.code === 200) {
        equipmentTypeOptions.value = response.data || []
        console.log('获取监测对象选项成功:', equipmentTypeOptions.value)
      } else {
        ElMessage.error(response.msg || '获取监测对象选项失败')
        equipmentTypeOptions.value = []
      }
    } catch (error) {
      console.error('获取监测对象选项失败:', error)
      ElMessage.error('获取监测对象选项失败，请稍后重试')
      equipmentTypeOptions.value = []
    } finally {
      equipmentTypeLoading.value = false
    }
  }

  // 处理项目楼栋变化
  const handleProjectBuildingChange = (data) => {
    console.log('项目楼栋选择变化:', data)
    // 保存项目信息，用于获取项目编号
    selectedProjectInfo.value = data.projectInfo
    // 注意：这里不自动发请求，只有点击查询按钮时才发请求
  }

  // 处理查询表单中设备类别变化
  const handleQueryCategoryChange = (value) => {
    // 当设备类别不是环境监测设备时，清空类别类型
    if (value !== '1') {
      queryParams.equipmentCategorytype = ''
    }
  }

  // 获取表格数据
  const fetchData = async () => {
    // 如果没有选择项目，不发请求
    if (!projectBuildingSelection.value.projectId) {
      console.log('没有选择项目，不发请求')
      return
    }

    loading.value = true
    try {
      // 构建查询参数
      const params = {
        projectId: projectBuildingSelection.value.projectId,
        buildingId: projectBuildingSelection.value.buildingId || undefined,
        code: queryParams.code || undefined,
        name: queryParams.name || undefined,
        equipmentType: queryParams.equipmentType || undefined,
        equipmentCategory: queryParams.equipmentCategory || undefined,
        equipmentCategorytype: queryParams.equipmentCategorytype || undefined,
        pageSize: queryParams.pageSize,
        pageNum: queryParams.pageNum
      }

      console.log('查询参数:', params)

      // 调用API
      const response = await getEquipmentList(params)

      if (response.code === 200) {
        tableData.value = response.rows || []
        totalCount.value = response.total || 0
        currentPage.value = queryParams.pageNum
        pageSize.value = queryParams.pageSize
        console.log('获取表计列表成功:', response)
      } else {
        ElMessage.error(response.msg || '获取表计列表失败')
        tableData.value = []
        totalCount.value = 0
      }
    } catch (error) {
      console.error('获取表计列表失败:', error)
      ElMessage.error('获取表计列表失败，请稍后重试')
      tableData.value = []
      totalCount.value = 0
    } finally {
      loading.value = false
    }
  }

  // 导出数据
  const handleExport = async () => {
    if (totalCount.value === 0) {
      ElMessage.warning('暂无数据可导出')
      return
    }

    ElMessageBox.confirm('确认导出所有数据?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        exporting.value = true
        try {
          // 模拟导出延迟
          await new Promise((resolve) => setTimeout(resolve, 1000))
          ElMessage.success('导出成功')
        } catch (error) {
          console.error('导出失败:', error)
          ElMessage.error('导出失败')
        } finally {
          exporting.value = false
        }
      })
      .catch(() => {})
  }

  // 监听项目选择器组件的项目列表加载完成
  const handleProjectListLoaded = (projectList) => {
    // 当项目列表加载完成后，自动选择第一个项目并发请求
    if (projectList && projectList.length > 0) {
      console.log('项目列表加载完成，自动选择第一个项目并发请求')
      // 保存第一个项目的信息
      selectedProjectInfo.value = projectList[0]
      // 自动发请求获取数据
      nextTick(() => {
        fetchData()
      })
    }
  }

  // 重置表单
  const resetForm = () => {
    // 重置项目楼栋选择
    if (projectBuildingSelectorRef.value) {
      projectBuildingSelectorRef.value.reset()
    }

    // 重置查询参数
    Object.keys(queryParams).forEach((key) => {
      if (key !== 'pageNum' && key !== 'pageSize') {
        queryParams[key] = ''
      }
    })
    queryParams.pageNum = 1

    // 重置设备类别类型筛选条件的联动状态
    queryParams.equipmentCategorytype = ''

    fetchData()
  }

  // 搜索按钮点击事件
  const handleSearch = () => {
    // 如果没有选择项目，不发请求
    if (!projectBuildingSelection.value.projectId) {
      ElMessage.warning('请先选择项目')
      return
    }

    console.log('查询参数:', {
      projectId: projectBuildingSelection.value.projectId,
      buildingId: projectBuildingSelection.value.buildingId,
      ...queryParams
    })
    queryParams.pageNum = 1
    fetchData()
  }

  // 页码改变事件
  const handleCurrentChange = (val) => {
    queryParams.pageNum = val
    fetchData()
  }

  // 获取监测对象标签
  const getEquipmentTypeLabel = (equipmentType) => {
    const option = equipmentTypeOptions.value.find((item) => item.key === equipmentType)
    return option ? option.label : equipmentType || '-'
  }

  // 获取能耗类型标签
  const getEnergyTypeLabel = (energyType) => {
    const energyTypeMap = {
      0: '耗水',
      1: '耗电',
      2: '耗气',
      3: '光伏'
    }
    return energyTypeMap[energyType] || energyType || '-'
  }

  // 获取设备类别标签
  const getEquipmentCategoryLabel = (category) => {
    const categoryMap = {
      0: '耗能监测设备',
      1: '环境监测设备'
    }
    return categoryMap[category] || category || '-'
  }

  // 获取设备类别类型标签
  const getEquipmentCategorytypeLabel = (categorytype, category) => {
    // 只有环境监测设备才显示类别类型
    if (category !== '1') {
      return '-'
    }

    const categorytypeMap = {
      0: '室外气象站',
      1: '室内七合一',
      2: '水质监测'
    }
    return categorytypeMap[categorytype] || categorytype || '-'
  }

  // 获取项目编号前缀
  const getProjectCodePrefix = () => {
    if (selectedProjectInfo.value && selectedProjectInfo.value.code) {
      return selectedProjectInfo.value.code
    }
    return '未选择项目'
  }

  // 对话框相关
  const dialogVisible = ref(false)
  const dialogType = ref('add') // 'add' 或 'edit'
  const deviceFormRef = ref(null)
  const submitLoading = ref(false)

  // 设备表单
  const deviceForm = reactive({
    id: '',
    code: '',
    codeSuffix: '', // 表计编码后缀（用户输入部分）
    name: '',
    equipmentType: '',
    projectId: '',
    buildingId: '',
    floorId: '',
    equipmentCategory: '',
    equipmentCategorytype: '',
    energyType: ''
  })

  // 表单验证规则 - 使用计算属性动态生成
  const deviceRules = computed(() => {
    const rules = {
      name: [{ required: true, message: '请输入表计名称', trigger: 'blur' }],
      equipmentCategory: [{ required: true, message: '请选择设备类别', trigger: 'change' }],
      equipmentType: [
        {
          validator: (rule, value, callback) => {
            if (deviceForm.equipmentCategory === '0' && !value) {
              callback(new Error('耗能监测设备必须选择监测对象'))
            } else {
              callback()
            }
          },
          trigger: 'change'
        }
      ],
      energyType: [
        {
          validator: (rule, value, callback) => {
            if (deviceForm.equipmentCategory === '0' && !value) {
              callback(new Error('耗能监测设备必须选择能源类型'))
            } else {
              callback()
            }
          },
          trigger: 'change'
        }
      ],
      equipmentCategorytype: [
        {
          validator: (rule, value, callback) => {
            if (deviceForm.equipmentCategory === '1' && !value) {
              callback(new Error('环境监测设备必须选择类别类型'))
            } else {
              callback()
            }
          },
          trigger: 'change'
        }
      ]
    }

    // 只在新增模式下验证表计编码
    if (dialogType.value === 'add') {
      rules.codeSuffix = [
        { required: true, message: '请输入表计编码', trigger: 'blur' },
        { min: 1, max: 30, message: '长度在 1 到 30 个字符', trigger: 'blur' }
      ]
    }

    return rules
  })

  // 添加设备
  const handleAdd = () => {
    dialogType.value = 'add'
    resetDeviceForm()
    dialogVisible.value = true
  }

  // 编辑设备
  const handleEdit = (row) => {
    dialogType.value = 'edit'
    resetDeviceForm()
    // 填充表单数据
    Object.keys(deviceForm).forEach((key) => {
      if (key in row) {
        deviceForm[key] = row[key]
      }
    })

    // 处理表计编码的回显 - 去掉项目前缀，只显示后缀部分
    const projectPrefix = getProjectCodePrefix()
    const fullCode = row.code || ''
    if (fullCode.startsWith(projectPrefix + '-')) {
      // 如果编码以项目前缀开头，则去掉前缀显示
      deviceForm.codeSuffix = fullCode.substring(projectPrefix.length + 1)
    } else {
      // 如果编码不以项目前缀开头，则直接显示（兼容旧数据）
      deviceForm.codeSuffix = fullCode
    }

    dialogVisible.value = true
  }

  // 设备类别变化处理
  const handleEquipmentCategoryChange = (value) => {
    // 根据设备类别清空相关字段
    if (value === '0') {
      // 耗能监测设备：清空类别类型
      deviceForm.equipmentCategorytype = ''
    } else if (value === '1') {
      // 环境监测设备：清空监测对象和能源类型
      deviceForm.equipmentType = ''
      deviceForm.energyType = ''
    }
  }

  // 重置设备表单
  const resetDeviceForm = () => {
    if (deviceFormRef.value) {
      deviceFormRef.value.resetFields()
    }
    Object.keys(deviceForm).forEach((key) => {
      deviceForm[key] = ''
    })

    // 设置默认项目和楼栋
    if (projectBuildingSelection.value.projectId) {
      deviceForm.projectId = projectBuildingSelection.value.projectId
    }
    if (projectBuildingSelection.value.buildingId) {
      deviceForm.buildingId = projectBuildingSelection.value.buildingId
    }
  }

  // 提交设备表单
  const submitDeviceForm = () => {
    if (!deviceFormRef.value) return

    deviceFormRef.value.validate(async (valid) => {
      if (valid) {
        submitLoading.value = true
        try {
          if (dialogType.value === 'add') {
            // 新增操作 - 只提交用户输入的编码后缀
            const createData = {
              projectId: Number(deviceForm.projectId),
              buildingId: Number(deviceForm.buildingId),
              floorId: deviceForm.floorId ? Number(deviceForm.floorId) : undefined,
              code: deviceForm.codeSuffix,
              name: deviceForm.name,
              equipmentType: deviceForm.equipmentType || undefined,
              equipmentCategory: deviceForm.equipmentCategory,
              equipmentCategorytype: deviceForm.equipmentCategorytype || undefined,
              energyType: deviceForm.energyType || undefined
            }

            const response = await createEquipment(createData)
            if (response.code === 200) {
              ElMessage.success('新增表计成功')
              dialogVisible.value = false
              fetchData() // 重新获取列表数据
            } else {
              ElMessage.error(response.msg || '新增表计失败')
            }
          } else {
            // 编辑操作 - 只提交用户输入的编码后缀
            const updateData = {
              id: Number(deviceForm.id),
              projectId: Number(deviceForm.projectId),
              buildingId: Number(deviceForm.buildingId),
              floorId: deviceForm.floorId ? Number(deviceForm.floorId) : undefined,
              code: deviceForm.codeSuffix,
              name: deviceForm.name,
              equipmentType: deviceForm.equipmentType || undefined,
              equipmentCategory: deviceForm.equipmentCategory,
              equipmentCategorytype: deviceForm.equipmentCategorytype || undefined,
              energyType: deviceForm.energyType || undefined
            }

            const response = await updateEquipment(updateData)
            if (response.code === 200) {
              ElMessage.success('编辑表计成功')
              dialogVisible.value = false
              fetchData() // 重新获取列表数据
            } else {
              ElMessage.error(response.msg || '编辑表计失败')
            }
          }
        } catch (error) {
          console.error(dialogType.value === 'add' ? '新增表计失败:' : '编辑表计失败:', error)
          ElMessage.error(dialogType.value === 'add' ? '新增表计失败' : '编辑表计失败')
        } finally {
          submitLoading.value = false
        }
      } else {
        ElMessage.warning('请填写完整的表单信息')
        return false
      }
    })
  }

  // 详情按钮点击事件
  const handleDetail = async (row) => {
    try {
      const response = await getEquipmentDetail(row.id)
      if (response.code === 200) {
        console.log('设备详情:', response.data)
        ElMessage.info(`查看表计编码为 ${row.code} 的详细数据`)
        // 这里可以实现详情查看逻辑，如弹出对话框显示详细信息
      } else {
        ElMessage.error(response.msg || '获取表计详情失败')
      }
    } catch (error) {
      console.error('获取表计详情失败:', error)
      ElMessage.error('获取表计详情失败，请稍后重试')
    }
  }

  // 删除单个表计
  const handleDelete = (row) => {
    ElMessageBox.confirm(`确认删除表计编码为 ${row.code} 的表计吗?`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        try {
          const response = await deleteEquipment(row.id)
          if (response.code === 200) {
            ElMessage.success(`删除表计 ${row.code} 成功`)
            fetchData() // 重新获取列表数据
          } else {
            ElMessage.error(response.msg || '删除表计失败')
          }
        } catch (error) {
          console.error('删除表计失败:', error)
          ElMessage.error('删除表计失败，请稍后重试')
        }
      })
      .catch(() => {})
  }

  // 分页大小改变事件
  const handleSizeChange = (val) => {
    pageSize.value = val
    queryParams.pageSize = val
    fetchData()
  }

  // 组件挂载时初始化数据
  onMounted(() => {
    // 获取监测对象选项
    fetchEquipmentTypeOptions()
  })
</script>

<style lang="scss" scoped>
  .query-container {
    padding: 20px;

    // 筛选条件区域样式
    .filter-section {
      padding: 20px;
      margin-bottom: 20px;
      background-color: var(--art-main-bg-color);
      border: 1px solid var(--art-border-color);
      border-radius: 4px;
      box-shadow: var(--art-box-shadow-xs);

      .el-form {
        display: flex;
        flex-wrap: wrap;

        .el-form-item {
          margin-right: 10px;
          margin-bottom: 10px;

          // 按钮间距
          .el-button + .el-button {
            margin-left: 10px;
          }
        }

        .date-separator {
          display: inline-block;
          line-height: 32px;
        }
      }

      :deep(.el-select) {
        width: 180px;
      }

      :deep(.el-date-editor) {
        width: 260px;
      }

      :deep(.el-input) {
        width: 180px;
      }
    }

    // 表格区域样式
    .table-section {
      padding: 20px;
      background-color: var(--art-main-bg-color);
      border: 1px solid var(--art-border-color);
      border-radius: 4px;
      box-shadow: var(--art-box-shadow-xs);

      // 表格操作区域
      .table-operation {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 15px;

        .el-button {
          margin-right: 10px;
        }
      }

      // 确保ArtTable组件有足够的显示空间
      :deep(.art-table) {
        height: auto;
        min-height: 400px;
      }

      // 标签样式
      .tag-container {
        display: flex;
        justify-content: center;

        .custom-tag {
          padding: 2px 8px;
          font-weight: 500;
          letter-spacing: 0.5px;
          border-radius: 4px;
        }
      }

      // 协议标签样式
      .protocol-tag {
        padding: 4px 8px;
        font-weight: 500;
        letter-spacing: 0.5px;
        border-radius: 4px;
      }

      // 自定义操作按钮样式
      .operation-btns {
        display: flex;
        gap: 12px;
        justify-content: center;
      }

      .operation-btn {
        width: 32px !important;
        height: 32px !important;
        padding: 6px !important;
        margin: 0 !important;
        line-height: 1 !important; /* 确保图标垂直居中 */
        border: none !important;
        border-radius: 4px !important; /* 方形边框 */
        box-shadow: 0 2px 4px rgb(0 0 0 / 10%) !important; /* 添加阴影效果 */
        transition: all 0.3s ease !important; /* 添加过渡效果 */
      }

      .operation-btn:hover {
        box-shadow: 0 4px 8px rgb(0 0 0 / 15%) !important; /* 悬停时增强阴影 */
        transform: translateY(-2px) !important; /* 悬停时上移效果 */
      }

      .view-btn {
        background-color: #e6f7ff !important; /* 浅蓝色背景 */
      }

      .view-btn .el-icon {
        font-size: 16px;
        color: #409eff !important; /* 蓝色图标 */
      }

      .edit-btn {
        background-color: #e6f7ff !important; /* 浅蓝色背景 */
      }

      .edit-btn .el-icon {
        font-size: 16px;
        color: #409eff !important; /* 蓝色图标 */
      }

      .delete-btn {
        background-color: #fff1f0 !important; /* 浅红色背景 */
      }

      .delete-btn .el-icon {
        font-size: 16px;
        color: #f56c6c !important; /* 红色图标 */
      }

      // 修复el-table__empty-block高度不断增长问题
      .device-table :deep(.el-table__empty-block) {
        height: auto !important;
        min-height: 60px;
        max-height: 400px;
        overflow: hidden;
      }

      // 确保空表格状态下的布局稳定
      .device-table :deep(.el-table__body-wrapper) {
        height: auto !important;
        min-height: 200px;
      }
    }
  }

  // 表计编码输入框样式
  .code-input-wrapper {
    display: flex;
    align-items: stretch;
    width: 100%;

    .code-prefix {
      display: flex;
      align-items: center;
      min-width: fit-content;
      padding: 0 12px;
      font-size: 14px;
      color: var(--el-text-color-regular);
      white-space: nowrap;
      background-color: var(--el-fill-color-light);
      border: 1px solid var(--el-border-color);
      border-right: none;
      border-radius: var(--el-border-radius-base) 0 0 var(--el-border-radius-base);
    }

    .code-suffix-input {
      flex: 1;

      :deep(.el-input__wrapper) {
        border-left: none;
        border-radius: 0 var(--el-border-radius-base) var(--el-border-radius-base) 0;
      }

      :deep(.el-input__wrapper:focus),
      :deep(.el-input__wrapper.is-focus) {
        border-left: 1px solid var(--el-color-primary);
      }
    }
  }
</style>
