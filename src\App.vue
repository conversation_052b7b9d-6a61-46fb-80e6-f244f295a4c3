<template>
  <ElConfigProvider :size="elSize" :locale="locales[language]" :z-index="3000">
    <RouterView></RouterView>
  </ElConfigProvider>
</template>

<script setup lang="ts">
  import { useUserStore } from './store/modules/user'
  import zh from 'element-plus/es/locale/lang/zh-cn'
  import en from 'element-plus/es/locale/lang/en'
  import { initState, saveUserData } from './utils/storage'
  import { getUserInfo as fetchUserInfo } from './api/usersApi'
  import { setThemeTransitionClass } from './utils/theme/animation'
  import type { UserInfoResponse } from './types/store'
  import { useExternalLogin } from './composables/useExternalLogin'

  const userStore = useUserStore()
  const { language } = storeToRefs(userStore)
  const elSize = computed(() => (document.body.clientWidth >= 500 ? 'large' : 'default'))

  // 初始化外部登录功能（子组件负责自动登录）
  const { initExternalLogin } = useExternalLogin()

  const locales = {
    zh: zh,
    en: en
  }

  onBeforeMount(() => {
    setThemeTransitionClass(true)
  })

  onMounted(() => {
    initState()
    saveUserData()
    setThemeTransitionClass(false)
    getUserInfo()

    // 初始化外部登录监听（如果在iframe中运行）
    if (window.parent !== window) {
      console.log('检测到iframe环境，启用外部登录功能（子组件负责自动登录）')
      initExternalLogin()
    }
  })

  // 获取用户信息（优化外部登录兼容性）
  const getUserInfo = async () => {
    if (userStore.isLogin) {
      // 检查是否为外部登录场景
      const isExternalLogin = localStorage.getItem('externalLogin') === 'true'
      const externalLoginTime = localStorage.getItem('externalLoginTime')

      if (isExternalLogin && externalLoginTime) {
        const timeDiff = Date.now() - parseInt(externalLoginTime)
        // 如果是外部登录且在10秒内，延迟获取用户信息，避免与外部登录流程冲突
        if (timeDiff < 10000) {
          console.log('检测到外部登录，延迟获取用户信息')
          setTimeout(() => getUserInfo(), 2000)
          return
        }
      }

      try {
        const userRes = (await fetchUserInfo()) as UserInfoResponse
        if (userRes.code === 200 && userRes.user) {
          // 存储完整的用户信息到 Pinia
          userStore.setUserInfo(userRes.user)
          console.log('用户信息已成功存储到 Pinia:', userRes.user)
        } else {
          console.warn('获取用户信息失败:', userRes.msg)
          // 检查是否为认证失败，如果是则跳转登录页
          if (userRes.code === 401 || (userRes.msg && userRes.msg.includes('认证失败'))) {
            // 在外部登录场景下，给更多容错时间
            if (isExternalLogin) {
              console.warn('外部登录场景下认证失败，延迟处理')
              setTimeout(() => {
                if (!userStore.checkTokenValid()) {
                  console.warn('延迟检查后仍然认证失败，跳转到登录页')
                  userStore.logOut()
                }
              }, 1000)
            } else {
              console.warn('认证失败，跳转到登录页')
              userStore.logOut()
            }
          }
        }
      } catch (error: any) {
        console.warn('获取用户信息失败:', error)
        // 检查错误是否为认证失败
        const isAuthError =
          error?.response?.status === 401 ||
          (error?.response?.data?.msg && error.response.data.msg.includes('认证失败')) ||
          (error?.message && error.message.includes('认证失败'))

        if (isAuthError) {
          // 在外部登录场景下，给更多容错时间
          if (isExternalLogin) {
            console.warn('外部登录场景下认证失败，延迟处理')
            setTimeout(() => {
              if (!userStore.checkTokenValid()) {
                console.warn('延迟检查后仍然认证失败，跳转到登录页')
                userStore.logOut()
              }
            }, 1000)
          } else {
            console.warn('认证失败，跳转到登录页')
            userStore.logOut()
          }
        }
      }
    }
  }
</script>
