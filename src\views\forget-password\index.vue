<template>
  <div class="login register">
    <div class="left-wrap">
      <LoginLeftView></LoginLeftView>
    </div>
    <div class="right-wrap">
      <div class="header">
        <img src="@imgs/login/logo.png" alt="logo" class="logo-img" />
        <h1>{{ systemName }}</h1>
      </div>
      <div class="login-wrap">
        <div class="form">
          <h3 class="title">{{ $t('forgetPassword.title') }}</h3>
          <p class="sub-title">{{ $t('forgetPassword.subTitle') }}</p>
          <div class="input-wrap">
            <span class="input-label" v-if="showInputLabel">账号</span>
            <el-input
              :placeholder="$t('forgetPassword.placeholder')"
              size="large"
              v-model.trim="username"
            />
          </div>

          <div style="margin-top: 15px">
            <el-button
              class="login-btn"
              size="large"
              type="primary"
              @click="register"
              :loading="loading"
              v-ripple
            >
              {{ $t('forgetPassword.submitBtnText') }}
            </el-button>
          </div>

          <div style="margin-top: 15px">
            <el-button style="width: 100%; height: 46px" size="large" plain @click="toLogin">
              {{ $t('forgetPassword.backBtnText') }}
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import AppConfig from '@/config'
  const router = useRouter()
  const showInputLabel = ref(false)

  const systemName = AppConfig.systemInfo.name
  const username = ref('')
  const loading = ref(false)

  const register = async () => {}

  const toLogin = () => {
    router.push('/login')
  }
</script>

<style lang="scss" scoped>
  .login.register {
    display: flex;
    align-items: center;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    background-color: var(--art-main-bg-color);

    .left-wrap {
      width: 60%;
      height: 100%;
    }

    .right-wrap {
      display: flex;
      flex: 1;
      flex-direction: column;
      height: 100%;
      padding: 0 40px;

      .header {
        display: flex;
        align-items: center;
        padding: 30px 0;

        .logo-img {
          width: 45px;
          height: 45px;
        }

        h1 {
          margin-left: 12px;
          font-size: 22px;
          color: var(--art-text-color);
        }
      }

      .login-wrap {
        display: flex;
        flex: 1;
        align-items: center;
        justify-content: center;

        .form {
          width: 100%;
          max-width: 380px;

          .title {
            margin-bottom: 12px;
            font-size: 28px;
            font-weight: 600;
            color: var(--art-text-color);
          }

          .sub-title {
            margin-bottom: 30px;
            font-size: 14px;
            color: var(--art-text-color-secondary);
          }

          .input-wrap {
            position: relative;
            margin-bottom: 20px;

            .input-label {
              position: absolute;
              top: -8px;
              left: 12px;
              z-index: 1;
              padding: 0 4px;
              font-size: 12px;
              color: var(--art-text-color-secondary);
              background-color: var(--art-main-bg-color);
            }
          }

          .login-btn {
            width: 100%;
          }
        }
      }
    }
  }

  @media screen and (width <= 768px) {
    .login.register {
      .left-wrap {
        display: none;
      }

      .right-wrap {
        width: 100%;
      }
    }
  }
</style>
