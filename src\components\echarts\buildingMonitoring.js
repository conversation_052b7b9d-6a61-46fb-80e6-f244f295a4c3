// 生成图表数据
export const generateChartData = (dateType, length, timeFormat) => {
  return Array.from({ length }, (_, i) => {
    const value = Math.random() * 30 + 20
    return {
      time: timeFormat(i),
      value: parseFloat(value.toFixed(2))
    }
  })
}

// 获取时间格式化函数
export const getTimeFormatter = (dateType) => {
  switch (dateType) {
    case 'time':
      return (i) => `${i.toString().padStart(2, '0')}:00`
    case 'day':
      return (i) => ['周一', '周二', '周三', '周四', '周五', '周六', '周日'][i]
    case 'month':
      return (i) => `${i + 1}月`
    default:
      return (i) => i.toString()
  }
}

// 获取数据长度
export const getDataLength = (dateType) => {
  switch (dateType) {
    case 'time':
      return 24
    case 'day':
      return 7
    case 'month':
      return 6
    default:
      return 24
  }
}

// 标准值配置
export const standardValues = {
  particle: 75, // 颗粒物标准值
  pollution: 50, // 污染气体标准值
  temperature: 26, // 温度标准值
  humidity: 65 // 湿度标准值
}

// 生成图表配置
export const getChartOption = ({ data, dateType, activeMenu, getTableColumnTitle, getUnit }) => {
  return {
    tooltip: {
      trigger: 'axis',
      formatter: function (params) {
        const param = params[0]
        return `${param.name}<br/>${getTableColumnTitle}: ${param.value}${getUnit}`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: data.map((item) => item.time),
      axisLabel: {
        interval: dateType === 'time' ? 2 : 0,
        rotate: dateType === 'time' ? 30 : 0
      },
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      type: 'value',
      name: getTableColumnTitle,
      nameLocation: 'middle',
      nameGap: 50,
      axisLabel: {
        formatter: (value) => value + getUnit
      }
    },
    series: [
      {
        name: getTableColumnTitle,
        data: data.map((item) => item.value),
        type: 'line',
        smooth: true,
        areaStyle: {
          opacity: 0.1
        },
        lineStyle: {
          width: 2
        },
        itemStyle: {
          borderWidth: 2
        },
        markLine: {
          silent: true,
          data: [
            {
              yAxis: standardValues[activeMenu],
              label: {
                formatter: '标准值'
              },
              lineStyle: {
                color: '#ff4949'
              }
            }
          ]
        }
      }
    ]
  }
}
