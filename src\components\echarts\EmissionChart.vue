<template>
  <div ref="chartContainer" class="chart-container"></div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted } from 'vue'
  import * as echarts from 'echarts'

  // Chart container ref
  const chartContainer = ref(null)
  let chartInstance = null

  // Data from the image
  const years = ['2025', '2026', '2027', '2028', '2029', '2030']
  const baselineEmissions = [1456.2, 1426.9, 1398.9, 1371.3, 1344.4, 1318.1]
  const predictedEmissions = [1225.6, 1155.8, 1087.5, 1020.7, 955.4, 892.1]
  const annualReductions = [230.6, 271.1, 311.4, 350.6, 389, 426]
  const reductionPercentages = [15.8, 19, 22.3, 25.6, 28.9, 32.3] // Approximated from the image
  const costEffectiveness = [1510, 1423, 1386, 1342, 1298, 1260]

  // Initialize chart
  const initChart = () => {
    if (chartContainer.value) {
      chartInstance = echarts.init(chartContainer.value)

      const option = {
        title: {
          text: '减排量预测对比图',
          subtext: '不同措施组合的减排效果对比',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function (params) {
            let result = `${params[0].axisValue}年<br/>`
            params.forEach((param) => {
              // Custom format based on series
              if (param.seriesName === '减排比例') {
                result += `${param.seriesName}: ${param.value}%<br/>`
              } else if (param.seriesName === '成本效益') {
                result += `${param.seriesName}: ${param.value} 元/吨<br/>`
              } else {
                result += `${param.seriesName}: ${param.value} 吨<br/>`
              }
            })
            return result
          }
        },
        legend: {
          data: ['基准排放量', '预测排放量', '年减排量', '减排比例', '成本效益'],
          top: 50
        },
        grid: {
          top: '30%',
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: years,
          name: '年份'
        },
        yAxis: [
          {
            type: 'value',
            name: '排放量 (吨)',
            position: 'left',
            axisLine: {
              show: true
            },
            axisLabel: {
              formatter: '{value}'
            }
          },
          {
            type: 'value',
            name: '百分比 / 元',
            position: 'right',
            axisLine: {
              show: true
            },
            axisLabel: {
              formatter: '{value}'
            }
          }
        ],
        series: [
          {
            name: '基准排放量',
            type: 'bar',
            data: baselineEmissions,
            barWidth: '20%',
            itemStyle: {
              color: '#acacac'
            }
          },
          {
            name: '预测排放量',
            type: 'bar',
            data: predictedEmissions,
            barWidth: '20%',
            itemStyle: {
              color: '#8db3e2'
            }
          },
          {
            name: '年减排量',
            type: 'bar',
            data: annualReductions,
            barWidth: '20%',
            itemStyle: {
              color: '#76c47e'
            }
          },
          {
            name: '减排比例',
            type: 'line',
            yAxisIndex: 1,
            data: reductionPercentages,
            symbol: 'circle',
            symbolSize: 8,
            lineStyle: {
              width: 2
            },
            itemStyle: {
              color: '#e27031'
            }
          },
          {
            name: '成本效益',
            type: 'line',
            yAxisIndex: 1,
            data: costEffectiveness,
            symbol: 'diamond',
            symbolSize: 8,
            lineStyle: {
              width: 2
            },
            itemStyle: {
              color: '#9747ff'
            }
          }
        ]
      }

      chartInstance.setOption(option)

      // Handle window resize
      window.addEventListener('resize', resizeChart)
    }
  }

  // Resize chart when window size changes
  const resizeChart = () => {
    if (chartInstance) {
      chartInstance.resize()
    }
  }

  // Component lifecycle hooks
  onMounted(() => {
    initChart()
  })

  onUnmounted(() => {
    // Clean up
    if (chartInstance) {
      chartInstance.dispose()
      chartInstance = null
    }
    window.removeEventListener('resize', resizeChart)
  })

  // Add a method that can be called from parent to update data if needed
  const updateChartData = (newData) => {
    if (chartInstance && newData) {
      // Update chart data here
      chartInstance.setOption({
        series: [
          { data: newData.baselineEmissions || baselineEmissions },
          { data: newData.predictedEmissions || predictedEmissions },
          { data: newData.annualReductions || annualReductions },
          { data: newData.reductionPercentages || reductionPercentages },
          { data: newData.costEffectiveness || costEffectiveness }
        ]
      })
    }
  }

  // Expose methods to parent component
  defineExpose({
    updateChartData
  })
</script>

<style scoped>
  .chart-container {
    width: 100%;
    height: 500px;
  }
</style>
