<template>
  <div class="project-card" @click="$emit('click')">
    <div class="project-image">
      <el-image
        :src="getProjectImageUrl(project)"
        :alt="project.name"
        fit="cover"
        class="project-image-el"
      >
        <template #error>
          <div class="image-error">
            <el-icon><Picture /></el-icon>
            <span>图片加载失败</span>
          </div>
        </template>
      </el-image>
      <div class="project-badge" v-if="project.isConnected">已接入</div>
    </div>
    <div class="project-info">
      <h3 class="project-name">{{ project.name }}</h3>
      <div class="project-details">
        <div class="detail-item">
          <span class="label">项目编号：</span>
          <span class="value">{{ project.code || '暂无' }}</span>
        </div>
        <div class="detail-item">
          <span class="label">占地面积：</span>
          <span class="value">{{ formatAreaValue(project.scale) }}m²</span>
        </div>
        <div class="detail-item">
          <span class="label">建筑功能：</span>
          <span class="value">{{ project.function }}</span>
        </div>
        <div class="detail-item">
          <span class="label">项目地址：</span>
          <span class="value">{{ project.location }}</span>
        </div>
        <div class="detail-item">
          <span class="label">建设单位：</span>
          <span class="value">{{ project.constructor }}</span>
        </div>
        <div class="detail-item">
          <span class="label">施工单位：</span>
          <span class="value">{{ project.contractor || '暂无' }}</span>
        </div>
        <div class="detail-item">
          <span class="label">设计单位：</span>
          <span class="value">{{ project.designer }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { Picture } from '@element-plus/icons-vue'
  import { getProjectImageUrl } from '@/utils/imageUtils'

  export default {
    name: 'ProjectCard',
    components: {
      Picture
    },
    props: {
      project: {
        type: Object,
        required: true
      }
    },
    emits: ['click'],
    setup() {
      // 格式化面积值显示
      const formatAreaValue = (value) => {
        if (!value || value === '暂无' || value === '') return '暂无'

        // 如果是字符串，尝试提取数字
        if (typeof value === 'string') {
          // 移除所有非数字和小数点的字符
          const numStr = value.replace(/[^\d.]/g, '')
          if (numStr === '') return '暂无'
          const numValue = parseFloat(numStr)
          return isNaN(numValue) ? '暂无' : Math.round(numValue).toString()
        }

        // 如果是数字，直接处理
        const numValue = parseFloat(value)
        return isNaN(numValue) ? '暂无' : Math.round(numValue).toString()
      }

      return {
        getProjectImageUrl,
        formatAreaValue
      }
    }
  }
</script>

<style scoped>
  .project-card {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow: hidden;
    cursor: pointer;
    background: var(--art-main-bg-color);
    border: 1px solid var(--art-border-color);
    border-radius: 12px;
    box-shadow: var(--art-box-shadow-sm);
    transition: all 0.3s ease;
  }

  .project-card:hover {
    box-shadow: var(--art-box-shadow);
    transform: translateY(-5px);
  }

  .project-image {
    position: relative;
    width: 100%;
    height: 220px;
    overflow: hidden;
  }

  .project-image-el {
    width: 100%;
    height: 100%;
  }

  .project-image-el :deep(.el-image__inner) {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
  }

  .project-card:hover .project-image-el :deep(.el-image__inner) {
    transform: scale(1.05);
  }

  .image-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    font-size: 14px;
    color: var(--art-text-gray-400);
    background: var(--art-bg-color-light);
  }

  .image-error .el-icon {
    margin-bottom: 8px;
    font-size: 32px;
  }

  .project-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    padding: 4px 10px;
    font-size: 12px;
    font-weight: bold;
    color: var(--art-text-gray-100);
    background: rgba(var(--art-success), 0.9);
    border-radius: 12px;
    box-shadow: 0 2px 6px rgba(var(--art-success), 0.4);
  }

  .project-info {
    display: flex;
    flex: 1;
    flex-direction: column;
    padding: 20px;
  }

  .project-name {
    display: -webkit-box;
    height: 50px;
    margin-bottom: 16px;
    overflow: hidden;
    font-size: 18px;
    font-weight: bold;
    line-height: 1.4;
    color: var(--art-text-gray-900);
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .project-details {
    margin-top: auto;
    font-size: 14px;
  }

  .detail-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 10px;
  }

  .detail-item:last-child {
    margin-bottom: 0;
  }

  .label {
    flex-shrink: 0;
    width: 80px;
    font-weight: 500;
    color: var(--art-text-gray-500);
  }

  .value {
    flex: 1;
    line-height: 1.5;
    color: var(--art-text-gray-700);
    text-align: left;
    word-break: break-all;
  }
</style>
