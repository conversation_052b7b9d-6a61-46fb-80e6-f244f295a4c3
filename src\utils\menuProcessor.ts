import type { MenuListType } from '@/types/menu'

/**
 * 处理菜单数据，自动忽略children中的inMainContainer属性
 * @param menuList 原始菜单列表
 * @param enableLogging 是否启用日志输出（用于调试）
 * @returns 处理后的菜单列表
 */
export function processMenuData(
  menuList: MenuListType[],
  enableLogging: boolean = false
): MenuListType[] {
  if (enableLogging) {
  }

  const processedData = menuList.map(processMenuRoute)

  if (enableLogging) {
    const childrenWithInMainContainer = countChildrenWithInMainContainer(menuList)
    const processedChildrenWithInMainContainer = countChildrenWithInMainContainer(processedData)
  }

  return processedData
}

/**
 * 统计children中包含inMainContainer属性的数量（用于调试）
 */
function countChildrenWithInMainContainer(menuList: MenuListType[]): number {
  let count = 0

  function countInRoute(route: MenuListType) {
    if (route.children) {
      route.children.forEach((child) => {
        if (child.meta.inMainContainer !== undefined) {
          count++
        }
        countInRoute(child)
      })
    }
  }

  menuList.forEach(countInRoute)
  return count
}

/**
 * 递归处理单个菜单路由
 * @param route 菜单路由
 * @returns 处理后的菜单路由
 */
function processMenuRoute(route: MenuListType): MenuListType {
  const { children, ...rest } = route

  // 如果有children，递归处理并移除children中的inMainContainer属性
  if (children?.length) {
    const processedChildren = children.map((child) => {
      const { meta, ...childRest } = child
      return {
        ...childRest,
        meta: {
          ...meta,
          inMainContainer: undefined // 子路由忽略inMainContainer属性
        },
        // 递归处理更深层的子路由
        children: child.children ? child.children.map(processMenuRoute) : undefined
      }
    })

    return {
      ...rest,
      children: processedChildren
    }
  }

  return route
}

/**
 * 检查路由是否需要在主容器中显示
 * 只有顶级路由的inMainContainer属性有效，子路由会被忽略
 * @param route 路由对象
 * @param isChild 是否是子路由
 * @returns 是否在主容器中显示
 */
export function shouldRenderInMainContainer(
  route: MenuListType,
  isChild: boolean = false
): boolean {
  // 如果是子路由，直接返回false，忽略inMainContainer属性
  if (isChild) {
    return false
  }

  // 只有顶级路由的inMainContainer属性才有效
  return Boolean(route.meta?.inMainContainer)
}

/**
 * 获取路由的显示标题
 * @param route 路由对象
 * @returns 显示标题
 */
export function getRouteDisplayTitle(route: MenuListType): string {
  return route.meta?.title || route.name
}
