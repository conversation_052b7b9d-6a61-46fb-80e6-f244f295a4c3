<template>
  <div class="carbon-overview">
    <!-- 头部筛选区域 -->
    <div class="filter-container">
      <div class="filter-row">
        <!-- 项目和楼栋选择器（多选） -->
        <MultiProjectBuildingSelector
          ref="multiProjectBuildingSelectorRef"
          v-model="multiProjectBuildingSelection"
          @change="handleMultiProjectBuildingChange"
          @project-loaded="handleProjectListLoaded"
        />
        <div class="filter-item">
          <span class="filter-label">时间</span>
          <el-radio-group
            v-model="searchForm.timeGranularity"
            class="time-granularity-buttons"
            @change="handleTimeGranularityChange"
          >
            <el-radio-button
              v-for="item in timeGranularityOptions"
              :key="item.value"
              :label="item.value"
            >
              {{ item.label }}
            </el-radio-button>
          </el-radio-group>
        </div>
        <div class="filter-item date-filter">
          <span class="filter-label">日期选择：</span>
          <div class="date-picker-group">
            <!-- 日期和月份使用范围选择器 -->
            <el-date-picker
              v-if="searchForm.timeGranularity !== 'year'"
              v-model="searchForm.dateRange"
              :type="datePickerType"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :shortcuts="dateShortcuts"
              :disabled-date="disabledDate"
              class="date-picker"
              @change="handleDateRangeChange"
            />
            <!-- 年份使用范围选择器 -->
            <el-date-picker
              v-else
              v-model="searchForm.yearRange"
              type="yearrange"
              range-separator="至"
              start-placeholder="开始年份"
              end-placeholder="结束年份"
              :disabled-date="disabledDate"
              class="date-picker"
              value-format="YYYY"
              @change="handleYearRangeChange"
            />
          </div>
        </div>
        <div class="filter-actions">
          <el-button
            type="primary"
            @click="handleSearch"
            :loading="isSearching"
            :disabled="!hasQueryConditionChanged"
          >
            查询
          </el-button>
          <el-button @click="handleReset"> 重置 </el-button>
          <el-button type="success" @click="exportAllData" :loading="isExporting">
            导出数据
          </el-button>
          <span class="auto-query-tip">提示：选择条件后将自动查询</span>
        </div>
      </div>
    </div>

    <!-- 图表内容区域 -->
    <div class="chart-section">
      <!-- 刷新按钮和说明文字 -->
      <div class="chart-header">
        <div class="chart-title">碳排放数据</div>
        <div class="chart-actions">
          <el-button size="small" @click="refreshAllCharts" :loading="isRefreshing">
            刷新数据
          </el-button>
        </div>
      </div>

      <!-- 图表内容 -->
      <div class="chart-content">
        <!-- 根据查询结果动态生成图表 -->
        <template v-if="chartResults.length > 0">
          <div class="chart-row" v-for="result in chartResults" :key="result.projectId">
            <!-- 总体碳排放图表 -->
            <div class="chart-card-wrapper">
              <component
                :is="getChartComponent(result.projectId, 'total')"
                :ref="(el) => registerChartRef(result.projectId, 'total', el)"
                :building-id="result.projectId"
                :building-name="getProjectName(result.projectId)"
                :chart-color="getChartColor(result.projectId)"
              />
            </div>

            <!-- 分项碳排放图表 -->
            <div class="chart-card-wrapper">
              <component
                :is="getChartComponent(result.projectId, 'detail')"
                :ref="(el) => registerChartRef(result.projectId, 'detail', el)"
                :building-id="result.projectId"
                :building-name="getProjectName(result.projectId)"
              />
            </div>
          </div>
        </template>
        <!-- 当没有查询结果时，显示提示信息 -->
        <template v-else>
          <el-empty>
            <template #description>
              <div v-if="isSearching">正在查询数据...</div>
              <div v-else>请选择项目并点击查询按钮以查看碳排放数据</div>
            </template>
          </el-empty>
        </template>
      </div>
    </div>

    <!-- 数据表格弹窗 -->
    <el-dialog
      v-model="tableDialog.visible"
      :title="tableDialog.title"
      width="80%"
      destroy-on-close
    >
      <div class="dialog-toolbar">
        <el-button type="primary" size="small" @click="exportTableData" :loading="isExporting">
          <el-icon><Download /></el-icon>导出数据
        </el-button>
      </div>
      <el-table
        :data="tableDialog.data"
        border
        stripe
        style="width: 100%"
        height="500px"
        v-loading="tableDialog.loading"
      >
        <el-table-column
          v-for="col in tableDialog.columns"
          :key="col.prop"
          :prop="col.prop"
          :label="col.label"
          :width="col.width"
          :fixed="col.fixed"
          align="center"
        />
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted, computed, nextTick, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import * as XLSX from 'xlsx' // 导入xlsx库
  import {
    getCarbonReport,
    transformCarbonDataToChart,
    timeTypeMap
  } from '@/api/carbon-emission/index' // 导入碳排放数据API
  import MultiProjectBuildingSelector from '@/components/MultiProjectBuildingSelector.vue'

  // 导入独立图表组件
  import GenericTotalChart from '@/components/echarts/CarbonEmission/GenericTotalChart.vue'
  import GenericDetailChart from '@/components/echarts/CarbonEmission/GenericDetailChart.vue'

  // 模拟数据函数已移除，现在直接使用API数据或显示空状态

  // 优化日期范围格式化参数，增加更精确的日期处理
  const formatDateRangeParams = (startDate, endDate, granularity = 'day') => {
    if (!startDate || !endDate) return {}

    const formatDate = (date, format) => {
      const d = new Date(date)

      switch (format) {
        case 'hour':
          return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(
            d.getDate()
          ).padStart(2, '0')} ${String(d.getHours()).padStart(2, '0')}:00`
        case 'day':
          return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(
            d.getDate()
          ).padStart(2, '0')}`
        case 'month':
          return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}`
        case 'year':
          return `${d.getFullYear()}`
        default:
          return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(
            d.getDate()
          ).padStart(2, '0')}`
      }
    }

    return {
      startDate: formatDate(startDate, granularity),
      endDate: formatDate(endDate, granularity),
      granularity
    }
  }

  // 导出XLSX文件
  const exportToXlsx = (data, fileName) => {
    if (!data) {
      console.error('导出数据格式错误')
      return
    }

    try {
      // 创建一个新的工作簿
      const workbook = XLSX.utils.book_new()

      // 处理每个数据集并添加到工作簿
      Object.keys(data).forEach((projectId) => {
        const projectData = data[projectId]
        const projectName = getProjectName(parseInt(projectId)) || `项目${projectId}`

        // 总体数据
        if (projectData.total) {
          const totalData = projectData.total.xAxis.map((date, index) => ({
            日期: date,
            碳排放量_kg_CO2: projectData.total.values[index]
          }))
          const totalWs = XLSX.utils.json_to_sheet(totalData)
          XLSX.utils.book_append_sheet(
            workbook,
            totalWs,
            `${projectName}-总体碳排放`.substring(0, 31) // Excel工作表名称限制31字符
          )
        }

        // 分项数据
        if (projectData.detail) {
          const detailData = projectData.detail.xAxis.map((date, dateIndex) => {
            const row = { 日期: date }
            projectData.detail.series.forEach((series) => {
              row[`${series.name}_kg_CO2`] = series.data[dateIndex]
            })
            return row
          })
          const detailWs = XLSX.utils.json_to_sheet(detailData)
          XLSX.utils.book_append_sheet(
            workbook,
            detailWs,
            `${projectName}-分项碳排放`.substring(0, 31) // Excel工作表名称限制31字符
          )
        }
      })

      // 导出工作簿为XLSX文件
      XLSX.writeFile(workbook, `${fileName}-${new Date().toISOString().split('T')[0]}.xlsx`)
      return true
    } catch (error) {
      console.error('导出XLSX错误:', error)
      return false
    }
  }

  // 项目和楼栋选择器相关数据（多选）
  const multiProjectBuildingSelectorRef = ref()
  const multiProjectBuildingSelection = ref({
    projectIds: [],
    buildingId: null
  })
  const selectedProjectInfoList = ref([])
  const selectedBuildingInfo = ref(null)

  // 页面状态
  const isSearching = ref(false)
  const isRefreshing = ref(false)
  const isExporting = ref(false)

  // 查询条件变化状态
  const hasQueryConditionChanged = ref(false)

  // 初始查询条件快照（用于比较是否发生变化）
  const initialQueryConditions = ref({
    projectIds: [],
    buildingId: null,
    timeGranularity: 'month',
    dateRange: [],
    yearRange: []
  })

  // 查询表单
  const searchForm = reactive({
    buildingType: [], // 选中的项目ID列表
    energyType: '',
    timeGranularity: 'month', // 默认按月查询
    dateRange: [],
    yearRange: [] // 年份范围选择值
  })

  // 时间粒度选项
  const timeGranularityOptions = [
    { value: 'day', label: '日' },
    { value: 'month', label: '月' },
    { value: 'year', label: '年' }
  ]

  // 根据选择的时间粒度动态设置日期选择器类型
  const datePickerType = computed(() => {
    switch (searchForm.timeGranularity) {
      case 'day':
        return 'daterange'
      case 'month':
        return 'monthrange'
      case 'year':
        return 'yearrange' // 年份使用范围选择
      default:
        return 'daterange'
    }
  })

  // 项目选项现在通过API动态获取，存储在 projectOptions 中

  // const energyTypeOptions = [
  //   { value: 'electricity', label: '电力' },
  //   { value: 'water', label: '水' },
  //   { value: 'gas', label: '天然气' }
  // ]

  // 日期快捷选项
  const dateShortcuts = [
    {
      text: '最近一周',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
        return [start, end]
      }
    },
    {
      text: '最近一个月',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        return [start, end]
      }
    },
    {
      text: '最近三个月',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
        return [start, end]
      }
    }
  ]

  // 禁用日期选择
  const disabledDate = (time) => {
    return time.getTime() > Date.now()
  }

  // 图表组件引用
  // const governmentTotalChart = ref(null)
  // const businessTotalChart = ref(null)
  // const governmentDetailChart = ref(null)
  // const businessDetailChart = ref(null)

  // 存储动态生成的图表引用
  const chartRefs = reactive({})

  // 存储查询结果，用于控制图表的创建和显示
  const chartResults = ref([])

  // 数据表格弹窗配置
  const tableDialog = reactive({
    visible: false,
    title: '',
    data: [],
    columns: [],
    loading: false,
    building: '',
    chartType: ''
  })

  // 注册图表引用
  const registerChartRef = (building, type, el) => {
    if (!el) return

    if (!chartRefs[building]) {
      chartRefs[building] = {}
    }

    chartRefs[building][type] = el
  }

  // 处理多项目和楼栋选择变化
  const handleMultiProjectBuildingChange = (data) => {
    selectedProjectInfoList.value = data.projectInfoList
    selectedBuildingInfo.value = data.buildingInfo

    // 更新查询表单中的项目选择
    if (data.projectIds && data.projectIds.length > 0) {
      searchForm.buildingType = data.projectIds
    } else {
      searchForm.buildingType = []
    }

    console.log('多项目楼栋选择变化:', data)

    // 检查查询条件是否变化
    checkQueryConditionChanged()

    // 自动触发查询
    if (
      data.projectIds &&
      data.projectIds.length > 0 &&
      (searchForm.dateRange?.length === 2 ||
        (searchForm.yearRange && searchForm.yearRange.length === 2))
    ) {
      nextTick(() => {
        handleSearch()
      })
    }
  }

  // 监听项目列表加载完成
  const handleProjectListLoaded = (projectList) => {
    console.log('项目列表加载完成:', projectList)
    // 当项目列表加载完成后，自动选择第一个项目
    if (projectList && projectList.length > 0) {
      const firstProject = projectList[0]
      multiProjectBuildingSelection.value.projectIds = [firstProject.id]
      selectedProjectInfoList.value = [firstProject]
      searchForm.buildingType = [firstProject.id]
      console.log('自动选择第一个项目:', firstProject)

      // 更新初始查询条件快照（包含选中的项目）
      nextTick(() => {
        updateInitialQueryConditions()
        // 由于条件已经设置，启用查询按钮
        hasQueryConditionChanged.value = true

        // 自动执行查询（如果有完整的查询条件）
        if (canAutoQuery()) {
          console.log('初始化完成，自动执行查询')
          handleSearch()
        }
      })
    }
  }

  // 根据项目ID获取项目名称
  const getProjectName = (projectId) => {
    const project = selectedProjectInfoList.value.find((p) => p.id === projectId)
    if (project) {
      return project.name
    }
    return `项目${projectId}`
  }

  // 根据项目ID获取对应的图表颜色
  const getChartColor = (projectId) => {
    // 为不同的项目分配不同的颜色
    const colors = [
      '#409EFF', // 蓝色
      '#67C23A', // 绿色
      '#E6A23C', // 黄色
      '#F56C6C', // 红色
      '#909399', // 灰色
      '#8E44AD', // 紫色
      '#16A085', // 青绿色
      '#2980B9', // 深蓝色
      '#D35400', // 橙色
      '#7F8C8D' // 石板灰
    ]

    // 使用项目ID对颜色数组长度取模，确保每个项目都有对应的颜色
    const colorIndex = (projectId - 1) % colors.length
    return colors[colorIndex]
  }

  // 根据项目和类型获取对应的图表组件
  const getChartComponent = (projectId, type) => {
    // 所有项目都使用通用图表组件
    return type === 'total' ? GenericTotalChart : GenericDetailChart
  }

  // 获取查询参数
  const getQueryParams = () => {
    const params = { ...searchForm }

    if (
      searchForm.timeGranularity === 'year' &&
      searchForm.yearRange &&
      searchForm.yearRange.length === 2
    ) {
      // 年份范围选择：如果开始和结束年份相同，只发送一个年份值
      if (searchForm.yearRange[0] === searchForm.yearRange[1]) {
        // 相同年份时，只发送一个值
        params.startDate = searchForm.yearRange[0]
        params.endDate = searchForm.yearRange[0]
      } else {
        // 不同年份时，发送范围
        params.startDate = searchForm.yearRange[0]
        params.endDate = searchForm.yearRange[1]
      }
      params.granularity = searchForm.timeGranularity
    } else if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      const { startDate, endDate, granularity } = formatDateRangeParams(
        searchForm.dateRange[0],
        searchForm.dateRange[1],
        searchForm.timeGranularity
      )

      params.startDate = startDate
      params.endDate = endDate
      params.granularity = granularity
    }

    delete params.dateRange
    delete params.yearRange
    return params
  }

  // 查询操作
  const handleSearch = async () => {
    console.log('=== 开始查询操作 ===')
    console.log('选中的项目:', searchForm.buildingType)
    console.log('查询表单:', searchForm)

    try {
      isSearching.value = true

      // 检查是否选择了项目
      if (
        !multiProjectBuildingSelection.value.projectIds ||
        multiProjectBuildingSelection.value.projectIds.length === 0
      ) {
        console.log('❌ 未选择项目')
        ElMessage.warning('请选择项目')
        isSearching.value = false
        return
      }

      // 检查日期选择是否为空
      if (searchForm.timeGranularity === 'year') {
        if (!searchForm.yearRange || searchForm.yearRange.length !== 2) {
          console.log('❌ 未选择年份范围')
          ElMessage.warning('请选择查询年份范围')
          isSearching.value = false
          return
        }
      } else {
        if (!searchForm.dateRange || searchForm.dateRange.length !== 2) {
          console.log('❌ 未选择日期范围')
          ElMessage.warning('请选择查询日期范围')
          isSearching.value = false
          return
        }
      }

      // 项目名称检查已在上面完成，这里不需要重复检查

      // 构建查询参数
      const params = getQueryParams()
      console.log('构建的查询参数:', params)

      // 检查参数中是否包含时间粒度
      if (!params.granularity) {
        console.log('❌ 缺少时间粒度参数')
        ElMessage.warning('请选择时间粒度')
        isSearching.value = false
        return
      }

      // 确定要查询的项目列表
      const projectsToQuery = searchForm.buildingType // 选中的项目ID列表

      console.log('准备查询的项目:', projectsToQuery)

      // 构建API请求参数
      const apiParams = {
        projectIdList: projectsToQuery,
        beginTime: params.startDate,
        endTime: params.endDate,
        timeType: timeTypeMap[params.granularity] || 0
      }

      let results = []

      try {
        // 调用碳排放总览数据API
        console.log('发送碳排放API请求，参数:', apiParams)
        const response = await getCarbonReport(apiParams)

        console.log('碳排放API响应:', response)

        if (response.code === 200 && response.data && response.data.length > 0) {
          console.log('✅ API调用成功，获取到数据:', response.data)

          // 转换API数据格式 - 每个项目对应一个数据项
          results = response.data.map((item, index) => {
            const projectId = projectsToQuery[index] || projectsToQuery[0]

            console.log(`项目${projectId}原始API数据:`, item)

            const chartData = transformCarbonDataToChart(item)

            console.log(`项目${projectId}转换后的图表数据:`, chartData)

            return {
              projectId,
              data: chartData
            }
          })
          console.log('✅ 转换后的所有结果:', results)
        } else {
          console.log('⚠️ API返回数据为空或无数据')
          results = []
        }
      } catch (error) {
        console.error('❌ 碳排放数据API调用失败:', error)
        ElMessage.error('数据查询失败，请稍后重试')
        results = []
      }

      // 先更新chartResults，触发图表组件的创建
      console.log('📊 更新chartResults之前:', chartResults.value)
      chartResults.value = results
      console.log('📊 更新chartResults之后:', chartResults.value)

      // 使用nextTick确保在下一个DOM更新周期同时渲染所有图表
      nextTick(() => {
        console.log('🔄 nextTick 中处理图表更新')
        results.forEach(({ projectId, data }) => {
          // 更新动态图表
          if (chartRefs[projectId]) {
            const totalChart = chartRefs[projectId].total
            const detailChart = chartRefs[projectId].detail

            if (totalChart && typeof totalChart.updateChart === 'function') {
              console.log(`📈 更新项目${projectId}的总体图表`)
              totalChart.updateChart(data.total)
            }

            if (detailChart && typeof detailChart.updateChart === 'function') {
              console.log(`📊 更新项目${projectId}的分项图表`)
              detailChart.updateChart(data.detail)
            }
          } else {
            console.warn(`⚠️ 项目${projectId}的图表引用不存在`)
          }
        })
      })

      // 查询成功后，更新初始查询条件快照
      updateInitialQueryConditions()

      console.log('✅ 查询操作完成')
    } catch (error) {
      console.error('❌ 查询失败:', error)
      ElMessage.error('查询失败')
    } finally {
      isSearching.value = false
      console.log('=== 查询操作结束 ===')
    }
  }

  // 获取时间粒度的中文标签
  const getTimeGranularityLabel = (granularity) => {
    const option = timeGranularityOptions.find((opt) => opt.value === granularity)
    return option ? option.label : '日'
  }

  // 检查查询条件是否发生变化
  const checkQueryConditionChanged = () => {
    const current = {
      projectIds: JSON.stringify(multiProjectBuildingSelection.value.projectIds),
      buildingId: multiProjectBuildingSelection.value.buildingId,
      timeGranularity: searchForm.timeGranularity,
      dateRange: JSON.stringify(searchForm.dateRange),
      yearRange: JSON.stringify(searchForm.yearRange)
    }

    const initial = {
      projectIds: initialQueryConditions.value.projectIds,
      buildingId: initialQueryConditions.value.buildingId,
      timeGranularity: initialQueryConditions.value.timeGranularity,
      dateRange: JSON.stringify(initialQueryConditions.value.dateRange),
      yearRange: JSON.stringify(initialQueryConditions.value.yearRange)
    }

    const changed = JSON.stringify(current) !== JSON.stringify(initial)
    hasQueryConditionChanged.value = changed

    console.log('查询条件变化检查:', {
      当前条件: current,
      初始条件: initial,
      是否变化: changed
    })

    return changed
  }

  // 更新初始查询条件快照
  const updateInitialQueryConditions = () => {
    initialQueryConditions.value = {
      projectIds: [...(multiProjectBuildingSelection.value.projectIds || [])],
      buildingId: multiProjectBuildingSelection.value.buildingId,
      timeGranularity: searchForm.timeGranularity,
      dateRange: [...(searchForm.dateRange || [])],
      yearRange: [...(searchForm.yearRange || [])]
    }
    hasQueryConditionChanged.value = false
    console.log('更新初始查询条件快照:', initialQueryConditions.value)
  }

  // 检查是否可以自动查询
  const canAutoQuery = () => {
    const hasProject =
      multiProjectBuildingSelection.value.projectIds &&
      multiProjectBuildingSelection.value.projectIds.length > 0
    const hasDateCondition =
      searchForm.timeGranularity === 'year'
        ? searchForm.yearRange && searchForm.yearRange.length === 2
        : searchForm.dateRange && searchForm.dateRange.length === 2

    return hasProject && hasDateCondition
  }

  // 处理时间粒度变化
  const handleTimeGranularityChange = () => {
    console.log('时间粒度变化:', searchForm.timeGranularity)
    checkQueryConditionChanged()

    // 自动触发查询
    if (canAutoQuery()) {
      nextTick(() => {
        handleSearch()
      })
    }
  }

  // 处理日期范围变化
  const handleDateRangeChange = () => {
    console.log('日期范围变化:', searchForm.dateRange)
    checkQueryConditionChanged()

    // 自动触发查询
    if (canAutoQuery()) {
      nextTick(() => {
        handleSearch()
      })
    }
  }

  // 处理年份范围选择变化
  const handleYearRangeChange = () => {
    console.log('年份范围选择变化:', searchForm.yearRange)
    checkQueryConditionChanged()

    // 自动触发查询
    if (canAutoQuery()) {
      nextTick(() => {
        handleSearch()
      })
    }
  }

  // 设置默认日期范围：前半年（6个月）
  const setDefaultDateRange = () => {
    const end = new Date()
    const start = new Date()
    start.setMonth(start.getMonth() - 6) // 前6个月
    searchForm.dateRange = [start, end]
    console.log('设置默认日期范围（前半年）:', {
      开始时间: start.toLocaleDateString(),
      结束时间: end.toLocaleDateString()
    })
  }

  // 重置表单时设置默认时间范围
  const handleReset = () => {
    // 重置项目楼栋选择
    multiProjectBuildingSelection.value = {
      projectIds: [],
      buildingId: null
    }
    selectedProjectInfoList.value = []
    selectedBuildingInfo.value = null

    // 重置查询表单
    Object.keys(searchForm).forEach((key) => {
      if (key === 'timeGranularity') {
        searchForm[key] = 'month' // 重置为默认值"月"
      } else if (key === 'buildingType') {
        searchForm[key] = []
      } else if (key === 'yearRange') {
        searchForm[key] = []
      } else if (Array.isArray(searchForm[key])) {
        searchForm[key] = []
      } else {
        searchForm[key] = ''
      }
    })

    // 设置默认时间范围为前半年（6个月）
    setDefaultDateRange()

    // 清空查询结果，隐藏图表
    chartResults.value = []

    // 重置查询条件状态
    updateInitialQueryConditions()

    ElMessage.success('已重置查询条件')
  }

  // 刷新所有图表
  const refreshAllCharts = async () => {
    // 检查是否有查询结果，如果没有则提示先查询
    if (chartResults.value.length === 0) {
      ElMessage.warning('请先选择项目并查询数据再刷新')
      return
    }
    try {
      isRefreshing.value = true
      await handleSearch() // 重新执行查询来刷新
      ElMessage.success('刷新数据成功')
    } catch (error) {
      ElMessage.error('刷新数据失败')
      console.error('刷新数据错误:', error)
    } finally {
      isRefreshing.value = false
    }
  }

  // 导出表格数据
  const exportTableData = () => {
    try {
      isExporting.value = true

      // 获取要导出的数据
      const data = tableDialog.data
      const title = tableDialog.title

      // 使用xlsx导出
      const ws = XLSX.utils.json_to_sheet(data)
      const wb = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(wb, ws, title)
      XLSX.writeFile(wb, `${title}-${new Date().toISOString().split('T')[0]}.xlsx`)

      ElMessage.success('表格数据导出成功')
    } catch (error) {
      ElMessage.error('导出表格数据失败')
      console.error('导出表格数据错误:', error)
    } finally {
      isExporting.value = false
    }
  }

  // 导出所有数据
  const exportAllData = async () => {
    // 检查是否有查询结果
    if (chartResults.value.length === 0) {
      ElMessage.warning('请先查询数据再导出')
      return
    }
    try {
      isExporting.value = true

      // 获取查询参数
      const params = getQueryParams()

      // 确定要导出的项目列表
      const projectsToExport = searchForm.buildingType // 选中的项目ID列表

      // 收集所有数据
      const allData = {}

      // 构建API请求参数
      const apiParams = {
        projectIdList: projectsToExport,
        beginTime: params.startDate,
        endTime: params.endDate,
        timeType: timeTypeMap[params.granularity] || 0
      }

      let results = []

      try {
        // 调用碳排放总览数据API
        const response = await getCarbonReport(apiParams)

        if (response.code === 200 && response.data && response.data.length > 0) {
          // 转换API数据格式 - 假设每个项目对应一个数据项
          results = response.data.map((item, index) => {
            const projectId = projectsToExport[index] || projectsToExport[0]
            const chartData = transformCarbonDataToChart(item)

            return {
              projectId,
              data: chartData
            }
          })
        } else {
          console.log('导出数据：API返回数据为空')
          ElMessage.warning('暂无数据可导出')
          return
        }
      } catch (error) {
        console.error('导出数据API调用失败:', error)
        ElMessage.error('导出数据失败，请稍后重试')
        return
      }

      // 组织数据
      results.forEach(({ projectId, data }) => {
        allData[projectId] = data
      })

      // 使用xlsx导出
      const success = exportToXlsx(allData, '碳排放数据')

      if (success) {
        ElMessage.success('所有数据导出成功')
      } else {
        ElMessage.error('导出数据失败')
      }
    } catch (error) {
      ElMessage.error('导出所有数据失败')
      console.error('导出所有数据错误:', error)
    } finally {
      isExporting.value = false
    }
  }

  // 监听年份范围选择变化，自动触发查询
  watch(
    () => searchForm.yearRange,
    (newVal, oldVal) => {
      if (JSON.stringify(newVal) !== JSON.stringify(oldVal) && newVal && newVal.length === 2) {
        console.log('年份范围选择变化:', newVal)
        checkQueryConditionChanged()
        // 年份范围变化时自动触发查询
        if (canAutoQuery()) {
          nextTick(() => {
            handleSearch()
          })
        }
      }
    },
    { deep: true }
  )

  // 监听查询条件变化（保留用于其他可能的监听需求）
  // 注意：主要的自动查询逻辑已经移到各个处理函数中

  // 页面加载完成后初始化数据
  onMounted(async () => {
    // 设置默认时间范围为本月到前6个月
    setDefaultDateRange()

    // 初始化查询条件快照
    updateInitialQueryConditions()

    // 项目列表将通过 ProjectBuildingSelector 组件自动加载
    // 当项目列表加载完成后，会自动选择第一个项目并执行查询
  })
</script>

<style lang="scss" scoped>
  .carbon-overview {
    min-height: calc(100vh - 60px);
    padding: 16px;
    background-color: var(--art-bg-color); // 修改背景色为主题变量

    .filter-container {
      padding: 16px;
      margin-bottom: 16px;
      background-color: var(--art-main-bg-color); // 修改背景色为主题变量
      border-radius: 8px;
      box-shadow: var(--art-root-card-box-shadow); // 修改阴影为主题变量

      .filter-row {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        align-items: center;
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .filter-item {
        display: flex;
        align-items: center;
        margin-right: 0;

        .filter-label {
          margin-right: 8px;
          font-size: 14px;
          color: var(--art-text-gray-600); // 修改文字颜色为主题变量
          white-space: nowrap;
        }

        .filter-select {
          min-width: 300px;
          max-width: 500px;
        }

        .time-granularity-buttons {
          margin-left: 8px;
        }
      }

      .date-filter {
        .date-picker-group {
          display: flex;

          .date-picker {
            width: 100%;
            max-width: 300px;
          }
        }
      }

      .filter-actions {
        display: flex;
        gap: 10px;
        align-items: center;
        justify-content: flex-end;
        margin-left: auto;

        .auto-query-tip {
          margin-left: 10px;
          font-size: 12px;
          color: var(--art-text-gray-500);
        }
      }
    }

    .chart-section {
      padding: 16px;
      background-color: var(--art-main-bg-color); // 修改背景色为主题变量
      border-radius: 8px;
      box-shadow: var(--art-root-card-box-shadow); // 修改阴影为主题变量

      .chart-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 16px;
        margin-bottom: 20px;
        border-bottom: 1px solid var(--art-border-color); // 修改边框颜色为主题变量

        .chart-title {
          font-size: 18px;
          font-weight: 600;
          color: var(--art-text-gray-800); // 修改文字颜色为主题变量
        }

        .chart-actions {
          display: flex;
          gap: 10px;
        }
      }

      .chart-content {
        margin-bottom: 20px;

        .chart-row {
          display: flex;
          gap: 15px;
          margin-bottom: 15px;

          &:last-child {
            margin-bottom: 0;
          }

          .chart-card-wrapper {
            position: relative;
            flex: 1;
          }
        }
      }
    }

    .dialog-toolbar {
      display: flex;
      justify-content: flex-end;
      margin-bottom: 16px;
    }

    :deep(.el-table) {
      .el-table__header-wrapper th {
        font-weight: 600;
        color: var(--art-text-gray-700); // 修改表头文字颜色为主题变量
        background-color: var(--art-gray-100); // 修改表头背景色为主题变量
      }

      .hover-row td {
        background-color: var(--art-hoverColor) !important; // 修改hover背景色为主题变量
      }
    }
  }

  @media screen and (width <= 1200px) {
    .carbon-overview {
      .filter-container {
        .filter-item {
          margin-right: 12px;

          .filter-select {
            width: 150px;
          }
        }

        .date-filter {
          .date-picker {
            width: 280px;
          }
        }
      }

      .chart-section {
        .chart-content {
          .chart-row {
            flex-direction: column;

            .chart-card-wrapper {
              margin-right: 0;
              margin-bottom: 16px;

              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
      }
    }
  }
</style>
