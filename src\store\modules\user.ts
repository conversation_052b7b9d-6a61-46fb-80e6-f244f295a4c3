import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { LanguageEnum } from '@/enums/appEnum'
import { router, setPageTitle } from '@/router'
import { UserInfo } from '@/types/store'
import { useSettingStore } from './setting'
import { useWorktabStore } from './worktab'
import { getSysStorage } from '@/utils/storage'
import { MenuListType } from '@/types/menu'

// 用户
export const useUserStore = defineStore('userStore', () => {
  const language = ref(LanguageEnum.ZH)
  const isLogin = ref(false)
  const isLock = ref(false)
  const lockPassword = ref('')
  const info = ref<Partial<UserInfo>>({})
  const searchHistory = ref<MenuListType[]>([])
  const accessToken = ref('')
  const refreshToken = ref('')

  const getUserInfo = computed(() => info.value)
  const getSettingState = computed(() => useSettingStore().$state)
  const getWorktabState = computed(() => useWorktabStore().$state)

  const initState = () => {
    const sys = getSysStorage()

    if (sys) {
      const {
        info: storedInfo,
        isLogin: storedIsLogin,
        language: storedLanguage,
        searchHistory: storedSearchHistory,
        isLock: storedIsLock,
        lockPassword: storedLockPassword,
        refreshToken: storedRefreshToken
      } = sys.user || {}

      info.value = storedInfo || {}
      isLogin.value = storedIsLogin || false
      isLock.value = storedIsLock || false
      language.value = storedLanguage || LanguageEnum.ZH
      searchHistory.value = storedSearchHistory || []
      lockPassword.value = storedLockPassword || ''
      refreshToken.value = storedRefreshToken || ''
      accessToken.value = localStorage.getItem('accessToken') || ''
    }
  }

  const saveUserData = () => {
    saveStoreStorage({
      user: {
        info: info.value,
        isLogin: isLogin.value,
        language: language.value,
        isLock: isLock.value,
        lockPassword: lockPassword.value,
        searchHistory: searchHistory.value,
        refreshToken: refreshToken.value,
        worktab: getWorktabState.value,
        setting: getSettingState.value
      }
    })
  }

  const setUserInfo = (newInfo: UserInfo) => {
    info.value = newInfo
  }

  const setLoginStatus = (status: boolean) => {
    isLogin.value = status
  }

  const setLanguage = (lang: LanguageEnum) => {
    setPageTitle(router.currentRoute.value)
    language.value = lang
  }

  const setSearchHistory = (list: MenuListType[]) => {
    searchHistory.value = list
  }

  const setLockStatus = (status: boolean) => {
    isLock.value = status
  }

  const setLockPassword = (password: string) => {
    lockPassword.value = password
  }

  const setToken = (newAccessToken: string, newRefreshToken?: string) => {
    accessToken.value = newAccessToken
    if (newRefreshToken) {
      refreshToken.value = newRefreshToken
    }

    // 增强的token存储机制，确保存储成功
    try {
      // 1. 先存储到localStorage
      localStorage.setItem('accessToken', newAccessToken)

      // 2. 验证存储是否成功
      const storedToken = localStorage.getItem('accessToken')
      if (storedToken !== newAccessToken) {
        console.error('Token存储验证失败，重试存储')
        localStorage.setItem('accessToken', newAccessToken)
      }

      // 3. 保存用户数据到版本化存储
      saveUserData()

      // 4. 再次验证版本化存储
      const sysData = getSysStorage()
      if (!sysData?.user || sysData.user.isLogin !== isLogin.value) {
        console.warn('版本化存储可能失败，重试保存')
        setTimeout(() => saveUserData(), 100)
      }

      console.log('✓ Token存储完成，验证通过')
    } catch (error) {
      console.error('Token存储过程中出现错误:', error)
      // 重试存储
      setTimeout(() => {
        try {
          localStorage.setItem('accessToken', newAccessToken)
          saveUserData()
        } catch (retryError) {
          console.error('Token存储重试失败:', retryError)
        }
      }, 200)
    }
  }

  const logOut = () => {
    setTimeout(() => {
      info.value = {}
      isLogin.value = false
      isLock.value = false
      lockPassword.value = ''
      accessToken.value = ''
      refreshToken.value = ''
      localStorage.removeItem('accessToken')
      useWorktabStore().opened = []
      saveUserData()
      sessionStorage.removeItem('iframeRoutes')
      router.push('/login')
    }, 300)
  }

  // 检查token是否有效（增强版）
  const checkTokenValid = () => {
    try {
      const localToken = localStorage.getItem('accessToken')
      const storeToken = accessToken.value

      // 优先使用localStorage中的token
      const token = localToken || storeToken

      if (!token && isLogin.value) {
        // 在外部登录场景下，给一个短暂的缓冲时间
        const isExternalLogin = localStorage.getItem('externalLogin') === 'true'
        const externalLoginTime = localStorage.getItem('externalLoginTime')

        if (isExternalLogin && externalLoginTime) {
          const timeDiff = Date.now() - parseInt(externalLoginTime)
          // 如果是外部登录且在5秒内，暂时不登出，给token存储更多时间
          if (timeDiff < 5000) {
            console.warn('外部登录中，暂缓token验证失败处理')
            return true
          }
        }

        console.warn('Token丢失，登录状态不一致，执行登出')
        logOut()
        return false
      }

      // 如果store中没有token但localStorage有，同步到store
      if (localToken && !storeToken) {
        accessToken.value = localToken
        console.log('已同步localStorage中的token到store')
      }

      return !!token
    } catch (error) {
      console.error('Token验证过程中出现错误:', error)
      return false
    }
  }

  return {
    language,
    isLogin,
    isLock,
    lockPassword,
    info,
    searchHistory,
    accessToken,
    refreshToken,
    getUserInfo,
    getSettingState,
    getWorktabState,
    initState,
    saveUserData,
    setUserInfo,
    setLoginStatus,
    setLanguage,
    setSearchHistory,
    setLockStatus,
    setLockPassword,
    setToken,
    logOut,
    checkTokenValid
  }
})

// 数据持久化存储
function saveStoreStorage<T>(newData: T) {
  const version = import.meta.env.VITE_VERSION
  initVersion(version)
  const vs = localStorage.getItem('version') || version
  const storedData = JSON.parse(localStorage.getItem(`sys-v${vs}`) || '{}')

  // 合并新数据与现有数据
  const mergedData = { ...storedData, ...newData }
  localStorage.setItem(`sys-v${vs}`, JSON.stringify(mergedData))
}

// 初始化版本
function initVersion(version: string) {
  const vs = localStorage.getItem('version')
  if (!vs) {
    localStorage.setItem('version', version)
  }
}
