<template>
  <div class="project-list page-content">
    <!-- 项目表格 -->
    <div class="table-section">
      <div class="table-operation">
        <el-button type="primary" @click="openAddProject" :icon="Plus">新增项目</el-button>
      </div>
      <ArtTable
        :data="projects"
        :loading="loading"
        row-key="id"
        :border="false"
        :highlight-current-row="false"
        :total="total"
        v-model:current-page="pagination.pageNum"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        @row-click="handleRowClick"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        index
        class="project-table"
      >
        <el-table-column prop="code" label="项目编号" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.code || '暂无' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="项目名称" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="project-name-cell">
              <span class="project-name">{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="function" label="建筑功能" show-overflow-tooltip />
        <el-table-column prop="scale" label="占地面积" align="center">
          <template #default="{ row }"> {{ formatAreaValue(row.scale) }}m² </template>
        </el-table-column>
        <el-table-column prop="location" label="项目地址" show-overflow-tooltip />
        <el-table-column prop="constructor" label="建设单位" show-overflow-tooltip />
        <el-table-column prop="designer" label="设计单位" show-overflow-tooltip />
        <el-table-column prop="isConnected" label="接入状态" align="center">
          <template #default="{ row }">
            <el-tag :type="row.isConnected ? 'success' : 'warning'">
              {{ row.isConnected ? '已接入' : '未接入' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" fixed="right">
          <template #default="scope">
            <div class="operation-btns">
              <el-button
                link
                size="small"
                @click="handleDetail(scope.row)"
                title="查看"
                class="operation-btn view-btn"
              >
                <el-icon><Search /></el-icon>
              </el-button>
              <el-button
                link
                size="small"
                @click="handleEdit(scope.row)"
                title="编辑"
                class="operation-btn edit-btn"
              >
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button
                link
                size="small"
                @click="handleDelete(scope.row)"
                title="删除"
                class="operation-btn delete-btn"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
      </ArtTable>
    </div>

    <!-- 项目详情对话框 -->
    <el-dialog
      v-model="showProjectDetail"
      :title="getDialogTitle()"
      width="80%"
      destroy-on-close
      top="5vh"
      :show-close="true"
      class="project-detail-dialog"
    >
      <ProjectDetail
        v-if="showProjectDetail"
        :project="selectedProject"
        :mode="detailMode"
        @update="handleProjectUpdate"
        @refresh="handleProjectFormSuccess"
        @close="showProjectDetail = false"
        @startEdit="handleStartEdit"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, reactive } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Plus, Search, Edit, Delete } from '@element-plus/icons-vue'
  import ProjectDetail from '@/components/ProjectDetail.vue'
  import ArtTable from '@/components/core/tables/ArtTable.vue'
  import { getProjectList, deleteProject } from '@/api/projectApi'
  import { mapProjectList } from '@/utils/projectMapper'

  // 项目数据
  const projects = ref<any[]>([])
  const loading = ref(false)
  const total = ref(0)

  // 分页信息
  const pagination = reactive({
    pageNum: 1,
    pageSize: 20
  })

  // 项目详情相关状态
  const showProjectDetail = ref(false)
  const selectedProject = ref<any>(null)
  const detailMode = ref('view') // 'view', 'edit', 'add'

  // 格式化面积值显示
  const formatAreaValue = (value: any) => {
    if (!value || value === '暂无' || value === '') return '暂无'

    // 如果是字符串，尝试提取数字
    if (typeof value === 'string') {
      // 移除所有非数字和小数点的字符
      const numStr = value.replace(/[^\d.]/g, '')
      if (numStr === '') return '暂无'
      const numValue = parseFloat(numStr)
      return isNaN(numValue) ? '暂无' : Math.round(numValue).toString()
    }

    // 如果是数字，直接处理
    const numValue = parseFloat(value)
    return isNaN(numValue) ? '暂无' : Math.round(numValue).toString()
  }

  // 获取项目列表数据
  const fetchProjects = async () => {
    try {
      loading.value = true
      const response = await getProjectList({
        pageNum: pagination.pageNum,
        pageSize: pagination.pageSize
      })

      if (response.code === 200) {
        // 使用映射函数转换数据格式
        projects.value = mapProjectList(response.rows)
        total.value = response.total
      } else {
        ElMessage.error(response.msg || '获取项目列表失败')
      }
    } catch (error: any) {
      console.error('获取项目列表失败:', error)

      // 处理认证失败的情况
      if (error.response?.status === 401) {
        ElMessage.error('认证失败，请重新登录')
      } else {
        ElMessage.error('获取项目列表失败，请稍后重试')
      }
    } finally {
      loading.value = false
    }
  }

  // 分页处理
  const handleSizeChange = (size: number) => {
    pagination.pageSize = size
    pagination.pageNum = 1
    fetchProjects()
  }

  const handleCurrentChange = (page: number) => {
    pagination.pageNum = page
    fetchProjects()
  }

  // 表格行点击
  const handleRowClick = (row: any) => {
    openProjectView(row)
  }

  // 打开项目详情（查看模式）
  const openProjectView = (project: any) => {
    selectedProject.value = project
    detailMode.value = 'view'
    showProjectDetail.value = true
  }

  // 查看项目详情
  const handleDetail = (project: any) => {
    openProjectView(project)
  }

  // 编辑项目详情
  const handleEdit = (project: any) => {
    selectedProject.value = project
    detailMode.value = 'edit'
    showProjectDetail.value = true
  }

  // 删除项目
  const handleDelete = async (project: any) => {
    try {
      await ElMessageBox.prompt(
        `请输入项目名称"${project.name}"以确认删除。删除后无法恢复！`,
        '删除确认',
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning',
          confirmButtonClass: 'el-button--danger',
          inputPlaceholder: '请输入项目名称',
          inputValidator: (value) => {
            if (!value) {
              return '请输入项目名称'
            }
            if (value !== project.name) {
              return '输入的项目名称不正确'
            }
            return true
          },
          inputErrorMessage: '项目名称不匹配'
        }
      )

      // 如果输入正确，执行删除
      const response = await deleteProject(project.id)
      if (response.code === 200) {
        ElMessage.success('项目删除成功')
        // 重新获取项目列表
        fetchProjects()
      } else {
        ElMessage.error(response.msg || '项目删除失败')
      }
    } catch (error: any) {
      if (error !== 'cancel') {
        console.error('删除项目失败:', error)
        ElMessage.error('删除项目失败，请稍后重试')
      }
    }
  }

  // 打开新增项目对话框
  const openAddProject = () => {
    // 创建一个空的项目对象用于新增
    selectedProject.value = createEmptyProject()
    detailMode.value = 'add'
    showProjectDetail.value = true
  }

  // 创建空的项目对象
  const createEmptyProject = () => {
    return {
      id: null,
      code: '', // 项目编号
      name: '',
      image: '',
      scale: '',
      function: '',
      location: '',
      constructor: '',
      contractor: '',
      designer: '',
      isConnected: false,
      areaId: '', // 区域ID
      energyId: null, // 能耗ID
      details: {
        designConcept: '',
        highlights: [],
        performance: []
      },
      coordinates: {
        longitude: 119.827884,
        latitude: 31.346137
      },
      rawData: {
        code: '', // 项目编号
        projectScale: '',
        projectArea: '',
        projectFunction: '',
        projectAddr: '',
        orgComp: '',
        designComp: '',
        sgComp: '',
        designRemark: '',
        addr: '',
        longitude: '119.827884',
        latitude: '31.346137',
        pic: '',
        picRemark: '',
        areaId: '', // 区域ID
        energyId: null, // 能耗ID
        zsProjectPerformanceList: [],
        zsProjectFeatureList: []
      }
    }
  }

  // 处理开始编辑
  const handleStartEdit = () => {
    detailMode.value = 'edit'
  }

  // 处理项目更新
  const handleProjectUpdate = (updatedProject: any) => {
    const index = projects.value.findIndex((p: any) => p.id === updatedProject.id)
    if (index !== -1) {
      projects.value[index] = updatedProject
    }
    // 更新成功后切换回查看模式
    detailMode.value = 'view'
  }

  // 处理项目表单提交成功
  const handleProjectFormSuccess = () => {
    // 重新获取项目列表
    fetchProjects()
  }

  // 获取对话框标题
  const getDialogTitle = () => {
    switch (detailMode.value) {
      case 'add':
        return '新增项目'
      case 'view':
        return `查看项目 - ${selectedProject.value?.name || ''}`
      default:
        return `编辑项目 - ${selectedProject.value?.name || ''}`
    }
  }

  // 页面加载时获取数据
  onMounted(() => {
    fetchProjects()
  })
</script>

<style scoped lang="scss">
  .project-list {
    padding: 20px;

    // 表格区域样式
    .table-section {
      padding: 20px;
      background-color: var(--art-main-bg-color);
      border: 1px solid var(--art-border-color);
      border-radius: 4px;
      box-shadow: var(--art-box-shadow-xs);

      // 表格操作区域
      .table-operation {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 15px;

        .el-button {
          margin-right: 10px;
        }
      }

      // 确保ArtTable组件有足够的显示空间
      :deep(.art-table) {
        height: auto;
        min-height: 400px;
      }

      .project-table {
        .project-name-cell {
          .project-name {
            font-weight: 500;
            color: var(--art-text-gray-900);
          }
        }

        :deep(.el-table__row) {
          cursor: pointer;
          transition: background-color 0.2s ease;

          &:hover {
            background-color: var(--art-bg-color-light);
          }
        }
      }

      // 自定义操作按钮样式
      .operation-btns {
        display: flex;
        gap: 12px;
        justify-content: center;
      }

      .operation-btn {
        width: 32px !important;
        height: 32px !important;
        padding: 6px !important;
        margin: 0 !important;
        line-height: 1 !important; /* 确保图标垂直居中 */
        border: none !important;
        border-radius: 4px !important; /* 方形边框 */
        box-shadow: 0 2px 4px rgb(0 0 0 / 10%) !important; /* 添加阴影效果 */
        transition: all 0.3s ease !important; /* 添加过渡效果 */
      }

      .operation-btn:hover {
        box-shadow: 0 4px 8px rgb(0 0 0 / 15%) !important; /* 悬停时增强阴影 */
        transform: translateY(-2px) !important; /* 悬停时上移效果 */
      }

      .view-btn {
        background-color: #e6f7ff !important; /* 浅蓝色背景 */
      }

      .view-btn .el-icon {
        font-size: 16px;
        color: #409eff !important; /* 蓝色图标 */
      }

      .edit-btn {
        background-color: #e6f7ff !important; /* 浅蓝色背景 */
      }

      .edit-btn .el-icon {
        font-size: 16px;
        color: #409eff !important; /* 蓝色图标 */
      }

      .delete-btn {
        background-color: #fff1f0 !important; /* 浅红色背景 */
      }

      .delete-btn .el-icon {
        font-size: 16px;
        color: #f56c6c !important; /* 红色图标 */
      }

      // 修复el-table__empty-block高度不断增长问题
      .project-table :deep(.el-table__empty-block) {
        height: auto !important;
        min-height: 60px;
        max-height: 400px;
        overflow: hidden;
      }

      // 确保空表格状态下的布局稳定
      .project-table :deep(.el-table__body-wrapper) {
        height: auto !important;
        min-height: 200px;
      }
    }
  }

  /* 对话框样式定制 */
  :deep(.project-detail-dialog .el-dialog__header) {
    padding: 20px 24px;
    margin: 0;
    border-bottom: 1px solid var(--art-border-color);
  }

  :deep(.project-detail-dialog .el-dialog__body) {
    padding: 0;
  }

  :deep(.project-detail-dialog .el-dialog__title) {
    font-size: 18px;
    font-weight: bold;
  }
</style>
