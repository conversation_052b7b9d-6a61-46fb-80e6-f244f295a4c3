<template>
  <div class="carbon-statistics">
    <!-- 筛选和报表类型选择 -->
    <el-card class="filter-card" shadow="never">
      <div class="filter-section">
        <!-- 第一行：项目选择、日期选择、导出按钮 -->
        <div class="top-filter-row">
          <div class="left-section">
            <ProjectSelector
              ref="projectSelectorRef"
              v-model="selectedProjectId"
              placeholder="请选择项目"
              class="center-select"
              style="width: 400px"
              @change="handleProjectChange"
              @loaded="handleProjectListLoaded"
            />

            <el-date-picker
              v-model="selectedYear"
              type="year"
              placeholder="选择年份"
              format="YYYY"
              value-format="YYYY"
              :disabled-date="disabledDate"
              class="date-picker"
              @change="handleYearChange"
            />
          </div>

          <div class="right-section">
            <el-button type="primary" class="export-btn" @click="exportData">
              <el-icon><Download /></el-icon> 导出
            </el-button>
          </div>
        </div>

        <!-- 第二行：报表类型按钮 -->
        <div class="bottom-filter-row">
          <div class="report-type-section">
            <el-button
              v-for="(item, index) in reportTypes"
              :key="index"
              :class="['report-type-btn', { active: currentReportType === item.value }]"
              @click="changeReportType(item.value)"
            >
              {{ item.label }}
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 统计表格 -->
    <el-card class="statistics-table-card" shadow="never">
      <h3 class="table-title">{{ tableTitle }}</h3>

      <!-- 图表组件 -->
      <!-- <statistics-chart v-if="showChart" :chart-data="tableData" /> -->

      <div class="table-container">
        <!-- 使用ArtTable替换el-table -->
        <ArtTable
          :data="tableData"
          :loading="loading"
          :border="false"
          :highlight-current-row="false"
          :pagination="false"
          class="statistics-table"
        >
          <el-table-column prop="time" label="时间" width="80" align="center" />
          <el-table-column prop="electricity" label="电 (kWh)" align="center" />
          <el-table-column prop="water" label="水 (m³)" align="center" />
          <el-table-column prop="naturalGas" label="天然气 (m³)" align="center" />
          <el-table-column prop="renewable" label="可再生能源 (kWh)" align="center" />
          <el-table-column prop="standardCoal" label="标准煤合计 (kgce)" align="center" />
          <el-table-column prop="carbonEmission" label="碳排放合计 (kgCO₂)" align="center" />
        </ArtTable>
      </div>

      <div class="description">
        <p>说明：</p>
        <p>
          1.
          根据DB32/T4001-2021《江苏省公共机构能耗定额及计算方法》能源折标煤参考系数：电力(等价值)：0.3kgce/kWh;
          水：不折标; 天然气：1.33kgce/m³;
        </p>
        <p>
          2.
          根据生态环境部办公厅发布的《关于做好2023-2025年发电行业企业温室气体排放报告管理有关工作的通知》中2022年度全国电网平均排放因子为0.5703t
          CO₂/MWh。
        </p>
        <p>
          3.
          各类能源碳排放参考系数根据《综合能耗计算通则》（GB/T2589）与《省级温室气体清单编制指南》(发改办气候[2011]1041号)计算得出：天然气的碳排放系数为2.1622kgCO₂/m³。
        </p>
      </div>
    </el-card>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted, watch, nextTick } from 'vue'
  import { ElMessage } from 'element-plus'
  import { Download } from '@element-plus/icons-vue'
  import ProjectSelector from '@/components/ProjectSelector.vue'
  import {
    getCarbonHistoryReport,
    getCarbonYearReport,
    getCarbonItemReport,
    getCarbonQuarterReport
  } from '@/api/carbon-emission-management/statisticalReportApi'

  // 项目选择相关数据
  const selectedProjectId = ref(null) // 初始为空，等待获取项目列表后设置第一个
  const selectedProjectInfo = ref(null)
  const projectSelectorRef = ref()

  // 状态数据
  const selectedYear = ref('2024') // 改为单个年份选择
  const currentReportType = ref('energyAnnual')
  const loading = ref(false)

  // 报表类型
  const reportTypes = [
    { label: '历年能耗', value: 'historicalEnergy' },
    { label: '能耗年报', value: 'energyAnnual' },
    { label: '分项用电年报', value: 'electricityItemsAnnual' },
    { label: '累计年报', value: 'cumulativeAnnual' }
  ]

  // 计算属性
  const tableTitle = computed(() => {
    const projectName = selectedProjectInfo.value?.name || '项目'
    if (selectedYear.value) {
      return `${projectName}${selectedYear.value}年能耗（碳排放）统计表`
    }
    return `${projectName}能耗（碳排放）统计表`
  })

  // 表格数据
  const tableData = ref([])

  // 处理项目变化
  const handleProjectChange = (projectInfo) => {
    selectedProjectInfo.value = projectInfo
    console.log('选中的项目:', projectInfo)
    // 项目变化时重新加载数据
    if (selectedYear.value) {
      loadReportData(currentReportType.value)
    }
  }

  // 监听项目选择器组件的项目列表加载完成
  const handleProjectListLoaded = (projectList) => {
    // 当项目列表加载完成后，自动选择第一个项目并发请求
    if (projectList && projectList.length > 0) {
      const firstProject = projectList[0]
      selectedProjectId.value = firstProject.id
      selectedProjectInfo.value = firstProject
      console.log('自动选择第一个项目:', firstProject)
      // 自动加载数据
      nextTick(() => {
        if (selectedYear.value) {
          loadReportData(currentReportType.value)
        }
      })
    }
  }

  // 方法
  const disabledDate = (time) => {
    return time.getTime() > Date.now()
  }

  const changeReportType = (type) => {
    currentReportType.value = type
    // 直接加载数据，不需要模拟延迟
    loadReportData(type)
  }

  const loadReportData = async (type) => {
    // 如果没有选择项目，不加载数据
    if (!selectedProjectId.value) {
      console.log('没有选择项目，不加载数据')
      tableData.value = []
      return
    }

    // 如果没有选择年份，使用默认值
    if (!selectedYear.value) {
      console.log('没有选择年份')
      return
    }

    loading.value = true

    try {
      const params = {
        projectId: selectedProjectId.value,
        beginTime: selectedYear.value,
        endTime: selectedYear.value
      }

      console.log(`加载${type}类型的报表数据，参数:`, params)

      let response
      if (type === 'historicalEnergy') {
        response = await getCarbonHistoryReport(params)
      } else if (type === 'energyAnnual') {
        response = await getCarbonYearReport(params)
      } else if (type === 'electricityItemsAnnual') {
        response = await getCarbonItemReport(params)
      } else if (type === 'cumulativeAnnual') {
        response = await getCarbonQuarterReport(params)
      }

      console.log('API响应:', response)

      if (response && response.code === 200 && response.data) {
        // 转换API数据为表格数据格式
        tableData.value = convertApiDataToTableData(response.data, type)
      } else {
        ElMessage.warning(response?.msg || '获取数据失败')
        tableData.value = []
      }
    } catch (error) {
      console.error('加载报表数据失败:', error)
      ElMessage.error('加载数据失败，请稍后重试')
      tableData.value = []
    } finally {
      loading.value = false
    }
  }

  // 数据转换函数
  const convertApiDataToTableData = (apiData, type) => {
    if (!Array.isArray(apiData)) {
      return []
    }

    if (type === 'historicalEnergy') {
      // 历年能耗数据转换
      return apiData.map((item, index) => ({
        time: `${parseInt(selectedYear.value) + index}年`,
        electricity: item.elecQuantity?.toFixed(2) || '0',
        water: item.waterQuantity?.toFixed(2) || '0',
        naturalGas: item.gasQuantity?.toFixed(2) || '0',
        renewable: item.reableQuantity?.toFixed(2) || '0',
        standardCoal: item.ceQuantity?.toFixed(2) || '0',
        carbonEmission: item.co2Quantity?.toFixed(2) || '0'
      }))
    } else if (type === 'energyAnnual') {
      // 能耗年报数据转换（月度数据）
      return apiData.map((item, index) => ({
        time: item.dayTime ? `${item.dayTime}月` : `${index + 1}月`,
        electricity: item.elecQuantity?.toFixed(2) || '0',
        water: item.waterQuantity?.toFixed(2) || '0',
        naturalGas: item.gasQuantity?.toFixed(2) || '0',
        renewable: item.reableQuantity?.toFixed(2) || '0',
        standardCoal: item.ceQuantity?.toFixed(2) || '0',
        carbonEmission: item.co2Quantity?.toFixed(2) || '0'
      }))
    } else if (type === 'electricityItemsAnnual') {
      // 分项用电年报数据转换
      return apiData.map((item) => ({
        time: item.itemName || '未知项目',
        electricity: item.elecQuantity?.toFixed(2) || '0',
        water: '-',
        naturalGas: '-',
        renewable: '-',
        standardCoal: item.ceQuantity?.toFixed(2) || '0',
        carbonEmission: item.co2Quantity?.toFixed(2) || '0'
      }))
    } else if (type === 'cumulativeAnnual') {
      // 累计年报数据转换（季度数据）
      return apiData.map((item, index) => ({
        time: item.dayTime ? `${item.dayTime}季度` : `${index + 1}季度`,
        electricity: item.elecQuantity?.toFixed(2) || '0',
        water: item.waterQuantity?.toFixed(2) || '0',
        naturalGas: item.gasQuantity?.toFixed(2) || '0',
        renewable: item.reableQuantity?.toFixed(2) || '0',
        standardCoal: item.ceQuantity?.toFixed(2) || '0',
        carbonEmission: item.co2Quantity?.toFixed(2) || '0'
      }))
    }

    return []
  }

  const exportData = () => {
    ElMessage.success('导出成功')
    // 实际项目中，这里应该调用导出API
  }

  // 监听年份变化
  watch(selectedYear, () => {
    if (selectedProjectId.value && selectedYear.value) {
      loadReportData(currentReportType.value)
    }
  })

  // 处理年份变化
  const handleYearChange = () => {
    if (selectedProjectId.value && selectedYear.value) {
      loadReportData(currentReportType.value)
    }
  }

  // 生命周期钩子
  onMounted(() => {
    // 页面初始化时不加载数据，等待项目选择器加载完成后自动加载
  })
</script>

<style lang="scss" scoped>
  .carbon-statistics {
    width: 100%;
    padding: 20px;
    padding-bottom: 40px;
    margin: 0 auto;
    color: var(--art-text-gray-800);
    background-color: var(--art-main-bg-color);

    .statistics-header {
      margin-bottom: 30px;
      text-align: center;

      .title {
        position: relative;
        display: inline-block;
        padding-bottom: 10px;
        font-size: 26px;
        font-weight: 600;
        color: var(--art-text-gray-900);
      }
    }

    .filter-card,
    .statistics-table-card {
      width: 100%;
      margin-right: auto;
      margin-bottom: 24px;
      margin-left: auto;
      background-color: var(--art-main-bg-color);
      border-color: var(--art-root-card-border-color);
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 16px 0 rgb(0 0 0 / 8%);
      }
    }

    .filter-section {
      display: flex;
      flex-direction: column;
      padding: 15px 20px;

      .top-filter-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        padding: 5px 0;
        margin-bottom: 15px;

        .left-section {
          display: flex;
          gap: 15px;
          align-items: center;

          .date-picker {
            width: 200px;
          }
        }

        .right-section {
          display: flex;
          align-items: center;

          .export-btn {
            display: flex;
            gap: 5px;
            align-items: center;
            padding: 8px 16px;
            font-weight: 500;
            border-radius: 6px;
            box-shadow: 0 2px 6px rgba(var(--art-primary), 0.25);
            transition: all 0.25s ease;

            &:hover {
              box-shadow: 0 4px 8px rgba(var(--art-primary), 0.35);
              transform: translateY(-2px);
            }

            &:active {
              transform: translateY(0);
            }
          }
        }
      }

      .bottom-filter-row {
        display: flex;
        justify-content: center;
        width: 100%;

        .report-type-section {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;
          justify-content: center;

          .report-type-btn {
            padding: 8px 16px;
            font-weight: 500;
            color: var(--art-text-gray-700);
            background-color: var(--art-bg-color);
            border: 1px solid var(--art-border-color);
            border-radius: 6px;
            transition: all 0.25s ease;

            &:hover:not(.active) {
              color: rgba(var(--art-primary), 1);
              background-color: rgba(var(--art-primary), 0.08);
              border-color: rgba(var(--art-primary), 0.3);
            }

            &.active {
              color: var(--art-text-gray-100);
              background-color: rgba(var(--art-primary), 1);
              border-color: rgba(var(--art-primary), 1);
              box-shadow: 0 2px 8px rgba(var(--art-primary), 0.4);
            }
          }
        }
      }
    }

    .statistics-table-card {
      min-height: 600px; /* 固定最小高度，防止塌陷 */

      .table-title {
        padding-top: 6px;
        margin-bottom: 24px;
        font-size: 20px;
        font-weight: 500;
        color: var(--art-text-gray-900);
        text-align: center;
      }

      .table-container {
        display: flex;
        justify-content: center;
        min-height: 400px; /* 固定表格容器最小高度 */
        padding: 0 20px;
        margin-bottom: 24px;
        overflow-x: auto;
      }

      .statistics-table {
        width: 100%;
        min-height: 300px; /* 确保表格最小高度 */
        margin: 0 auto;

        :deep(.el-table) {
          min-height: 300px; /* 表格本身的最小高度 */
          overflow: hidden;
          background-color: var(--art-main-bg-color);
          border-radius: 6px;

          th.el-table__cell {
            padding: 12px 0;
            font-size: 14px;
            font-weight: 600;
            color: var(--art-text-gray-800);
            text-align: center;
            background-color: var(--art-gray-100);
          }

          td.el-table__cell {
            padding: 14px 0;
            color: var(--art-text-gray-700);
            text-align: center;
            transition: background-color 0.2s ease;
          }

          tr.el-table__row {
            transition: all 0.2s ease;

            &:hover {
              background-color: rgba(var(--art-primary), 0.04);
            }
          }

          // 移除垂直分隔线
          .el-table__cell {
            border-right: none !important;
          }

          // 顶部居中效果
          .el-table__body,
          .el-table__header {
            width: 100% !important;
          }

          // 总计行样式特殊处理
          tr.el-table__row:last-child {
            td {
              font-weight: 600;
              color: var(--art-text-gray-900);
              background-color: rgba(var(--art-primary), 0.06);
            }
          }
        }
      }

      .description {
        width: calc(100% - 40px);
        max-width: none;
        padding: 16px;
        margin: 24px auto 0;
        margin-right: auto;
        margin-left: auto;
        font-size: 13px;
        line-height: 1.6;
        color: var(--art-text-muted);
        background-color: var(--art-bg-color);
        border-right: 3px solid rgba(var(--art-primary), 0.5);
        border-left: 3px solid rgba(var(--art-primary), 0.5);
        border-radius: 6px;

        p {
          margin-bottom: 10px;
          text-align: center;

          &:first-child {
            margin-bottom: 12px;
            font-weight: 600;
            color: var(--art-text-gray-800);
          }
        }
      }
    }
  }

  // 响应式设计
  @media screen and (width <= 768px) {
    .carbon-statistics {
      padding: 12px;

      .statistics-header {
        margin-bottom: 20px;

        .title {
          font-size: 22px;
        }
      }

      .filter-card,
      .statistics-table-card {
        margin-bottom: 16px;
      }

      .filter-section {
        padding: 0 10px;

        .top-filter-row {
          flex-direction: column;
          gap: 15px;
          align-items: flex-start;
          padding: 10px;

          .left-section {
            gap: 10px;
            justify-content: space-between;
            width: 100%;

            .center-select {
              width: 48% !important;
            }

            .date-picker {
              width: 48% !important;
              min-width: 180px !important;
            }
          }

          .right-section {
            width: 100%;

            .export-btn {
              justify-content: center;
              width: 100%;
            }
          }
        }

        .bottom-filter-row {
          justify-content: flex-start;
          width: 100%;
          padding: 10px 0;

          .report-type-section {
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
            width: 100%;
            padding: 0;
            margin: 0;

            .report-type-btn {
              flex: 1;
              min-width: calc(50% - 4px);
              max-width: calc(50% - 4px);
            }
          }
        }
      }

      .statistics-table-card {
        min-height: 500px; /* 移动端固定最小高度 */

        .table-title {
          font-size: 18px;
        }

        .table-container {
          min-height: 350px; /* 移动端表格容器最小高度 */
          padding: 0 10px;
        }

        .description {
          width: calc(100% - 20px);
          padding: 12px;

          p {
            text-align: left;
          }
        }
      }
    }
  }
</style>
