<template>
  <div class="dictionary-management-container page-content">
    <!-- 标签页切换 -->
    <el-tabs v-model="activeTab" @tab-change="handleTabChange">
      <!-- 字典类型管理 -->
      <el-tab-pane label="字典类型" name="type">
        <DictionaryTypeManager
          ref="typeManagerRef"
          @view-data="handleViewData"
          @data-updated="handleTypeDataUpdated"
          @all-types-updated="handleAllTypesUpdated"
        />
      </el-tab-pane>

      <!-- 字典数据管理 -->
      <el-tab-pane label="字典数据" name="data">
        <DictionaryDataManager
          ref="dataManagerRef"
          :dict-type-options="dictTypeOptions"
          :default-dict-type="defaultDictType"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
  import { ref } from 'vue'
  import DictionaryTypeManager from './components/DictionaryTypeManager.vue'
  import DictionaryDataManager from './components/DictionaryDataManager.vue'

  // 当前激活的标签页
  const activeTab = ref('type')

  // 组件引用
  const typeManagerRef = ref(null)
  const dataManagerRef = ref(null)

  // 字典类型选项（用于字典数据的下拉选择）
  const dictTypeOptions = ref([])

  // 默认字典类型（用于查看数据时的筛选）
  const defaultDictType = ref('')

  // ========== 标签页切换 ==========
  const handleTabChange = (tabName) => {
    activeTab.value = tabName
    // 清空默认字典类型
    defaultDictType.value = ''
  }

  // 处理字典类型数据更新（表格显示用）
  const handleTypeDataUpdated = (data) => {
    // 这里的data是当前页的数据，用于表格显示
    // 不再用于下拉选择
  }

  // 处理全部字典类型数据更新（下拉选择用）
  const handleAllTypesUpdated = (data) => {
    dictTypeOptions.value = data.map((item) => ({
      dictType: item.dictType,
      dictName: item.dictName
    }))
  }

  // 查看字典数据
  const handleViewData = (row) => {
    activeTab.value = 'data'
    defaultDictType.value = row.dictType
  }
</script>

<style lang="scss" scoped>
  .dictionary-management-container {
    padding: 20px;

    // 标签页样式
    :deep(.el-tabs__header) {
      margin-bottom: 20px;
    }

    :deep(.el-tabs__nav-wrap) {
      padding: 0 10px;
      background-color: var(--art-main-bg-color);
      border-radius: 4px;
    }
  }
</style>
