<template>
  <div class="dashboard page-content">
    <div class="project-container">
      <!-- 所有项目统一展示 -->
      <div class="category">
        <div class="category-header">
          <h2 class="category-title">项目列表</h2>
          <div class="category-stats">
            <span
              class="category-count"
              :class="{ active: currentFilter === 'all' }"
              @click="setFilter('all')"
              >共 {{ projects.length }}个项目</span
            >
            <span
              class="status-badge connected"
              :class="{ active: currentFilter === 'connected' }"
              @click="setFilter('connected')"
              >已接入: {{ connectedProjects.length }}</span
            >
          </div>
        </div>
        <div class="project-grid" v-loading="loading">
          <ProjectCard
            v-for="project in filteredProjects"
            :key="project.id"
            :project="project"
            @click="openProjectDetail(project)"
          />
          <!-- 空状态提示 -->
          <div v-if="!loading && projects.length === 0" class="empty-state">
            <p>暂无项目数据</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 项目详情对话框 -->
    <el-dialog
      v-model="showProjectDetail"
      :title="getDialogTitle()"
      width="80%"
      destroy-on-close
      top="5vh"
      :show-close="true"
      class="project-detail-dialog"
    >
      <ProjectDetail
        v-if="showProjectDetail"
        :project="selectedProject"
        :mode="detailMode"
        @close="showProjectDetail = false"
      />
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted } from 'vue'
  import { ElMessage } from 'element-plus'

  import ProjectCard from '@/components/ProjectCard.vue'
  import ProjectDetail from '@/components/ProjectDetail.vue'
  import { getProjectList } from '@/api/projectApi'
  import { mapProjectList } from '@/utils/projectMapper'

  // 项目数据
  const projects = ref([])
  const loading = ref(false)
  const total = ref(0)

  // 当前过滤状态：'all', 'connected', 'unconnected'
  const currentFilter = ref('all')

  // 设置过滤器
  const setFilter = (filter) => {
    currentFilter.value = filter
  }

  // 已接入和未接入项目的计算属性
  const connectedProjects = computed(() => projects.value.filter((p) => p.isConnected))

  const unconnectedProjects = computed(() => projects.value.filter((p) => !p.isConnected))

  // 根据当前过滤条件筛选项目
  const filteredProjects = computed(() => {
    switch (currentFilter.value) {
      case 'connected':
        return connectedProjects.value
      case 'unconnected':
        return unconnectedProjects.value
      default:
        return projects.value
    }
  })

  // 项目详情相关状态
  const showProjectDetail = ref(false)
  const selectedProject = ref(null)
  const detailMode = ref('view') // 'view', 'edit', 'add'

  // 打开项目详情（查看模式）
  const openProjectDetail = (project) => {
    selectedProject.value = project
    detailMode.value = 'view'
    showProjectDetail.value = true
  }

  // 获取对话框标题
  const getDialogTitle = () => {
    return selectedProject.value?.name || '项目详情'
  }

  // 获取项目列表数据
  const fetchProjects = async () => {
    try {
      loading.value = true
      const response = await getProjectList({
        pageNum: 1,
        pageSize: 100 // 获取所有项目
      })

      if (response.code === 200) {
        console.log(response, 'responseresponseresponse')

        // 使用映射函数转换数据格式
        projects.value = mapProjectList(response.rows)
        total.value = response.total
      } else {
        ElMessage.error(response.msg || '获取项目列表失败')
      }
    } catch (error) {
      console.error('获取项目列表失败:', error)

      // 处理认证失败的情况
      if (error.response?.status === 401) {
        ElMessage.error('认证失败，请重新登录')
        // 这里不需要手动跳转，HTTP拦截器会处理
      } else {
        ElMessage.error('获取项目列表失败，请稍后重试')
      }
    } finally {
      loading.value = false
    }
  }

  // 页面加载时获取数据
  onMounted(() => {
    fetchProjects()
  })
</script>

<style lang="scss" scoped>
  .dashboard {
    min-height: 100vh;
    padding: 30px;
    background-color: var(--art-bg-color);

    .dashboard-header {
      margin-bottom: 30px;

      .page-title {
        margin-bottom: 10px;
        font-size: 28px;
        font-weight: bold;
        color: var(--art-text-gray-900);
      }

      .page-description {
        font-size: 16px;
        color: var(--art-text-gray-600);
      }
    }

    .project-container {
      display: flex;
      flex-direction: column;

      .category {
        padding: 24px;
        background: var(--art-main-bg-color);
        border-radius: 16px;
        box-shadow: var(--art-box-shadow-sm);
        transition: all 0.3s ease;

        &:hover {
          box-shadow: var(--art-box-shadow);
        }

        .category-header {
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          margin-bottom: 24px;

          .category-title {
            margin: 0;
            margin-right: 24px;
            font-size: 20px;
            font-weight: bold;
            line-height: 1;
            color: var(--art-text-gray-900);
          }

          .category-stats {
            display: flex;
            flex: 1;
            flex-wrap: wrap;
            gap: 12px;
            align-items: center;

            .category-count,
            .status-badge {
              cursor: pointer;
              transition: all 0.3s ease;

              &:hover {
                transform: scale(1.05);
              }

              &.active {
                font-weight: bold;
                box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
                transform: scale(1.05);
              }
            }

            .category-count {
              padding: 4px 12px;
              font-size: 14px;
              color: var(--art-text-gray-500);
              background-color: var(--art-gray-200);
              border-radius: 14px;

              &.active {
                color: var(--art-text-gray-900);
                background-color: var(--art-gray-300);
              }
            }

            .status-badge {
              padding: 4px 12px;
              font-size: 14px;
              font-weight: 500;
              border-radius: 14px;

              &.connected {
                color: rgb(var(--art-success));
                background-color: rgba(var(--art-bg-success), 0.5);
                border: 1px solid rgba(var(--art-success), 0.2);

                &.active {
                  background-color: rgba(var(--art-bg-success), 0.7);
                  border: 1px solid rgba(var(--art-success), 0.4);
                }
              }

              &.unconnected {
                color: rgb(var(--art-warning));
                background-color: rgba(var(--art-bg-warning), 0.1);
                border: 1px solid rgba(var(--art-warning), 0.2);

                &.active {
                  background-color: rgba(var(--art-bg-warning), 0.2);
                  border: 1px solid rgba(var(--art-warning), 0.4);
                }
              }
            }
          }
        }

        .project-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
          gap: 24px;

          .empty-state {
            grid-column: 1 / -1;
            padding: 60px 20px;
            font-size: 16px;
            color: var(--art-text-gray-500);
            text-align: center;
          }
        }
      }
    }
  }

  /* 对话框样式定制 */
  :deep(.project-detail-dialog .el-dialog__header) {
    padding: 20px 24px;
    margin: 0;
    border-bottom: 1px solid var(--art-border-color);
  }

  :deep(.project-detail-dialog .el-dialog__body) {
    padding: 0;
  }

  :deep(.project-detail-dialog .el-dialog__title) {
    font-size: 18px;
    font-weight: bold;
  }
</style>
