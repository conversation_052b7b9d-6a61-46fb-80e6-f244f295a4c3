/**
 * 路由导航守卫工具集
 * 提供路由导航过程中的各种检查和处理功能
 */

import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import { useMenuStore } from '@/store/modules/menu'
import type { RouteLocationNormalized } from 'vue-router'

/**
 * 路由导航错误类型
 */
export enum NavigationErrorType {
  AUTH_FAILED = 'AUTH_FAILED',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  ROUTE_NOT_FOUND = 'ROUTE_NOT_FOUND',
  COMPONENT_LOAD_FAILED = 'COMPONENT_LOAD_FAILED',
  NETWORK_ERROR = 'NETWORK_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * 导航错误处理器
 */
export class NavigationErrorHandler {
  private static instance: NavigationErrorHandler
  private errorCount = 0
  private maxErrors = 5
  private resetTime = 60000 // 1分钟后重置错误计数

  static getInstance(): NavigationErrorHandler {
    if (!NavigationErrorHandler.instance) {
      NavigationErrorHandler.instance = new NavigationErrorHandler()
    }
    return NavigationErrorHandler.instance
  }

  /**
   * 处理导航错误
   * @param error 错误对象
   * @param errorType 错误类型
   * @param route 当前路由
   * @returns 处理结果
   */
  async handleError(
    error: any,
    errorType: NavigationErrorType,
    route?: RouteLocationNormalized
  ): Promise<{
    shouldRetry: boolean
    redirectTo?: string
    showMessage?: boolean
    message?: string
  }> {
    this.errorCount++

    console.error(`导航错误 [${errorType}]:`, error, route?.path)

    // 如果错误次数过多，暂时停止处理
    if (this.errorCount > this.maxErrors) {
      return {
        shouldRetry: false,
        redirectTo: '/exception/500',
        showMessage: true,
        message: '系统出现多次错误，请刷新页面或联系管理员'
      }
    }

    // 设置错误计数重置定时器
    setTimeout(() => {
      this.errorCount = 0
    }, this.resetTime)

    switch (errorType) {
      case NavigationErrorType.AUTH_FAILED:
        return this.handleAuthError(error)

      case NavigationErrorType.TOKEN_EXPIRED:
        return this.handleTokenExpiredError(error)

      case NavigationErrorType.PERMISSION_DENIED:
        return this.handlePermissionError(error)

      case NavigationErrorType.ROUTE_NOT_FOUND:
        return this.handleRouteNotFoundError(error, route)

      case NavigationErrorType.COMPONENT_LOAD_FAILED:
        return this.handleComponentLoadError(error, route)

      case NavigationErrorType.NETWORK_ERROR:
        return this.handleNetworkError(error)

      case NavigationErrorType.SERVER_ERROR:
        return this.handleServerError(error)

      default:
        return this.handleUnknownError(error)
    }
  }

  private async handleAuthError(error: any) {
    ElMessage.error('认证失败，请重新登录')
    useUserStore().logOut()
    return {
      shouldRetry: false,
      redirectTo: '/login',
      showMessage: false
    }
  }

  private async handleTokenExpiredError(error: any) {
    try {
      const result = await ElMessageBox.confirm('登录状态已过期，是否重新登录？', '提示', {
        confirmButtonText: '重新登录',
        cancelButtonText: '取消',
        type: 'warning'
      })

      if (result === 'confirm') {
        useUserStore().logOut()
        return {
          shouldRetry: false,
          redirectTo: '/login',
          showMessage: false
        }
      }
    } catch {
      // 用户取消，直接登出
      useUserStore().logOut()
    }

    return {
      shouldRetry: false,
      redirectTo: '/login',
      showMessage: false
    }
  }

  private async handlePermissionError(error: any) {
    ElMessage.error('权限不足，无法访问该页面')
    return {
      shouldRetry: false,
      redirectTo: '/exception/403',
      showMessage: false
    }
  }

  private async handleRouteNotFoundError(error: any, route?: RouteLocationNormalized) {
    console.warn('路由未找到:', route?.path)
    return {
      shouldRetry: false,
      redirectTo: '/exception/404',
      showMessage: false
    }
  }

  private async handleComponentLoadError(error: any, route?: RouteLocationNormalized) {
    ElMessage.error('页面组件加载失败，请刷新重试')
    return {
      shouldRetry: true,
      showMessage: false
    }
  }

  private async handleNetworkError(error: any) {
    ElMessage.error('网络连接异常，请检查网络设置')
    return {
      shouldRetry: true,
      showMessage: false
    }
  }

  private async handleServerError(error: any) {
    ElMessage.error('服务器错误，请稍后重试')
    return {
      shouldRetry: false,
      redirectTo: '/exception/500',
      showMessage: false
    }
  }

  private async handleUnknownError(error: any) {
    console.error('未知导航错误:', error)
    ElMessage.error('系统出现未知错误')
    return {
      shouldRetry: false,
      redirectTo: '/exception/500',
      showMessage: false
    }
  }

  /**
   * 重置错误计数
   */
  resetErrorCount(): void {
    this.errorCount = 0
  }

  /**
   * 获取当前错误计数
   */
  getErrorCount(): number {
    return this.errorCount
  }
}

/**
 * 路由健康检查器
 */
export class RouteHealthChecker {
  /**
   * 检查路由健康状态
   * @param route 路由对象
   * @returns 健康检查结果
   */
  static async checkRouteHealth(route: RouteLocationNormalized): Promise<{
    isHealthy: boolean
    issues: string[]
    warnings: string[]
  }> {
    const issues: string[] = []
    const warnings: string[] = []

    try {
      // 检查路由基本信息
      if (!route.name && route.path !== '/') {
        warnings.push('路由缺少名称')
      }

      if (!route.path) {
        issues.push('路由缺少路径')
      }

      // 检查路由匹配
      if (route.matched.length === 0) {
        issues.push('路由未匹配到任何记录')
      }

      // 检查路由组件
      for (const record of route.matched) {
        if (record.components) {
          const component = record.components.default
          if (!component) {
            issues.push(`路由 ${record.path} 缺少默认组件`)
          } else if (typeof component === 'function') {
            // 可以在这里添加组件预加载检查
            try {
              // await component()
            } catch (error) {
              issues.push(`路由 ${record.path} 组件加载失败: ${error}`)
            }
          }
        }
      }

      // 检查路由元信息
      if (route.meta) {
        if (route.meta.requiresAuth && !useUserStore().isLogin) {
          issues.push('路由需要认证但用户未登录')
        }
      }

      return {
        isHealthy: issues.length === 0,
        issues,
        warnings
      }
    } catch (error) {
      console.error('路由健康检查异常:', error)
      return {
        isHealthy: false,
        issues: ['路由健康检查异常'],
        warnings
      }
    }
  }
}

/**
 * 导航守卫工具函数
 */
export const NavigationGuardUtils = {
  /**
   * 安全导航到指定路由
   * @param path 目标路径
   * @param router 路由实例
   * @param fallbackPath 失败时的备用路径
   */
  async safeNavigate(path: string, router: any, fallbackPath = '/') {
    try {
      await router.push(path)
    } catch (error) {
      console.error('导航失败:', error)
      try {
        await router.push(fallbackPath)
      } catch (fallbackError) {
        console.error('备用导航也失败:', fallbackError)
        window.location.href = fallbackPath
      }
    }
  },

  /**
   * 检查用户是否有访问权限
   * @param route 路由对象
   * @returns 是否有权限
   */
  checkPermission(route: RouteLocationNormalized): boolean {
    const userStore = useUserStore()
    const menuStore = useMenuStore()

    // 如果路由不需要认证，直接通过
    if (route.meta?.noLogin) {
      return true
    }

    // 检查用户是否登录
    if (!userStore.isLogin) {
      return false
    }

    // 检查菜单权限（如果有菜单列表）
    const menuList = menuStore.menuList
    if (menuList && menuList.length > 0) {
      const hasPermission = menuList.some(
        (menu: any) => menu.path === route.path || route.path.startsWith(menu.path)
      )
      return hasPermission
    }

    // 默认通过（如果没有菜单权限控制）
    return true
  }
}
