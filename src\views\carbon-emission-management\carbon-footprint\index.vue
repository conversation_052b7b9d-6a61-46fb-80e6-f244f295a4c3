<template>
  <div class="carbon-footprint-page page-content">
    <!-- 日期筛选控制区域 -->
    <div class="filter-controls">
      <div class="filter-row">
        <div class="filter-left">
          <div class="filter-item">
            <span class="filter-label">项目名称:</span>
            <ProjectSelector
              v-model="searchForm.projectId"
              placeholder="请选择项目"
              @change="handleProjectChange"
              @loaded="handleProjectListLoaded"
            />
          </div>

          <div class="filter-item">
            <span class="filter-label">时间</span>
            <el-radio-group v-model="searchForm.timeGranularity" class="time-granularity-buttons">
              <el-radio-button
                v-for="item in timeGranularityOptions"
                :key="item.value"
                :label="item.value"
              >
                {{ item.label }}
              </el-radio-button>
            </el-radio-group>
          </div>

          <div class="filter-item date-filter">
            <span class="filter-label">日期选择：</span>
            <div class="date-picker-group">
              <!-- 日期和月份使用范围选择器 -->
              <el-date-picker
                v-if="searchForm.timeGranularity !== 'year'"
                v-model="searchForm.dateRange"
                :type="datePickerType"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :shortcuts="dateShortcuts"
                :disabled-date="disabledDate"
                class="date-picker"
                @change="onDateRangeChange"
              />
              <!-- 年份使用单选选择器 -->
              <el-date-picker
                v-else
                v-model="searchForm.yearValue"
                type="year"
                placeholder="选择年份"
                :disabled-date="disabledDate"
                class="date-picker"
                value-format="YYYY"
                @change="onYearChange"
              />
            </div>
          </div>
        </div>

        <div class="filter-actions">
          <el-button type="primary" @click="handleQuery" :loading="isLoading"> 查询 </el-button>
          <el-button @click="handleReset"> 重置 </el-button>
          <el-button type="success" @click="exportData" :loading="isExporting">
            导出数据
          </el-button>
        </div>
      </div>
    </div>

    <div class="chart-section">
      <div class="chart-container">
        <CarbonSankeyChart :carbon-footprint-data="carbonFootprintData" :loading="isLoading" />
      </div>
    </div>

    <!-- 碳排放数据和建议 - 采用网格布局 -->
    <div class="data-grid-container">
      <!-- 碳足迹统计卡片 -->
      <div class="data-card carbon-stats">
        <h2>碳足迹统计</h2>

        <!-- 有数据时显示统计信息 -->
        <template v-if="carbonStats.hasData">
          <div class="total-carbon">
            <span class="carbon-value">{{ carbonStats.totalEmission }}</span>
            <span class="carbon-unit">kgCO₂</span>
          </div>
          <div class="carbon-details">碳排放总量</div>

          <div class="carbon-sources-grid">
            <div
              v-for="energyType in carbonStats.energyTypes"
              :key="energyType.name"
              class="carbon-source-item"
            >
              <div class="source-title">{{ energyType.name }}</div>
              <div class="source-value">{{ energyType.value.toFixed(1) }}</div>
              <div class="source-unit">kgCO₂</div>
              <div class="source-percent">{{ energyType.percent }}%</div>
            </div>
          </div>
        </template>

        <!-- 无数据时显示友好提示 -->
        <template v-else>
          <div class="no-data-tip">
            <div class="no-data-icon">📊</div>
            <div class="no-data-text">暂无碳足迹数据</div>
            <div class="no-data-desc">请选择项目和时间范围后查询</div>
          </div>
        </template>
      </div>

      <!-- 减排建议卡片 -->
      <div class="data-card carbon-suggestions">
        <h2>减排建议</h2>
        <div class="suggestion-list">
          <div class="suggestion-item">
            <div class="suggestion-icon orange">
              <el-icon><ColdDrink /></el-icon>
            </div>
            <div class="suggestion-content">
              <div class="suggestion-title">优化空调运行策略</div>
              <div class="suggestion-desc"
                >提高空调温度设定值1-2℃，节能效果明显，对舒适度影响较小</div
              >
              <div class="suggestion-saving"
                >预计减排: <span class="highlight">3.5吨CO₂e/月</span></div
              >
            </div>
          </div>
          <div class="suggestion-item">
            <div class="suggestion-icon yellow">
              <el-icon><Sunny /></el-icon>
            </div>
            <div class="suggestion-content">
              <div class="suggestion-title">照明系统节能改造</div>
              <div class="suggestion-desc">更换为LED节能灯具，并在非必要区域安装感应式开关</div>
              <div class="suggestion-saving"
                >预计减排: <span class="highlight">2.1吨CO₂e/月</span></div
              >
            </div>
          </div>
          <div class="suggestion-item">
            <div class="suggestion-icon yellow">
              <el-icon><Monitor /></el-icon>
            </div>
            <div class="suggestion-content">
              <div class="suggestion-title">办公设备节能管理</div>
              <div class="suggestion-desc">设置电脑、打印机等设备自动休眠，非工作时间完全关机</div>
              <div class="suggestion-saving"
                >预计减排: <span class="highlight">1.8吨CO₂e/月</span></div
              >
            </div>
          </div>
          <div class="suggestion-item">
            <div class="suggestion-icon green">
              <el-icon><Lightning /></el-icon>
            </div>
            <div class="suggestion-content">
              <div class="suggestion-title">绿色能源替代</div>
              <div class="suggestion-desc">增加光伏系统建设，部分替代传统使用电网</div>
              <div class="suggestion-saving"
                >预计减排: <span class="highlight">8.5吨CO₂e/月</span></div
              >
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 可折叠的碳足迹概念信息面板 -->
    <div class="collapsible-panel">
      <div class="collapsible-header" @click="toggleInfoPanel">
        <h2>碳足迹知识</h2>
        <span class="toggle-icon">{{ isInfoPanelOpen ? '▲' : '▼' }}</span>
      </div>
      <div class="collapsible-content" :class="{ open: isInfoPanelOpen }">
        <CarbonInfoPanel />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import { ColdDrink, Sunny, Monitor, Lightning } from '@element-plus/icons-vue'
  import CarbonSankeyChart from '@/components/echarts/CarbonFootprint/CarbonSankeyChart.vue'
  import CarbonInfoPanel from '@/components/echarts/CarbonFootprint/CarbonInfoPanel.vue'
  import ProjectSelector from '@/components/ProjectSelector.vue'
  import { getCarbonFootprintReport, type CarbonFootprintEnergyType } from '@/api/carbon-emission'
  import type { ProjectOption } from '@/api/projectApi'

  // 控制信息面板的折叠状态
  const isInfoPanelOpen = ref(false)

  // 加载状态
  const isLoading = ref(false)
  const isExporting = ref(false)

  // 碳足迹数据
  const carbonFootprintData = ref<CarbonFootprintEnergyType[]>([])
  const selectedProjectInfo = ref<ProjectOption | null>(null)

  // 计算碳足迹统计数据
  const carbonStats = computed(() => {
    if (!carbonFootprintData.value || carbonFootprintData.value.length === 0) {
      return {
        totalEmission: 0,
        energyTypes: [],
        hasData: false
      }
    }

    // 计算总排放量
    const totalEmission = carbonFootprintData.value.reduce(
      (sum, item) => sum + item.totalQuantity,
      0
    )

    // 计算各能源类型的数据和百分比
    const energyTypes = carbonFootprintData.value.map((item) => ({
      name: item.name,
      value: item.totalQuantity,
      percent: totalEmission > 0 ? Math.round((item.totalQuantity / totalEmission) * 100) : 0,
      devices: item.dataList
    }))

    // 按排放量降序排序
    energyTypes.sort((a, b) => b.value - a.value)

    return {
      totalEmission: Math.round(totalEmission * 10) / 10, // 保留一位小数
      energyTypes,
      hasData: true
    }
  })

  // 查询表单
  const searchForm = reactive({
    projectId: null as number | null, // 默认为空，等待项目列表加载后自动选择第一个
    timeGranularity: 'day', // 默认按日查询
    dateRange: [] as any[],
    yearValue: null as string | null // 年份单选值
  })

  // 时间粒度选项
  const timeGranularityOptions = [
    { value: 'day', label: '日' },
    { value: 'month', label: '月' },
    { value: 'year', label: '年' }
  ]

  // 根据选择的时间粒度动态设置日期选择器类型
  const datePickerType = computed(() => {
    switch (searchForm.timeGranularity) {
      case 'day':
        return 'daterange'
      case 'month':
        return 'monthrange'
      case 'year':
        return 'year' // 年份使用单选，不是范围选择
      default:
        return 'daterange'
    }
  })

  // 日期快捷选项
  const dateShortcuts = [
    {
      text: '最近一周',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
        return [start, end]
      }
    },
    {
      text: '最近一个月',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        return [start, end]
      }
    },
    {
      text: '最近三个月',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
        return [start, end]
      }
    }
  ]

  // 禁用日期选择
  const disabledDate = (time: Date) => {
    return time.getTime() > Date.now()
  }

  // 设置默认日期
  const setDefaultDates = () => {
    // 设置默认时间为昨天
    const yesterday = new Date()
    yesterday.setDate(yesterday.getDate() - 1)
    searchForm.dateRange = [yesterday, yesterday]
  }

  // 处理项目变化
  const handleProjectChange = (projectInfo: ProjectOption | null) => {
    selectedProjectInfo.value = projectInfo
  }

  // 处理项目列表加载完成
  const handleProjectListLoaded = (projectList: ProjectOption[]) => {
    // 自动选择第一个项目
    if (projectList && projectList.length > 0 && !searchForm.projectId) {
      const firstProject = projectList[0]
      searchForm.projectId = firstProject.id
      selectedProjectInfo.value = firstProject
      console.log('自动选择第一个项目:', firstProject)

      // 自动执行查询
      setTimeout(() => {
        handleQuery()
      }, 100)
    }
  }

  // 格式化日期为 YYYY-MM-DD 格式
  const formatDate = (date: Date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  // 日期范围变化处理
  const onDateRangeChange = (value: any) => {}

  // 年份变化处理
  const onYearChange = (value: any) => {}

  // 查询数据
  const handleQuery = async () => {
    if (!searchForm.projectId) {
      ElMessage.warning('请选择项目')
      return
    }

    // 验证时间选择
    if (searchForm.timeGranularity === 'year') {
      if (!searchForm.yearValue) {
        ElMessage.warning('请选择年份')
        return
      }
    } else {
      if (!searchForm.dateRange || searchForm.dateRange.length !== 2) {
        ElMessage.warning('请选择日期范围')
        return
      }
    }

    isLoading.value = true
    try {
      // 构建时间参数
      let beginTime, endTime
      if (searchForm.timeGranularity === 'year' && searchForm.yearValue) {
        // 年份选择：设置为该年的第一天和最后一天
        beginTime = `${searchForm.yearValue}-01-01`
        endTime = `${searchForm.yearValue}-12-31`
      } else if (searchForm.dateRange && searchForm.dateRange.length === 2) {
        beginTime = formatDate(searchForm.dateRange[0])
        endTime = formatDate(searchForm.dateRange[1])
      }

      const params = {
        projectId: searchForm.projectId,
        beginTime,
        endTime
      }

      const response = await getCarbonFootprintReport(params)

      if (response.code === 200) {
        carbonFootprintData.value = response.data || []

        ElMessage.success('数据查询成功')
      } else {
        throw new Error(response.msg || 'API返回数据异常')
      }
    } catch (error) {
      console.error('查询失败:', error)
      ElMessage.error('数据查询失败，请稍后重试')
      carbonFootprintData.value = []
    } finally {
      isLoading.value = false
    }
  }

  // 导出数据
  const exportData = async () => {
    isExporting.value = true
    try {
      // 模拟导出操作
      await new Promise((resolve) => setTimeout(resolve, 1500))
      ElMessage.success('数据导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('数据导出失败')
    } finally {
      isExporting.value = false
    }
  }

  // 重置筛选条件
  const handleReset = () => {
    searchForm.projectId = null // 重置为空，等待重新选择
    searchForm.timeGranularity = 'day'
    searchForm.yearValue = null
    setDefaultDates()
    carbonFootprintData.value = []
    selectedProjectInfo.value = null
    ElMessage.info('已重置为默认查询条件')
  }

  // 切换信息面板的显示/隐藏
  const toggleInfoPanel = () => {
    isInfoPanelOpen.value = !isInfoPanelOpen.value
  }

  // 监听时间粒度变化，自动设置默认日期
  const handleTimeGranularityChange = () => {
    setDefaultDates()
  }

  // 监听时间粒度变化
  watch(
    () => searchForm.timeGranularity,
    () => {
      handleTimeGranularityChange()
    }
  )

  // 初始化数据加载
  const initializeData = async () => {
    setDefaultDates()
    // 不再在初始化时自动发送请求，等待项目列表加载完成后自动选择第一个项目并查询
  }

  // 初始化
  onMounted(() => {
    initializeData()
  })
</script>

<style scoped>
  .carbon-footprint-page {
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 100%;
    padding: 20px;
    background-color: var(--art-bg-color);
  }

  h1 {
    margin-bottom: 10px;
    font-size: 22px;
    font-weight: 600;
    color: var(--art-text-gray-800);
  }

  .page-description {
    margin-bottom: 24px;
    font-size: 14px;
    color: var(--art-text-gray-600);
  }

  /* 筛选控制区域样式 */
  .filter-controls {
    padding: 20px;
    background-color: var(--art-main-bg-color);
    border: 1px solid var(--art-border-color);
    border-radius: 8px;
    box-shadow: var(--art-box-shadow-xs);
  }

  .filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: center;
    justify-content: space-between;
  }

  .filter-left {
    display: flex;
    flex: 1;
    flex-wrap: wrap;
    gap: 20px;
    align-items: center;
  }

  .filter-item {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .filter-label {
    font-size: 14px;
    color: var(--art-text-gray-800);
    white-space: nowrap;
  }

  .time-granularity-buttons {
    min-width: 180px;
  }

  .date-filter {
    flex: 1;
    max-width: 400px;
  }

  .date-picker-group {
    display: flex;
    align-items: center;
  }

  .date-picker {
    min-width: 280px;
  }

  .filter-actions {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .page-content {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  /* 桑基图区域样式 */
  .chart-section {
    width: 100%;
    padding: 16px;
    background-color: var(--art-main-bg-color);
    border: 1px solid var(--art-border-color);
    border-radius: 8px;
    box-shadow: var(--art-box-shadow-xs);
  }

  .chart-container {
    width: 100%;
    height: 500px;
  }

  /* 数据网格布局 */
  .data-grid-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    width: 100%;
  }

  /* 数据卡片样式 */
  .data-card {
    padding: 20px;
    background-color: var(--art-main-bg-color);
    border: 1px solid var(--art-border-color);
    border-radius: 8px;
    box-shadow: var(--art-box-shadow-xs);
  }

  .data-card h2 {
    margin: 0 0 16px;
    font-size: 18px;
    font-weight: 500;
    color: var(--art-text-gray-800);
  }

  /* 碳足迹统计卡片 */
  .carbon-stats .total-carbon {
    display: flex;
    align-items: baseline;
    margin-bottom: 4px;
  }

  .carbon-value {
    font-size: 32px;
    font-weight: 600;
    color: var(--art-text-gray-800);
  }

  .carbon-unit {
    margin-left: 6px;
    font-size: 16px;
    color: var(--art-text-gray-600);
  }

  .carbon-trend {
    margin-left: 12px;
    font-size: 14px;
    font-weight: 500;
  }

  .carbon-trend.positive {
    color: var(--el-color-danger);
  }

  .carbon-trend.negative {
    color: var(--el-color-success);
  }

  .carbon-details {
    margin-bottom: 24px;
    font-size: 14px;
    color: var(--art-text-gray-600);
  }

  .carbon-sources-grid {
    display: grid;
    grid-template-rows: repeat(2, 1fr);
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
  }

  .carbon-source-item {
    position: relative;
    padding: 12px;
    background-color: var(--art-gray-100);
    border: 1px solid var(--art-border-color);
    border-radius: 6px;
  }

  .source-title {
    margin-bottom: 8px;
    font-size: 14px;
    color: var(--art-text-gray-600);
  }

  .source-value {
    font-size: 22px;
    font-weight: 600;
    color: var(--art-text-gray-800);
  }

  .source-unit {
    margin-top: 4px;
    font-size: 12px;
    color: var(--art-text-gray-500);
  }

  .source-percent {
    position: absolute;
    top: 12px;
    right: 12px;
    font-size: 14px;
    font-weight: 500;
    color: var(--el-color-primary);
  }

  /* 无数据提示样式 */
  .no-data-tip {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
  }

  .no-data-icon {
    margin-bottom: 16px;
    font-size: 48px;
    opacity: 0.6;
  }

  .no-data-text {
    margin-bottom: 8px;
    font-size: 16px;
    font-weight: 500;
    color: var(--art-text-gray-600);
  }

  .no-data-desc {
    font-size: 14px;
    color: var(--art-text-gray-500);
  }

  /* 减排建议卡片 */
  .suggestion-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .suggestion-item {
    display: flex;
    gap: 12px;
    padding: 12px;
    background-color: var(--art-gray-100);
    border: 1px solid var(--art-border-color);
    border-radius: 6px;
  }

  .suggestion-icon {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 6px;
  }

  .suggestion-icon.orange {
    color: #ff7849;
    background-color: rgb(255 120 73 / 10%);
  }

  .suggestion-icon.yellow {
    color: #ffc82c;
    background-color: rgb(255 200 44 / 10%);
  }

  .suggestion-icon.green {
    color: #13ce66;
    background-color: rgb(19 206 102 / 10%);
  }

  .suggestion-icon .el-icon {
    font-size: 18px;
  }

  .suggestion-content {
    flex: 1;
  }

  .suggestion-title {
    margin-bottom: 6px;
    font-size: 15px;
    font-weight: 500;
    color: var(--art-text-gray-800);
  }

  .suggestion-desc {
    margin-bottom: 6px;
    font-size: 13px;
    line-height: 1.4;
    color: var(--art-text-gray-600);
  }

  .suggestion-saving {
    font-size: 12px;
    color: var(--art-text-gray-500);
  }

  .suggestion-saving .highlight {
    font-weight: 500;
    color: var(--el-color-success);
  }

  /* 可折叠面板样式 */
  .collapsible-panel {
    width: 100%;
    margin-top: 10px;
  }

  .collapsible-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    cursor: pointer;
    user-select: none;
    background-color: var(--art-main-bg-color);
    border: 1px solid var(--art-border-color);
    border-radius: 8px;
    box-shadow: var(--art-box-shadow-xs);
  }

  .collapsible-header h2 {
    margin: 0;
  }

  .toggle-icon {
    font-size: 14px;
    color: var(--art-text-gray-600);
    transition: transform 0.3s;
  }

  .collapsible-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.5s ease;
  }

  .collapsible-content.open {
    max-height: 2000px; /* 足够大以容纳内容 */
  }

  /* 响应式布局 */
  @media (width <= 1200px) {
    .chart-container {
      height: 400px;
    }

    .data-grid-container {
      grid-template-columns: 1fr;
    }
  }

  @media (width <= 768px) {
    .filter-row {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }

    .filter-left {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }

    .filter-item {
      justify-content: space-between;
    }

    .time-granularity-buttons {
      width: 100%;
      min-width: auto;
    }

    .date-filter {
      max-width: none;
    }

    .date-picker {
      width: 100%;
      min-width: auto;
    }

    .filter-actions {
      justify-content: center;
    }

    .carbon-sources-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (width <= 480px) {
    .carbon-sources-grid {
      grid-template-columns: 1fr;
    }

    .filter-controls {
      padding: 16px;
    }
  }
</style>
