<script setup>
  import { ref, onMounted, watch } from 'vue'
  import * as echarts from 'echarts'

  const props = defineProps({
    timeRange: {
      type: String,
      default: 'day'
    },
    chartData: {
      type: Object,
      default: () => ({
        xAxisData: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
        seriesData: [30, 28, 36, 42, 38, 34, 32]
      })
    }
  })

  const chartRef = ref(null)
  let chartInstance = null

  // 初始化图表
  const initChart = () => {
    if (!chartRef.value) return

    // 销毁旧实例
    if (chartInstance) {
      chartInstance.dispose()
    }

    // 创建新实例
    chartInstance = echarts.init(chartRef.value)

    // 图表配置
    const option = {
      tooltip: {
        trigger: 'axis',
        formatter: '{a} <br/>{b}: {c} μg/m³'
      },
      legend: {
        data: ['PM2.5'],
        bottom: 0
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: props.chartData.xAxisData
      },
      yAxis: {
        type: 'value',
        name: 'μg/m³',
        nameTextStyle: {
          padding: [0, 0, 0, 10]
        }
      },
      series: [
        {
          name: 'PM2.5',
          type: 'line',
          smooth: true,
          data: props.chartData.seriesData,
          itemStyle: {
            color: '#409EFF'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(64, 158, 255, 0.5)'
                },
                {
                  offset: 1,
                  color: 'rgba(64, 158, 255, 0.1)'
                }
              ]
            }
          }
        }
      ]
    }

    chartInstance.setOption(option)
  }

  // 监听数据变化
  watch(
    () => props.chartData,
    () => {
      initChart()
    },
    { deep: true }
  )

  // 监听时间范围变化
  watch(
    () => props.timeRange,
    () => {
      // 可以在这里添加特定于时间范围变化的逻辑
      initChart()
    }
  )

  // 组件挂载时初始化
  onMounted(() => {
    initChart()

    // 监听窗口大小变化
    window.addEventListener('resize', () => {
      if (chartInstance) {
        chartInstance.resize()
      }
    })
  })

  // 定义方法，可以由父组件调用来更新图表数据
  const updateChartData = (data) => {
    if (chartInstance) {
      chartInstance.setOption({
        xAxis: {
          data: data.xAxisData || props.chartData.xAxisData
        },
        series: [
          {
            data: data.seriesData || props.chartData.seriesData
          }
        ]
      })
    }
  }

  // 导出方法供父组件使用
  defineExpose({
    updateChartData
  })
</script>

<template>
  <div ref="chartRef" class="chart-container"></div>
</template>

<style lang="scss" scoped>
  .chart-container {
    width: 100%;
    height: 300px;

    // 确保图表容器有正确的大小
    &:deep(.echarts-tooltip) {
      padding: 10px;
      background: rgb(255 255 255 / 90%) !important;
      border-radius: 4px;
      box-shadow: 0 0 10px rgb(0 0 0 / 10%);

      // 确保提示框显示正确
      .echarts-tooltip-title {
        margin-bottom: 5px;
        font-weight: bold;
      }
    }
  }
</style>
