<template>
  <div class="project-building-selector" :class="customClass">
    <!-- 项目名称选择 -->
    <div class="filter-item">
      <span class="filter-label">项目名称:</span>
      <ProjectSelector
        ref="projectSelectorRef"
        v-model="internalProjectId"
        placeholder="请选择项目"
        class="filter-select"
        :disabled="disabled"
        @change="handleProjectChange"
        @loaded="handleProjectListLoaded"
      />
    </div>

    <!-- 楼栋名称选择 -->
    <div class="filter-item">
      <span class="filter-label">楼栋名称:</span>
      <el-select
        v-model="internalBuildingId"
        placeholder="请选择楼栋名称"
        clearable
        class="filter-select"
        :disabled="disabled || !internalProjectId"
        :loading="buildingListLoading"
        @change="handleBuildingChange"
      >
        <el-option
          v-for="item in buildingList"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
    </div>
  </div>
</template>

<script setup>
  import { ref, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import { getBuildingList } from '@/api/buildingApi'
  import ProjectSelector from '@/components/ProjectSelector.vue'

  // Props 定义
  const props = defineProps({
    modelValue: {
      type: Object,
      default: () => ({
        projectId: null,
        buildingId: null
      })
    },
    disabled: {
      type: Boolean,
      default: false
    },
    autoSelectFirst: {
      type: Boolean,
      default: true
    },
    customClass: {
      type: String,
      default: ''
    }
  })

  // Emits 定义
  const emit = defineEmits(['update:modelValue', 'change', 'project-loaded'])

  // 内部状态
  const projectSelectorRef = ref()
  const selectedProjectInfo = ref(null)
  const buildingList = ref([])
  const buildingListLoading = ref(false)

  // 内部值，用于双向绑定
  const internalProjectId = ref(props.modelValue?.projectId || null)
  const internalBuildingId = ref(props.modelValue?.buildingId || null)

  // 监听外部值变化
  watch(
    () => props.modelValue,
    (newValue) => {
      if (newValue) {
        if (newValue.projectId !== internalProjectId.value) {
          internalProjectId.value = newValue.projectId || null
        }
        if (newValue.buildingId !== internalBuildingId.value) {
          internalBuildingId.value = newValue.buildingId || null
        }
      }
    },
    { deep: true }
  )

  // 监听内部值变化，向外发送
  watch([internalProjectId, internalBuildingId], ([newProjectId, newBuildingId]) => {
    const newValue = {
      projectId: newProjectId,
      buildingId: newBuildingId
    }
    emit('update:modelValue', newValue)
  })

  // 获取楼栋列表
  const fetchBuildingList = async (projectId) => {
    if (!projectId) {
      buildingList.value = []
      return
    }

    try {
      buildingListLoading.value = true
      const response = await getBuildingList(projectId)

      if (response.code === 200) {
        buildingList.value = response.data || []
      } else {
        ElMessage.error(response.msg || '获取楼栋列表失败')
        buildingList.value = []
      }
    } catch (error) {
      console.error('获取楼栋列表失败:', error)
      ElMessage.error('获取楼栋列表失败，请稍后重试')
      buildingList.value = []
    } finally {
      buildingListLoading.value = false
    }
  }

  // 处理项目变化
  const handleProjectChange = (projectInfo) => {
    selectedProjectInfo.value = projectInfo

    // 清空楼栋选择
    internalBuildingId.value = null

    // 获取新项目的楼栋列表
    if (projectInfo && projectInfo.id) {
      fetchBuildingList(projectInfo.id)
    } else {
      buildingList.value = []
    }

    // 发送变化事件
    emit('change', {
      projectId: internalProjectId.value,
      buildingId: internalBuildingId.value,
      projectInfo: projectInfo,
      buildingInfo: null
    })
  }

  // 处理楼栋变化
  const handleBuildingChange = (buildingId) => {
    const buildingInfo = buildingList.value.find((item) => item.id === buildingId) || null

    // 发送变化事件
    emit('change', {
      projectId: internalProjectId.value,
      buildingId: buildingId,
      projectInfo: selectedProjectInfo.value,
      buildingInfo: buildingInfo
    })
  }

  // 监听项目选择器组件的项目列表加载完成
  const handleProjectListLoaded = (projectList) => {
    // 发送项目列表加载完成事件
    emit('project-loaded', projectList)

    // 如果启用自动选择第一个项目
    if (
      props.autoSelectFirst &&
      projectList &&
      projectList.length > 0 &&
      !internalProjectId.value
    ) {
      const firstProject = projectList[0]
      internalProjectId.value = firstProject.id
      selectedProjectInfo.value = firstProject

      // 获取第一个项目的楼栋列表
      fetchBuildingList(firstProject.id)
    }
  }

  // 重置选择
  const reset = () => {
    internalProjectId.value = null
    internalBuildingId.value = null
    selectedProjectInfo.value = null
    buildingList.value = []

    // 如果启用自动选择第一个项目
    if (
      props.autoSelectFirst &&
      projectSelectorRef.value &&
      projectSelectorRef.value.projectList?.length > 0
    ) {
      const firstProject = projectSelectorRef.value.projectList[0]
      internalProjectId.value = firstProject.id
      selectedProjectInfo.value = firstProject
      fetchBuildingList(firstProject.id)
    }
  }

  // 获取当前选中的项目信息
  const getSelectedProjectInfo = () => {
    return selectedProjectInfo.value
  }

  // 获取当前楼栋列表
  const getBuildingListData = () => {
    return buildingList.value
  }

  // 暴露方法给父组件
  defineExpose({
    reset,
    getSelectedProjectInfo,
    getBuildingListData,
    fetchBuildingList
  })
</script>

<style lang="scss" scoped>
  .project-building-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: center;

    .filter-item {
      display: flex;
      align-items: center;
    }

    .filter-label {
      margin-right: 8px;
      font-size: 14px;
      color: var(--el-text-color-secondary);
      white-space: nowrap;
    }

    .filter-select {
      width: 180px;
    }
  }
</style>
