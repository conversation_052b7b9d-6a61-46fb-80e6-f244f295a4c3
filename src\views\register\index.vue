<template>
  <div class="login register">
    <div class="left-wrap">
      <LoginLeftView></LoginLeftView>
    </div>
    <div class="right-wrap">
      <div class="header">
        <svg class="icon" aria-hidden="true">
          <use xlink:href="#iconsys-zhaopian-copy"></use>
        </svg>
        <h1>{{ systemName }}</h1>
      </div>
      <div class="login-wrap">
        <div class="form">
          <h3 class="title">{{ $t('register.title') }}</h3>
          <p class="sub-title">{{ $t('register.subTitle') }}</p>
          <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
            <el-form-item prop="username">
              <el-input
                v-model.trim="formData.username"
                :placeholder="$t('register.placeholder[0]')"
                size="large"
              />
            </el-form-item>

            <el-form-item prop="password">
              <el-input
                v-model.trim="formData.password"
                :placeholder="$t('register.placeholder[1]')"
                size="large"
                type="password"
                autocomplete="off"
              />
            </el-form-item>

            <el-form-item prop="confirmPassword">
              <el-input
                v-model.trim="formData.confirmPassword"
                :placeholder="$t('register.placeholder[2]')"
                size="large"
                type="password"
                autocomplete="off"
                @keyup.enter="register"
              />
            </el-form-item>

            <el-form-item prop="agreement">
              <el-checkbox v-model="formData.agreement">
                {{ $t('register.agreeText') }}
                <router-link
                  style="color: var(--main-color); text-decoration: none"
                  to="/privacy-policy"
                  >{{ $t('register.privacyPolicy') }}</router-link
                >
              </el-checkbox>
            </el-form-item>

            <div style="margin-top: 15px">
              <el-button
                class="register-btn"
                size="large"
                type="primary"
                @click="register"
                :loading="loading"
                v-ripple
              >
                {{ $t('register.submitBtnText') }}
              </el-button>
            </div>

            <div class="footer">
              <p>
                {{ $t('register.hasAccount') }}
                <router-link to="/login">{{ $t('register.toLogin') }}</router-link>
              </p>
            </div>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import AppConfig from '@/config'
  import { ElMessage } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'
  import { useI18n } from 'vue-i18n'

  const { t } = useI18n()

  const router = useRouter()
  const formRef = ref<FormInstance>()

  const systemName = AppConfig.systemInfo.name
  const loading = ref(false)

  const formData = reactive({
    username: '',
    password: '',
    confirmPassword: '',
    agreement: false
  })

  const validatePass = (rule: any, value: string, callback: any) => {
    if (value === '') {
      callback(new Error(t('register.placeholder[1]')))
    } else {
      if (formData.confirmPassword !== '') {
        formRef.value?.validateField('confirmPassword')
      }
      callback()
    }
  }

  const validatePass2 = (rule: any, value: string, callback: any) => {
    if (value === '') {
      callback(new Error(t('register.rule[0]')))
    } else if (value !== formData.password) {
      callback(new Error(t('register.rule[1]')))
    } else {
      callback()
    }
  }

  const rules = reactive<FormRules>({
    username: [
      { required: true, message: t('register.placeholder[0]'), trigger: 'blur' },
      { min: 3, max: 20, message: t('register.rule[2]'), trigger: 'blur' }
    ],
    password: [
      { required: true, validator: validatePass, trigger: 'blur' },
      { min: 6, message: t('register.rule[3]'), trigger: 'blur' }
    ],
    confirmPassword: [{ required: true, validator: validatePass2, trigger: 'blur' }],
    agreement: [
      {
        validator: (rule: any, value: boolean, callback: any) => {
          if (!value) {
            callback(new Error(t('register.rule[4]')))
          } else {
            callback()
          }
        },
        trigger: 'change'
      }
    ]
  })

  const register = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()
      loading.value = true

      // 模拟注册请求
      setTimeout(() => {
        loading.value = false
        ElMessage.success('注册成功')
        toLogin()
      }, 1000)
    } catch (error) {
      console.log('验证失败', error)
    }
  }

  const toLogin = () => {
    setTimeout(() => {
      router.push('/login')
    }, 1000)
  }
</script>

<style lang="scss" scoped>
  .login.register {
    display: flex;
    align-items: center;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    background-color: var(--art-main-bg-color);

    .left-wrap {
      width: 60%;
      height: 100%;
    }

    .right-wrap {
      display: flex;
      flex: 1;
      flex-direction: column;
      height: 100%;
      padding: 0 40px;

      .header {
        display: flex;
        align-items: center;
        padding: 30px 0;

        .icon {
          width: 45px;
          height: 45px;
          color: var(--el-color-primary);
        }

        h1 {
          margin-left: 12px;
          font-size: 22px;
          color: var(--art-text-color);
        }
      }

      .login-wrap {
        display: flex;
        flex: 1;
        align-items: center;
        justify-content: center;

        .form {
          width: 100%;
          max-width: 380px;

          .title {
            margin-bottom: 12px;
            font-size: 28px;
            font-weight: 600;
            color: var(--art-text-color);
          }

          .sub-title {
            margin-bottom: 30px;
            font-size: 14px;
            color: var(--art-text-color-secondary);
          }

          .register-btn {
            width: 100%;
          }

          .footer {
            margin-top: 20px;
            color: var(--art-text-color-secondary);
            text-align: center;

            a {
              color: var(--el-color-primary);
              text-decoration: none;
            }
          }
        }
      }
    }
  }

  @media screen and (width <= 768px) {
    .login.register {
      .left-wrap {
        display: none;
      }

      .right-wrap {
        width: 100%;
      }
    }
  }
</style>
