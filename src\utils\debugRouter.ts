/**
 * 路由调试工具
 * 用于诊断路由导航问题
 */

import { useUserStore } from '@/store/modules/user'
import { useMenuStore } from '@/store/modules/menu'
import { router } from '@/router'

/**
 * 路由调试器
 */
export class RouterDebugger {
  /**
   * 检查当前用户状态
   */
  static checkUserStatus() {
    const userStore = useUserStore()
    const status = {
      isLogin: userStore.isLogin,
      hasToken: !!userStore.accessToken,
      tokenLength: userStore.accessToken?.length || 0,
      tokenValid: userStore.checkTokenValid(),
      userInfo: userStore.getUserInfo,
      timestamp: new Date().toISOString()
    }

    return status
  }

  /**
   * 检查菜单状态
   */
  static checkMenuStatus() {
    const menuStore = useMenuStore()
    const status = {
      hasMenuList: !!menuStore.menuList,
      menuCount: menuStore.menuList?.length || 0,
      menuList: menuStore.menuList,
      timestamp: new Date().toISOString()
    }

    return status
  }

  /**
   * 检查路由状态
   */
  static checkRouterStatus() {
    const currentRoute = router.currentRoute.value
    const allRoutes = router.getRoutes()

    const status = {
      currentPath: currentRoute.path,
      currentName: currentRoute.name,
      currentMatched: currentRoute.matched.length,
      totalRoutes: allRoutes.length,
      dynamicRoutes: allRoutes.filter((route) => !route.path.startsWith('/exception')).length,
      timestamp: new Date().toISOString()
    }

    return status
  }

  /**
   * 检查本地存储
   */
  static checkLocalStorage() {
    const accessToken = localStorage.getItem('accessToken')
    const sysData = localStorage.getItem('sys-v' + import.meta.env.VITE_VERSION)

    const status = {
      hasAccessToken: !!accessToken,
      accessTokenLength: accessToken?.length || 0,
      hasSysData: !!sysData,
      sysDataSize: sysData?.length || 0,
      timestamp: new Date().toISOString()
    }

    return status
  }

  /**
   * 全面诊断
   */
  static fullDiagnosis() {
    const userStatus = this.checkUserStatus()
    const menuStatus = this.checkMenuStatus()
    const routerStatus = this.checkRouterStatus()
    const storageStatus = this.checkLocalStorage()

    console.log('='.repeat(50))

    // 分析问题
    const issues = []
    const suggestions = []

    if (!userStatus.isLogin) {
      issues.push('用户未登录')
      suggestions.push('需要跳转到登录页面')
    }

    if (!userStatus.hasToken) {
      issues.push('缺少访问令牌')
      suggestions.push('需要重新登录获取令牌')
    }

    if (!userStatus.tokenValid) {
      issues.push('令牌无效或已过期')
      suggestions.push('需要刷新令牌或重新登录')
    }

    if (!menuStatus.hasMenuList || menuStatus.menuCount === 0) {
      issues.push('菜单数据缺失')
      suggestions.push('需要重新获取菜单数据')
    }

    if (routerStatus.currentMatched === 0) {
      issues.push('当前路由未匹配')
      suggestions.push('可能需要注册本地路由')
    }

    console.log('⚠️ 发现的问题:', issues)
    console.log('💡 建议的解决方案:', suggestions)

    // 返回诊断结果
    return {
      userStatus,
      menuStatus,
      routerStatus,
      storageStatus,
      issues,
      suggestions,
      timestamp: new Date().toISOString()
    }
  }

  /**
   * 强制跳转到登录页
   */
  static forceToLogin() {
    const userStore = useUserStore()
    userStore.logOut()
    router
      .push('/login')
      .then(() => {})
      .catch((error) => {
        // 如果路由跳转失败，直接修改浏览器地址
        window.location.href = '/login'
      })
  }

  /**
   * 清理所有状态
   */
  static clearAllState() {
    // 清理用户状态
    const userStore = useUserStore()
    userStore.logOut()

    // 清理菜单状态
    const menuStore = useMenuStore()
    menuStore.setMenuList([])

    // 清理本地存储
    localStorage.removeItem('accessToken')
    const version = import.meta.env.VITE_VERSION
    localStorage.removeItem(`sys-v${version}`)
    sessionStorage.clear()
  }

  /**
   * 重新初始化
   */
  static reinitialize() {
    this.clearAllState()

    setTimeout(() => {
      this.forceToLogin()
    }, 500)
  }
}

// 在开发环境下，将调试工具挂载到全局对象
if (process.env.NODE_ENV === 'development') {
  ;(window as any).routerDebugger = RouterDebugger
}

export default RouterDebugger
