<template>
  <div :id="chartId" class="water-emission-chart"></div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted, watch } from 'vue'
  import * as echarts from 'echarts'

  // 使用props接收数据
  const props = defineProps({
    chartData: {
      type: Array,
      required: true
    }
  })

  // 生成唯一的图表ID，避免多个图表实例冲突
  const chartId = `water-emission-chart-${Date.now()}`
  // 图表实例
  const chartInstance = ref(null)

  // 处理窗口大小变化
  const handleResize = () => {
    if (chartInstance.value) {
      chartInstance.value.resize()
    }
  }

  // 初始化图表
  const initChart = () => {
    // 获取DOM元素
    const chartDom = document.getElementById(chartId)
    if (!chartDom) return

    // 创建图表实例
    chartInstance.value = echarts.init(chartDom)

    // 更新图表数据
    updateChart()

    // 添加窗口大小变化的监听器
    window.addEventListener('resize', handleResize)
  }

  // 更新图表数据
  const updateChart = () => {
    if (!chartInstance.value) return

    const data = props.chartData.filter((item) => item.type !== '总计')

    const types = data.map((item) => item.type)
    const consumptionData = data.map((item) => parseFloat(item.consumption.replace(/,/g, '')))
    const emissionData = data.map((item) => parseFloat(item.emission.replace(/,/g, '')))

    const option = {
      title: {
        text: '水资源碳排放分析',
        left: 'center',
        top: 10,
        textStyle: {
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: ['用水量 (m³)', '碳排放量 (kg CO₂)'],
        bottom: 10
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        top: '15%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          data: types,
          axisPointer: {
            type: 'shadow'
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          name: '用水量 (m³)',
          position: 'left',
          axisLine: {
            show: true,
            lineStyle: {
              color: '#409EFF'
            }
          },
          axisLabel: {
            formatter: '{value}'
          }
        },
        {
          type: 'value',
          name: '碳排放量 (kg CO₂)',
          position: 'right',
          axisLine: {
            show: true,
            lineStyle: {
              color: '#67C23A'
            }
          },
          axisLabel: {
            formatter: '{value}'
          }
        }
      ],
      series: [
        {
          name: '用水量 (m³)',
          type: 'bar',
          yAxisIndex: 0,
          data: consumptionData,
          itemStyle: {
            color: '#409EFF'
          },
          barWidth: '30%'
        },
        {
          name: '碳排放量 (kg CO₂)',
          type: 'bar',
          yAxisIndex: 1,
          data: emissionData,
          itemStyle: {
            color: '#67C23A'
          },
          barWidth: '30%'
        }
      ]
    }

    // 使用配置项设置图表
    chartInstance.value.setOption(option)
  }

  // 监听数据变化
  watch(
    () => props.chartData,
    () => {
      updateChart()
    },
    { deep: true }
  )

  // 组件挂载时初始化图表
  onMounted(() => {
    initChart()
    // 延迟执行一次resize，确保图表正确渲染
    setTimeout(() => {
      handleResize()
    }, 200)
  })

  // 组件卸载时销毁图表实例
  onUnmounted(() => {
    if (chartInstance.value) {
      chartInstance.value.dispose()
      chartInstance.value = null
    }
    window.removeEventListener('resize', handleResize)
  })
</script>

<style scoped>
  .water-emission-chart {
    width: 100%;
    height: 300px;
    min-height: 300px;
  }
</style>
