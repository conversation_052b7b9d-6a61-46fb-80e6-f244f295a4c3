<template>
  <div class="test-container">
    <h2>时间格式化测试</h2>

    <div class="test-section">
      <h3>测试数据</h3>
      <div class="test-data">
        <div v-for="(item, index) in testData" :key="index" class="test-item">
          <span class="label">原始数据:</span>
          <span class="value">{{ JSON.stringify(item.reportTime) }}</span>
          <span class="label">格式化后:</span>
          <span class="formatted">{{ formatReportTime(item.reportTime) }}</span>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>模拟表格</h3>
      <el-table :data="testData" border>
        <el-table-column prop="projectName" label="项目名称" align="center" />
        <el-table-column prop="buildingName" label="楼栋" align="center" />
        <el-table-column label="时间" align="center">
          <template #default="{ row }">
            {{ formatReportTime(row.reportTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="elecQuantity" label="电(kWh)" align="center" />
      </el-table>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue'

  // 测试数据
  const testData = ref([
    {
      projectName: '测试项目1',
      buildingName: '1号楼',
      reportTime: '20250531',
      elecQuantity: 100.5
    },
    {
      projectName: '测试项目2',
      buildingName: '2号楼',
      reportTime: '2025-05-31',
      elecQuantity: 200.3
    },
    {
      projectName: '测试项目3',
      buildingName: '3号楼',
      reportTime: null,
      elecQuantity: 150.8
    },
    {
      projectName: '测试项目4',
      buildingName: '4号楼',
      reportTime: undefined,
      elecQuantity: 300.2
    },
    {
      projectName: '测试项目5',
      buildingName: '5号楼',
      reportTime: '',
      elecQuantity: 250.7
    }
  ])

  // 格式化报告时间，将 YYYYMMDD 格式转换为 YYYY-MM-DD
  const formatReportTime = (reportTime: string | undefined | null) => {
    console.log('formatReportTime 输入:', reportTime, '类型:', typeof reportTime)

    if (!reportTime) {
      return '--'
    }

    // 转换为字符串
    const timeStr = String(reportTime).trim()

    // 如果是8位数字格式 YYYYMMDD
    if (timeStr.length === 8 && /^\d{8}$/.test(timeStr)) {
      const year = timeStr.substring(0, 4)
      const month = timeStr.substring(4, 6)
      const day = timeStr.substring(6, 8)
      return `${year}-${month}-${day}`
    }

    // 如果已经是 YYYY-MM-DD 格式，直接返回
    if (/^\d{4}-\d{2}-\d{2}$/.test(timeStr)) {
      return timeStr
    }

    // 其他情况返回原值或 --
    return timeStr || '--'
  }
</script>

<style scoped>
  .test-container {
    max-width: 1200px;
    padding: 20px;
    margin: 0 auto;
  }

  .test-section {
    margin-bottom: 30px;
  }

  .test-data {
    padding: 15px;
    background: #f5f5f5;
    border-radius: 8px;
  }

  .test-item {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 10px;
  }

  .label {
    min-width: 80px;
    font-weight: bold;
    color: #666;
  }

  .value {
    padding: 4px 8px;
    font-family: monospace;
    background: #e3f2fd;
    border-radius: 4px;
  }

  .formatted {
    padding: 4px 8px;
    font-family: monospace;
    color: #2e7d32;
    background: #e8f5e8;
    border-radius: 4px;
  }
</style>
