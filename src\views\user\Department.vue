<template>
  <div class="page-content">
    <el-row :gutter="12">
      <el-col :xs="24" :sm="12" :lg="8">
        <el-input v-model="searchForm.deptName" placeholder="部门名称" clearable></el-input>
      </el-col>
      <el-col :xs="24" :sm="12" :lg="8" class="el-col2">
        <el-button v-ripple @click="handleSearch">搜索</el-button>
        <el-button @click="resetSearch" v-ripple>重置</el-button>
        <el-button @click="showDialog('add')" type="primary" v-ripple>新增部门</el-button>
      </el-col>
    </el-row>

    <art-table
      :data="tableData"
      v-loading="loading"
      row-key="deptId"
      default-expand-all
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
      <template #default>
        <el-table-column prop="deptName" label="部门名称" />
        <el-table-column prop="orderNum" label="排序" sortable />
        <el-table-column prop="leader" label="负责人" />
        <el-table-column prop="phone" label="联系电话" />
        <el-table-column prop="email" label="邮箱" />
        <el-table-column prop="createTime" label="创建时间" />

        <el-table-column label="状态" prop="status">
          <template #default="scope">
            <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">
              {{ scope.row.status === '0' ? '正常' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="150px">
          <template #default="scope">
            <ArtButtonTable type="edit" @click="showDialog('edit', scope.row)" />
            <ArtButtonTable type="delete" @click="deleteDepartment(scope.row)" />
          </template>
        </el-table-column>
      </template>
    </art-table>

    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '添加部门' : '编辑部门'"
      width="500px"
      @closed="resetForm"
    >
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="80px">
        <el-form-item label="上级部门" prop="parentId">
          <el-tree-select
            v-model="formData.parentId"
            :data="deptTreeOptions"
            :props="{ value: 'deptId', label: 'deptName', children: 'children' }"
            placeholder="选择上级部门"
            check-strictly
            :render-after-expand="false"
            clearable
          />
        </el-form-item>
        <el-form-item label="部门名称" prop="deptName">
          <el-input v-model="formData.deptName" placeholder="请输入部门名称" />
        </el-form-item>
        <el-form-item label="显示排序" prop="orderNum">
          <el-input-number v-model="formData.orderNum" :min="0" controls-position="right" />
        </el-form-item>
        <el-form-item label="负责人" prop="leader">
          <el-input v-model="formData.leader" placeholder="请输入负责人" />
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="formData.phone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="formData.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="部门状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio value="0">正常</el-radio>
            <el-radio value="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitLoading">
            {{ dialogType === 'add' ? '新增' : '修改' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ElMessageBox, ElMessage } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'
  import {
    getDepartmentList,
    createDepartment,
    updateDepartment,
    deleteDepartment as deleteDepartmentApi,
    type DepartmentInfo,
    type DepartmentQueryParams
  } from '@/api/departmentApi'

  // 响应式数据
  const dialogType = ref('add')
  const dialogVisible = ref(false)
  const loading = ref(false)
  const submitLoading = ref(false)

  // 搜索表单
  const searchForm = reactive<DepartmentQueryParams>({
    deptName: ''
  })

  // 表单数据
  const formData = reactive({
    deptId: undefined as number | undefined,
    parentId: 0,
    deptName: '',
    orderNum: 0,
    leader: '',
    phone: '',
    email: '',
    status: '0'
  })

  // 表格数据
  const tableData = ref<DepartmentInfo[]>([])

  // 部门树形选择器数据
  const deptTreeOptions = ref<DepartmentInfo[]>([])

  // 表单引用
  const formRef = ref<FormInstance>()

  // 表单验证规则
  const rules = reactive<FormRules>({
    deptName: [
      { required: true, message: '请输入部门名称', trigger: 'blur' },
      { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
    ],
    orderNum: [{ required: true, message: '请输入显示排序', trigger: 'blur' }],
    email: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }],
    phone: [{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }]
  })

  // 获取部门列表
  const getList = async () => {
    try {
      loading.value = true
      const response = await getDepartmentList(searchForm)
      if (response.code === 200) {
        tableData.value = response.data
        // 构建部门树形选择器数据（添加根节点）
        deptTreeOptions.value = [
          {
            deptId: 0,
            deptName: '主类目',
            parentId: -1,
            orderNum: 0,
            status: '0',
            children: response.data
          }
        ]
      }
    } catch (error) {
      ElMessage.error('获取部门列表失败')
    } finally {
      loading.value = false
    }
  }

  // 搜索
  const handleSearch = () => {
    getList()
  }

  // 重置搜索
  const resetSearch = () => {
    searchForm.deptName = ''
    getList()
  }

  // 重置表单
  const resetForm = () => {
    formData.deptId = undefined
    formData.parentId = 0
    formData.deptName = ''
    formData.orderNum = 0
    formData.leader = ''
    formData.phone = ''
    formData.email = ''
    formData.status = '0'
  }

  // 显示对话框
  const showDialog = (type: string, row?: DepartmentInfo) => {
    dialogType.value = type
    dialogVisible.value = true

    if (type === 'edit' && row) {
      formData.deptId = row.deptId
      formData.parentId = row.parentId
      formData.deptName = row.deptName
      formData.orderNum = row.orderNum
      formData.leader = row.leader || ''
      formData.phone = row.phone || ''
      formData.email = row.email || ''
      formData.status = row.status
    } else {
      resetForm()
    }
  }

  // 删除部门
  const deleteDepartment = (row: DepartmentInfo) => {
    ElMessageBox.confirm(`确定要删除部门"${row.deptName}"吗？`, '删除部门', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      try {
        const response = await deleteDepartmentApi(row.deptId!)
        if (response.code === 200) {
          ElMessage.success('删除成功')
          getList()
        } else {
          ElMessage.error(response.msg || '删除失败')
        }
      } catch (error) {
        ElMessage.error('删除失败')
      }
    })
  }

  // 提交表单
  const submitForm = async () => {
    if (!formRef.value) return

    try {
      const valid = await formRef.value.validate()
      if (valid) {
        submitLoading.value = true

        const submitData = {
          ...formData
        }

        let response
        if (dialogType.value === 'add') {
          const { deptId, ...createData } = submitData
          response = await createDepartment(createData)
        } else {
          if (!submitData.deptId) {
            ElMessage.error('部门ID不能为空')
            return
          }
          response = await updateDepartment(submitData as any)
        }

        if (response.code === 200) {
          ElMessage.success(dialogType.value === 'add' ? '新增成功' : '修改成功')
          dialogVisible.value = false
          getList()
        } else {
          ElMessage.error(response.msg || '操作失败')
        }
      }
    } catch (error) {
      ElMessage.error('操作失败')
    } finally {
      submitLoading.value = false
    }
  }

  // 页面加载时获取数据
  onMounted(() => {
    getList()
  })
</script>

<style lang="scss" scoped>
  .page-content {
    .svg-icon {
      width: 1.8em;
      height: 1.8em;
      overflow: hidden;
      vertical-align: -8px;
      fill: currentcolor;
    }
  }
</style>
