<template>
  <div class="chart-container">
    <div ref="chartRef" class="chart"></div>
  </div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted, watch } from 'vue'
  import * as echarts from 'echarts'

  // 定义props
  const props = defineProps({
    // 图表类型：line - 折线图，bar - 柱状图，pie - 饼图
    chartType: {
      type: String,
      default: 'line'
    },
    // 图表标题
    title: {
      type: String,
      default: '数据分析'
    },
    // 图表数据
    chartData: {
      type: Object,
      required: true
    },
    // 图表高度
    height: {
      type: String,
      default: '400px'
    }
  })

  const chartRef = ref(null)
  let chart = null
  let resizeObserver = null

  // 初始化图表
  const initChart = () => {
    if (!chartRef.value) return

    // 销毁已存在的图表实例
    if (chart) {
      chart.dispose()
    }

    // 创建图表实例
    chart = echarts.init(chartRef.value)

    // 设置响应式
    resizeObserver = new ResizeObserver(() => {
      chart && chart.resize()
    })
    resizeObserver.observe(chartRef.value)

    // 更新图表
    updateChart()
  }

  // 更新图表数据
  const updateChart = () => {
    if (!chart) return

    // 根据图表类型设置不同的配置项
    let option = {}

    switch (props.chartType) {
      case 'line':
        option = getLineChartOption()
        break
      case 'bar':
        option = getBarChartOption()
        break
      case 'pie':
        option = getPieChartOption()
        break
      default:
        option = getLineChartOption()
    }

    // 设置图表配置
    chart.setOption(option)
  }

  // 获取折线图配置
  const getLineChartOption = () => {
    const { xAxis = [], series = [], legend = [] } = props.chartData

    return {
      title: {
        text: props.title,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        }
      },
      legend: {
        data: legend,
        bottom: 0
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: xAxis,
        axisLabel: {
          rotate: xAxis.length > 10 ? 45 : 0
        }
      },
      yAxis: {
        type: 'value'
      },
      series: series.map((item) => ({
        name: item.name,
        type: 'line',
        smooth: true,
        data: item.data,
        // 面积图效果
        areaStyle: item.areaStyle || null,
        // 线条样式
        lineStyle: item.lineStyle || { width: 2 },
        // 标记点
        markPoint: item.markPoint || {
          data: [
            { type: 'max', name: '最大值' },
            { type: 'min', name: '最小值' }
          ]
        }
      }))
    }
  }

  // 获取柱状图配置
  const getBarChartOption = () => {
    const { xAxis = [], series = [], legend = [] } = props.chartData

    return {
      title: {
        text: props.title,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: legend,
        bottom: 0
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: xAxis,
        axisLabel: {
          rotate: xAxis.length > 10 ? 45 : 0
        }
      },
      yAxis: {
        type: 'value'
      },
      series: series.map((item) => ({
        name: item.name,
        type: 'bar',
        data: item.data,
        // 柱状图样式
        itemStyle: item.itemStyle || {
          borderRadius: [4, 4, 0, 0]
        },
        // 标签
        label: item.label || {
          show: false,
          position: 'top'
        }
      }))
    }
  }

  // 获取饼图配置
  const getPieChartOption = () => {
    const { series = [] } = props.chartData

    return {
      title: {
        text: props.title,
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'horizontal',
        bottom: 0,
        data: series[0]?.data.map((item) => item.name) || []
      },
      series: [
        {
          name: series[0]?.name || '数据占比',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: series[0]?.data || []
        }
      ]
    }
  }

  // 监听图表类型和数据变化
  watch(
    () => [props.chartType, props.chartData],
    () => {
      nextTick(() => {
        updateChart()
      })
    },
    { deep: true }
  )

  // 组件挂载时初始化图表
  onMounted(() => {
    nextTick(() => {
      initChart()
    })
  })

  // 组件销毁时清理图表实例
  onUnmounted(() => {
    if (resizeObserver) {
      resizeObserver.disconnect()
    }
    if (chart) {
      chart.dispose()
      chart = null
    }
  })
</script>

<style lang="scss" scoped>
  .chart-container {
    width: 100%;

    .chart {
      width: 100%;
      height: v-bind('props.height');
    }
  }
</style>
