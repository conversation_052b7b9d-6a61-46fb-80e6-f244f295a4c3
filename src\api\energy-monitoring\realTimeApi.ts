import request from '@/utils/http'

// 实时能耗相关接口

/**
 * 获取实时能耗列表
 * @param params 查询参数
 */
export interface RealEnergyListParams {
  projectId?: string | number
  buildingId?: string | number
  energyType?: string
  pageSize: number
  pageNum: number
}

/**
 * 实时能耗数据项
 */
export interface RealEnergyItem {
  id: number
  projectName: string
  buildingName: string
  energyType: string | null
  equipmentStatus: string
  totalEnergy: number | null
  todayEnergy: number | null
  unitValue: string | null
}

/**
 * 实时能耗列表响应
 */
export interface RealEnergyListResponse {
  total: number
  rows: RealEnergyItem[]
  code: number
  msg: string
}

/**
 * 获取实时能耗列表
 */
export function getRealEnergyList(params: RealEnergyListParams): Promise<RealEnergyListResponse> {
  return request.get({
    url: '/energy/energyconsume/realEnergyList',
    params
  })
}

/**
 * 表计趋势数据参数
 */
export interface MeterTrendParams {
  meterId: number
  startTime: string
  endTime: string
}

/**
 * 表计趋势数据响应
 */
export interface MeterTrendResponse {
  code: number
  message: string
  data: {
    times: string[]
    energyValues: number[]
    carbonValues: number[]
  }
  timestamp: number
}

/**
 * 获取表计趋势数据 (暂时使用模拟数据，等待后端提供真实接口)
 */
export function getMeterTrend(params: MeterTrendParams): Promise<MeterTrendResponse> {
  // TODO: 替换为真实的API调用
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        message: '成功',
        data: {
          times: [
            '00:00',
            '01:00',
            '02:00',
            '03:00',
            '04:00',
            '05:00',
            '06:00',
            '07:00',
            '08:00',
            '09:00',
            '10:00',
            '11:00',
            '12:00',
            '13:00',
            '14:00'
          ],
          energyValues: [
            0.1, 0.12, 0.08, 0.06, 0.05, 0.07, 0.1, 0.3, 0.5, 0.6, 0.7, 0.65, 0.58, 0.62, 0.7
          ],
          carbonValues: [
            0.05, 0.06, 0.04, 0.03, 0.025, 0.035, 0.05, 0.15, 0.25, 0.3, 0.35, 0.325, 0.29, 0.31,
            0.35
          ]
        },
        timestamp: Date.now()
      })
    }, 500)
  })
}

/**
 * 今日能耗曲线数据参数
 */
export interface RealEnergyDayListParams {
  id: number // energyId，对应实时能耗接口返回的id字段
}

/**
 * 今日能耗曲线数据项
 */
export interface RealEnergyDayDataItem {
  energyValue: number // 能耗值
  co2Value: number // 碳排放值
}

/**
 * 今日能耗曲线数据结构
 */
export interface RealEnergyDayData {
  timeList: string[] // 时间数组，如 ["00", "01", "02", ...]
  dataList: RealEnergyDayDataItem[] // 数据数组
}

/**
 * 今日能耗曲线响应
 */
export interface RealEnergyDayListResponse {
  code: number
  msg: string
  data: RealEnergyDayData
}

/**
 * 获取今日能耗曲线数据
 */
export function selectRealEnergyDayList(
  params: RealEnergyDayListParams
): Promise<RealEnergyDayListResponse> {
  // 调试信息：打印请求参数
  console.log('selectRealEnergyDayList 请求参数:', params)
  console.log('参数类型检查:', {
    id: params.id,
    idType: typeof params.id,
    idIsValid: params.id !== undefined && params.id !== null
  })

  // 确保参数有效
  if (!params.id) {
    console.error('selectRealEnergyDayList: id参数无效', params)
    return Promise.reject(new Error('id参数无效'))
  }

  return request.get({
    url: '/energy/energyconsume/selectRealEnergyDayList',
    params
  })
}
