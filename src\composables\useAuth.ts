import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import { useSettingStore } from '@/store/modules/setting'
import { login, getUserInfo } from '@/api/usersApi'
import { HOME_PAGE, resetRouteRegistration } from '@/router'
import { useRouter } from 'vue-router'

/**
 * 认证相关的组合函数
 */
export function useAuth() {
  const userStore = useUserStore()
  const settingStore = useSettingStore()
  const router = useRouter()

  /**
   * 用户登录
   * @param credentials 登录凭据
   * @returns Promise<boolean> 登录是否成功
   */
  const performLogin = async (credentials: {
    username: string
    password: string
  }): Promise<boolean> => {
    try {
      // 调用登录接口
      const res: any = await login(credentials)

      if (res.code === 200 && res.token) {
        // 设置 token
        userStore.setToken(res.token)

        // 获取用户信息
        try {
          const userRes: any = await getUserInfo()
          if (userRes.code === 200 && userRes.data) {
            userStore.setUserInfo(userRes.data)
          }
        } catch (error) {
          console.warn('获取用户信息失败:', error)
        }

        // 设置登录状态
        userStore.setLoginStatus(true)

        // 重置路由注册状态，允许重新获取路由
        resetRouteRegistration()

        // 保存用户数据（包含所有状态的持久化存储）
        userStore.saveUserData()

        // 隐藏设置引导popover
        settingStore.hideSettingGuide()

        return true
      } else {
        ElMessage.error(res.msg || '登录失败')
        return false
      }
    } catch (error: any) {
      console.error('登录错误:', error)
      ElMessage.error(error.response?.data?.msg || '登录失败，请检查网络连接')
      return false
    }
  }

  /**
   * 跳转到首页
   */
  const navigateToHome = () => {
    router.push(HOME_PAGE)
  }

  /**
   * 用户登出
   */
  const performLogout = () => {
    userStore.logOut()
  }

  return {
    performLogin,
    navigateToHome,
    performLogout
  }
}
