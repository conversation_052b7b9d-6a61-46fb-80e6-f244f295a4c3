<template>
  <div class="historical-dataset-container page-content">
    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <div class="filter-container">
        <div class="filter-row">
          <!-- 项目和楼栋选择器 -->
          <div class="filter-item">
            <ProjectBuildingSelector
              ref="projectBuildingSelectorRef"
              v-model="projectBuildingSelection"
              :auto-select-first="true"
              @change="handleProjectBuildingChange"
              @project-loaded="handleProjectLoaded"
            />
          </div>

          <div class="filter-item">
            <span class="filter-label">设备类型:</span>
            <el-select
              v-model="filterForm.equipmentType"
              placeholder="请选择设备类型"
              clearable
              class="filter-select"
              @change="handleEquipmentTypeChange"
            >
              <el-option label="室外环境监测" value="outdoor" />
              <el-option label="室内空气质量监测" value="indoor" />
              <el-option label="水质监测" value="water" />
            </el-select>
          </div>

          <div class="filter-item">
            <span class="filter-label">设备选择:</span>
            <el-select
              v-model="filterForm.equipmentId"
              placeholder="请选择设备"
              clearable
              class="filter-select"
              @change="handleEquipmentChange"
            >
              <el-option
                v-for="equipment in equipmentOptions"
                :key="equipment.id"
                :label="equipment.name"
                :value="equipment.id"
              />
            </el-select>
          </div>

          <div class="filter-item">
            <span class="filter-label">设备编号:</span>
            <el-input
              v-model="filterForm.equipmentCode"
              placeholder="请输入设备编号"
              clearable
              class="filter-select"
              @change="handleEquipmentCodeChange"
            />
          </div>

          <div class="filter-item">
            <span class="filter-label">时间粒度:</span>
            <el-radio-group
              v-model="filterForm.timeGranularity"
              @change="handleTimeGranularityChange"
              class="time-granularity-buttons"
            >
              <el-radio-button label="day">日</el-radio-button>
              <el-radio-button label="month">月</el-radio-button>
              <el-radio-button label="year">年</el-radio-button>
            </el-radio-group>
          </div>

          <div class="filter-item date-filter">
            <span class="filter-label">选择时间:</span>
            <div class="date-picker-group">
              <!-- 日期范围选择器 -->
              <el-date-picker
                v-if="filterForm.timeGranularity === 'day'"
                v-model="filterForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                class="date-picker"
                @change="handleDateRangeChange"
              />
              <!-- 月份范围选择器 -->
              <el-date-picker
                v-else-if="filterForm.timeGranularity === 'month'"
                v-model="filterForm.dateRange"
                type="monthrange"
                range-separator="至"
                start-placeholder="开始月份"
                end-placeholder="结束月份"
                value-format="YYYY-MM"
                class="date-picker"
                @change="handleDateRangeChange"
              />
              <!-- 年份单选选择器 -->
              <el-date-picker
                v-else-if="filterForm.timeGranularity === 'year'"
                v-model="filterForm.yearValue"
                type="year"
                placeholder="选择年份"
                value-format="YYYY"
                class="date-picker"
                @change="handleYearChange"
              />
            </div>
          </div>

          <div class="filter-actions">
            <el-button type="primary" @click="handleQuery" :disabled="queryButtonDisabled">
              <el-icon><Search /></el-icon> 查询
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon> 重置
            </el-button>
            <el-button type="success" @click="handleExport">
              <el-icon><Download /></el-icon> 导出列表
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 表格和操作区域 -->
    <el-card class="table-card">
      <!-- 使用 ArtTable 替代 el-table 和 el-pagination -->
      <ArtTable
        :data="tableData"
        :loading="loading"
        row-key="id"
        :border="false"
        :highlight-current-row="false"
        :total="totalCount"
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        index
        class="historical-table"
      >
        <el-table-column prop="projectName" label="项目名称" width="180" align="center" />
        <el-table-column prop="equipmentName" label="设备名称" width="180" align="center" />
        <el-table-column prop="equipmentCode" label="设备编号" width="120" align="center" />
        <el-table-column prop="monitoringTime" label="监测时间" width="160" align="center" />
        <el-table-column prop="dataType" label="数据类型" width="100" align="center" />
        <el-table-column prop="monitoringValue" label="监测值" width="100" align="center" />
        <el-table-column prop="unit" label="单位" width="80" align="center" />
        <el-table-column prop="status" label="数据状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">{{ getStatusText(row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" align="center" />
      </ArtTable>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted, watch, nextTick } from 'vue'
  import { ElMessage, FormInstance } from 'element-plus'
  import { Search, Refresh, Download } from '@element-plus/icons-vue'
  import ProjectBuildingSelector from '@/components/ProjectBuildingSelector.vue'
  // ArtTable组件已在全局注册，无需导入

  // 定义接口或类型
  interface TableRow {
    id?: number
    projectName: string
    equipmentName: string
    equipmentId: string
    equipmentCode: string
    equipmentType: string
    monitoringTime: string
    dataType: string
    monitoringValue: number | string
    unit: string
    status: 'normal' | 'abnormal' | 'offline'
    remark: string
  }

  interface FilterForm {
    equipmentType: string
    equipmentId: string
    equipmentCode: string
    timeGranularity: 'day' | 'month' | 'year'
    dateRange: [string, string] | undefined
    yearValue: string | undefined
  }

  interface EquipmentOption {
    id: string
    name: string
    code: string
    type: string
  }

  // 筛选表单的引用
  const filterFormRef = ref<FormInstance>()
  const loading = ref(false) // 表格加载状态

  // 项目楼栋选择相关数据
  const projectBuildingSelection = ref({
    projectId: null, // 初始为空，等待项目列表加载后设置第一个
    buildingId: null
  })
  const projectBuildingSelectorRef = ref()

  // 设置默认日期为当前日期往前推30天
  const setDefaultDateRange = (): [string, string] => {
    const today = new Date()
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const todayStr = today.toISOString().split('T')[0]
    const thirtyDaysAgoStr = thirtyDaysAgo.toISOString().split('T')[0]

    console.log('设置默认日期范围（前30天）:', {
      开始时间: thirtyDaysAgoStr,
      结束时间: todayStr
    })

    return [thirtyDaysAgoStr, todayStr]
  }

  // 设置默认月份范围为当前月份往前推一个月
  const setDefaultMonthRange = (): [string, string] => {
    const today = new Date()
    const oneMonthAgo = new Date()
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1)

    const todayStr = `${today.getFullYear()}-${(today.getMonth() + 1).toString().padStart(2, '0')}`
    const oneMonthAgoStr = `${oneMonthAgo.getFullYear()}-${(oneMonthAgo.getMonth() + 1).toString().padStart(2, '0')}`

    console.log('设置默认月份范围（前一个月）:', {
      开始月份: oneMonthAgoStr,
      结束月份: todayStr
    })

    return [oneMonthAgoStr, todayStr]
  }

  // 筛选表单数据
  const filterForm = reactive<FilterForm>({
    equipmentType: '',
    equipmentId: '',
    equipmentCode: '',
    timeGranularity: 'day', // 默认按日查询
    dateRange: setDefaultDateRange(), // 设置默认日期范围
    yearValue: undefined // 年份模式下才使用
  })

  // 设备选项数据 - 模拟数据，不调用接口
  const equipmentOptions = ref<EquipmentOption[]>([
    {
      id: 'outdoor_001',
      name: '室外环境监测设备01',
      code: 'ENV-OUT-001',
      type: 'outdoor'
    },
    {
      id: 'outdoor_002',
      name: '室外环境监测设备02',
      code: 'ENV-OUT-002',
      type: 'outdoor'
    },
    {
      id: 'indoor_001',
      name: '室内空气质量监测设备01',
      code: 'ENV-IN-001',
      type: 'indoor'
    },
    {
      id: 'indoor_002',
      name: '室内空气质量监测设备02',
      code: 'ENV-IN-002',
      type: 'indoor'
    },
    {
      id: 'water_001',
      name: '水质监测设备01',
      code: 'ENV-WAT-001',
      type: 'water'
    },
    {
      id: 'water_002',
      name: '水质监测设备02',
      code: 'ENV-WAT-002',
      type: 'water'
    }
  ])

  // 完整的模拟数据 - 与设备选择器对应
  const allMockData = ref<TableRow[]>([
    // 室外环境监测设备01 数据
    {
      id: 1,
      projectName: '示范项目A',
      equipmentName: '室外环境监测设备01',
      equipmentId: 'outdoor_001',
      equipmentCode: 'ENV-OUT-001',
      equipmentType: 'outdoor',
      monitoringTime: '2025-06-24 14:30:00',
      dataType: '温度',
      monitoringValue: 25.6,
      unit: '℃',
      status: 'normal',
      remark: '数据正常'
    },
    {
      id: 2,
      projectName: '示范项目A',
      equipmentName: '室外环境监测设备01',
      equipmentId: 'outdoor_001',
      equipmentCode: 'ENV-OUT-001',
      equipmentType: 'outdoor',
      monitoringTime: '2025-06-24 14:30:00',
      dataType: '湿度',
      monitoringValue: 68.2,
      unit: '%',
      status: 'normal',
      remark: '数据正常'
    },
    {
      id: 3,
      projectName: '示范项目A',
      equipmentName: '室外环境监测设备01',
      equipmentId: 'outdoor_001',
      equipmentCode: 'ENV-OUT-001',
      equipmentType: 'outdoor',
      monitoringTime: '2025-06-24 14:25:00',
      dataType: '噪声',
      monitoringValue: 42.3,
      unit: 'dB',
      status: 'normal',
      remark: '环境安静'
    },
    // 室外环境监测设备02 数据
    {
      id: 4,
      projectName: '示范项目C',
      equipmentName: '室外环境监测设备02',
      equipmentId: 'outdoor_002',
      equipmentCode: 'ENV-OUT-002',
      equipmentType: 'outdoor',
      monitoringTime: '2025-06-24 14:15:00',
      dataType: '温度',
      monitoringValue: 28.1,
      unit: '℃',
      status: 'normal',
      remark: '温度适宜'
    },
    {
      id: 5,
      projectName: '示范项目C',
      equipmentName: '室外环境监测设备02',
      equipmentId: 'outdoor_002',
      equipmentCode: 'ENV-OUT-002',
      equipmentType: 'outdoor',
      monitoringTime: '2025-06-24 14:15:00',
      dataType: '湿度',
      monitoringValue: 72.5,
      unit: '%',
      status: 'abnormal',
      remark: '湿度偏高'
    },
    {
      id: 6,
      projectName: '示范项目C',
      equipmentName: '室外环境监测设备02',
      equipmentId: 'outdoor_002',
      equipmentCode: 'ENV-OUT-002',
      equipmentType: 'outdoor',
      monitoringTime: '2025-06-24 14:10:00',
      dataType: '噪声',
      monitoringValue: 55.8,
      unit: 'dB',
      status: 'offline',
      remark: '设备离线'
    },
    // 室内空气质量监测设备01 数据
    {
      id: 7,
      projectName: '示范项目B',
      equipmentName: '室内空气质量监测设备01',
      equipmentId: 'indoor_001',
      equipmentCode: 'ENV-IN-001',
      equipmentType: 'indoor',
      monitoringTime: '2025-06-24 14:25:00',
      dataType: 'PM2.5',
      monitoringValue: 35,
      unit: 'μg/m³',
      status: 'abnormal',
      remark: '数值偏高'
    },
    {
      id: 8,
      projectName: '示范项目B',
      equipmentName: '室内空气质量监测设备01',
      equipmentId: 'indoor_001',
      equipmentCode: 'ENV-IN-001',
      equipmentType: 'indoor',
      monitoringTime: '2025-06-24 14:25:00',
      dataType: 'PM10',
      monitoringValue: 68,
      unit: 'μg/m³',
      status: 'normal',
      remark: '空气质量良好'
    },
    {
      id: 9,
      projectName: '示范项目B',
      equipmentName: '室内空气质量监测设备01',
      equipmentId: 'indoor_001',
      equipmentCode: 'ENV-IN-001',
      equipmentType: 'indoor',
      monitoringTime: '2025-06-24 14:20:00',
      dataType: 'CO2',
      monitoringValue: 450,
      unit: 'ppm',
      status: 'normal',
      remark: '二氧化碳浓度正常'
    },
    // 室内空气质量监测设备02 数据
    {
      id: 10,
      projectName: '示范项目A',
      equipmentName: '室内空气质量监测设备02',
      equipmentId: 'indoor_002',
      equipmentCode: 'ENV-IN-002',
      equipmentType: 'indoor',
      monitoringTime: '2025-06-24 14:18:00',
      dataType: 'PM2.5',
      monitoringValue: 28,
      unit: 'μg/m³',
      status: 'normal',
      remark: '空气质量优'
    },
    {
      id: 11,
      projectName: '示范项目A',
      equipmentName: '室内空气质量监测设备02',
      equipmentId: 'indoor_002',
      equipmentCode: 'ENV-IN-002',
      equipmentType: 'indoor',
      monitoringTime: '2025-06-24 14:18:00',
      dataType: 'TVOC',
      monitoringValue: 0.15,
      unit: 'mg/m³',
      status: 'normal',
      remark: '有机物浓度正常'
    },
    // 水质监测设备01 数据
    {
      id: 12,
      projectName: '示范项目B',
      equipmentName: '水质监测设备01',
      equipmentId: 'water_001',
      equipmentCode: 'ENV-WAT-001',
      equipmentType: 'water',
      monitoringTime: '2025-06-24 14:20:00',
      dataType: 'pH值',
      monitoringValue: 7.2,
      unit: '',
      status: 'normal',
      remark: '水质良好'
    },
    {
      id: 13,
      projectName: '示范项目B',
      equipmentName: '水质监测设备01',
      equipmentId: 'water_001',
      equipmentCode: 'ENV-WAT-001',
      equipmentType: 'water',
      monitoringTime: '2025-06-24 14:20:00',
      dataType: '溶解氧',
      monitoringValue: 8.5,
      unit: 'mg/L',
      status: 'normal',
      remark: '溶解氧充足'
    },
    {
      id: 14,
      projectName: '示范项目B',
      equipmentName: '水质监测设备01',
      equipmentId: 'water_001',
      equipmentCode: 'ENV-WAT-001',
      equipmentType: 'water',
      monitoringTime: '2025-06-24 14:15:00',
      dataType: '浊度',
      monitoringValue: 2.1,
      unit: 'NTU',
      status: 'abnormal',
      remark: '浊度略高'
    },
    // 水质监测设备02 数据
    {
      id: 15,
      projectName: '示范项目C',
      equipmentName: '水质监测设备02',
      equipmentId: 'water_002',
      equipmentCode: 'ENV-WAT-002',
      equipmentType: 'water',
      monitoringTime: '2025-06-24 14:12:00',
      dataType: 'pH值',
      monitoringValue: 6.8,
      unit: '',
      status: 'normal',
      remark: '酸碱度正常'
    },
    {
      id: 16,
      projectName: '示范项目C',
      equipmentName: '水质监测设备02',
      equipmentId: 'water_002',
      equipmentCode: 'ENV-WAT-002',
      equipmentType: 'water',
      monitoringTime: '2025-06-24 14:12:00',
      dataType: '电导率',
      monitoringValue: 285,
      unit: 'μS/cm',
      status: 'normal',
      remark: '电导率正常'
    },
    {
      id: 17,
      projectName: '示范项目C',
      equipmentName: '水质监测设备02',
      equipmentId: 'water_002',
      equipmentCode: 'ENV-WAT-002',
      equipmentType: 'water',
      monitoringTime: '2025-06-24 14:08:00',
      dataType: '氨氮',
      monitoringValue: 0.8,
      unit: 'mg/L',
      status: 'offline',
      remark: '传感器故障'
    }
  ])

  // 当前显示的表格数据
  const tableData = ref<TableRow[]>([])
  const totalCount = ref(0) // 总数据量

  // 分页配置
  const pagination = reactive({
    currentPage: 1,
    pageSize: 10
  })

  // 获取状态类型
  const getStatusType = (status: string) => {
    switch (status) {
      case 'normal':
        return 'success'
      case 'abnormal':
        return 'warning'
      case 'offline':
        return 'danger'
      default:
        return 'info'
    }
  }

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'normal':
        return '正常'
      case 'abnormal':
        return '异常'
      case 'offline':
        return '离线'
      default:
        return '未知'
    }
  }

  // 处理项目楼栋选择变化
  const handleProjectBuildingChange = (data: any) => {
    console.log('项目楼栋选择变化:', data)
    // 重置分页到第一页
    if (data.projectId) {
      pagination.currentPage = 1
    }
    // 不需要在这里手动触发查询，watch监听器会自动处理
  }

  // 处理设备类型变化
  const handleEquipmentTypeChange = (value: string | null) => {
    console.log('设备类型变化:', value)
    // 当设备类型变化时，清空设备选择和设备编号
    filterForm.equipmentId = ''
    filterForm.equipmentCode = ''
    // 不需要在这里手动触发查询，watch监听器会自动处理
  }

  // 处理设备选择变化
  const handleEquipmentChange = (value: string | null) => {
    console.log('设备选择变化:', value)
    // 当选择设备时，自动填充设备编号
    if (value) {
      const selectedEquipment = equipmentOptions.value.find((eq) => eq.id === value)
      if (selectedEquipment) {
        filterForm.equipmentCode = selectedEquipment.code
        console.log('自动填充设备编号:', selectedEquipment.code)
      }
    } else {
      filterForm.equipmentCode = ''
    }
    // 不需要在这里手动触发查询，watch监听器会自动处理
  }

  // 处理设备编号变化
  const handleEquipmentCodeChange = (value: string | null) => {
    console.log('设备编号变化:', value)
    // 当手动输入设备编号时，尝试匹配对应的设备
    if (value) {
      const matchedEquipment = equipmentOptions.value.find((eq) => eq.code === value)
      if (matchedEquipment && filterForm.equipmentId !== matchedEquipment.id) {
        filterForm.equipmentId = matchedEquipment.id
        console.log('自动匹配设备:', matchedEquipment.name)
      }
    }
    // 不需要在这里手动触发查询，watch监听器会自动处理
  }

  // 处理时间粒度变化
  const handleTimeGranularityChange = (value: string | number | boolean | undefined) => {
    const granularity = value as string
    console.log('时间粒度变化:', granularity)
    // 清空之前的时间选择
    filterForm.dateRange = undefined
    filterForm.yearValue = undefined

    // 根据时间粒度设置默认值
    if (granularity === 'day') {
      filterForm.dateRange = setDefaultDateRange()
    } else if (granularity === 'month') {
      filterForm.dateRange = setDefaultMonthRange()
    } else if (granularity === 'year') {
      // 年份默认选择当前年份
      const currentYear = new Date().getFullYear().toString()
      filterForm.yearValue = currentYear
    }

    // 不需要在这里手动触发查询，watch监听器会自动处理
  }

  // 处理日期范围变化
  const handleDateRangeChange = (value: [string, string] | null) => {
    console.log('日期范围变化:', value)
    // 不需要在这里手动触发查询，watch监听器会自动处理
  }

  // 处理年份变化
  const handleYearChange = (value: string | null) => {
    console.log('年份变化:', value)
    // 不需要在这里手动触发查询，watch监听器会自动处理
  }

  // 监听项目列表加载完成
  const handleProjectLoaded = (projectList: any[]) => {
    console.log('项目列表加载完成:', projectList)
    // 自动选择第一个项目
    if (projectList && projectList.length > 0 && !projectBuildingSelection.value.projectId) {
      const firstProject = projectList[0]
      projectBuildingSelection.value.projectId = firstProject.id
      console.log('自动选择第一个项目:', firstProject)

      // 设置初始化完成标记，启用自动查询
      setTimeout(() => {
        isInitialized.value = true
        // 自动执行查询（这里只是模拟，不实际发送请求）
        fetchHistoricalData()
      }, 100) // 稍微延迟确保组件完全初始化
    } else {
      // 即使没有项目，也要设置初始化完成标记
      setTimeout(() => {
        isInitialized.value = true
      }, 100)
    }
  }

  // 数据筛选函数
  const filterData = () => {
    let filteredData = [...allMockData.value]

    // 根据设备类型筛选
    if (filterForm.equipmentType) {
      filteredData = filteredData.filter((item) => item.equipmentType === filterForm.equipmentType)
    }

    // 根据设备ID筛选
    if (filterForm.equipmentId) {
      filteredData = filteredData.filter((item) => item.equipmentId === filterForm.equipmentId)
    }

    // 根据设备编号筛选
    if (filterForm.equipmentCode) {
      filteredData = filteredData.filter((item) =>
        item.equipmentCode.includes(filterForm.equipmentCode)
      )
    }

    // 根据时间范围筛选（这里简化处理，实际应该根据具体时间范围筛选）
    // 由于是模拟数据，这里只做示例性筛选
    if (filterForm.dateRange || filterForm.yearValue) {
      // 可以根据需要添加时间筛选逻辑
      console.log('时间筛选条件:', {
        timeGranularity: filterForm.timeGranularity,
        dateRange: filterForm.dateRange,
        yearValue: filterForm.yearValue
      })
    }

    return filteredData
  }

  // 获取历史数据 - 模拟查询，不发送实际请求
  const fetchHistoricalData = async () => {
    // 如果没有选择项目，不执行查询
    if (!projectBuildingSelection.value.projectId) {
      console.log('没有选择项目，不执行查询')
      tableData.value = []
      totalCount.value = 0
      return
    }

    try {
      loading.value = true

      // 构建查询参数（仅用于日志输出）
      let beginTime: string | undefined
      let endTime: string | undefined

      // 根据时间粒度设置时间参数
      if (filterForm.timeGranularity === 'year' && filterForm.yearValue) {
        // 年份选择：设置为该年的第一天和最后一天
        beginTime = `${filterForm.yearValue}-01-01`
        endTime = `${filterForm.yearValue}-12-31`
      } else if (filterForm.dateRange) {
        // 日期范围或月份范围选择
        beginTime = filterForm.dateRange[0]
        endTime = filterForm.dateRange[1]

        // 如果是月份选择，需要转换为具体的日期
        if (filterForm.timeGranularity === 'month') {
          if (beginTime) {
            beginTime = `${beginTime}-01` // 月份开始日期
          }
          if (endTime) {
            // 计算月份的最后一天
            const [year, month] = endTime.split('-')
            const lastDay = new Date(parseInt(year), parseInt(month), 0).getDate()
            endTime = `${endTime}-${lastDay.toString().padStart(2, '0')}`
          }
        }
      }

      const params = {
        projectId: projectBuildingSelection.value.projectId,
        buildingId: projectBuildingSelection.value.buildingId || undefined,
        beginTime,
        endTime,
        equipmentType: filterForm.equipmentType || undefined,
        equipmentId: filterForm.equipmentId || undefined,
        equipmentCode: filterForm.equipmentCode || undefined,
        pageSize: pagination.pageSize,
        pageNum: pagination.currentPage
      }

      console.log('模拟查询参数:', params)

      // 模拟API调用延迟
      await new Promise((resolve) => setTimeout(resolve, 300))

      // 筛选数据
      const filteredData = filterData()

      // 分页处理
      const startIndex = (pagination.currentPage - 1) * pagination.pageSize
      const endIndex = startIndex + pagination.pageSize
      const paginatedData = filteredData.slice(startIndex, endIndex)

      // 更新表格数据
      tableData.value = paginatedData
      totalCount.value = filteredData.length

      console.log(
        '模拟查询成功，筛选后数据量:',
        filteredData.length,
        '当前页数据:',
        paginatedData.length
      )

      // 保存当前查询参数
      lastQueryParams.value = { ...params }
      // 禁用查询按钮（因为已经查询过相同条件）
      queryButtonDisabled.value = true
    } catch (error) {
      console.error('模拟查询失败:', error)
      ElMessage.error('查询失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  // 查询状态管理 - 防重复查询
  const lastQueryParams = ref<any>(null)
  const queryButtonDisabled = ref(false) // 查询按钮状态

  // 比较查询参数是否相同
  const isSameQueryParams = (params1: any, params2: any) => {
    if (!params1 || !params2) return false
    return (
      params1.projectId === params2.projectId &&
      params1.buildingId === params2.buildingId &&
      params1.beginTime === params2.beginTime &&
      params1.endTime === params2.endTime &&
      params1.equipmentType === params2.equipmentType &&
      params1.equipmentId === params2.equipmentId &&
      params1.equipmentCode === params2.equipmentCode &&
      params1.pageSize === params2.pageSize &&
      params1.pageNum === params2.pageNum
    )
  }

  // 查询处理
  const handleQuery = () => {
    // 如果查询按钮已禁用，不执行查询
    if (queryButtonDisabled.value) {
      ElMessage.info('查询条件未变化，无需重复查询')
      return
    }

    // 构建当前查询参数
    let beginTime: string | undefined
    let endTime: string | undefined

    // 根据时间粒度设置时间参数
    if (filterForm.timeGranularity === 'year' && filterForm.yearValue) {
      beginTime = `${filterForm.yearValue}-01-01`
      endTime = `${filterForm.yearValue}-12-31`
    } else if (filterForm.dateRange) {
      beginTime = filterForm.dateRange[0]
      endTime = filterForm.dateRange[1]

      if (filterForm.timeGranularity === 'month') {
        if (beginTime) {
          beginTime = `${beginTime}-01`
        }
        if (endTime) {
          const [year, month] = endTime.split('-')
          const lastDay = new Date(parseInt(year), parseInt(month), 0).getDate()
          endTime = `${endTime}-${lastDay.toString().padStart(2, '0')}`
        }
      }
    }

    const currentParams = {
      projectId: projectBuildingSelection.value.projectId,
      buildingId: projectBuildingSelection.value.buildingId || undefined,
      beginTime,
      endTime,
      equipmentType: filterForm.equipmentType || undefined,
      equipmentId: filterForm.equipmentId || undefined,
      equipmentCode: filterForm.equipmentCode || undefined,
      pageSize: pagination.pageSize,
      pageNum: 1 // 查询时重置为第一页
    }

    // 检查查询参数是否与上次相同
    if (isSameQueryParams(currentParams, lastQueryParams.value)) {
      ElMessage.info('查询条件未变化')
      queryButtonDisabled.value = true
      return
    }

    pagination.currentPage = 1
    fetchHistoricalData()
  }

  // 重置处理
  const handleReset = () => {
    // 重置项目楼栋选择
    if (projectBuildingSelectorRef.value) {
      projectBuildingSelectorRef.value.reset()
    }

    // 重置筛选表单
    filterFormRef.value?.resetFields()
    filterForm.equipmentType = ''
    filterForm.equipmentId = ''
    filterForm.equipmentCode = ''
    filterForm.timeGranularity = 'day' // 重置为日期模式
    filterForm.dateRange = setDefaultDateRange() // 设置默认日期范围
    filterForm.yearValue = undefined // 清空年份值

    // 重置分页
    pagination.currentPage = 1

    // 清空查询缓存，允许重新查询
    lastQueryParams.value = null
    queryButtonDisabled.value = false

    ElMessage.info('筛选条件已重置。')
  }

  // 导出列表处理 - 暂时占位
  const handleExport = () => {
    ElMessage.info('导出列表功能待实现。')
  }

  // 分页大小改变处理
  const handleSizeChange = (val: number) => {
    pagination.pageSize = val
    pagination.currentPage = 1
    fetchHistoricalData()
  }

  // 当前页改变处理
  const handleCurrentChange = (val: number) => {
    pagination.currentPage = val
    fetchHistoricalData()
  }

  // 标记是否已初始化完成
  const isInitialized = ref(false)

  // 监听查询条件变化，自动触发查询
  watch(
    [
      () => projectBuildingSelection.value,
      () => filterForm.equipmentType,
      () => filterForm.equipmentId,
      () => filterForm.equipmentCode,
      () => filterForm.timeGranularity,
      () => filterForm.dateRange,
      () => filterForm.yearValue
    ],
    (newVal, oldVal) => {
      // 只有在初始化完成后才自动触发查询
      if (!isInitialized.value) {
        return
      }

      // 当查询条件发生变化时，自动触发查询
      if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
        console.log('查询条件变化，自动触发查询:', {
          项目选择: projectBuildingSelection.value,
          设备类型: filterForm.equipmentType,
          设备选择: filterForm.equipmentId,
          设备编号: filterForm.equipmentCode,
          时间粒度: filterForm.timeGranularity,
          日期范围: filterForm.dateRange,
          年份值: filterForm.yearValue
        })

        // 清空上次查询参数，允许重新查询
        lastQueryParams.value = null
        queryButtonDisabled.value = false

        // 如果有项目选择，自动触发查询
        if (projectBuildingSelection.value.projectId) {
          nextTick(() => {
            fetchHistoricalData()
          })
        }
      }
    },
    { deep: true }
  )

  onMounted(() => {
    // 页面加载完成后，如果有默认日期，等待项目列表加载完成后自动查询
    console.log('页面初始化完成，默认日期范围:', filterForm.dateRange)

    // 初始化时显示所有数据
    const filteredData = filterData()
    const startIndex = (pagination.currentPage - 1) * pagination.pageSize
    const endIndex = startIndex + pagination.pageSize
    const paginatedData = filteredData.slice(startIndex, endIndex)

    tableData.value = paginatedData
    totalCount.value = filteredData.length

    console.log('初始化数据加载完成，总数据量:', filteredData.length)
  })
</script>

<style scoped>
  .historical-dataset-container {
    padding: 20px;
    background-color: #f0f2f5;
  }

  .filter-card {
    margin-bottom: 20px;
    border-radius: 4px;
  }

  .table-card {
    min-height: 400px;
    border-radius: 4px;
  }

  /* 确保ArtTable组件有足够的显示空间 */
  .table-card :deep(.art-table) {
    height: auto;
    min-height: 400px;
  }

  /* 筛选容器布局 - 参考可再生能源管理页面 */
  .filter-container {
    padding: 16px;
    margin-bottom: 0;

    .filter-row {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      align-items: center;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .filter-item {
      display: flex;
      align-items: center;
      margin-right: 0;

      .filter-label {
        margin-right: 8px;
        font-size: 14px;
        color: var(--el-text-color-regular);
        white-space: nowrap;
      }

      .filter-select {
        width: 200px;
      }

      .time-granularity-buttons {
        margin-left: 8px;
      }
    }

    .date-filter {
      .date-picker-group {
        display: flex;

        .date-picker {
          width: 100%;
          max-width: 300px;
        }
      }
    }

    .filter-actions {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      margin-left: auto;
    }
  }

  /* 修复el-table__empty-block高度不断增长问题 */
  .historical-table :deep(.el-table__empty-block) {
    height: auto !important;
    min-height: 60px;
    max-height: 400px;
    overflow: hidden;
  }

  /* 确保空表格状态下的布局稳定 */
  .historical-table :deep(.el-table__body-wrapper) {
    height: auto !important;
    min-height: 200px;
  }

  .el-button .el-icon + span {
    margin-left: 5px;
  }

  /* 响应式布局 */
  @media (width <= 1200px) {
    .filter-container .filter-row {
      gap: 12px;
    }

    .filter-item .filter-select {
      width: 180px;
    }

    .date-filter .date-picker-group .date-picker {
      max-width: 250px;
    }
  }

  @media (width <= 768px) {
    .filter-container .filter-row {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      .filter-item {
        justify-content: space-between;
        margin-right: 0;

        .filter-label {
          margin-right: 12px;
        }

        .filter-select {
          width: 100%;
          max-width: 300px;
        }

        .time-granularity-buttons {
          width: 100%;
          margin-left: 0;
        }
      }

      .date-filter {
        .date-picker-group .date-picker {
          width: 100%;
          max-width: none;
        }
      }

      .filter-actions {
        justify-content: center;
        margin-top: 10px;
        margin-left: 0;
      }
    }
  }

  @media (width <= 480px) {
    .filter-container {
      padding: 12px;
    }

    .filter-actions {
      flex-direction: column;
      gap: 8px;
      width: 100%;

      .el-button {
        width: 100%;
      }
    }
  }
</style>
