import api from '@/utils/http'

// ========== 信息安全参数配置相关接口 ==========

// 信息安全参数配置接口
export interface SecurityConfig {
  id?: number
  configName: string
  configKey: string
  configValue: string
  configType: string
  ipAddr?: string
  port?: string
  userName?: string
  pwd?: string
  qos?: string
  businessType?: string
  field1?: string
  field2?: string
  remark?: string
  createBy?: string
  createTime?: string
  updateBy?: string
  updateTime?: string
}

// 信息安全参数配置列表响应接口
export interface SecurityConfigListResponse {
  msg: string
  code: number
  total: number
  rows: SecurityConfig[]
}

// 信息安全参数配置查询参数
export interface SecurityConfigQueryParams {
  configName?: string
  configKey?: string
  configValue?: string
  configType?: string
  pageSize: number
  pageNum: number
}

// 通用响应接口
export interface CommonResponse {
  msg: string
  code: number
  data?: any
}

/**
 * 获取信息安全参数配置列表
 * @param params 查询参数
 * @returns 信息安全参数配置列表
 */
export function getSecurityConfigList(
  params: SecurityConfigQueryParams
): Promise<SecurityConfigListResponse> {
  return api.get({
    url: '/system/securityconfig/list',
    params
  })
}

/**
 * 获取信息安全参数配置详细信息
 * @param id 配置ID
 * @returns 配置详情
 */
export function getSecurityConfigDetail(id: number): Promise<CommonResponse> {
  return api.get({
    url: `/system/securityconfig/${id}`
  })
}

/**
 * 新增信息安全参数配置
 * @param data 配置数据
 * @returns 操作结果
 */
export function addSecurityConfig(data: Omit<SecurityConfig, 'id'>): Promise<CommonResponse> {
  return api.post({
    url: '/system/securityconfig/',
    data
  })
}

/**
 * 修改信息安全参数配置
 * @param data 配置数据
 * @returns 操作结果
 */
export function updateSecurityConfig(data: SecurityConfig): Promise<CommonResponse> {
  return api.put({
    url: '/system/securityconfig/',
    data
  })
}

/**
 * 删除信息安全参数配置
 * @param id 配置ID
 * @returns 操作结果
 */
export function deleteSecurityConfig(id: number): Promise<CommonResponse> {
  return api.del({
    url: `/system/securityconfig/${id}`
  })
}
