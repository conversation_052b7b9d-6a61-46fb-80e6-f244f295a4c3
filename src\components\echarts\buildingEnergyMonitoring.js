// 格式化日期的函数
export const formatDate = (date, type) => {
  const d = new Date(date)
  switch (type) {
    case 'day':
      return d.toISOString().split('T')[0]
    case 'month':
      return d.getFullYear() + '-' + String(d.getMonth() + 1).padStart(2, '0')
    case 'year':
      return String(d.getFullYear())
    default:
      return d.toISOString().split('T')[0]
  }
}

// 生成默认数据
export const generateDefaultData = (dateRange, type, options = {}) => {
  const { unit = 'kWh' } = options
  const data = []
  const end = new Date()
  let current = new Date(dateRange[0])

  while (current <= end) {
    data.push({
      date: formatDate(current, type),
      value: (Math.random() * 100 + 100).toFixed(2),
      unit: unit
    })

    switch (type) {
      case 'day':
        current.setDate(current.getDate() + 1)
        break
      case 'month':
        current.setMonth(current.getMonth() + 1)
        break
      case 'year':
        current.setFullYear(current.getFullYear() + 1)
        break
    }
  }
  return data
}

// 菜单标题配置 - 更新为正确的key对应关系
export const menuTitles = {
  // 用电数据 (0-4)
  building_total_electricity: '建筑总用电量',
  building_hvac_electricity: '建筑空调用电数据',
  building_lighting_electricity: '建筑照明用电数据',
  building_special_electricity: '建筑特殊用电数据',
  building_power_electricity: '建筑动力用电数据',
  // 用水数据 (5)
  building_water: '建筑用水数据',
  // 用气数据 (6)
  building_gas: '建筑用气数据',
  // 其他数据 (7)
  building_other: '其他数据',
  // 数字key对应关系
  0: '建筑总用电量',
  1: '建筑空调用电数据',
  2: '建筑照明用电数据',
  3: '建筑特殊用电数据',
  4: '建筑动力用电数据',
  5: '建筑用水数据',
  6: '建筑用气数据',
  7: '其他数据'
}
