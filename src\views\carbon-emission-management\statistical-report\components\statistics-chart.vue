<template>
  <div class="statistics-chart" ref="chartRef"></div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
  import * as echarts from 'echarts'
  import { useSettingStore } from '@/store/modules/setting'
  import { SystemThemeEnum } from '@/enums/appEnum'

  // 获取主题设置
  const settingStore = useSettingStore()
  const isDark = computed(() => settingStore.systemThemeType === SystemThemeEnum.DARK)

  // 接收props
  const props = defineProps({
    chartData: {
      type: Array,
      required: true
    }
  })

  // 图表引用
  const chartRef = ref(null)
  let chartInstance = null

  // 初始化图表
  const initChart = () => {
    if (!chartRef.value) return

    // 创建图表实例
    chartInstance = echarts.init(chartRef.value)

    // 更新图表
    updateChart()

    // 监听窗口大小变化
    window.addEventListener('resize', handleResize)
  }

  // 更新图表
  const updateChart = () => {
    if (!chartInstance || !props.chartData || props.chartData.length === 0) return

    // 准备数据
    const xAxisData = props.chartData.map((item) => item.time)
    const electricityData = props.chartData.map((item) => parseFloat(item.electricity))
    const waterData = props.chartData.map((item) => parseFloat(item.water))
    const carbonEmissionData = props.chartData.map((item) => parseFloat(item.carbonEmission))

    // 设置图表选项
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: ['电力消耗', '水资源消耗', '碳排放量'],
        textStyle: {
          color: isDark.value ? 'rgba(255, 255, 255, 0.7)' : '#333'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: xAxisData,
        axisLine: {
          lineStyle: {
            color: isDark.value ? 'rgba(255, 255, 255, 0.3)' : '#ccc'
          }
        },
        axisLabel: {
          color: isDark.value ? 'rgba(255, 255, 255, 0.7)' : '#666'
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '消耗量',
          axisLine: {
            lineStyle: {
              color: isDark.value ? 'rgba(255, 255, 255, 0.3)' : '#ccc'
            }
          },
          axisLabel: {
            color: isDark.value ? 'rgba(255, 255, 255, 0.7)' : '#666',
            formatter: '{value}'
          },
          splitLine: {
            lineStyle: {
              color: isDark.value ? 'rgba(255, 255, 255, 0.1)' : '#eee'
            }
          }
        },
        {
          type: 'value',
          name: '碳排放量',
          position: 'right',
          axisLine: {
            lineStyle: {
              color: isDark.value ? 'rgba(255, 255, 255, 0.3)' : '#ccc'
            }
          },
          axisLabel: {
            color: isDark.value ? 'rgba(255, 255, 255, 0.7)' : '#666',
            formatter: '{value} kg'
          },
          splitLine: {
            show: false
          }
        }
      ],
      series: [
        {
          name: '电力消耗',
          type: 'bar',
          barWidth: '20%',
          data: electricityData,
          itemStyle: {
            color: isDark.value ? 'rgba(93, 135, 255, 0.8)' : 'rgba(93, 135, 255, 1)'
          }
        },
        {
          name: '水资源消耗',
          type: 'bar',
          barWidth: '20%',
          data: waterData,
          itemStyle: {
            color: isDark.value ? 'rgba(19, 222, 185, 0.8)' : 'rgba(19, 222, 185, 1)'
          }
        },
        {
          name: '碳排放量',
          type: 'line',
          yAxisIndex: 1,
          data: carbonEmissionData,
          symbolSize: 8,
          lineStyle: {
            width: 3,
            color: isDark.value ? 'rgba(255, 174, 31, 0.8)' : 'rgba(255, 174, 31, 1)'
          },
          itemStyle: {
            color: isDark.value ? 'rgba(255, 174, 31, 0.8)' : 'rgba(255, 174, 31, 1)'
          }
        }
      ]
    }

    // 应用配置
    chartInstance.setOption(option)
  }

  // 处理窗口大小变化
  const handleResize = () => {
    chartInstance && chartInstance.resize()
  }

  // 监听主题变化
  watch(
    () => settingStore.systemThemeType,
    () => {
      if (chartInstance) {
        // 销毁并重新创建图表
        chartInstance.dispose()
        chartInstance = echarts.init(chartRef.value)
        updateChart()
      }
    }
  )

  // 监听数据变化
  watch(
    () => props.chartData,
    () => {
      updateChart()
    },
    { deep: true }
  )

  // 组件挂载时初始化图表
  onMounted(() => {
    initChart()
  })

  // 组件卸载时清理资源
  onUnmounted(() => {
    if (chartInstance) {
      window.removeEventListener('resize', handleResize)
      chartInstance.dispose()
      chartInstance = null
    }
  })
</script>

<style lang="scss" scoped>
  .statistics-chart {
    width: 100%;
    height: 400px;
    margin-bottom: 20px;
  }
</style>
