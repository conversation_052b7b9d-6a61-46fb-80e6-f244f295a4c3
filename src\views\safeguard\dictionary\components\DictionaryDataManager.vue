<template>
  <div class="dictionary-data-manager">
    <!-- 查询条件区域 -->
    <div class="filter-section">
      <el-form :model="queryParams" inline>
        <el-form-item label="字典类型:">
          <el-select v-model="queryParams.dictType" placeholder="请选择字典类型" clearable>
            <el-option
              v-for="item in dictTypeOptions"
              :key="item.dictType"
              :label="`${item.dictName}(${item.dictType})`"
              :value="item.dictType"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="字典标签:">
          <el-input v-model="queryParams.dictLabel" placeholder="请输入字典标签" clearable />
        </el-form-item>
        <el-form-item label="字典键值:">
          <el-input v-model="queryParams.dictValue" placeholder="请输入字典键值" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :icon="Search">查询</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 字典数据表格 -->
    <div class="table-section">
      <div class="table-operation">
        <el-button type="primary" @click="handleAdd" :icon="Plus">新增字典数据</el-button>
      </div>

      <ArtTable
        :data="tableData"
        :loading="loading"
        row-key="dictCode"
        :border="false"
        :highlight-current-row="false"
        :total="totalCount"
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        class="dict-table"
      >
        <el-table-column prop="index" label="序号" align="center" width="80" />
        <el-table-column prop="dictSort" label="排序" align="center" width="80" />
        <el-table-column prop="dictLabel" label="字典标签" align="center" />
        <el-table-column prop="dictValue" label="字典键值" align="center" />
        <el-table-column prop="dictType" label="字典类型" align="center" />
        <el-table-column prop="remark" label="备注" align="center">
          <template #default="scope">
            <span>{{ scope.row.remark || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" align="center">
          <template #default="scope">
            <span>{{ formatDate(scope.row.createTime) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="180" align="center">
          <template #default="scope">
            <div class="operation-btns">
              <el-button
                link
                size="small"
                @click="handleEdit(scope.row)"
                title="编辑"
                class="operation-btn edit-btn"
              >
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button
                link
                size="small"
                @click="handleDelete(scope.row)"
                title="删除"
                class="operation-btn delete-btn"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
      </ArtTable>
    </div>

    <!-- 新增/编辑字典数据对话框 -->
    <el-dialog
      :title="dialogType === 'add' ? '新增字典数据' : '编辑字典数据'"
      v-model="dialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="字典类型" prop="dictType">
          <el-select v-model="form.dictType" placeholder="请选择字典类型" style="width: 100%">
            <el-option
              v-for="item in dictTypeOptions"
              :key="item.dictType"
              :label="`${item.dictName}(${item.dictType})`"
              :value="item.dictType"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="字典标签" prop="dictLabel">
          <el-input v-model="form.dictLabel" placeholder="请输入字典标签" />
        </el-form-item>
        <el-form-item label="字典键值" prop="dictValue">
          <el-input v-model="form.dictValue" placeholder="请输入字典键值" />
        </el-form-item>
        <el-form-item label="排序" prop="dictSort">
          <el-input-number v-model="form.dictSort" :min="0" style="width: 100%" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitLoading"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, reactive, watch, onMounted, computed } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Search, Edit, Delete, Plus } from '@element-plus/icons-vue'
  import {
    getDictDataList,
    createDictData,
    updateDictData,
    deleteDictData,
    getDictTypeList
  } from '@/api/dictApi.ts'

  // 定义组件props
  const props = defineProps({
    dictTypeOptions: {
      type: Array,
      default: () => []
    },
    defaultDictType: {
      type: String,
      default: ''
    }
  })

  // 内部字典类型选项（当父组件未提供时使用）
  const internalDictTypeOptions = ref([])

  // 计算属性：获取字典类型选项（优先使用父组件传递的，否则使用内部获取的）
  const dictTypeOptions = computed(() => {
    return props.dictTypeOptions.length > 0 ? props.dictTypeOptions : internalDictTypeOptions.value
  })

  // 查询参数
  const queryParams = reactive({
    dictType: '',
    dictLabel: '',
    dictValue: '',
    pageNum: 1,
    pageSize: 10
  })

  // 表格数据和分页
  const tableData = ref([])
  const loading = ref(false)
  const totalCount = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(10)

  // 对话框相关
  const dialogVisible = ref(false)
  const dialogType = ref('add')
  const formRef = ref(null)
  const submitLoading = ref(false)

  // 表单数据
  const form = reactive({
    dictCode: null,
    dictType: '',
    dictLabel: '',
    dictValue: '',
    dictSort: 1,
    remark: ''
  })

  // 表单验证规则
  const rules = {
    dictType: [{ required: true, message: '请选择字典类型', trigger: 'change' }],
    dictLabel: [
      { required: true, message: '请输入字典标签', trigger: 'blur' },
      { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
    ],
    dictValue: [
      { required: true, message: '请输入字典键值', trigger: 'blur' },
      { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
    ],
    dictSort: [{ required: true, message: '请输入排序', trigger: 'blur' }]
  }

  // 监听默认字典类型变化
  watch(
    () => props.defaultDictType,
    (newVal) => {
      if (newVal) {
        queryParams.dictType = newVal
        fetchData()
      }
    },
    { immediate: true }
  )

  // 获取字典类型列表（内部使用）
  const fetchDictTypeList = async () => {
    try {
      const response = await getDictTypeList({
        pageSize: 99,
        pageNum: 1
      })
      if (response.code === 200 || response.code === 1) {
        internalDictTypeOptions.value = (response.rows || []).map((item) => ({
          dictType: item.dictType,
          dictName: item.dictName
        }))
      }
    } catch (error) {
      console.error('获取字典类型列表失败:', error)
    }
  }

  // 格式化日期
  const formatDate = (dateString) => {
    if (!dateString) return '-'
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  }

  // 获取字典数据列表
  const fetchData = async () => {
    loading.value = true
    try {
      const params = {
        dictType: queryParams.dictType || undefined,
        dictLabel: queryParams.dictLabel || undefined,
        dictValue: queryParams.dictValue || undefined,
        pageSize: pageSize.value,
        pageNum: currentPage.value
      }

      console.log('获取字典数据参数:', params)
      const response = await getDictDataList(params)
      console.log('字典数据响应:', response)

      if (response.code === 200 || response.code === 1) {
        const data = response.rows || response.data || []
        tableData.value = data.map((item, index) => ({
          ...item,
          // 确保有 dictCodeId 字段用于删除
          dictCodeId: item.dictCodeId || item.dictCode,
          index: (currentPage.value - 1) * pageSize.value + index + 1
        }))
        totalCount.value = response.total || data.length || 0

        if (data.length === 0) {
          console.log('数据为空，当前查询条件:', queryParams)
        }
      } else {
        console.error('获取字典数据失败:', response)
        ElMessage.error(response.msg || '获取字典数据列表失败')
        tableData.value = []
        totalCount.value = 0
      }
    } catch (error) {
      console.error('获取字典数据列表失败:', error)
      // 如果接口不存在，使用模拟数据
      if (error.response?.status === 404) {
        ElMessage.warning('字典数据接口暂未实现，使用模拟数据')
        const mockData = [
          {
            dictCode: 1,
            dictCodeId: 1,
            dictSort: 1,
            dictLabel: '耗水',
            dictValue: '0',
            dictType: 'yw_nhlx',
            remark: '水资源消耗',
            createTime: '2024-01-01 10:00:00'
          },
          {
            dictCode: 2,
            dictCodeId: 2,
            dictSort: 2,
            dictLabel: '耗电',
            dictValue: '1',
            dictType: 'yw_nhlx',
            remark: '电力消耗',
            createTime: '2024-01-01 10:01:00'
          },
          {
            dictCode: 3,
            dictCodeId: 3,
            dictSort: 3,
            dictLabel: '耗气',
            dictValue: '2',
            dictType: 'yw_nhlx',
            remark: '天然气消耗',
            createTime: '2024-01-01 10:02:00'
          },
          {
            dictCode: 4,
            dictCodeId: 4,
            dictSort: 4,
            dictLabel: '光伏',
            dictValue: '3',
            dictType: 'yw_nhlx',
            remark: '光伏发电',
            createTime: '2024-01-01 10:03:00'
          }
        ]

        // 根据查询条件过滤数据
        let filteredData = mockData
        if (queryParams.dictType) {
          filteredData = filteredData.filter((item) => item.dictType === queryParams.dictType)
        }
        if (queryParams.dictLabel) {
          filteredData = filteredData.filter((item) =>
            item.dictLabel.includes(queryParams.dictLabel)
          )
        }
        if (queryParams.dictValue) {
          filteredData = filteredData.filter((item) =>
            item.dictValue.includes(queryParams.dictValue)
          )
        }

        // 分页处理
        const startIndex = (currentPage.value - 1) * pageSize.value
        const endIndex = startIndex + pageSize.value
        const paginatedData = filteredData.slice(startIndex, endIndex)

        tableData.value = paginatedData.map((item, index) => ({
          ...item,
          index: startIndex + index + 1
        }))
        totalCount.value = filteredData.length

        console.log('使用模拟数据，总数:', filteredData.length, '当前页数据:', paginatedData.length)
      } else {
        ElMessage.error('获取字典数据列表失败，请稍后重试')
        tableData.value = []
        totalCount.value = 0
      }
    } finally {
      loading.value = false
    }
  }

  // 搜索
  const handleSearch = () => {
    currentPage.value = 1
    fetchData()
  }

  // 重置表单
  const resetForm = () => {
    queryParams.dictType = ''
    queryParams.dictLabel = ''
    queryParams.dictValue = ''
    currentPage.value = 1
    fetchData()
  }

  // 分页处理
  const handleCurrentChange = (val) => {
    currentPage.value = val
    fetchData()
  }

  const handleSizeChange = (val) => {
    pageSize.value = val
    currentPage.value = 1
    fetchData()
  }

  // 新增
  const handleAdd = () => {
    dialogType.value = 'add'
    resetFormData()
    dialogVisible.value = true
  }

  // 编辑
  const handleEdit = (row) => {
    dialogType.value = 'edit'
    resetFormData()
    form.dictCode = row.dictCode
    form.dictType = row.dictType
    form.dictLabel = row.dictLabel
    form.dictValue = row.dictValue
    form.dictSort = row.dictSort
    form.remark = row.remark || ''
    dialogVisible.value = true
  }

  // 删除
  const handleDelete = (row) => {
    ElMessageBox.confirm(`确认删除字典数据 "${row.dictLabel}" 吗？删除后将无法恢复！`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        try {
          // 根据接口文档，删除接口需要 dictCodeId 参数
          // 数据中可能使用 dictCode 字段，需要适配
          const dictCodeId = row.dictCodeId || row.dictCode
          const response = await deleteDictData(dictCodeId)
          if (response.code === 200) {
            ElMessage.success(`删除字典数据 "${row.dictLabel}" 成功`)
            fetchData()
          } else {
            ElMessage.error(response.msg || '删除字典数据失败')
          }
        } catch (error) {
          console.error('删除字典数据失败:', error)
          if (error.response?.status === 404) {
            ElMessage.warning('删除接口暂未实现，请联系管理员')
          } else {
            ElMessage.error('删除字典数据失败')
          }
        }
      })
      .catch(() => {})
  }

  // 重置表单数据
  const resetFormData = () => {
    if (formRef.value) {
      formRef.value.resetFields()
    }
    form.dictCode = null
    form.dictType = ''
    form.dictLabel = ''
    form.dictValue = ''
    form.dictSort = 1
    form.remark = ''
  }

  // 提交表单
  const submitForm = () => {
    if (!formRef.value) return

    formRef.value.validate(async (valid) => {
      if (valid) {
        submitLoading.value = true
        try {
          const formData = {
            dictType: form.dictType,
            dictLabel: form.dictLabel,
            dictValue: form.dictValue,
            dictSort: form.dictSort,
            remark: form.remark
          }

          if (dialogType.value === 'add') {
            const response = await createDictData(formData)
            if (response.code === 200) {
              ElMessage.success('新增字典数据成功')
              dialogVisible.value = false
              fetchData()
            } else {
              ElMessage.error(response.msg || '新增字典数据失败')
            }
          } else {
            const requestData = {
              dictCode: form.dictCode,
              ...formData
            }
            const response = await updateDictData(requestData)
            if (response.code === 200) {
              ElMessage.success('编辑字典数据成功')
              dialogVisible.value = false
              fetchData()
            } else {
              ElMessage.error(response.msg || '编辑字典数据失败')
            }
          }
        } catch (error) {
          console.error(
            dialogType.value === 'add' ? '新增字典数据失败:' : '编辑字典数据失败:',
            error
          )
          ElMessage.error(dialogType.value === 'add' ? '新增字典数据失败' : '编辑字典数据失败')
        } finally {
          submitLoading.value = false
        }
      } else {
        ElMessage.warning('请填写完整的表单信息')
        return false
      }
    })
  }

  // 组件初始化
  onMounted(async () => {
    // 如果父组件没有提供字典类型选项，则自己获取
    if (props.dictTypeOptions.length === 0) {
      await fetchDictTypeList()
    }

    // 如果没有默认字典类型，也尝试加载数据
    if (!props.defaultDictType) {
      fetchData()
    }
  })

  // 暴露方法给父组件
  defineExpose({
    fetchData
  })
</script>

<style lang="scss" scoped>
  .dictionary-data-manager {
    // 筛选条件区域样式
    .filter-section {
      padding: 20px;
      margin-bottom: 20px;
      background-color: var(--art-main-bg-color);
      border: 1px solid var(--art-border-color);
      border-radius: 4px;
      box-shadow: var(--art-box-shadow-xs);

      .el-form {
        display: flex;
        flex-wrap: wrap;

        .el-form-item {
          margin-right: 10px;
          margin-bottom: 10px;

          // 按钮间距
          .el-button + .el-button {
            margin-left: 10px;
          }
        }
      }

      :deep(.el-select) {
        width: 200px;
      }

      :deep(.el-input) {
        width: 180px;
      }
    }

    // 表格区域样式
    .table-section {
      padding: 20px;
      background-color: var(--art-main-bg-color);
      border: 1px solid var(--art-border-color);
      border-radius: 4px;
      box-shadow: var(--art-box-shadow-xs);

      // 表格操作区域
      .table-operation {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 15px;

        .el-button {
          margin-right: 10px;
        }
      }

      // 确保ArtTable组件有足够的显示空间
      :deep(.art-table) {
        height: auto;
        min-height: 400px;
      }

      // 自定义操作按钮样式
      .operation-btns {
        display: flex;
        gap: 12px;
        justify-content: center;
      }

      .operation-btn {
        width: 32px !important;
        height: 32px !important;
        padding: 6px !important;
        margin: 0 !important;
        line-height: 1 !important;
        border: none !important;
        border-radius: 4px !important;
        box-shadow: 0 2px 4px rgb(0 0 0 / 10%) !important;
        transition: all 0.3s ease !important;
      }

      .operation-btn:hover {
        box-shadow: 0 4px 8px rgb(0 0 0 / 15%) !important;
        transform: translateY(-2px) !important;
      }

      .edit-btn {
        background-color: #e6f7ff !important;
      }

      .edit-btn .el-icon {
        font-size: 16px;
        color: #409eff !important;
      }

      .delete-btn {
        background-color: #fff1f0 !important;
      }

      .delete-btn .el-icon {
        font-size: 16px;
        color: #f56c6c !important;
      }

      // 修复el-table__empty-block高度不断增长问题
      .dict-table :deep(.el-table__empty-block) {
        height: auto !important;
        min-height: 60px;
        max-height: 400px;
        overflow: hidden;
      }

      // 确保空表格状态下的布局稳定
      .dict-table :deep(.el-table__body-wrapper) {
        height: auto !important;
        min-height: 200px;
      }
    }
  }

  // 对话框样式
  :deep(.el-dialog) {
    .el-dialog__header {
      background-color: var(--art-main-bg-color);
      border-bottom: 1px solid var(--art-border-color);
    }

    .el-dialog__body {
      padding: 20px;
    }

    .el-form-item__label {
      font-weight: 500;
    }
  }
</style>
