<template>
  <div>
    <div ref="chartRef" class="chart-container"></div>
  </div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted, watch } from 'vue'
  import * as echarts from 'echarts'

  // 从父组件接收查询参数
  const props = defineProps({
    timeGranularity: {
      type: String,
      default: 'day'
    },
    dateRange: {
      type: Array,
      default: () => []
    },
    chartData: {
      type: Object,
      default: null
    }
  })

  // 当前显示的数据点
  const currentValue = ref('')
  const currentTime = ref('')

  // 暴露图表DOM引用给父组件
  const chartRef = ref(null)
  defineExpose({ chartRef })

  // 模拟数据生成函数 (实际开发中应该通过API获取)
  const generateMockData = (granularity, startDate, endDate) => {
    const data = []
    const dates = []
    let current = new Date(startDate)
    const end = new Date(endDate)

    const step = granularity === 'day' ? 1 : granularity === 'month' ? 30 : 365

    const format =
      granularity === 'day'
        ? (date) => `${date.getMonth() + 1}.${date.getDate()}`
        : granularity === 'month'
          ? (date) => `${date.getFullYear()}-${date.getMonth() + 1}`
          : (date) => `${date.getFullYear()}`

    while (current <= end) {
      // 生成随机数据，但保持一定的趋势性
      const base = 5000 + Math.random() * 3000
      // 模拟白天发电量高，有日照变化的趋势
      let value
      if (granularity === 'day') {
        const dayFactor = Math.sin((current.getHours() / 24) * Math.PI) * 0.5 + 0.5
        value = Math.round(base * dayFactor)
      } else {
        value = Math.round(base + Math.random() * 2000)
      }

      dates.push(format(current))
      data.push(value)

      // 根据粒度增加日期
      if (granularity === 'day') {
        current.setDate(current.getDate() + step)
      } else if (granularity === 'month') {
        current.setMonth(current.getMonth() + 1)
      } else {
        current.setFullYear(current.getFullYear() + 1)
      }
    }

    // 设置初始当前值
    if (data.length > 0) {
      currentValue.value = data[0]
      currentTime.value = dates[0]
    }

    return { dates, data }
  }

  // 获取图表配置
  const getChartOption = (xAxisData, seriesData) => {
    return {
      animation: true,
      grid: {
        left: '5%',
        right: '5%',
        bottom: '8%',
        top: '15%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        formatter: function (params) {
          return `${params[0].name}<br/>${params[0].marker} 发电量: ${params[0].value} kWh`
        },
        axisPointer: {
          type: 'line',
          lineStyle: {
            color: 'rgba(0,0,0,0.2)',
            width: 1,
            type: 'solid'
          }
        }
      },
      xAxis: {
        type: 'category',
        data: xAxisData,
        axisLine: {
          lineStyle: {
            color: '#E5E7EB'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#909399',
          fontSize: 12,
          margin: 15
        }
      },
      yAxis: {
        type: 'value',
        name: 'kWh',
        nameTextStyle: {
          color: '#909399',
          padding: [0, 30, 0, 0],
          fontSize: 12
        },
        splitLine: {
          lineStyle: {
            color: '#F0F0F0',
            type: 'dashed'
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#909399',
          fontSize: 12,
          margin: 15
        }
      },
      series: [
        {
          data: seriesData,
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            color: '#409EFF'
          },
          lineStyle: {
            color: '#409EFF',
            width: 2
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(64, 158, 255, 0.3)'
                },
                {
                  offset: 1,
                  color: 'rgba(64, 158, 255, 0.05)'
                }
              ]
            }
          }
        }
      ]
    }
  }

  // 初始化图表
  let chart = null

  // 更新图表数据
  const updateChart = () => {
    let dates, data

    // 优先使用API数据，如果没有则使用模拟数据
    if (props.chartData && props.chartData.timeList && props.chartData.dataList) {
      dates = props.chartData.timeList
      data = props.chartData.dataList
      console.log('使用API数据更新图表:', { dates, data })
    } else if (props.dateRange && props.dateRange.length === 2) {
      const [startDate, endDate] = props.dateRange
      const mockData = generateMockData(props.timeGranularity, startDate, endDate)
      dates = mockData.dates
      data = mockData.data
      console.log('使用模拟数据更新图表:', { dates, data })
    } else {
      console.log('没有可用数据，跳过图表更新')
      return
    }

    if (chart) {
      chart.setOption(getChartOption(dates, data))

      // 监听图表鼠标悬停事件，显示当前值
      chart.off('mouseover')
      chart.off('mouseout')

      chart.on('mouseover', 'series', (params) => {
        currentValue.value = params.value
        currentTime.value = params.name
      })

      chart.on('mouseout', 'series', () => {
        // 鼠标移出时恢复到第一个数据点
        if (data.length > 0) {
          currentValue.value = data[0]
          currentTime.value = dates[0]
        }
      })
    }
  }

  // 监听属性变化
  watch(
    () => [props.timeGranularity, props.dateRange, props.chartData],
    () => {
      updateChart()
    },
    { deep: true }
  )

  onMounted(() => {
    if (chartRef.value) {
      chart = echarts.init(chartRef.value)
      updateChart()

      // 窗口大小变化时，重新调整图表大小
      window.addEventListener('resize', handleResize)
    }
  })

  onUnmounted(() => {
    if (chart) {
      chart.dispose()
      window.removeEventListener('resize', handleResize)
    }
  })

  const handleResize = () => {
    if (chart) {
      chart.resize()
    }
  }
</script>

<style lang="scss" scoped>
  .chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    margin-bottom: 10px;

    .chart-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }

    .current-data {
      display: flex;
      align-items: center;
      padding: 8px 15px;
      font-size: 14px;
      color: #606266;
      border-radius: 4px;
      box-shadow: 0 1px 2px rgb(0 0 0 / 5%);

      .value-container {
        margin-left: 5px;
        font-weight: 500;
        color: #409eff;
      }
    }
  }

  .chart-container {
    width: 100%;
    height: 350px;
    border-radius: 8px;
  }
</style>
