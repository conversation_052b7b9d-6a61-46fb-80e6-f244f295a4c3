// 路由别名
export enum RoutesAlias {
  Overview = '/project/overview',
  Home = '/index/index', // 首页
  Login = '/login', // 登录
  Register = '/register', // 注册
  ForgetPassword = '/forget-password', // 忘记密码
  Exception403 = '/exception/403', // 403
  Exception404 = '/exception/404', // 404
  Exception500 = '/exception/500', // 500
  Success = '/result/Success', // 成功
  Fail = '/result/Fail', // 失败
  Chat = '/ai-assistant', // 智能助手
  Account = '/user/Account', // 账户
  Department = '/user/Department', // 部门
  Role = '/user/Role', // 角色
  RolePermission = '/user/RolePermission', // 角色权限
  UserCenter = '/user/User', // 用户中心
  DeviceList = '/safeguard/DeviceList', // 设备列表
  // 系统管理
  PermissionPlaceholder = '/system/PermissionPlaceholder', // 权限占位符
  DemoProjectOverview = '/comprehensive-overview/project-overview',
  VisualizationDashboard = '/visualization-dashboard', // 可视化大屏
  EnergyMonitoringRealTime = '/energy-monitoring/real-time',
  EnergyMonitoringAnalysis = '/energy-monitoring/analysis',
  EnergyMonitoringData = '/energy-monitoring/data',
  EnergyMonitoringRenewableEnergy = '/energy-monitoring/renewable-energy',
  EnergyMonitoringQuery = '/energy-monitoring/query',
  EnvironmentMonitoringOverview = '/environment-monitoring/overview',
  EnvironmentMonitoringOutdoor = '/environment-monitoring/outdoor',
  EnvironmentMonitoringIndoorAirQuality = '/environment-monitoring/indoor-air-quality',
  EnvironmentMonitoringWaterQuality = '/environment-monitoring/water-quality',
  CarbonEmissionManagementOverview = '/carbon-emission-management/overview',
  CarbonEmissionManagementMonthOnMonth = '/carbon-emission-management/month-on-month',
  CarbonEmissionManagementYearOnYear = '/carbon-emission-management/year-on-year',
  CarbonEmissionManagementComparison = '/carbon-emission-management/comparison',
  CarbonEmissionManagementQuery = '/carbon-emission-management/query',
  CarbonEmissionManagementStatisticalReport = '/carbon-emission-management/statistical-report',
  CarbonEmissionManagementToolbox = '/carbon-emission-management/toolbox',
  CarbonFootprint = '/carbon-emission-management/carbon-footprint', // 碳足迹
  // 添加缺失的路由别名
  Fireworks = '/template/fireworks', // 礼花效果
  ChangeLog = '/template/changelog' // 更新日志
}
