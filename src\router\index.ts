import type { App } from 'vue'
import {
  createRouter,
  createWebHashHistory,
  RouteLocationNormalized,
  RouteRecordRaw
} from 'vue-router'
import { ref } from 'vue'
import Home from '@views/index/index.vue'
import AppConfig from '@/config'
import { useUserStore } from '@/store/modules/user'
import { menuService } from '@/api/menuApi'
import { useMenuStore } from '@/store/modules/menu'
import { useSettingStore } from '@/store/modules/setting'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { useTheme } from '@/composables/useTheme'
import { RoutesAlias } from './modules/routesAlias'
import { setWorktab } from '@/utils/worktab'
import { registerAsyncRoutes } from './modules/dynamicRoutes'
import { formatMenuTitle } from '@/utils/menu'
import { ElMessage } from 'element-plus'

// 开发环境下导入调试工具
if (process.env.NODE_ENV === 'development') {
  import('@/utils/debugRouter').then(() => {})
}

/** 顶部进度条配置 */
NProgress.configure({
  easing: 'ease',
  speed: 600,
  showSpinner: false,
  trickleSpeed: 200,
  parent: 'body'
})

/** 扩展的路由配置类型 */
export type AppRouteRecordRaw = RouteRecordRaw & {
  hidden?: boolean
}

/** 首页路径常量 */
export const HOME_PAGE = 'comprehensive-overview/project-overview'

/** 静态路由配置 */
const staticRoutes: AppRouteRecordRaw[] = [
  { path: '/', redirect: HOME_PAGE },
  {
    path: RoutesAlias.Login,
    name: 'Login',
    component: () => import('@views/login/index.vue'),
    meta: { title: 'menus.login.title', isHideTab: true, setTheme: true }
  },
  {
    path: RoutesAlias.Register,
    name: 'Register',
    component: () => import('@views/register/index.vue'),
    meta: { title: 'menus.register.title', isHideTab: true, noLogin: true, setTheme: true }
  },
  {
    path: RoutesAlias.ForgetPassword,
    name: 'ForgetPassword',
    component: () => import('@views/forget-password/index.vue'),
    meta: { title: 'menus.forgetPassword.title', isHideTab: true, noLogin: true, setTheme: true }
  },
  {
    path: '/test-time-format',
    name: 'TestTimeFormat',
    component: () => import('@views/test-time-format.vue'),
    meta: { title: '时间格式化测试', isHideTab: true, noLogin: true }
  },
  {
    path: '/exception',
    component: Home,
    name: 'Exception',
    meta: { title: 'menus.exception.title' },
    children: [
      {
        path: RoutesAlias.Exception403,
        name: 'Exception403',
        component: () => import('@/views/exception/403.vue'),
        meta: { title: '403' }
      },
      {
        path: '/:catchAll(.*)',
        name: 'Exception404',
        component: () => import('@views/exception/404.vue'),
        meta: { title: '404' }
      },
      {
        path: RoutesAlias.Exception500,
        name: 'Exception500',
        component: () => import('@views/exception/500.vue'),
        meta: { title: '500' }
      }
    ]
  },
  {
    path: '/outside',
    component: Home,
    name: 'Outside',
    meta: { title: 'menus.outside.title' },
    children: [
      {
        path: '/outside/iframe/:path',
        name: 'Iframe',
        component: () => import('@/views/outside/Iframe.vue'),
        meta: { title: 'iframe' }
      }
    ]
  }
]

/** 创建路由实例 */
export const router = createRouter({
  history: createWebHashHistory(),
  routes: staticRoutes,
  scrollBehavior: () => ({ left: 0, top: 0 })
})

// 标记是否已经注册本地路由
const isRouteRegistered = ref(false)
// 标记路由注册是否失败，避免无限重试
const routeRegistrationFailed = ref(false)

// 重置路由注册状态（用于重新登录后重试）
export const resetRouteRegistration = () => {
  isRouteRegistered.value = false
  routeRegistrationFailed.value = false
}

/**
 * 增强版路由全局前置守卫
 * 处理进度条、获取菜单列表、本地路由注册、404 检查、工作标签页及页面标题设置
 * 重点解决500错误和登录跳转问题
 */
router.beforeEach(async (to, from, next) => {
  const settingStore = useSettingStore()
  const userStore = useUserStore()

  // 开始进度条
  if (settingStore.showNprogress) NProgress.start()

  try {
    // 设置登录注册页面主题
    setSystemTheme(to)

    // 检查是否为白名单路由（不需要认证的路由）
    const whiteList = ['/login', '/register', '/forget-password']
    const isWhiteListRoute = whiteList.includes(to.path) || to.meta.noLogin

    // 如果是白名单路由，直接通过
    if (isWhiteListRoute) {
      return next()
    }

    // 强制检查登录状态 - 如果未登录或token无效，立即跳转登录页
    if (!userStore.isLogin || !userStore.checkTokenValid()) {
      ElMessage.warning('请先登录系统')
      userStore.logOut()
      return next('/login')
    }

    // 如果用户已登录且本地路由未注册，则注册本地路由
    if (!isRouteRegistered.value && userStore.isLogin && !routeRegistrationFailed.value) {
      // 跳过异常页面的路由注册，避免无限循环
      if (to.path.startsWith('/exception/')) {
        return next()
      }

      try {
        await getMenuData()

        // 路由注册成功后重新导航
        if (to.name === 'Exception404') {
          return next({ path: to.path, query: to.query, replace: true })
        } else {
          return next({ ...to, replace: true })
        }
      } catch (error) {
        routeRegistrationFailed.value = true

        // 根据错误类型决定跳转
        if ((error as any)?.response?.status === 401) {
          ElMessage.error('登录状态已过期，请重新登录')
          userStore.logOut()
          return next('/login')
        } else if ((error as any)?.response?.status === 403) {
          ElMessage.error('权限不足，无法获取菜单')
          return next('/exception/403')
        } else {
          // 对于其他错误（包括500），也跳转到登录页面
          ElMessage.error('系统出现问题，请重新登录')
          userStore.logOut()
          return next('/login')
        }
      }
    }

    // 检查路由是否存在，若不存在则跳转至404页面
    if (to.matched.length === 0) {
      return next('/exception/404')
    }

    // 设置工作标签页和页面标题
    setWorktab(to)
    setPageTitle(to)

    next()
  } catch (error) {
    // 对于任何未捕获的异常，都跳转到登录页面
    ElMessage.error('系统出现异常，请重新登录')
    userStore.logOut()
    return next('/login')
  }
})

/**
 * 根据接口返回的菜单列表注册本地路由（增强版）
 * @throws 若菜单列表为空或获取失败则抛出错误
 */
async function getMenuData(): Promise<void> {
  let closeLoading: (() => void) | null = null

  try {
    // 获取菜单列表
    const result = await menuService.getMenuList()
    const { menuList } = result
    closeLoading = result.closeLoading

    // 验证菜单列表数据
    if (!Array.isArray(menuList)) {
      throw new Error(`菜单列表数据格式错误: 期望数组，实际为 ${typeof menuList}`)
    }

    if (menuList.length === 0) {
      throw new Error('菜单列表为空，可能是权限不足或数据异常')
    }

    // 过滤和处理菜单列表
    const filteredMenuList = menuList.filter((menu) => {
      if (!menu || typeof menu !== 'object') {
        return false
      }
      return menu.path !== '/project/overview'
    })

    // 设置菜单列表到store
    useMenuStore().setMenuList(filteredMenuList as [])

    // 注册异步路由
    registerAsyncRoutes(router, filteredMenuList)

    // 标记路由已注册
    isRouteRegistered.value = true
  } catch (error) {
    // 根据错误类型进行不同处理
    const errorResponse = (error as any)?.response
    if (errorResponse?.status === 401) {
      throw new Error('认证失败，请重新登录')
    } else if (errorResponse?.status === 403) {
      throw new Error('权限不足，无法获取菜单列表')
    } else if (errorResponse?.status >= 500) {
      throw new Error('服务器错误，请稍后重试')
    } else if ((error as any)?.code === 'NETWORK_ERROR') {
      throw new Error('网络连接异常，请检查网络设置')
    } else {
      // 对于其他未知错误，也当作认证问题处理
      throw error
    }
  } finally {
    // 确保关闭加载动画
    if (closeLoading && typeof closeLoading === 'function') {
      try {
        closeLoading()
      } catch (e) {
        // 关闭加载动画失败，忽略此错误
      }
    }
  }
}

/* ============================
   路由守卫辅助函数
============================ */

/**
 * 根据路由元信息设置系统主题
 * @param to 当前路由对象
 */
const setSystemTheme = (to: RouteLocationNormalized): void => {
  if (to.meta.setTheme) {
    useTheme().switchThemeStyles(useSettingStore().systemThemeType)
  }
}

/**
 * 设置页面标题，始终显示系统名称
 * @param to 当前路由对象
 */
export const setPageTitle = (to: RouteLocationNormalized): void => {
  setTimeout(() => {
    document.title = AppConfig.systemInfo.name
  }, 150)
}

/** 路由全局后置守卫 */
router.afterEach(() => {
  if (useSettingStore().showNprogress) NProgress.done()
})

/**
 * 初始化路由，将 Vue Router 实例挂载到 Vue 应用中
 * @param app Vue 应用实例
 */
export function initRouter(app: App<Element>): void {
  app.use(router)
}
