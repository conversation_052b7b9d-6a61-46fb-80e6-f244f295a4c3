<template>
  <div class="project-building-selector" :class="customClass">
    <!-- 项目名称选择（多选） -->
    <div class="filter-item">
      <span class="filter-label">项目名称:</span>
      <MultiProjectSelector
        ref="multiProjectSelectorRef"
        v-model="internalProjectIds"
        placeholder="请选择项目"
        class="filter-select"
        :disabled="disabled"
        @change="handleProjectChange"
        @loaded="handleProjectListLoaded"
      />
    </div>

    <!-- 楼栋选择（当只选择一个项目时显示） -->
    <div class="filter-item" v-if="showBuildingSelector">
      <span class="filter-label">楼栋:</span>
      <el-select
        v-model="internalBuildingId"
        placeholder="请选择楼栋"
        class="filter-select"
        :disabled="disabled || buildingListLoading"
        :loading="buildingListLoading"
        clearable
        filterable
        @change="handleBuildingChange"
        style="width: 180px"
      >
        <el-option
          v-for="building in buildingList"
          :key="building.id"
          :label="building.name"
          :value="building.id"
        />
      </el-select>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch, nextTick } from 'vue'
  import { ElMessage } from 'element-plus'
  import MultiProjectSelector from './MultiProjectSelector.vue'
  import { getBuildingList } from '@/api/buildingApi'

  // 组件属性定义
  interface Props {
    modelValue?: {
      projectIds: number[]
      buildingId?: number | null
    }
    disabled?: boolean
    autoSelectFirst?: boolean
    customClass?: string
  }

  // 组件事件定义
  interface Emits {
    (e: 'update:modelValue', value: { projectIds: number[]; buildingId?: number | null }): void
    (
      e: 'change',
      data: {
        projectIds: number[]
        buildingId?: number | null
        projectInfoList: any[]
        buildingInfo?: any | null
      }
    ): void
    (e: 'project-loaded', projectList: any[]): void
  }

  // 定义属性和事件
  const props = withDefaults(defineProps<Props>(), {
    modelValue: () => ({ projectIds: [], buildingId: null }),
    disabled: false,
    autoSelectFirst: false,
    customClass: ''
  })

  const emit = defineEmits<Emits>()

  // 响应式数据
  const multiProjectSelectorRef = ref()
  const buildingList = ref([])
  const buildingListLoading = ref(false)
  const selectedProjectInfoList = ref([])

  // 内部状态
  const internalProjectIds = ref<number[]>(props.modelValue?.projectIds || [])
  const internalBuildingId = ref<number | null>(props.modelValue?.buildingId || null)

  // 计算属性：是否显示楼栋选择器（只有选择一个项目时才显示）
  const showBuildingSelector = computed(() => {
    return internalProjectIds.value.length === 1
  })

  // 监听外部值变化
  watch(
    () => props.modelValue,
    (newValue) => {
      if (newValue) {
        internalProjectIds.value = newValue.projectIds || []
        internalBuildingId.value = newValue.buildingId || null
      }
    },
    { deep: true, immediate: true }
  )

  // 监听内部值变化，向外发送
  watch([internalProjectIds, internalBuildingId], ([newProjectIds, newBuildingId]) => {
    const newValue = {
      projectIds: newProjectIds,
      buildingId: newBuildingId
    }
    emit('update:modelValue', newValue)
  })

  // 获取楼栋列表
  const fetchBuildingList = async (projectId: number) => {
    if (!projectId) {
      buildingList.value = []
      return
    }

    try {
      buildingListLoading.value = true
      const response = await getBuildingList(projectId)

      if (response.code === 200) {
        buildingList.value = response.data || []
      } else {
        ElMessage.error(response.msg || '获取楼栋列表失败')
        buildingList.value = []
      }
    } catch (error) {
      console.error('获取楼栋列表失败:', error)
      ElMessage.error('获取楼栋列表失败，请稍后重试')
      buildingList.value = []
    } finally {
      buildingListLoading.value = false
    }
  }

  // 处理项目变化
  const handleProjectChange = (projectInfoList: any[]) => {
    selectedProjectInfoList.value = projectInfoList

    // 如果选择的项目数量不是1，清空楼栋选择
    if (projectInfoList.length !== 1) {
      internalBuildingId.value = null
      buildingList.value = []
    } else {
      // 如果只选择了一个项目，获取该项目的楼栋列表
      const projectId = projectInfoList[0].id
      fetchBuildingList(projectId)
    }

    // 发送变化事件
    emit('change', {
      projectIds: internalProjectIds.value,
      buildingId: internalBuildingId.value,
      projectInfoList: projectInfoList,
      buildingInfo: null
    })
  }

  // 处理楼栋变化
  const handleBuildingChange = (buildingId: number | null) => {
    const buildingInfo = buildingId
      ? buildingList.value.find((item: any) => item.id === buildingId) || null
      : null

    // 发送变化事件
    emit('change', {
      projectIds: internalProjectIds.value,
      buildingId: buildingId,
      projectInfoList: selectedProjectInfoList.value,
      buildingInfo: buildingInfo
    })
  }

  // 监听项目列表加载完成
  const handleProjectListLoaded = (projectList: any[]) => {
    emit('project-loaded', projectList)

    // 如果启用自动选择第一个项目
    if (props.autoSelectFirst && projectList.length > 0) {
      const firstProject = projectList[0]
      internalProjectIds.value = [firstProject.id]
      selectedProjectInfoList.value = [firstProject]
      fetchBuildingList(firstProject.id)
    }
  }

  // 重置选择
  const reset = () => {
    internalProjectIds.value = []
    internalBuildingId.value = null
    selectedProjectInfoList.value = []
    buildingList.value = []

    // 如果启用自动选择第一个项目
    if (
      props.autoSelectFirst &&
      multiProjectSelectorRef.value &&
      multiProjectSelectorRef.value.projectList?.length > 0
    ) {
      const firstProject = multiProjectSelectorRef.value.projectList[0]
      internalProjectIds.value = [firstProject.id]
      selectedProjectInfoList.value = [firstProject]
      fetchBuildingList(firstProject.id)
    }
  }

  // 获取当前选中的项目信息列表
  const getSelectedProjectInfoList = () => {
    return selectedProjectInfoList.value
  }

  // 获取当前楼栋列表
  const getBuildingListData = () => {
    return buildingList.value
  }

  // 暴露方法给父组件
  defineExpose({
    reset,
    getSelectedProjectInfoList,
    getBuildingListData,
    fetchBuildingList
  })
</script>

<style scoped>
  .project-building-selector {
    display: flex;
    gap: 16px;
    align-items: center;
  }

  .filter-item {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .filter-label {
    font-size: 14px;
    color: #606266;
    white-space: nowrap;
  }

  .filter-select {
    min-width: 180px;
  }

  /* 内联样式 */
  .inline-selector {
    display: inline-flex;
  }

  .inline-selector .filter-item {
    margin-right: 16px;
  }
</style>
