<template>
  <div class="login-page">
    <!-- Logo 和平台名称 -->
    <div class="login-logo-container">
      <img src="@/assets/img/login/logo.png" alt="平台Logo" class="login-logo-img" />
      <div class="login-logo-text-container">
        <span class="login-logo-text">网点碳排放监管平台</span>
        <span class="login-logo-text-en"
          >Carbon Emission supervision platform for Bank Branches</span
        >
      </div>
    </div>

    <div class="login-card">
      <div class="platform-title">
        <h2>南京银行网点碳排放监管平台</h2>
        <h3>Bank of Nanjing Carbon Emission Platform</h3>
      </div>

      <el-form ref="formRef" :model="formData" :rules="rules" @keyup.enter="handleSubmit">
        <div class="input-group">
          <el-input
            :placeholder="$t('login.placeholder[0]')"
            v-model.trim="formData.username"
            class="custom-input"
          />
        </div>

        <div class="input-group">
          <el-input
            :placeholder="$t('login.placeholder[1]')"
            v-model.trim="formData.password"
            :type="showPassword ? 'text' : 'password'"
            autocomplete="off"
            class="custom-input"
          >
            <template #suffix>
              <el-icon class="password-toggle-icon" @click="togglePasswordVisibility">
                <Hide v-if="!showPassword" />
                <View v-if="showPassword" />
              </el-icon>
            </template>
          </el-input>
        </div>

        <div class="drag-verify">
          <div class="drag-verify-content" :class="{ error: !isPassing && isClickPass }">
            <ArtDragVerify
              ref="dragVerify"
              v-model:value="isPassing"
              :width="418"
              :text="$t('login.sliderText')"
              textColor="var(--art-gray-800)"
              :successText="$t('login.sliderSuccessText')"
              :progressBarBg="getCssVariable('--el-color-primary')"
              background="var(--art-gray-200)"
              handlerBg="var(--art-main-bg-color)"
              @pass="onPass"
            />
          </div>
          <p class="error-text" :class="{ 'show-error-text': !isPassing && isClickPass }">
            {{ $t('login.placeholder[2]') }}
          </p>
        </div>

        <div class="links-container">
          <el-checkbox v-model="formData.rememberPassword">
            {{ $t('login.rememberPwd') }}
          </el-checkbox>
          <router-link to="/forget-password" class="forgot-password">
            {{ $t('login.forgetPwd') }}
          </router-link>
        </div>

        <el-button
          class="login-button"
          type="primary"
          @click="handleSubmit"
          :loading="loading"
          v-ripple
        >
          {{ $t('login.btnText') }}
        </el-button>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
  import AppConfig from '@/config'
  import { ElNotification } from 'element-plus'

  import { getCssVariable } from '@/utils/colors'

  import { useI18n } from 'vue-i18n'
  import type { FormInstance, FormRules } from 'element-plus'
  import { useAuth } from '@/composables/useAuth'

  const { t } = useI18n()
  const dragVerify = ref()

  const { performLogin, navigateToHome } = useAuth()
  const isPassing = ref(false)
  const isClickPass = ref(false)
  const systemName = AppConfig.systemInfo.name
  const formRef = ref<FormInstance>()
  const showPassword = ref(false)
  const formData = reactive({
    username: AppConfig.systemInfo.login.username,
    password: AppConfig.systemInfo.login.password,
    rememberPassword: true
  })

  const rules = computed<FormRules>(() => ({
    username: [{ required: true, message: t('login.placeholder[0]'), trigger: 'blur' }],
    password: [{ required: true, message: t('login.placeholder[1]'), trigger: 'blur' }]
  }))

  const loading = ref(false)

  // 切换密码可见性
  const togglePasswordVisibility = () => {
    showPassword.value = !showPassword.value
  }

  const onPass = () => {}

  const handleSubmit = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
      if (valid) {
        if (!isPassing.value) {
          isClickPass.value = true
          return
        }

        loading.value = true

        // 延时辅助函数
        const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

        try {
          // 使用认证组合函数进行登录
          const loginSuccess = await performLogin({
            username: formData.username,
            password: formData.password
          })

          if (loginSuccess) {
            // 延时辅助函数
            await delay(1000)

            // 登录成功提示
            showLoginSuccessNotice()

            // 跳转首页
            navigateToHome()
          } else {
            resetDragVerify()
          }
        } catch (error: any) {
          console.error('登录错误:', error)
          ElMessage.error('登录失败，请检查网络连接')
          resetDragVerify()
        } finally {
          await delay(1000)
          loading.value = false
        }
      }
    })
  }

  // 重置拖拽验证
  const resetDragVerify = () => {
    dragVerify.value.reset()
  }

  // 登录成功提示
  const showLoginSuccessNotice = () => {
    setTimeout(() => {
      ElNotification({
        title: t('login.success.title'),
        type: 'success',
        showClose: false,
        duration: 2500,
        zIndex: 10000,
        message: `${t('login.success.message')}, ${systemName}!`
      })
    }, 300)
  }
</script>

<style lang="scss" scoped>
  /* 整体背景样式 */
  .login-page {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100vh;
    background-image: url('@/assets/img/login/background.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
  }

  /* 给背景添加白色蒙版效果 */
  .login-page::before {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    content: '';
    background-color: rgb(255 255 255 / 70%);
  }

  /* 左上角 Logo 和平台名称样式 */
  .login-logo-container {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 2; /* 高于登录卡片和背景蒙版 */
    display: flex;
    align-items: center;
  }

  .login-logo-img {
    width: auto; /* 保持图片原始宽高比 */
    height: 40px; /* 根据用户要求设置高度 */
    margin-right: 12px; /* Logo 和文字之间的间距 */
  }

  .login-logo-text-container {
    display: flex;
    flex-direction: column;
  }

  .login-logo-text {
    margin-bottom: 1px; /* 减小中英文之间的间距 */
    font-size: 26px; /* 进一步增大中文字体 */
    font-weight: 700; /* 加粗字体 */
    line-height: 1.1; /* 紧凑行高 */
    color: #333; /* 更深的颜色 */
    letter-spacing: 1px; /* 增加字间距让中文更宽 */
  }

  .login-logo-text-en {
    font-size: 12px; /* 缩小英文字体 */
    font-weight: 400; /* 英文字体更细 */
    line-height: 1.2; /* 减小行高 */
    color: #666; /* 英文颜色更淡 */
    letter-spacing: -0.2px; /* 紧缩字间距 */
  }

  /* 登录卡片样式 */
  .login-card {
    position: relative;
    z-index: 1;
    width: 420px;
    padding: 30px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgb(0 0 0 / 10%);
  }

  /* 平台标题样式 */
  .platform-title {
    margin-bottom: 30px;
    text-align: center;
  }

  .platform-title h2 {
    margin-bottom: 8px;
    font-size: 26px;
    font-weight: 600;
    color: #0075b0;
  }

  .platform-title h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    color: #33c16c;
  }

  /* 输入框组样式 */
  .input-group {
    position: relative;
    margin-bottom: 20px;
  }

  /* 左侧输入框图标样式 */
  .input-icon-left {
    position: absolute;
    top: 50%;
    left: 15px;
    z-index: 1;
    color: #aaa;
    transform: translateY(-50%);
  }

  /* 自定义输入框样式 */
  .custom-input :deep(.el-input__wrapper) {
    height: 46px;
  }

  /* 密码切换图标样式 */
  .password-toggle-icon {
    color: #aaa;
    cursor: pointer;
    transition: color 0.3s ease;
  }

  .password-toggle-icon:hover {
    color: #409eff;
  }

  /* 滑动验证区域 */
  .drag-verify {
    position: relative;
    margin-bottom: 20px;
  }

  .drag-verify-content {
    position: relative;
    z-index: 2;
    box-sizing: border-box;
    user-select: none;
    border-radius: 8px;
    transition: all 0.3s;

    &.error {
      border-color: #f56c6c;
    }
  }

  .error-text {
    position: absolute;
    top: 0;
    z-index: 1;
    margin-top: 10px;
    font-size: 13px;
    color: #f56c6c;
    transition: all 0.3s;

    &.show-error-text {
      transform: translateY(40px);
    }
  }

  /* 底部链接容器 */
  .links-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 25px;
    font-size: 14px;
  }

  /* 忘记密码链接 */
  .forgot-password {
    color: #0075b0;
    text-decoration: none;
    transition: color 0.3s;
  }

  .forgot-password:hover {
    color: #33c16c;
  }

  /* 登录按钮样式 */
  .login-button {
    width: 100%;
    height: 46px;
    padding: 12px;
    font-size: 16px;
    background-color: #33c16c !important;
    border-color: #33c16c !important;
  }

  .login-button:hover {
    background-color: #2aa55d !important;
    border-color: #2aa55d !important;
  }

  /* 右上角工具按钮 */
  .top-right-wrap {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 100;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .btn {
      display: inline-block;
      padding: 5px;
      margin-left: 10px;
      cursor: pointer;
      user-select: none;
      transition: all 0.3s;

      i {
        font-size: 16px;
      }

      &:hover {
        color: var(--main-color) !important;
      }
    }
  }

  /* 响应式调整 */
  @media only screen and (width <= 500px) {
    .login-card {
      width: 90%;
      padding: 20px;
    }

    .platform-title h2 {
      font-size: 20px;
    }

    .platform-title h3 {
      font-size: 16px;
    }
  }
</style>
