<template>
  <div class="page-content user">
    <div class="content">
      <div class="left-wrap">
        <div class="user-wrap box-style">
          <img class="bg" :src="backgroundImageUrl" />
          <img class="avatar" src="@/assets/img/login/logo.png" />
          <h2 class="name">{{ displayName }}</h2>
          <p class="des">{{ userInfo.remark || '南京银行·网点碳排放监管平台 ' }}</p>

          <div class="outer-info">
            <div>
              <i class="iconfont-sys">&#xe72e;</i>
              <span>{{ displayEmail }}</span>
            </div>
            <div>
              <i class="iconfont-sys">&#xe608;</i>
              <span>{{ displayDepartment }}</span>
            </div>
            <div>
              <i class="iconfont-sys">&#xe736;</i>
              <span>{{ address || '未设置地址' }}</span>
            </div>
            <div>
              <i class="iconfont-sys">&#xe811;</i>
              <span>{{ displayRole }}</span>
            </div>
          </div>
        </div>

        <!-- <el-carousel class="gallery" height="160px"
          :interval="5000"
          indicator-position="none"
        >
          <el-carousel-item class="item" v-for="item in galleryList" :key="item">
            <img :src="item"/>
          </el-carousel-item>
        </el-carousel> -->
      </div>
      <div class="right-wrap">
        <div class="info box-style">
          <h1 class="title">基本设置</h1>

          <el-form
            :model="form"
            class="form"
            ref="ruleFormRef"
            :rules="rules"
            label-width="86px"
            label-position="top"
          >
            <el-row>
              <el-form-item label="姓名" prop="realName">
                <el-input v-model="form.realName" :disabled="!isEdit" />
              </el-form-item>
              <el-form-item label="性别" prop="sex" class="right-input">
                <el-select v-model="form.sex" placeholder="请选择性别" :disabled="!isEdit">
                  <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-row>

            <el-row>
              <el-form-item label="昵称" prop="nikeName">
                <el-input v-model="form.nikeName" :disabled="!isEdit" />
              </el-form-item>
              <el-form-item label="邮箱" prop="email" class="right-input">
                <el-input v-model="form.email" :disabled="!isEdit" />
              </el-form-item>
            </el-row>

            <el-row>
              <el-form-item label="手机" prop="mobile">
                <el-input v-model="form.mobile" :disabled="!isEdit" />
              </el-form-item>
              <el-form-item label="地址" prop="address" class="right-input">
                <el-input v-model="form.address" :disabled="!isEdit" />
              </el-form-item>
            </el-row>

            <el-form-item label="个人介绍" prop="des" :style="{ height: '130px' }">
              <el-input type="textarea" :rows="4" v-model="form.des" :disabled="!isEdit" />
            </el-form-item>

            <div class="el-form-item-right">
              <el-button type="primary" style="width: 90px" v-ripple @click="edit">
                {{ isEdit ? '保存' : '编辑' }}
              </el-button>
            </div>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useUser } from '@/composables/useUser'
  import { FormInstance, FormRules } from 'element-plus'
  import { getUserProfile, updateUserProfile, type UserProfileResponse } from '@/api/usersApi'
  import { getProjectList } from '@/api/projectApi'
  import { getFullImageUrl } from '@/utils/imageUtils'
  import { ElMessage } from 'element-plus'

  const {
    userInfo,
    displayName,
    displayEmail,
    displayDepartment,
    displayRole,
    avatarUrl,
    ensureUserInfo
  } = useUser()

  const isEdit = ref(false)
  const date = ref('')
  const address = ref('')
  const backgroundImageUrl = ref('@/assets/img/demo-project-overview/image1.png') // 默认图片

  const form = reactive({
    realName: '',
    nikeName: '',
    email: '',
    mobile: '',
    address: '',
    sex: '0', // 默认为男
    des: ''
  })

  const ruleFormRef = ref<FormInstance>()

  const rules = reactive<FormRules>({
    realName: [
      { required: true, message: '请输入姓名', trigger: 'blur' },
      { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
    ],
    nikeName: [
      { required: true, message: '请输入昵称', trigger: 'blur' },
      { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
    ],
    email: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
    ],
    mobile: [
      { required: true, message: '请输入手机号码', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
    ],
    address: [{ required: false, message: '请输入地址', trigger: 'blur' }],
    sex: [{ required: true, message: '请选择性别', trigger: 'blur' }]
  })

  const options = [
    {
      value: '0',
      label: '男'
    },
    {
      value: '1',
      label: '女'
    },
    {
      value: '2',
      label: '未知'
    }
  ]

  // 初始化用户信息到表单
  const initializeForm = (user: any) => {
    if (user) {
      form.realName = user.userName || ''
      form.nikeName = user.nickName || ''
      form.email = user.email || ''
      form.mobile = user.phonenumber || ''
      // 性别字段：如果没有值，默认设置为'0'（男）
      form.sex = user.sex !== undefined && user.sex !== null ? user.sex : '0'
      form.des = user.remark || ''

      // 设置地址，优先使用API返回的地址
      if (user.addr) {
        form.address = user.addr
        address.value = user.addr
      } else {
        // 如果没有地址信息，设置默认地址
        address.value = '广东省深圳市'
        form.address = address.value
      }

      console.log('表单已初始化:', form)
    }
  }

  // 获取项目背景图片
  const loadProjectBackgroundImage = async () => {
    try {
      const response = await getProjectList({
        pageNum: 1,
        pageSize: 10 // 获取多个项目以便回退
      })

      if (response.code === 200 && response.rows && response.rows.length > 0) {
        console.log('获取到的项目列表:', response.rows)

        // 遍历项目列表，找到第一个有图片的项目
        for (let i = 0; i < response.rows.length; i++) {
          const project = response.rows[i]
          if (project.pic) {
            backgroundImageUrl.value = getFullImageUrl(project.pic)
            console.log(`使用第${i + 1}个项目的图片作为背景:`, backgroundImageUrl.value)
            return // 找到图片后立即返回
          }
        }

        console.log('所有项目都没有图片，使用默认图片')
      }
    } catch (error) {
      console.error('获取项目图片失败:', error)
      // 保持默认图片
    }
  }

  // 获取个人详细信息
  const loadUserProfile = async () => {
    try {
      const response: UserProfileResponse = await getUserProfile()
      if (response.code === 200 && response.data) {
        console.log('获取个人信息成功:', response.data)
        initializeForm(response.data)
      } else {
        console.warn('获取个人信息失败，使用基础用户信息')
        // 如果获取个人信息失败，回退到基础用户信息
        await loadUserInfo()
      }
    } catch (error) {
      console.error('获取个人信息失败:', error)
      // 如果获取个人信息失败，回退到基础用户信息
      await loadUserInfo()
    }
  }

  // 加载用户信息 - 优先使用Pinia中的数据
  const loadUserInfo = async () => {
    // 检查Pinia中是否已有用户信息
    if (userInfo.value.userId) {
      console.log('从Pinia中读取用户信息:', userInfo.value)
      initializeForm(userInfo.value)
    } else {
      // 如果Pinia中没有数据，则尝试获取
      console.log('Pinia中无用户信息，尝试获取...')
      const user = await ensureUserInfo()
      if (user) {
        initializeForm(user)
      }
    }
  }

  // 监听用户信息变化，自动更新表单
  watch(
    () => userInfo.value,
    (newUserInfo) => {
      if (newUserInfo.userId) {
        initializeForm(newUserInfo)
      }
    },
    { immediate: true }
  )

  onMounted(() => {
    getDate()
    loadUserProfile()
    loadProjectBackgroundImage()
  })

  const getDate = () => {
    const d = new Date()
    const h = d.getHours()
    let text = ''

    if (h >= 6 && h < 9) {
      text = '早上好'
    } else if (h >= 9 && h < 11) {
      text = '上午好'
    } else if (h >= 11 && h < 13) {
      text = '中午好'
    } else if (h >= 13 && h < 18) {
      text = '下午好'
    } else if (h >= 18 && h < 24) {
      text = '晚上好'
    } else if (h >= 0 && h < 6) {
      text = '很晚了，早点睡'
    }

    date.value = text
  }

  const edit = async () => {
    if (isEdit.value) {
      // 保存逻辑
      try {
        // 调用API保存用户信息
        const response = await updateUserProfile({
          nickName: form.nikeName,
          sex: form.sex,
          email: form.email,
          phonenumber: form.mobile,
          addr: form.address,
          remark: form.des
        })

        if (response.code === 200) {
          ElMessage.success('个人信息更新成功')
          // 保存成功后关闭编辑模式
          isEdit.value = false
          // 刷新用户信息
          await ensureUserInfo()
        } else {
          ElMessage.error(response.msg || '更新失败')
        }
      } catch (error) {
        console.error('保存用户信息失败:', error)
        ElMessage.error('保存用户信息失败')
      }
    } else {
      isEdit.value = true
    }
  }
</script>

<style lang="scss">
  .user {
    .icon {
      width: 1.4em;
      height: 1.4em;
      overflow: hidden;
      vertical-align: -0.15em;
      fill: currentcolor;
    }
  }
</style>

<style lang="scss" scoped>
  .page-content {
    width: 100%;
    height: 100%;
    padding: 0 !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;

    $box-radius: calc(var(--custom-radius) + 4px);

    .box-style {
      border: 1px solid var(--art-border-color);
    }

    .content {
      position: relative;
      display: flex;
      justify-content: space-between;
      margin-top: 10px;

      .left-wrap {
        width: 450px;
        margin-right: 25px;

        .user-wrap {
          position: relative;
          height: 600px;
          padding: 35px 40px;
          overflow: hidden;
          text-align: center;
          background: var(--art-main-bg-color);
          border-radius: $box-radius;

          .bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 200px;
            object-fit: cover;
          }

          .avatar {
            position: relative;
            z-index: 10;
            width: 80px;
            height: 80px;
            margin-top: 120px;
            object-fit: cover;
            border: 2px solid #fff;
            border-radius: 50%;
          }

          .name {
            margin-top: 20px;
            font-size: 22px;
            font-weight: 400;
          }

          .des {
            margin-top: 20px;
            font-size: 14px;
          }

          .outer-info {
            width: 300px;
            margin: auto;
            margin-top: 30px;
            text-align: left;

            > div {
              margin-top: 10px;

              span {
                margin-left: 8px;
                font-size: 14px;
              }
            }
          }

          .lables {
            margin-top: 40px;

            h3 {
              font-size: 15px;
              font-weight: 500;
            }

            > div {
              display: flex;
              flex-wrap: wrap;
              justify-content: center;
              margin-top: 15px;

              > div {
                padding: 3px 6px;
                margin: 0 10px 10px 0;
                font-size: 12px;
                background: var(--art-main-bg-color);
                border: 1px solid var(--art-border-color);
                border-radius: 2px;
              }
            }
          }
        }

        .gallery {
          margin-top: 25px;
          border-radius: 10px;

          .item {
            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
        }
      }

      .right-wrap {
        flex: 1;
        overflow: hidden;
        border-radius: $box-radius;

        .info {
          background: var(--art-main-bg-color);
          border-radius: $box-radius;

          .title {
            padding: 15px 25px;
            font-size: 20px;
            font-weight: 400;
            color: var(--art-text-gray-800);
            border-bottom: 1px solid var(--art-border-color);
          }

          .form {
            box-sizing: border-box;
            padding: 30px 25px;

            > .el-row {
              .el-form-item {
                width: calc(50% - 10px);
              }

              .el-input,
              .el-select {
                width: 100%;
              }
            }

            .right-input {
              margin-left: 20px;
            }

            .el-form-item-right {
              display: flex;
              align-items: center;
              justify-content: end;

              .el-button {
                width: 110px !important;
              }
            }
          }
        }
      }
    }
  }

  @media only screen and (max-width: $device-ipad-vertical) {
    .page-content {
      .content {
        display: block;
        margin-top: 5px;

        .left-wrap {
          width: 100%;
        }

        .right-wrap {
          width: 100%;
          margin-top: 15px;
        }
      }
    }
  }
</style>
