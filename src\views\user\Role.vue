<template>
  <div class="page-content">
    <!-- 搜索栏 -->
    <el-row class="search-bar">
      <el-col :xs="24" :sm="12" :lg="6">
        <el-input
          v-model="searchForm.roleName"
          placeholder="请输入角色名称"
          clearable
          @clear="handleSearch"
          @keyup.enter="handleSearch"
        />
      </el-col>
      <div style="width: 12px"></div>
      <el-col :xs="24" :sm="12" :lg="6">
        <el-input
          v-model="searchForm.roleKey"
          placeholder="请输入角色标识"
          clearable
          @clear="handleSearch"
          @keyup.enter="handleSearch"
        />
      </el-col>
      <div style="width: 12px"></div>
      <el-col :xs="24" :sm="12" :lg="6" class="el-col2">
        <el-button type="primary" @click="handleSearch" v-ripple>
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        <el-button @click="handleReset" v-ripple>
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
        <el-button type="primary" @click="showDialog('add')" v-ripple>
          <el-icon><Plus /></el-icon>
          新增角色
        </el-button>
      </el-col>
    </el-row>

    <!-- 表格 -->
    <el-card shadow="never" class="table-card">
      <el-table :data="tableData" :loading="loading" stripe style="width: 100%" v-loading="loading">
        <el-table-column prop="roleId" label="角色ID" width="80" />
        <el-table-column prop="roleName" label="角色名称" />
        <el-table-column prop="roleKey" label="角色标识" />
        <el-table-column prop="remark" label="备注" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">
              {{ scope.row.status === '0' ? '正常' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="200">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="showDialog('edit', scope.row)"
              :disabled="scope.row.admin"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(scope.row)"
              :disabled="scope.row.admin"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增角色' : '编辑角色'"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="100px"
        :loading="submitLoading"
      >
        <el-form-item label="角色名称" prop="roleName">
          <el-input
            v-model="formData.roleName"
            placeholder="请输入角色名称"
            maxlength="30"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="角色标识" prop="roleKey">
          <el-input
            v-model="formData.roleKey"
            placeholder="请输入角色标识"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="显示顺序" prop="roleSort">
          <el-input-number
            v-model="formData.roleSort"
            :min="0"
            :max="999"
            controls-position="right"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio value="0">正常</el-radio>
            <el-radio value="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, nextTick } from 'vue'
  import { ElMessage, ElMessageBox, FormInstance, FormRules } from 'element-plus'
  import { Search, Refresh, Plus } from '@element-plus/icons-vue'
  import {
    getRoleList,
    addRole,
    updateRole,
    deleteRole as deleteRoleApi,
    getRoleDetail
  } from '@/api/usersApi'

  // 类型定义
  interface ApiResponse {
    code: number
    msg?: string
    rows?: any[]
    total?: number
    data?: any
  }

  interface RoleData {
    roleId?: number
    roleName?: string
    roleKey?: string
    roleSort?: number
    dataScope?: string
    menuCheckStrictly?: boolean
    deptCheckStrictly?: boolean
    status?: string
    delFlag?: string
    flag?: boolean
    menuIds?: number[]
    deptIds?: number[]
    permissions?: string[]
    createBy?: string
    createTime?: string
    updateBy?: string
    updateTime?: string
    remark?: string
    admin?: boolean
  }

  // 响应式数据
  const loading = ref(false)
  const submitLoading = ref(false)
  const dialogVisible = ref(false)
  const dialogType = ref<'add' | 'edit'>('add')
  const formRef = ref<FormInstance>()

  // 搜索表单
  const searchForm = reactive({
    roleName: '',
    roleKey: ''
  })

  // 分页数据
  const pagination = reactive({
    pageNum: 1,
    pageSize: 10,
    total: 0
  })

  // 表格数据
  const tableData = ref<RoleData[]>([])

  // 对话框表单数据
  const formData = reactive({
    roleId: undefined as number | undefined,
    roleName: '',
    roleKey: '',
    roleSort: 1,
    status: '0',
    remark: ''
  })

  // 表单验证规则
  const rules: FormRules = {
    roleName: [
      { required: true, message: '请输入角色名称', trigger: 'blur' },
      { min: 2, max: 30, message: '角色名称长度在 2 到 30 个字符', trigger: 'blur' }
    ],
    roleKey: [
      { required: true, message: '请输入角色标识', trigger: 'blur' },
      { min: 2, max: 100, message: '角色标识长度在 2 到 100 个字符', trigger: 'blur' },
      {
        pattern: /^[a-zA-Z0-9_-]+$/,
        message: '角色标识只能包含字母、数字、下划线和中划线',
        trigger: 'blur'
      }
    ],
    roleSort: [{ required: true, message: '请输入显示顺序', trigger: 'blur' }],
    status: [{ required: true, message: '请选择状态', trigger: 'change' }]
  }

  // 获取角色列表
  const getTableData = async () => {
    loading.value = true
    try {
      const params = {
        pageSize: pagination.pageSize,
        pageNum: pagination.pageNum,
        roleName: searchForm.roleName || undefined,
        roleKey: searchForm.roleKey || undefined
      }

      const response = (await getRoleList(params)) as ApiResponse
      if (response.code === 200 || response.code === 1) {
        tableData.value = response.rows || []
        pagination.total = response.total || 0
      } else {
        ElMessage.error(response.msg || '获取角色列表失败')
      }
    } catch (error) {
      console.error('获取角色列表失败:', error)
      ElMessage.error('获取角色列表失败')
    } finally {
      loading.value = false
    }
  }

  // 搜索处理
  const handleSearch = () => {
    pagination.pageNum = 1
    getTableData()
  }

  // 重置搜索
  const handleReset = () => {
    searchForm.roleName = ''
    searchForm.roleKey = ''
    pagination.pageNum = 1
    getTableData()
  }

  // 分页变化处理
  const handlePageChange = (page: number) => {
    pagination.pageNum = page
    getTableData()
  }

  const handleSizeChange = (size: number) => {
    pagination.pageSize = size
    pagination.pageNum = 1
    getTableData()
  }

  // 显示对话框
  const showDialog = async (type: 'add' | 'edit', row?: RoleData) => {
    dialogType.value = type

    if (type === 'add') {
      // 重置表单数据
      Object.assign(formData, {
        roleId: undefined,
        roleName: '',
        roleKey: '',
        roleSort: 1,
        status: '0',
        remark: ''
      })
    } else if (row && row.roleId) {
      // 编辑时获取详细信息
      try {
        const response = (await getRoleDetail(row.roleId)) as ApiResponse
        if (response.code === 200 && response.data) {
          const data = response.data
          Object.assign(formData, {
            roleId: data.roleId,
            roleName: data.roleName || '',
            roleKey: data.roleKey || '',
            roleSort: data.roleSort || 1,
            status: data.status || '0',
            remark: data.remark || ''
          })
        } else {
          // 如果获取详情失败，使用行数据
          Object.assign(formData, {
            roleId: row.roleId,
            roleName: row.roleName || '',
            roleKey: row.roleKey || '',
            roleSort: row.roleSort || 1,
            status: row.status || '0',
            remark: row.remark || ''
          })
        }
      } catch (error) {
        console.error('获取角色详情失败:', error)
        // 如果获取详情失败，使用行数据
        Object.assign(formData, {
          roleId: row.roleId,
          roleName: row.roleName || '',
          roleKey: row.roleKey || '',
          roleSort: row.roleSort || 1,
          status: row.status || '0',
          remark: row.remark || ''
        })
      }
    }

    dialogVisible.value = true
    nextTick(() => {
      formRef.value?.clearValidate()
    })
  }

  // 删除处理
  const handleDelete = async (row: RoleData) => {
    if (!row.roleId) return

    try {
      await ElMessageBox.confirm(`确定要删除角色"${row.roleName}"吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      const response = (await deleteRoleApi(row.roleId)) as ApiResponse
      if (response.code === 200) {
        ElMessage.success('删除成功')
        // 如果当前页没有数据了，回到上一页
        if (tableData.value.length === 1 && pagination.pageNum > 1) {
          pagination.pageNum--
        }
        getTableData()
      } else {
        ElMessage.error(response.msg || '删除失败')
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除角色失败:', error)
        ElMessage.error('删除失败')
      }
    }
  }

  // 提交处理
  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()
      submitLoading.value = true

      let response: ApiResponse
      if (dialogType.value === 'add') {
        response = (await addRole({
          roleName: formData.roleName,
          roleKey: formData.roleKey,
          remark: formData.remark
        })) as ApiResponse
      } else {
        response = (await updateRole({
          roleId: formData.roleId!,
          roleName: formData.roleName,
          roleKey: formData.roleKey,
          remark: formData.remark
        })) as ApiResponse
      }

      if (response.code === 200) {
        ElMessage.success(dialogType.value === 'add' ? '新增成功' : '修改成功')
        dialogVisible.value = false
        getTableData()
      } else {
        ElMessage.error(response.msg || (dialogType.value === 'add' ? '新增失败' : '修改失败'))
      }
    } catch (error) {
      console.error('提交失败:', error)
    } finally {
      submitLoading.value = false
    }
  }

  // 格式化日期
  const formatDate = (date: string) => {
    if (!date) return '-'
    return new Date(date)
      .toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
      .replace(/\//g, '-')
  }

  // 初始化
  onMounted(() => {
    getTableData()
  })
</script>

<style lang="scss" scoped>
  .page-content {
    .search-bar {
      margin-bottom: 20px;

      .el-col2 {
        display: flex;
        gap: 8px;
      }
    }

    .table-card {
      .pagination-container {
        display: flex;
        justify-content: flex-end;
        padding: 20px 0;
      }
    }

    .dialog-footer {
      text-align: right;
    }

    .svg-icon {
      width: 1.8em;
      height: 1.8em;
      overflow: hidden;
      vertical-align: -8px;
      fill: currentcolor;
    }
  }
</style>
