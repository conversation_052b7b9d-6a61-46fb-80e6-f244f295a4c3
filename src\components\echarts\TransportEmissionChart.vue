<template>
  <div :id="chartId" class="transport-emission-chart"></div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted, watch } from 'vue'
  import * as echarts from 'echarts'

  // 使用props接收数据
  const props = defineProps({
    chartData: {
      type: Array,
      required: true
    }
  })

  // 生成唯一的图表ID，避免多个图表实例冲突
  const chartId = `transport-emission-chart-${Date.now()}`
  // 图表实例
  const chartInstance = ref(null)

  // 处理窗口大小变化
  const handleResize = () => {
    if (chartInstance.value) {
      chartInstance.value.resize()
    }
  }

  // 初始化图表
  const initChart = () => {
    // 获取DOM元素
    const chartDom = document.getElementById(chartId)
    if (!chartDom) return

    // 创建图表实例
    chartInstance.value = echarts.init(chartDom)

    // 更新图表数据
    updateChart()

    // 添加窗口大小变化的监听器
    window.addEventListener('resize', handleResize)
  }

  // 更新图表数据
  const updateChart = () => {
    if (!chartInstance.value) return

    // 过滤掉总计项，只展示各类交通数据
    const data = props.chartData.filter((item) => item.type !== '总计')

    // 准备数据
    const types = data.map((item) => item.type)
    const mileageData = data.map((item) => parseFloat(item.mileage.replace(/,/g, '')))
    const emissionData = data.map((item) => parseFloat(item.emission.replace(/,/g, '')))

    // 设置图表选项
    const option = {
      title: {
        text: '交通碳排放分析',
        left: 'center',
        top: 10,
        textStyle: {
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function (params) {
          const type = params[0].name
          const mileage = params[0].value
          const emission = params[1].value
          return `${type}<br/>行驶里程: ${mileage.toLocaleString()} km<br/>碳排放量: ${emission.toLocaleString()} kg CO₂`
        }
      },
      legend: {
        data: ['行驶里程 (km)', '碳排放量 (kg CO₂)'],
        bottom: 10
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: types,
        axisLabel: {
          interval: 0,
          rotate: 30
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '行驶里程 (km)',
          position: 'left',
          axisLine: {
            show: true,
            lineStyle: {
              color: '#4caf50'
            }
          },
          axisLabel: {
            formatter: '{value}'
          }
        },
        {
          type: 'value',
          name: '碳排放量 (kg CO₂)',
          position: 'right',
          axisLine: {
            show: true,
            lineStyle: {
              color: '#1e88e5'
            }
          },
          axisLabel: {
            formatter: '{value}'
          }
        }
      ],
      series: [
        {
          name: '行驶里程 (km)',
          type: 'bar',
          yAxisIndex: 0,
          data: mileageData,
          itemStyle: {
            color: '#4caf50'
          },
          barWidth: '30%'
        },
        {
          name: '碳排放量 (kg CO₂)',
          type: 'bar',
          yAxisIndex: 1,
          data: emissionData,
          itemStyle: {
            color: '#1e88e5'
          },
          barWidth: '30%'
        }
      ]
    }

    // 使用配置项设置图表
    chartInstance.value.setOption(option)
  }

  // 监听数据变化
  watch(
    () => props.chartData,
    () => {
      updateChart()
    },
    { deep: true }
  )

  // 组件挂载时初始化图表
  onMounted(() => {
    initChart()
    // 延迟执行一次resize，确保图表正确渲染
    setTimeout(() => {
      handleResize()
    }, 200)
  })

  // 组件卸载时销毁图表实例
  onUnmounted(() => {
    if (chartInstance.value) {
      chartInstance.value.dispose()
      chartInstance.value = null
    }
    window.removeEventListener('resize', handleResize)
  })
</script>

<style scoped>
  .transport-emission-chart {
    width: 100% !important;
    height: 300px !important;
    min-height: 300px !important;
  }
</style>
