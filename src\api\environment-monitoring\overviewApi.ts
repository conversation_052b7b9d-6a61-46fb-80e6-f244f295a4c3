import api from '@/utils/http'

// 环境监测概览相关接口

/**
 * 设备信息
 */
export interface EquipmentInfo {
  name: string
  num: number
  status: string
}

/**
 * 监测数据基础接口
 */
export interface MonitorData {
  level: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR'
}

/**
 * 温湿度监测数据
 */
export interface SsdMonitorData extends MonitorData {
  wd: number // 温度
  sd: number // 湿度
}

/**
 * 有害气体监测数据
 */
export interface YhqtMonitorData extends MonitorData {
  tvoc?: number // TVOC
  co2?: number // 二氧化碳
  formaldehyde?: number // 甲醛
}

/**
 * 颗粒物监测数据
 */
export interface KlwMonitorData extends MonitorData {
  pm2_5: number // PM2.5
  pm10: number // PM10
}

/**
 * 噪声监测数据
 */
export interface ZsMonitorData extends MonitorData {
  zs: number // 噪声
}

/**
 * 水质监测数据
 */
export interface WaterMonitorData extends MonitorData {
  wd?: number // 水温
  ph?: number // pH值
  doValue?: number // 溶解氧
  cod?: number // COD
  ad?: number // 氨氮
  waterLevel?: number // 水位
}

/**
 * 室外环境监测数据
 */
export interface SwMonitorData extends MonitorData {
  wd: number // 温度
  sd: number // 湿度
  pm2_5: number // PM2.5
  pm10: number // PM10
  fx: string // 风向
  fs: number // 风速
}

/**
 * 环境监测概览数据
 */
export interface OverviewData {
  equipInfoList: EquipmentInfo[]
  ssdMonitor: SsdMonitorData | null // 温湿度监测
  yhqtMonitor: YhqtMonitorData | null // 有害气体监测
  klwMonitor: KlwMonitorData | null // 颗粒物监测
  zsMonitor: ZsMonitorData | null // 噪声监测
  waterMonitor: WaterMonitorData | null // 水质监测
  swMonitor: SwMonitorData | null // 室外环境监测
}

/**
 * 环境监测概览响应
 */
export interface OverviewResponse {
  msg: string
  code: number
  data: OverviewData
}

/**
 * 查询参数
 */
export interface OverviewParams {
  projectId: number | string
  buildingId?: number | string
}

/**
 * 获取环境监测概览数据
 * @param params 查询参数
 * @returns 概览数据
 */
export function getEnvironmentOverview(params: OverviewParams): Promise<OverviewResponse> {
  return api.get({
    url: '/envmonitor/overview/overview',
    params
  })
}

/**
 * 等级映射工具函数
 */
export const levelMap = {
  EXCELLENT: { text: '优秀', value: 95, color: '#10b981' },
  GOOD: { text: '好', value: 80, color: '#22c55e' },
  FAIR: { text: '一般', value: 60, color: '#f59e0b' },
  POOR: { text: '差', value: 30, color: '#ef4444' }
} as const

/**
 * 获取等级信息
 * @param level 等级
 * @returns 等级信息
 */
export function getLevelInfo(level: string) {
  return levelMap[level as keyof typeof levelMap] || levelMap.POOR
}
