// 辅助函数：获取CSS变量值
function getCssVariableValue(variableName) {
  if (typeof window === 'undefined' || typeof document === 'undefined') return null
  try {
    const value = getComputedStyle(document.documentElement).getPropertyValue(variableName).trim()
    return value || null //确保返回 null 而不是空字符串
  } catch (e) {
    console.error(`Error getting CSS variable ${variableName}:`, e)
    return null
  }
}

// 辅助函数：将CSS变量（RGB字符串）转换为 'rgb(r,g,b)'
function getRgbColor(variableName, fallbackColor) {
  const rgbString = getCssVariableValue(variableName)
  if (rgbString && /^\d{1,3},\s*\d{1,3},\s*\d{1,3}$/.test(rgbString)) {
    return `rgb(${rgbString})`
  }
  return fallbackColor
}

// 辅助函数：将CSS变量（RGB字符串）和alpha转换为 'rgba(r,g,b,a)'
function getRgbaColor(variableName, alpha, fallbackColor) {
  const rgbString = getCssVariableValue(variableName)
  if (rgbString && /^\d{1,3},\s*\d{1,3},\s*\d{1,3}$/.test(rgbString)) {
    return `rgba(${rgbString}, ${alpha})`
  }
  return fallbackColor
}

// 定义多项目对比图表配置
export function getProjectComparisonOption(
  projectsData,
  xAxisData,
  isUnitArea = false,
  dataType = 'carbon'
) {
  // 确定数据单位和图表标题
  const unitText =
    dataType === 'carbon'
      ? isUnitArea
        ? '碳排放量(kgCO₂/m²)'
        : '碳排放量(kgCO₂)'
      : isUnitArea
        ? '能耗量(kgce/m²)'
        : '能耗量(标准煤)'

  const titleText = isUnitArea ? '单位面积' : ''

  // 生成系列数据
  const series = projectsData.map((project) => {
    return {
      name: project.name,
      type: 'bar',
      data: project.data,
      barWidth: '15%', // 可以考虑也用变量控制
      barGap: '10%', // 可以考虑也用变量控制
      markPoint: {
        data: [
          { type: 'max', name: '最大值' },
          { type: 'min', name: '最小值' }
        ]
      }
    }
  })

  // 主题颜色配置
  const themedColors = [
    getRgbColor('--art-primary', '#5470c6'),
    getRgbColor('--art-success', '#91cc75'),
    getRgbColor('--art-warning', '#fac858'),
    getRgbColor('--art-danger', '#ee6666'),
    getRgbColor('--art-info', '#73c0de'),
    getRgbColor('--art-secondary', '#3ba272'),
    // 为了保持与原颜色数量一致，可以从灰阶或其他颜色派生，或定义更多CSS变量
    getCssVariableValue('--art-text-gray-700') || '#fc8452', // 示例：使用灰色系
    getCssVariableValue('--art-text-gray-500') || '#9a60b4' // 示例：使用灰色系
  ]

  return {
    color: themedColors,
    title: {
      text: `${titleText}${dataType === 'carbon' ? '碳排放' : '能耗'}对比图`,
      left: 'center',
      textStyle: {
        color: getCssVariableValue('--art-text-gray-900') || '#333333'
      }
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: getCssVariableValue('--art-main-bg-color') || '#ffffff',
      borderColor: getCssVariableValue('--art-border-color') || '#e7e7e7',
      textStyle: {
        color: getCssVariableValue('--art-text-gray-800') || '#333333'
      },
      axisPointer: {
        type: 'shadow',
        shadowStyle: {
          color: getRgbaColor('--art-primary', 0.1, 'rgba(93,135,255,0.1)')
        }
      },
      formatter: function (params) {
        let result = params[0].axisValue + '<br/>'
        params.forEach((param) => {
          const marker = `<span style="display:inline-block;margin-right:4px;border-radius:50%;width:10px;height:10px;background-color:${param.color};"></span>`
          result +=
            marker +
            param.seriesName +
            ': ' +
            param.value.toFixed(2) +
            (dataType === 'carbon'
              ? isUnitArea
                ? ' kgCO₂/m²'
                : ' kgCO₂'
              : isUnitArea
                ? ' kgce/m²'
                : ' 标煤') +
            '<br/>'
        })
        return result
      }
    },
    legend: {
      data: projectsData.map((project) => project.name),
      top: '40px',
      textStyle: {
        color: getCssVariableValue('--art-text-gray-700') || '#666666'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '80px',
      containLabel: true
    },
    toolbox: {
      feature: {
        saveAsImage: { title: '保存为图片' },
        dataZoom: { title: { zoom: '区域缩放', back: '区域还原' } },
        magicType: { type: ['line', 'bar'], title: { line: '切换为折线图', bar: '切换为柱状图' } },
        restore: { title: '还原' }
      },
      iconStyle: {
        borderColor: getCssVariableValue('--art-text-gray-600') || '#666666'
      },
      emphasis: {
        iconStyle: {
          borderColor: getRgbColor('--art-primary', '#5470c6')
        }
      }
    },
    dataZoom: [
      {
        type: 'slider',
        show: true,
        start: 0,
        end: 100,
        backgroundColor: getCssVariableValue('--art-gray-100') || 'rgba(255,255,255,0)',
        dataBackground: {
          lineStyle: { color: getCssVariableValue('--art-gray-300') || '#d3d3d3' },
          areaStyle: { color: getCssVariableValue('--art-gray-200') || '#f0f0f0' }
        },
        selectedDataBackground: {
          lineStyle: { color: getRgbColor('--art-primary', '#5470C6') },
          areaStyle: { color: getRgbaColor('--art-primary', 0.2, 'rgba(93,135,255,0.2)') }
        },
        fillerColor: getRgbaColor('--art-primary', 0.15, 'rgba(93,135,255,0.15)'),
        borderColor: getCssVariableValue('--art-border-color') || '#ddd',
        handleStyle: {
          color: getRgbColor('--art-primary', '#5D87FF'),
          borderWidth: 1,
          borderColor: getRgbColor('--art-primary', '#5D87FF')
        },
        moveHandleStyle: {
          color: getRgbColor('--art-primary', '#5D87FF'),
          opacity: 0.7
        },
        textStyle: {
          color: getCssVariableValue('--art-text-gray-700') || '#333'
        }
      },
      {
        type: 'inside',
        start: 0,
        end: 100
      }
    ],
    xAxis: [
      {
        type: 'category',
        data: xAxisData,
        axisTick: {
          alignWithLabel: true,
          lineStyle: {
            color: getCssVariableValue('--art-border-color') || '#cccccc'
          }
        },
        axisLine: {
          lineStyle: {
            color: getCssVariableValue('--art-border-color') || '#cccccc'
          }
        },
        axisLabel: {
          rotate: xAxisData.length > 12 ? 45 : 0,
          color: getCssVariableValue('--art-text-gray-600') || '#666666'
        },
        splitLine: { show: false }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: unitText,
        nameTextStyle: {
          color: getCssVariableValue('--art-text-gray-700') || '#666666'
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: getCssVariableValue('--art-border-color') || '#cccccc'
          }
        },
        axisTick: {
          show: true,
          lineStyle: {
            color: getCssVariableValue('--art-border-color') || '#cccccc'
          }
        },
        axisLabel: {
          color: getCssVariableValue('--art-text-gray-600') || '#666666'
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: getCssVariableValue('--art-border-dashed-color') || '#eeeeee'
          }
        }
      }
    ],
    series: series
  }
}

// 原有的月度对比配置保留
export function getMonthComparisonOption(
  currentMonth,
  lastMonth,
  currentData,
  lastData,
  xAxisData
) {
  return {
    title: {
      text: '碳排放环比分析',
      subtext: `${lastMonth} vs ${currentMonth}`,
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function (params) {
        const day = params[0].axisValue
        const current = params[0].data.toFixed(2)
        const last = params[1].data.toFixed(2)
        const diff = (params[0].data - params[1].data).toFixed(2)
        const rate = (((params[0].data - params[1].data) / params[1].data) * 100).toFixed(2)

        const sign = diff >= 0 ? '+' : ''
        const rateSymbol = rate >= 0 ? '↑' : '↓'

        return `${day}<br/>
                ${currentMonth}: ${current} kgCO₂<br/>
                ${lastMonth}: ${last} kgCO₂<br/>
                环比变化: ${sign}${diff} kgCO₂<br/>
                环比增长率: ${sign}${rate}% ${rateSymbol}`
      }
    },
    legend: {
      data: [currentMonth, lastMonth],
      top: '40px'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '80px',
      containLabel: true
    },
    toolbox: {
      feature: {
        saveAsImage: {
          title: '保存为图片'
        },
        dataZoom: {
          title: {
            zoom: '区域缩放',
            back: '区域还原'
          }
        },
        magicType: {
          type: ['line', 'bar'],
          title: {
            line: '切换为折线图',
            bar: '切换为柱状图'
          }
        },
        restore: {
          title: '还原'
        }
      }
    },
    dataZoom: [
      {
        type: 'slider',
        show: true,
        start: 0,
        end: 100
      },
      {
        type: 'inside',
        start: 0,
        end: 100
      }
    ],
    xAxis: [
      {
        type: 'category',
        data: xAxisData,
        axisTick: {
          alignWithLabel: true
        },
        axisLabel: {
          rotate: xAxisData.length > 12 ? 45 : 0
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '碳排放量(kgCO₂)',
        splitLine: {
          lineStyle: {
            type: 'dashed'
          }
        }
      }
    ],
    series: [
      {
        name: currentMonth,
        type: 'bar',
        barWidth: '40%',
        data: currentData,
        itemStyle: {
          color: '#5470c6'
        },
        markPoint: {
          data: [
            { type: 'max', name: '最大值' },
            { type: 'min', name: '最小值' }
          ]
        }
      },
      {
        name: lastMonth,
        type: 'bar',
        barWidth: '40%',
        data: lastData,
        itemStyle: {
          color: '#fac858'
        },
        markPoint: {
          data: [
            { type: 'max', name: '最大值' },
            { type: 'min', name: '最小值' }
          ]
        }
      }
    ]
  }
}

// 生成模拟数据
export function generateMockData(count) {
  return Array.from(
    { length: count },
    () => Math.floor(Math.random() * 500) + 500 // 生成500-1000范围内的随机数
  )
}

// 生成模拟单位面积数据
export function generateMockUnitAreaData(count, area) {
  // 生成原始数据并转换为单位面积数据（kg/m²或kgce/m²）
  return Array.from(
    { length: count },
    () => ((Math.floor(Math.random() * 500) + 500) / area) * 1000 // 转换为kg/m²或kgce/m²
  )
}

// 生成多个项目的模拟数据
export function generateMultiProjectsData(projects, count, isUnitArea = false) {
  return projects.map((project) => {
    // 假设每个项目都有area属性表示建筑面积
    const data = isUnitArea
      ? generateMockUnitAreaData(count, project.area || 10000)
      : generateMockData(count)

    return {
      name: project.name,
      data: data,
      area: project.area
    }
  })
}

// 根据天数生成X轴标签
export function generateDayLabels(days) {
  return Array.from({ length: days }, (_, i) => `${i + 1}日`)
}

// 根据年月生成日期标签
export function generateMonthLabels() {
  return Array.from({ length: 12 }, (_, i) => `${i + 1}月`)
}

// 根据当前时间获取上一年度或上一月度
export function getPreviousPeriod(date, type = 'month') {
  const currentDate = new Date(date)

  if (type === 'month') {
    // 获取上个月
    currentDate.setMonth(currentDate.getMonth() - 1)
  } else if (type === 'year') {
    // 获取去年同期
    currentDate.setFullYear(currentDate.getFullYear() - 1)
  } else if (type === 'day') {
    // 获取前一天
    currentDate.setDate(currentDate.getDate() - 1)
  }

  return currentDate
}

// 格式化日期为易读字符串
export function formatDateLabel(date, type = 'month') {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()

  if (type === 'year') {
    return `${year}年`
  } else if (type === 'month') {
    return `${year}年${month.toString().padStart(2, '0')}月`
  } else if (type === 'day') {
    return `${year}年${month.toString().padStart(2, '0')}月${day.toString().padStart(2, '0')}日`
  }

  return `${year}年${month.toString().padStart(2, '0')}月`
}

// 转换原始数据为单位面积数据
export function convertToUnitAreaData(data, area) {
  // 吨转换为kg/m²，标煤转换为kgce/m²
  return data.map((value) => (value * 1000) / area)
}
