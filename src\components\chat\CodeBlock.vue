<template>
  <div class="code-block" :class="{ 'dark-mode': isDark }">
    <div class="code-header">
      <span class="language">{{ language }}</span>
      <button class="copy-button" @click="copyCode">
        <el-icon v-if="!copied"><Document /></el-icon>
        <el-icon v-else><Select /></el-icon>
        {{ copied ? '已复制' : '复制' }}
      </button>
    </div>
    <pre
      class="code-content"
    ><code :class="`language-${language}`" ref="codeRef">{{ code }}</code></pre>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, computed } from 'vue'
  import { Document, Select } from '@element-plus/icons-vue'
  import { useSettingStore } from '@/store/modules/setting'
  import { storeToRefs } from 'pinia'
  import hljs from 'highlight.js'
  import 'highlight.js/styles/atom-one-light.css'
  import 'highlight.js/styles/atom-one-dark.css'

  // 获取主题设置
  const settingStore = useSettingStore()
  const { isDark } = storeToRefs(settingStore)

  // 定义 props
  const props = defineProps<{
    code: string
    language: string
  }>()

  // 复制状态
  const copied = ref(false)
  const codeRef = ref<HTMLElement | null>(null)

  // 复制代码
  const copyCode = () => {
    navigator.clipboard
      .writeText(props.code)
      .then(() => {
        copied.value = true
        setTimeout(() => {
          copied.value = false
        }, 2000)
      })
      .catch((err) => {
        console.error('复制失败:', err)
      })
  }

  // 格式化语言名称
  const language = computed(() => {
    const lang = props.language.toLowerCase()

    // 语言名称映射
    const langMap: Record<string, string> = {
      js: 'javascript',
      ts: 'typescript',
      py: 'python',
      rb: 'ruby',
      sh: 'bash',
      yml: 'yaml',
      md: 'markdown'
    }

    return langMap[lang] || lang
  })

  // 在组件挂载后高亮代码
  onMounted(() => {
    if (codeRef.value) {
      hljs.highlightElement(codeRef.value)
    }
  })
</script>

<style lang="scss" scoped>
  .code-block {
    margin: 16px 0;
    overflow: hidden;
    background-color: #f6f8fa;
    border-radius: 6px;

    &.dark-mode {
      background-color: #282c34;

      .code-header {
        color: #e6e6e6;
        background-color: #3a3f4b;

        .copy-button {
          color: #e6e6e6;
          background-color: #4a4f5a;

          &:hover {
            background-color: #5a5f6a;
          }
        }
      }
    }

    .code-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 16px;
      font-size: 14px;
      color: #333;
      background-color: #e6e6e6;

      .language {
        font-weight: 500;
        text-transform: capitalize;
      }

      .copy-button {
        display: flex;
        gap: 4px;
        align-items: center;
        padding: 4px 8px;
        font-size: 12px;
        color: #333;
        cursor: pointer;
        background-color: #d6d6d6;
        border: none;
        border-radius: 4px;
        transition: background-color 0.2s;

        &:hover {
          background-color: #c6c6c6;
        }
      }
    }

    .code-content {
      padding: 16px;
      margin: 0;
      overflow: auto;
      font-family: SFMono-Regular, Consolas, 'Liberation Mono', Menlo, monospace;
      font-size: 14px;
      line-height: 1.5;
    }
  }
</style>
