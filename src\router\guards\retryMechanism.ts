/**
 * 路由重试机制
 * 提供路由导航失败时的重试功能
 */

import { ElMessage, ElNotification } from 'element-plus'
import { nextTick } from 'vue'

/**
 * 重试配置接口
 */
export interface RetryConfig {
  maxRetries: number
  retryDelay: number
  exponentialBackoff: boolean
  showProgress: boolean
  onRetry?: (attempt: number, maxRetries: number) => void
  onSuccess?: () => void
  onFinalFailure?: (error: any) => void
}

/**
 * 默认重试配置
 */
const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  retryDelay: 1000,
  exponentialBackoff: true,
  showProgress: true
}

/**
 * 路由重试管理器
 */
export class RouteRetryManager {
  private static instance: RouteRetryManager
  private activeRetries = new Map<string, AbortController>()

  static getInstance(): RouteRetryManager {
    if (!RouteRetryManager.instance) {
      RouteRetryManager.instance = new RouteRetryManager()
    }
    return RouteRetryManager.instance
  }

  /**
   * 执行带重试的异步操作
   * @param operation 要执行的操作
   * @param config 重试配置
   * @param operationId 操作ID（用于取消重试）
   * @returns 操作结果
   */
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    config: Partial<RetryConfig> = {},
    operationId?: string
  ): Promise<T> {
    const finalConfig = { ...DEFAULT_RETRY_CONFIG, ...config }
    const { maxRetries, retryDelay, exponentialBackoff, showProgress } = finalConfig

    // 如果提供了操作ID，创建取消控制器
    let abortController: AbortController | undefined
    if (operationId) {
      abortController = new AbortController()
      this.activeRetries.set(operationId, abortController)
    }

    let lastError: any
    let currentDelay = retryDelay

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // 检查是否被取消
        if (abortController?.signal.aborted) {
          throw new Error('操作已被取消')
        }

        if (showProgress && attempt > 1) {
          ElMessage.info(`正在重试... (${attempt}/${maxRetries})`)
        }

        // 执行回调
        finalConfig.onRetry?.(attempt, maxRetries)

        // 执行操作
        const result = await operation()

        // 成功时清理并返回结果
        if (operationId) {
          this.activeRetries.delete(operationId)
        }

        if (attempt > 1 && showProgress) {
          ElMessage.success('操作成功')
        }

        finalConfig.onSuccess?.()
        return result
      } catch (error) {
        lastError = error
        console.error(`操作失败 (第${attempt}次尝试):`, error)

        // 如果是最后一次尝试，不再重试
        if (attempt === maxRetries) {
          break
        }

        // 检查是否被取消
        if (abortController?.signal.aborted) {
          break
        }

        // 等待后重试
        await this.delay(currentDelay)

        // 指数退避
        if (exponentialBackoff) {
          currentDelay *= 2
        }
      }
    }

    // 所有重试都失败了
    if (operationId) {
      this.activeRetries.delete(operationId)
    }

    if (showProgress) {
      ElMessage.error(`操作失败，已重试${maxRetries}次`)
    }

    finalConfig.onFinalFailure?.(lastError)
    throw lastError
  }

  /**
   * 取消指定的重试操作
   * @param operationId 操作ID
   */
  cancelRetry(operationId: string): void {
    const controller = this.activeRetries.get(operationId)
    if (controller) {
      controller.abort()
      this.activeRetries.delete(operationId)
    }
  }

  /**
   * 取消所有重试操作
   */
  cancelAllRetries(): void {
    for (const [id, controller] of this.activeRetries) {
      controller.abort()
    }
    this.activeRetries.clear()
  }

  /**
   * 延迟函数
   * @param ms 延迟毫秒数
   */
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }

  /**
   * 获取活跃的重试操作数量
   */
  getActiveRetryCount(): number {
    return this.activeRetries.size
  }
}

/**
 * 路由操作重试工具
 */
export class RouteOperationRetry {
  private retryManager = RouteRetryManager.getInstance()

  /**
   * 重试菜单数据获取
   * @param getMenuDataFn 获取菜单数据的函数
   * @param config 重试配置
   */
  async retryGetMenuData(
    getMenuDataFn: () => Promise<void>,
    config: Partial<RetryConfig> = {}
  ): Promise<void> {
    const retryConfig: Partial<RetryConfig> = {
      maxRetries: 3,
      retryDelay: 2000,
      exponentialBackoff: true,
      showProgress: true,
      onRetry: (attempt, maxRetries) => {},
      onSuccess: () => {
        ElNotification.success({
          title: '成功',
          message: '菜单数据获取成功',
          duration: 2000
        })
      },
      onFinalFailure: (error) => {
        ElNotification.error({
          title: '获取菜单失败',
          message: '多次尝试后仍无法获取菜单数据，请刷新页面或联系管理员',
          duration: 5000
        })
      },
      ...config
    }

    return this.retryManager.executeWithRetry(getMenuDataFn, retryConfig, 'getMenuData')
  }

  /**
   * 重试路由导航
   * @param navigationFn 导航函数
   * @param targetPath 目标路径
   * @param config 重试配置
   */
  async retryNavigation(
    navigationFn: () => Promise<void>,
    targetPath: string,
    config: Partial<RetryConfig> = {}
  ): Promise<void> {
    const retryConfig: Partial<RetryConfig> = {
      maxRetries: 2,
      retryDelay: 1000,
      exponentialBackoff: false,
      showProgress: false,
      onRetry: (attempt, maxRetries) => {
        console.log(`正在重试导航到 ${targetPath}... (${attempt}/${maxRetries})`)
      },
      onFinalFailure: (error) => {
        console.error(`导航到 ${targetPath} 最终失败:`, error)
        ElMessage.error('页面导航失败，请刷新页面重试')
      },
      ...config
    }

    return this.retryManager.executeWithRetry(navigationFn, retryConfig, `navigation-${targetPath}`)
  }

  /**
   * 重试组件加载
   * @param loadComponentFn 组件加载函数
   * @param componentName 组件名称
   * @param config 重试配置
   */
  async retryComponentLoad<T>(
    loadComponentFn: () => Promise<T>,
    componentName: string,
    config: Partial<RetryConfig> = {}
  ): Promise<T> {
    const retryConfig: Partial<RetryConfig> = {
      maxRetries: 2,
      retryDelay: 500,
      exponentialBackoff: true,
      showProgress: false,
      onRetry: (attempt, maxRetries) => {
        console.log(`正在重试加载组件 ${componentName}... (${attempt}/${maxRetries})`)
      },
      onFinalFailure: (error) => {
        console.error(`组件 ${componentName} 加载最终失败:`, error)
      },
      ...config
    }

    return this.retryManager.executeWithRetry(
      loadComponentFn,
      retryConfig,
      `component-${componentName}`
    )
  }

  /**
   * 取消菜单数据获取重试
   */
  cancelMenuDataRetry(): void {
    this.retryManager.cancelRetry('getMenuData')
  }

  /**
   * 取消导航重试
   * @param targetPath 目标路径
   */
  cancelNavigationRetry(targetPath: string): void {
    this.retryManager.cancelRetry(`navigation-${targetPath}`)
  }

  /**
   * 取消组件加载重试
   * @param componentName 组件名称
   */
  cancelComponentLoadRetry(componentName: string): void {
    this.retryManager.cancelRetry(`component-${componentName}`)
  }
}

/**
 * 创建路由操作重试实例
 */
export function createRouteOperationRetry(): RouteOperationRetry {
  return new RouteOperationRetry()
}

/**
 * 全局路由重试实例
 */
export const routeRetry = createRouteOperationRetry()
