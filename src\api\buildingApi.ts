import api from '@/utils/http'

// 楼栋信息接口
export interface BuildingInfo {
  id: number
  sourceId: string | null
  projectId: number
  name: string
  buildingType: string | null
  status: string | null
  delFlag: string
  createBy: string
  createTime: string
  updateBy: string | null
  updateTime: string | null
  remark: string | null
  zsFloorList?: FloorInfo[]
}

// 楼层信息接口
export interface FloorInfo {
  id: number
  sourceId: string | null
  projectId: number
  buildingId: number
  name: string
  status: string | null
  delFlag: string | null
  createBy: string
  createTime: string
  updateBy: string | null
  updateTime: string | null
  remark: string | null
}

// 楼栋列表响应接口
export interface BuildingListResponse {
  msg: string
  code: number
  data: BuildingInfo[]
}

// 楼栋详情响应接口
export interface BuildingDetailResponse {
  msg: string
  code: number
  data: BuildingInfo
}

// 新增楼栋请求参数
export interface CreateBuildingRequest {
  projectId: number
  name: string
  zsFloorList?: Array<{
    name: string
  }>
}

// 修改楼栋请求参数
export interface UpdateBuildingRequest {
  id: number
  name: string
  zsFloorList?: Array<{
    id?: number
    name: string
  }>
}

// 通用响应接口
export interface CommonResponse {
  msg: string
  code: number
}

/**
 * 获取楼栋管理列表
 * @param projectId 项目ID
 * @returns 楼栋列表
 */
export function getBuildingList(projectId: number) {
  return api.get<BuildingListResponse>({
    url: '/basedata/building/list',
    params: { projectId }
  })
}

/**
 * 获取楼栋管理详细信息
 * @param id 楼栋ID
 * @returns 楼栋详情
 */
export function getBuildingDetail(id: number) {
  return api.get<BuildingDetailResponse>({
    url: `/basedata/building/${id}`
  })
}

/**
 * 新增楼栋管理
 * @param data 楼栋数据
 * @returns 新增结果
 */
export function createBuilding(data: CreateBuildingRequest) {
  return api.post<CommonResponse>({
    url: '/basedata/building/',
    data
  })
}

/**
 * 修改楼栋管理
 * @param data 楼栋数据
 * @returns 修改结果
 */
export function updateBuilding(data: UpdateBuildingRequest) {
  return api.put<CommonResponse>({
    url: '/basedata/building/',
    data
  })
}

/**
 * 删除楼栋管理
 * @param id 楼栋ID
 * @returns 删除结果
 */
export function deleteBuilding(id: number) {
  return api.del<CommonResponse>({
    url: `/basedata/building/${id}`
  })
}
