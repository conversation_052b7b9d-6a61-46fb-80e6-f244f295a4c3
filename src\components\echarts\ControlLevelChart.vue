<template>
  <div :id="chartId" class="control-level-chart"></div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted, watch } from 'vue'
  import * as echarts from 'echarts'

  // 使用props接收数据
  const props = defineProps({
    chartData: {
      type: Array,
      required: true
    }
  })

  // 生成唯一的图表ID，避免多个图表实例冲突
  const chartId = `control-level-chart-${Date.now()}`
  // 图表实例
  const chartInstance = ref(null)

  // 处理窗口大小变化
  const handleResize = () => {
    if (chartInstance.value) {
      chartInstance.value.resize()
    }
  }

  // 初始化图表
  const initChart = () => {
    // 获取DOM元素
    const chartDom = document.getElementById(chartId)
    if (!chartDom) return

    // 创建图表实例
    chartInstance.value = echarts.init(chartDom)

    // 更新图表数据
    updateChart()

    // 添加窗口大小变化的监听器
    window.addEventListener('resize', handleResize)
  }

  // 更新图表数据
  const updateChart = () => {
    if (!chartInstance.value) return

    // 从props获取数据
    const data = props.chartData || []

    // 定义每个管控等级对应的颜色
    const colorMap = {
      一级管控: '#F56C6C', // 对应danger类型，红色
      二级管控: '#E6A23C', // 对应warning类型，橙色
      三级管控: '#909399', // 对应info类型，灰色
      四级管控: '#67C23A' // 对应success类型，绿色
    }

    // 准备饼图数据
    const pieData = data.map((item) => ({
      value: item.percentage,
      name: item.level,
      itemStyle: {
        color: colorMap[item.level]
      }
    }))

    // 图表配置项
    const option = {
      title: {
        text: '管控等级分布',
        left: 'center',
        top: 10,
        textStyle: {
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c}% ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 10,
        top: 'center',
        data: data.map((item) => item.level)
      },
      series: [
        {
          name: '管控等级',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: true,
            formatter: '{b}: {c}%'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 14,
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: true
          },
          data: pieData
        }
      ]
    }

    // 使用配置项设置图表
    chartInstance.value.setOption(option)
  }

  // 监听数据变化
  watch(
    () => props.chartData,
    () => {
      updateChart()
    },
    { deep: true }
  )

  // 组件挂载时初始化图表
  onMounted(() => {
    initChart()
    // 延迟执行一次resize，确保图表正确渲染
    setTimeout(() => {
      handleResize()
    }, 200)
  })

  // 组件卸载时销毁图表实例
  onUnmounted(() => {
    if (chartInstance.value) {
      chartInstance.value.dispose()
      chartInstance.value = null
    }
    window.removeEventListener('resize', handleResize)
  })
</script>

<style scoped>
  .control-level-chart {
    width: 100%;
    height: 300px;
    min-height: 300px;
  }
</style>
