import { MenuListType } from '@/types/menu'

/**
 * 异步路由类型定义
 *
 * 注意：路由配置现在从后端API动态获取，不再使用硬编码配置
 * 后端getRouters接口返回的数据格式应与MenuListType[]类型一致
 *
 * 菜单标题（title）:
 * 直接使用中文字符串
 */

// 导出空数组，实际路由数据从API获取
// 如果需要临时回退到硬编码路由，请取消下面的注释并注释掉空数组
export const asyncRoutes: MenuListType[] = []
// export const asyncRoutes: MenuListType[] = asyncRoutesBackup

// 以下是原硬编码路由配置的备份，仅供参考
// 实际使用时请确保后端API返回相同格式的数据
/*
export const asyncRoutesBackup: MenuListType[] = [
  {
      "name": "示范工程概览",
      "path": "/demo-project-overview",
      "hidden": false,
      "component": "/demo-project-overview/index",
      "meta": {
          "title": "示范工程概览",
          "icon": "&#xe8af;",
          "noCache": false,
          "link": null,
          "keepAlive": true,
          "inMainContainer": true
      }
  },
  {
      "name": "可视化大屏",
      "path": "/visualization-dashboard",
      "hidden": false,
      "component": "/visualization-dashboard/index",
      "meta": {
          "title": "可视化大屏",
          "icon": "&#xe899;",
          "noCache": false,
          "link": null,
          "keepAlive": true,
          "inMainContainer": true
      }
  },
  {
      "name": "能耗监测",
      "path": "/energy-monitoring",
      "hidden": false,
      "redirect": "noRedirect",
      "component": "/index/index",
      "alwaysShow": true,
      "meta": {
          "title": "能耗监测",
          "icon": "&#xe827;",
          "noCache": false,
          "link": null,
          "keepAlive": false,
          "inMainContainer": true
      },
      "children": [
          {
              "name": "实时监测",
              "path": "real-time",
              "hidden": false,
              "component": "/energy-monitoring/real-time/index",
              "meta": {
                  "title": "实时能耗",
                  "icon": "#",
                  "noCache": false,
                  "link": null,
                  "keepAlive": true,
                  "inMainContainer": true
              }
          },
          {
              "name": "能耗数据",
              "path": "data",
              "hidden": false,
              "component": "/energy-monitoring/data/index",
              "meta": {
                  "title": "能耗数据",
                  "icon": "#",
                  "noCache": false,
                  "link": null,
                  "keepAlive": true,
                  "inMainContainer": true
              }
          },
          {
              "name": "能耗分析",
              "path": "analysis",
              "hidden": false,
              "component": "/energy-monitoring/analysis/index",
              "meta": {
                  "title": "能耗分析",
                  "icon": "#",
                  "noCache": false,
                  "link": null,
                  "keepAlive": true,
                  "inMainContainer": true
              }
          },
          {
              "name": "可再生能源",
              "path": "renewable-energy",
              "hidden": false,
              "component": "/energy-monitoring/renewable-energy/index",
              "meta": {
                  "title": "可再生能源",
                  "icon": "#",
                  "noCache": false,
                  "link": null,
                  "keepAlive": true,
                  "inMainContainer": true
              }
          },
          {
              "name": "数据查询",
              "path": "query",
              "hidden": false,
              "component": "/energy-monitoring/query/index",
              "meta": {
                  "title": "数据查询",
                  "icon": "#",
                  "noCache": false,
                  "link": null,
                  "keepAlive": true,
                  "inMainContainer": true
              }
          }
      ]
  },
  {
      "name": "环境监测",
      "path": "/environment-monitoring",
      "hidden": false,
      "redirect": "noRedirect",
      "component": "/index/index",
      "alwaysShow": true,
      "meta": {
          "title": "环境监测",
          "icon": "&#xe821;",
          "noCache": false,
          "link": null,
          "keepAlive": false,
          "inMainContainer": true
      },
      "children": [
          {
              "name": "环境监测总览",
              "path": "overview",
              "hidden": false,
              "component": "/environment-monitoring/overview/index",
              "meta": {
                  "title": "环境监测总览",
                  "icon": "#",
                  "noCache": false,
                  "link": null,
                  "keepAlive": true,
                  "inMainContainer": true
              }
          },
          {
              "name": "室外环境监测",
              "path": "outdoor",
              "hidden": false,
              "component": "/environment-monitoring/outdoor/index",
              "meta": {
                  "title": "室外环境监测",
                  "icon": "#",
                  "noCache": false,
                  "link": null,
                  "keepAlive": true,
                  "inMainContainer": true
              }
          },
          {
              "name": "室内空气质量监测",
              "path": "indoor-air-quality",
              "hidden": false,
              "component": "/environment-monitoring/indoor-air-quality/index",
              "meta": {
                  "title": "室内空气质量监测",
                  "icon": "#",
                  "noCache": false,
                  "link": null,
                  "keepAlive": true,
                  "inMainContainer": true
              }
          },
          {
              "name": "水质监测",
              "path": "water-quality",
              "hidden": false,
              "component": "/environment-monitoring/water-quality/index",
              "meta": {
                  "title": "水质监测",
                  "icon": "#",
                  "noCache": false,
                  "link": null,
                  "keepAlive": true,
                  "inMainContainer": true
              }
          }
      ]
  },
  {
      "name": "碳排放管理",
      "path": "/carbon-emission-management",
      "hidden": false,
      "redirect": "noRedirect",
      "component": "/index/index",
      "alwaysShow": true,
      "meta": {
          "title": "碳排放管理",
          "icon": "&#xe80d;",
          "noCache": false,
          "link": null,
          "keepAlive": false,
          "inMainContainer": true
      },
      "children": [
          {
              "name": "碳排放总览",
              "path": "overview",
              "hidden": false,
              "component": "/carbon-emission-management/overview/index",
              "meta": {
                  "title": "碳排放总览",
                  "icon": "#",
                  "noCache": false,
                  "link": null,
                  "keepAlive": true,
                  "inMainContainer": true
              }
          },
          {
              "name": "碳排放环比",
              "path": "month-on-month",
              "hidden": false,
              "component": "/carbon-emission-management/month-on-month/index",
              "meta": {
                  "title": "碳排放环比",
                  "icon": "#",
                  "noCache": false,
                  "link": null,
                  "keepAlive": true,
                  "inMainContainer": true
              }
          },
          {
              "name": "碳排放同比",
              "path": "year-on-year",
              "hidden": false,
              "component": "/carbon-emission-management/year-on-year/index",
              "meta": {
                  "title": "碳排放同比",
                  "icon": "#",
                  "noCache": false,
                  "link": null,
                  "keepAlive": true,
                  "inMainContainer": true
              }
          },
          {
              "name": "碳排放对比",
              "path": "comparison",
              "hidden": false,
              "component": "/carbon-emission-management/comparison/index",
              "meta": {
                  "title": "碳排放对比",
                  "icon": "#",
                  "noCache": false,
                  "link": null,
                  "keepAlive": true,
                  "inMainContainer": true
              }
          },
          {
              "name": "数据查询",
              "path": "query",
              "hidden": false,
              "component": "/carbon-emission-management/query/index",
              "meta": {
                  "title": "数据查询",
                  "icon": "#",
                  "noCache": false,
                  "link": null,
                  "keepAlive": true,
                  "inMainContainer": true
              }
          },
          {
              "name": "统计报表",
              "path": "statistical-report",
              "hidden": false,
              "component": "/carbon-emission-management/statistical-report/index",
              "meta": {
                  "title": "统计报表",
                  "icon": "#",
                  "noCache": false,
                  "link": null,
                  "keepAlive": true,
                  "inMainContainer": true
              }
          },
          {
              "name": "碳足迹",
              "path": "carbon-footprint",
              "hidden": false,
              "component": "/carbon-emission-management/carbon-footprint/index",
              "meta": {
                  "title": "碳足迹",
                  "icon": "#",
                  "noCache": false,
                  "link": null,
                  "keepAlive": false,
                  "inMainContainer": true
              }
          },
          {
              "name": "碳排放工具箱",
              "path": "toolbox",
              "hidden": false,
              "component": "/carbon-emission-management/toolbox/index",
              "meta": {
                  "title": "碳排放工具箱",
                  "icon": "#",
                  "noCache": false,
                  "link": null,
                  "keepAlive": true,
                  "inMainContainer": true
              }
          }
      ]
  },
  {
      "name": "配置管理",
      "path": "/safeguard",
      "hidden": false,
      "redirect": "noRedirect",
      "component": "/index/index",
      "alwaysShow": true,
      "meta": {
          "title": "配置管理",
          "icon": "&#xe72b;",
          "noCache": false,
          "link": null,
          "keepAlive": false,
          "inMainContainer": true
      },
      "children": [
          {
              "name": "设备列表",
              "path": "device-list",
              "hidden": false,
              "component": "/safeguard/DeviceList",
              "meta": {
                  "title": "设备列表",
                  "icon": "#",
                  "noCache": false,
                  "link": null,
                  "keepAlive": true,
                  "inMainContainer": true
              }
          },
          {
              "name": "设备参数",
              "path": "device-params",
              "hidden": false,
              "component": "/safeguard/DeviceParams",
              "meta": {
                  "title": "设备参数",
                  "icon": "#",
                  "noCache": false,
                  "link": null,
                  "keepAlive": true,
                  "inMainContainer": true
              }
          },
          {
              "name": "网络通讯参数",
              "path": "network-params",
              "hidden": false,
              "component": "/safeguard/NetworkParams",
              "meta": {
                  "title": "网络通讯参数",
                  "icon": "#",
                  "noCache": false,
                  "link": null,
                  "keepAlive": true,
                  "inMainContainer": true
              }
          },
          {
              "name": "信息安全参数",
              "path": "security-params",
              "hidden": false,
              "component": "/safeguard/SecurityParams",
              "meta": {
                  "title": "信息安全参数",
                  "icon": "#",
                  "noCache": false,
                  "link": null,
                  "keepAlive": true,
                  "inMainContainer": true
              }
          }
      ]
  },
  {
      "name": "用户管理",
      "path": "/user",
      "hidden": false,
      "redirect": "noRedirect",
      "component": "/index/index",
      "alwaysShow": true,
      "meta": {
          "title": "用户管理",
          "icon": "&#xe734;",
          "noCache": false,
          "link": null,
          "keepAlive": false,
          "inMainContainer": true
      },
      "children": [
          {
              "name": "账号管理",
              "path": "account",
              "hidden": false,
              "component": "/user/Account",
              "meta": {
                  "title": "账号管理",
                  "icon": "user",
                  "noCache": false,
                  "link": null,
                  "keepAlive": true,
                  "inMainContainer": true
              }
          },
          {
              "name": "角色管理",
              "path": "role",
              "hidden": false,
              "component": "/user/Role",
              "meta": {
                  "title": "角色管理",
                  "icon": null,
                  "noCache": false,
                  "link": null,
                  "keepAlive": true,
                  "inMainContainer": true
              }
          },
          {
              "name": "部门管理",
              "path": "department",
              "hidden": false,
              "component": "/user/Department",
              "meta": {
                  "title": "部门管理",
                  "icon": "#",
                  "noCache": false,
                  "link": null,
                  "keepAlive": true,
                  "inMainContainer": true
              }
          },
          {
              "name": "角色权限",
              "path": "role-permission",
              "hidden": false,
              "component": "/user/RolePermission",
              "meta": {
                  "title": "角色权限",
                  "icon": "#",
                  "noCache": false,
                  "link": null,
                  "keepAlive": false,
                  "inMainContainer": true
              }
          },
          {
              "name": "用户中心",
              "path": "user",
              "hidden": false,
              "component": "/user/User",
              "meta": {
                  "title": "用户中心",
                  "icon": null,
                  "noCache": false,
                  "link": null,
                  "keepAlive": true,
                  "inMainContainer": true
              }
          },
          {
              "name": "Dict",
              "path": "dict",
              "hidden": false,
              "component": "system/dict/index",
              "meta": {
                  "title": "字典管理",
                  "icon": "#",
                  "noCache": false,
                  "link": null,
                  "keepAlive": false,
                  "inMainContainer": true
              }
          },
          {
              "name": "Config",
              "path": "config",
              "hidden": false,
              "component": "system/config/index",
              "meta": {
                  "title": "参数设置",
                  "icon": "#",
                  "noCache": false,
                  "link": null,
                  "keepAlive": false,
                  "inMainContainer": true
              }
          }
      ]
  },
  {
      "name": "智能助手",
      "path": "/ai-assistant",
      "hidden": false,
      "component": "/ai-assistant/index",
      "meta": {
          "title": "智能助手",
          "icon": "&#xe819;",
          "noCache": false,
          "link": null,
          "keepAlive": true,
          "inMainContainer": true
      }
  },
  {
      "name": "异常页面",
      "path": "/exception",
      "hidden": false,
      "redirect": "noRedirect",
      "component": "/index/index",
      "alwaysShow": true,
      "meta": {
          "title": "异常页面",
          "icon": "&#xe820;",
          "noCache": false,
          "link": null,
          "keepAlive": true,
          "inMainContainer": true
      },
      "children": [
          {
              "name": "403",
              "path": "403",
              "hidden": false,
              "component": "/exception/403",
              "meta": {
                  "title": "403禁止访问",
                  "icon": null,
                  "noCache": false,
                  "link": null,
                  "keepAlive": true,
                  "inMainContainer": true
              }
          },
          {
              "name": "404",
              "path": "404",
              "hidden": false,
              "component": "/exception/404",
              "meta": {
                  "title": "404页面不存在",
                  "icon": null,
                  "noCache": false,
                  "link": null,
                  "keepAlive": true,
                  "inMainContainer": true
              }
          },
          {
              "name": "500",
              "path": "500",
              "hidden": false,
              "component": "/exception/500",
              "meta": {
                  "title": "500服务器错误",
                  "icon": null,
                  "noCache": false,
                  "link": null,
                  "keepAlive": true,
                  "inMainContainer": true
              }
          }
      ]
  },
  {
      "name": "结果页面",
      "path": "/result",
      "hidden": false,
      "redirect": "noRedirect",
      "component": "/index/index",
      "alwaysShow": true,
      "meta": {
          "title": "结果页面",
          "icon": "&#xe715;",
          "noCache": false,
          "link": null,
          "keepAlive": true,
          "inMainContainer": true
      },
      "children": [
          {
              "name": "成功页面",
              "path": "success",
              "hidden": false,
              "component": "/result/Success",
              "meta": {
                  "title": "成功页面",
                  "icon": null,
                  "noCache": false,
                  "link": null,
                  "keepAlive": true,
                  "inMainContainer": true
              }
          },
          {
              "name": "失败页面",
              "path": "fail",
              "hidden": false,
              "component": "/result/Fail",
              "meta": {
                  "title": "失败页面",
                  "icon": null,
                  "noCache": false,
                  "link": null,
                  "keepAlive": true,
                  "inMainContainer": true
              }
          }
      ]
  }
]
*/
