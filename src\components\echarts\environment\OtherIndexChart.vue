<template>
  <div class="chart-container" ref="chartContainer"></div>
</template>

<script setup>
  import { ref, onMounted, watch, nextTick } from 'vue'
  import * as echarts from 'echarts'

  const props = defineProps({
    timeRange: {
      type: String,
      default: 'day'
    },
    chartData: {
      type: Object,
      default: () => ({
        xAxisData: [],
        co2Data: [],
        formaldehydeData: [],
        illuminanceData: [],
        noiseData: []
      })
    },
    // 指定要显示的两个指标，可选值有 'co2', 'formaldehyde', 'illuminance', 'noise'
    indicators: {
      type: Array,
      default: () => ['co2']
    }
  })

  const chartContainer = ref(null)
  let chart = null

  // 指标配置
  const indicatorConfig = {
    co2: {
      name: '二氧化碳',
      unit: 'ppm',
      color: '#409EFF'
    },
    formaldehyde: {
      name: '',
      unit: '',
      color: '#F56C6C'
    },
    illuminance: {
      name: '照度',
      unit: 'lux',
      color: '#E6A23C'
    },
    noise: {
      name: '噪声',
      unit: 'dB',
      color: '#909399'
    }
  }

  // 根据颜色字符串获取rgba颜色
  const getColorWithOpacity = (color, opacity) => {
    // 简单的颜色映射
    const colorMap = {
      '#409EFF': `rgba(64, 158, 255, ${opacity})`,
      '#F56C6C': `rgba(245, 108, 108, ${opacity})`,
      '#E6A23C': `rgba(230, 162, 60, ${opacity})`,
      '#909399': `rgba(144, 147, 153, ${opacity})`
    }

    return colorMap[color] || `rgba(64, 158, 255, ${opacity})`
  }

  // 初始化图表
  const initChart = () => {
    if (!chartContainer.value) return

    // 如果图表已经存在，销毁它以避免重复创建
    if (chart) {
      chart.dispose()
    }

    // 获取要显示的两个指标
    const firstIndicator = props.indicators[0] || 'co2'
    const secondIndicator = props.indicators[1] || 'formaldehyde'

    const firstConfig = indicatorConfig[firstIndicator]
    const secondConfig = indicatorConfig[secondIndicator]

    // 确保数据存在
    const firstData = props.chartData[`${firstIndicator}Data`] || []
    const secondData = props.chartData[`${secondIndicator}Data`] || []
    const xAxisData = props.chartData.xAxisData || []

    console.log('初始化图表：', {
      firstIndicator,
      secondIndicator,
      firstData,
      secondData,
      xAxisData
    })

    // 创建新的图表实例
    chart = echarts.init(chartContainer.value)

    // 设置图表选项
    const option = {
      tooltip: {
        trigger: 'axis',
        formatter: function (params) {
          let result = params[0].axisValue + '<br/>'
          params.forEach((param) => {
            let marker = `<span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${param.color};"></span>`
            let config = param.seriesName === firstConfig.name ? firstConfig : secondConfig
            let value = parseFloat(param.value).toFixed(2) + config.unit
            result += marker + param.seriesName + ': ' + value + '<br/>'
          })
          return result
        }
      },
      legend: {
        data: [firstConfig.name, secondConfig.name],
        bottom: 0
      },
      grid: {
        left: '5%',
        right: '5%',
        bottom: '15%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: xAxisData,
        axisLine: {
          lineStyle: {
            color: '#999'
          }
        },
        axisLabel: {
          color: '#666'
        }
      },
      yAxis: [
        {
          type: 'value',
          name: `${firstConfig.name}(${firstConfig.unit})`,
          nameTextStyle: {
            padding: [0, 30, 0, 0],
            color: '#666',
            fontWeight: 'bold'
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: firstConfig.color
            }
          },
          axisLabel: {
            formatter: '{value}',
            color: '#666'
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(224, 230, 241, 0.8)'
            }
          }
        },
        {
          type: 'value',
          name: `${secondConfig.name}(${secondConfig.unit})`,
          nameTextStyle: {
            padding: [0, 0, 0, 30],
            color: '#666',
            fontWeight: 'bold'
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: secondConfig.color
            }
          },
          axisLabel: {
            formatter: '{value}',
            color: '#666'
          },
          splitLine: {
            show: false
          }
        }
      ],
      series: [
        {
          name: firstConfig.name,
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          showSymbol: true,
          showAllSymbol: 'auto',
          data: firstData,
          itemStyle: {
            color: firstConfig.color
          },
          lineStyle: {
            width: 2
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: getColorWithOpacity(firstConfig.color, 0.3)
              },
              {
                offset: 1,
                color: getColorWithOpacity(firstConfig.color, 0.1)
              }
            ])
          }
        },
        {
          name: secondConfig.name,
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          showSymbol: true,
          showAllSymbol: 'auto',
          yAxisIndex: 1,
          data: secondData,
          itemStyle: {
            color: secondConfig.color
          },
          lineStyle: {
            width: 2
          }
        }
      ]
    }

    // 应用选项
    chart.setOption(option)

    // 添加响应式调整
    window.addEventListener('resize', () => {
      if (chart) {
        chart.resize()
      }
    })
  }

  // 更新图表
  const updateChart = () => {
    if (!chart) {
      initChart()
      return
    }

    const firstIndicator = props.indicators[0] || 'co2'
    const secondIndicator = props.indicators[1] || 'formaldehyde'

    // 确保数据存在
    const firstData = props.chartData[`${firstIndicator}Data`] || []
    const secondData = props.chartData[`${secondIndicator}Data`] || []

    console.log('更新图表数据：', firstIndicator, firstData, secondIndicator, secondData)

    chart.setOption({
      xAxis: {
        data: props.chartData.xAxisData || []
      },
      series: [
        {
          data: firstData
        },
        {
          data: secondData
        }
      ]
    })
  }

  // 监听图表数据变化
  watch(
    () => props.chartData,
    () => {
      nextTick(() => {
        updateChart()
      })
    },
    { deep: true }
  )

  // 监听时间范围变化
  watch(
    () => props.timeRange,
    () => {
      nextTick(() => {
        updateChart()
      })
    }
  )

  // 监听指标变化
  watch(
    () => props.indicators,
    () => {
      nextTick(() => {
        initChart() // 指标变化需要重新初始化
      })
    },
    { deep: true }
  )

  // 组件挂载时初始化图表
  onMounted(() => {
    initChart()
  })
</script>

<style scoped lang="scss">
  .chart-container {
    width: 100%;
    height: 350px;
  }
</style>
