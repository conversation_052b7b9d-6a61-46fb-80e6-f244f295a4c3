<template>
  <div class="air-quality-container">
    <div class="content-section">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card class="overview-card">
            <template #header>
              <div class="card-header">
                <span>水质监测数据</span>
                <div class="filter-controls">
                  <ProjectSelector
                    ref="projectSelectorRef"
                    v-model="selectedProjectId"
                    placeholder="请选择项目"
                    style="width: 250px"
                    @change="handleProjectChange"
                    @loaded="handleProjectListLoaded"
                  />
                  <el-select
                    v-model="selectedEquipmentId"
                    placeholder="选择设备"
                    style="width: 200px"
                    :disabled="!selectedProjectId"
                    :loading="equipmentLoading"
                    @change="handleEquipmentChange"
                  >
                    <el-option
                      v-for="equipment in equipmentOptions"
                      :key="equipment.value"
                      :label="equipment.label"
                      :value="equipment.value"
                    ></el-option>
                  </el-select>
                </div>
              </div>
            </template>

            <div class="data-grid">
              <div class="data-item" v-for="(item, index) in waterQualityData" :key="index">
                <div class="item-value" :class="item.status"> {{ item.value }}{{ item.unit }} </div>
                <div class="item-name">{{ item.name }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="chart-row">
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>pH值与溶解氧趋势</span>
                <el-radio-group v-model="phTimeRange" size="small">
                  <el-radio-button label="day">日</el-radio-button>
                  <el-radio-button label="week">周</el-radio-button>
                  <el-radio-button label="month">月</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <PHChart :timeRange="phTimeRange" />
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>COD与氨氮趋势</span>
                <el-radio-group v-model="codTimeRange" size="small">
                  <el-radio-button label="day">日</el-radio-button>
                  <el-radio-button label="week">周</el-radio-button>
                  <el-radio-button label="month">月</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <CODChart :timeRange="codTimeRange" />
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-card class="history-card">
            <template #header>
              <div class="card-header">
                <span>历史监测数据</span>
                <div>
                  <el-date-picker
                    v-model="dateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    format="YYYY/MM/DD"
                    value-format="YYYY-MM-DD"
                    style="width: 320px"
                    :disabled-date="disabledDate"
                  ></el-date-picker>
                  <el-button
                    type="primary"
                    size="small"
                    style="margin-left: 10px"
                    @click="searchData"
                    :disabled="historyQueryButtonDisabled"
                    >查询</el-button
                  >
                  <!-- 导出 -->
                  <el-button type="primary" size="small" @click="exportData">导出</el-button>
                </div>
              </div>
            </template>

            <el-table :data="historyData" border style="width: 100%" v-loading="historyLoading">
              <el-table-column label="序号" align="center" width="80">
                <template #default="scope">
                  {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
                </template>
              </el-table-column>
              <el-table-column
                prop="date"
                label="监测时间"
                align="center"
                width="200"
              ></el-table-column>
              <el-table-column prop="ph" label="pH值" align="center"></el-table-column>
              <el-table-column prop="temperature" label="水温(°C)" align="center"></el-table-column>
              <el-table-column prop="cod" label="COD(mg/l)" align="center"></el-table-column>
              <el-table-column
                prop="dissolvedOxygen"
                label="溶解氧(mg/l)"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="ammoniaNitrogen"
                label="氨氮(mg/l)"
                align="center"
              ></el-table-column>
              <el-table-column prop="turbidity" label="浊度(NTU)" align="center"></el-table-column>
            </el-table>

            <div class="pagination-container">
              <el-pagination
                background
                layout="total, sizes, prev, pager, next, jumper"
                :total="totalItems"
                :page-sizes="[10, 20, 50, 100]"
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              ></el-pagination>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted, reactive, provide, computed, watch, nextTick } from 'vue'
  import { ElMessage } from 'element-plus'
  import PHChart from '@/components/echarts/WaterQuality/PHChart.vue'
  import CODChart from '@/components/echarts/WaterQuality/CODChart.vue'
  import ProjectSelector from '@/components/ProjectSelector.vue'
  import {
    getEquipmentList,
    getWaterQualityMonitoring,
    getWaterQualityTrend,
    getWaterQualityHistory,
    exportWaterQualityData
  } from '@/api/environment-monitoring/waterQualityApi'

  // 项目选择相关数据
  const selectedProjectId = ref(null) // 初始为空，等待获取项目列表后设置第一个
  const selectedProjectInfo = ref(null)
  const projectSelectorRef = ref()

  // 设备选择相关数据
  const selectedEquipmentId = ref(null) // 当前选中的设备ID
  const equipmentList = ref([]) // 设备列表
  const equipmentLoading = ref(false) // 设备列表加载状态

  // 查询状态管理 - 历史数据查询
  const lastHistoryQueryParams = ref(null)
  const historyQueryButtonDisabled = ref(false)

  // 比较历史数据查询参数是否相同
  const isSameHistoryQueryParams = (params1, params2) => {
    if (!params1 || !params2) return false
    return (
      params1.projectId === params2.projectId &&
      params1.equipmentId === params2.equipmentId &&
      params1.beginTime === params2.beginTime &&
      params1.endTime === params2.endTime &&
      params1.pageSize === params2.pageSize &&
      params1.pageNum === params2.pageNum
    )
  }

  // 计算设备选项
  const equipmentOptions = computed(() => {
    return equipmentList.value.map((equipment) => ({
      label: equipment.name,
      value: equipment.id
    }))
  })

  // 获取设备列表
  const fetchEquipmentList = async (projectId) => {
    if (!projectId) {
      equipmentList.value = []
      return
    }

    try {
      equipmentLoading.value = true
      const response = await getEquipmentList({
        projectId: projectId,
        buildingId: '', // 可选参数
        floorId: '', // 可选参数
        equipmentCategory: 1, // 环境监测设备
        equipmentCategorytype: 2 // 水质监测
      })

      if (response.code === 200) {
        equipmentList.value = response.data || []
        console.log('设备列表获取成功:', response.data)
      } else {
        ElMessage.error(response.msg || '获取设备列表失败')
        equipmentList.value = []
      }
    } catch (error) {
      console.error('获取设备列表失败:', error)
      ElMessage.error('获取设备列表失败，请稍后重试')
      equipmentList.value = []
    } finally {
      equipmentLoading.value = false
    }
  }

  // 处理项目变化
  const handleProjectChange = (projectInfo) => {
    selectedProjectInfo.value = projectInfo
    selectedEquipmentId.value = null // 项目变化时清空设备选择
    console.log('选中的项目:', projectInfo)

    // 获取新项目的设备列表
    if (projectInfo && projectInfo.id) {
      fetchEquipmentList(projectInfo.id)
    } else {
      equipmentList.value = []
    }
    // 项目变化时自动发送请求（包含所有接口）
    nextTick(() => {
      autoSearch()
    })
  }

  // 处理设备变化
  const handleEquipmentChange = (equipmentId) => {
    console.log('选中的设备:', equipmentId)
    // 设备变化时自动发送请求（包含所有接口）
    autoSearch()
  }

  // 监听项目选择器组件的项目列表加载完成
  const handleProjectListLoaded = (projectList) => {
    // 当项目列表加载完成后，自动选择第一个项目并发请求
    if (projectList && projectList.length > 0) {
      const firstProject = projectList[0]
      selectedProjectId.value = firstProject.id
      selectedProjectInfo.value = firstProject
      console.log('自动选择第一个项目:', firstProject)

      // 获取第一个项目的设备列表
      fetchEquipmentList(firstProject.id)

      // 自动发请求获取数据（包含所有接口）
      nextTick(() => {
        autoSearch()
      })
    }
  }

  // 获取今天的日期字符串（YYYY-MM-DD格式）
  const getTodayDateString = () => {
    const today = new Date()
    const year = today.getFullYear()
    const month = String(today.getMonth() + 1).padStart(2, '0')
    const day = String(today.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  // 自动查询方法（包含所有接口）
  const autoSearch = async () => {
    // 自动查询时不需要检查项目选择，使用固定参数
    console.log('水质监测自动查询触发，使用固定参数:', {
      projectId: 17,
      equipmentId: 1,
      beginTime: getTodayDateString(),
      endTime: getTodayDateString()
    })

    // 获取实时水质监测数据（如果有选择项目的话）
    if (selectedProjectId.value) {
      await fetchWaterQualityData()
      // 获取pH趋势数据（默认使用"日"时间粒度）
      await fetchPhTrendData()
      // 获取COD趋势数据（默认使用"日"时间粒度）
      await fetchCodTrendData()
    }

    // 获取历史数据（使用自动参数）
    await fetchHistoryData(true)
  }

  // 监听项目选择变化
  watch(selectedProjectId, (newVal, oldVal) => {
    if (newVal !== oldVal) {
      selectedEquipmentId.value = null // 项目改变时清空设备选择
      // 可以在这里触发数据加载等操作
    }
  })

  // 获取实时水质监测数据
  const fetchWaterQualityData = async () => {
    if (!selectedProjectId.value) return

    try {
      const response = await getWaterQualityMonitoring({
        projectId: selectedProjectId.value,
        equipmentId: selectedEquipmentId.value || undefined
      })

      if (response.code === 200 && response.data) {
        const data = response.data
        // 更新水质数据显示
        waterQualityData[0].value = data.ph?.toString() || '0'
        waterQualityData[1].value = data.sw?.toString() || '0'
        waterQualityData[2].value = data.cod?.toString() || '0'
        waterQualityData[3].value = data.rjy?.toString() || '0'
        waterQualityData[4].value = data.adlz?.toString() || '0'
        waterQualityData[5].value = data.zd?.toString() || '0'
        console.log('获取水质监测数据成功:', data)
      } else {
        ElMessage.error(response.msg || '获取水质监测数据失败')
      }
    } catch (error) {
      console.error('获取水质监测数据失败:', error)
      ElMessage.error('获取水质监测数据失败，请稍后重试')
    }
  }

  // 获取pH和溶解氧趋势数据
  const fetchPhTrendData = async () => {
    if (!selectedProjectId.value) return

    try {
      const timeType = phTimeRange.value === 'day' ? 0 : phTimeRange.value === 'week' ? 1 : 2
      const response = await getWaterQualityTrend({
        projectId: selectedProjectId.value,
        equipmentId: selectedEquipmentId.value || undefined,
        timeType: timeType
      })

      if (response.code === 200 && response.data) {
        // 更新pH趋势数据
        phTrendData.value = response.data
        console.log('获取pH趋势数据成功:', response.data)
      } else {
        ElMessage.error(response.msg || '获取pH趋势数据失败')
      }
    } catch (error) {
      console.error('获取pH趋势数据失败:', error)
      ElMessage.error('获取pH趋势数据失败，请稍后重试')
    }
  }

  // 获取COD和氨氮趋势数据
  const fetchCodTrendData = async () => {
    if (!selectedProjectId.value) return

    try {
      const timeType = codTimeRange.value === 'day' ? 0 : codTimeRange.value === 'week' ? 1 : 2
      const response = await getWaterQualityTrend({
        projectId: selectedProjectId.value,
        equipmentId: selectedEquipmentId.value || undefined,
        timeType: timeType
      })

      if (response.code === 200 && response.data) {
        // 更新COD趋势数据
        codTrendData.value = response.data
        console.log('获取COD趋势数据成功:', response.data)
      } else {
        ElMessage.error(response.msg || '获取COD趋势数据失败')
      }
    } catch (error) {
      console.error('获取COD趋势数据失败:', error)
      ElMessage.error('获取COD趋势数据失败，请稍后重试')
    }
  }

  const waterQualityData = reactive([
    {
      name: '酸碱度',
      value: '7.2',
      unit: '',
      status: 'good',
      range: '6.5-8.5'
    },
    {
      name: '水温',
      value: '24.5',
      unit: '°C',
      status: 'good',
      range: '0°C~+35°C'
    },
    {
      name: 'COD',
      value: '28',
      unit: 'mg/l',
      status: 'normal',
      range: '≤40mg/l'
    },
    {
      name: '溶解氧',
      value: '6.8',
      unit: 'mg/l',
      status: 'good',
      range: '≥5mg/l'
    },
    {
      name: '氨氮离子',
      value: '0.8',
      unit: 'mg/l',
      status: 'good',
      range: '≤1.5mg/l'
    },
    {
      name: '浊度',
      value: '10971.24',
      unit: 'NTU',
      status: 'bad',
      range: '≤5NTU'
    }
  ])

  // pH趋势数据
  const phTrendData = ref({
    timeList: [],
    dataList: []
  })

  // COD趋势数据
  const codTrendData = ref({
    timeList: [],
    dataList: []
  })

  // 提供数据给子组件
  provide('waterQualityData', waterQualityData)
  provide('phTrendData', phTrendData)
  provide('codTrendData', codTrendData)

  // 图表时间范围
  // 日显示24小时，周显示7天，月显示30天
  const phTimeRange = ref('day')
  const codTimeRange = ref('day')

  // 监听pH时间范围变化
  watch(phTimeRange, () => {
    console.log('pH时间范围变化:', phTimeRange.value)
    if (selectedProjectId.value) {
      fetchPhTrendData()
    }
  })

  // 监听COD时间范围变化
  watch(codTimeRange, () => {
    console.log('COD时间范围变化:', codTimeRange.value)
    if (selectedProjectId.value) {
      fetchCodTrendData()
    }
  })

  // 日期范围选择 - 初始化时不设置默认日期
  const dateRange = ref(null) // 初始化为null，不显示默认日期

  // 监听日期范围变化
  watch(
    dateRange,
    (newVal, oldVal) => {
      // 当日期范围发生变化时，启用查询按钮
      if (newVal !== oldVal) {
        historyQueryButtonDisabled.value = false
        console.log('日期范围变化，启用查询按钮:', newVal)
      }
    },
    { deep: true }
  )

  // 日期选择器限制函数
  const disabledDate = (time) => {
    const today = new Date()
    const currentMonth = today.getMonth()
    const currentYear = today.getFullYear()
    const selectedDate = new Date(time)

    // 不能选择未来的日期
    if (selectedDate > today) {
      return true
    }

    // 如果已经选择了开始日期，限制结束日期必须在同一个月内
    if (dateRange.value && dateRange.value[0] && !dateRange.value[1]) {
      const startDate = new Date(dateRange.value[0])
      const startMonth = startDate.getMonth()
      const startYear = startDate.getFullYear()

      // 结束日期必须在同一个月内
      if (selectedDate.getMonth() !== startMonth || selectedDate.getFullYear() !== startYear) {
        return true
      }

      // 结束日期不能早于开始日期
      if (selectedDate < startDate) {
        return true
      }

      // 限制最多选择当月的天数
      const daysInMonth = new Date(startYear, startMonth + 1, 0).getDate()
      const daysDiff = Math.ceil((selectedDate - startDate) / (1000 * 60 * 60 * 24)) + 1
      if (daysDiff > daysInMonth) {
        return true
      }
    }

    return false
  }

  // 分页
  const currentPage = ref(1)
  const pageSize = ref(10)
  const totalItems = ref(0)

  // 历史数据
  const historyData = ref([])
  const historyLoading = ref(false)

  // 获取历史数据
  const fetchHistoryData = async (useAutoParams = false) => {
    try {
      historyLoading.value = true

      // 基础参数配置
      const params = {
        projectId: useAutoParams ? 17 : selectedProjectId.value, // 自动查询时使用固定项目ID
        equipmentId: useAutoParams ? 1 : selectedEquipmentId.value || undefined, // 自动查询时使用固定设备ID
        pageSize: pageSize.value,
        pageNum: currentPage.value
      }

      // 如果是自动查询或没有选择日期范围，使用当天日期
      if (useAutoParams || !dateRange.value || dateRange.value.length !== 2) {
        const todayDate = getTodayDateString()
        params.beginTime = todayDate
        params.endTime = todayDate
      } else {
        // 使用用户选择的日期范围
        params.beginTime = dateRange.value[0]
        params.endTime = dateRange.value[1]
      }
      // 如果使用默认时间范围，不发送时间参数，获取全部历史数据

      const response = await getWaterQualityHistory(params)

      if (response.code === 200) {
        // 转换数据格式以匹配表格显示
        historyData.value = (response.rows || []).map((item) => ({
          date: item.createTime || '',
          ph: item.ph?.toString() || '0',
          temperature: item.sw?.toString() || '0', // sw是水温
          cod: item.cod?.toString() || '0',
          dissolvedOxygen: item.rjy?.toString() || '0', // rjy是溶解氧
          ammoniaNitrogen: item.adlz?.toString() || '0', // adlz是氨氮离子
          turbidity: item.zd?.toString() || '0' // zd是浊度
        }))
        totalItems.value = response.total || 0
        console.log('获取历史数据成功:', response)
      } else {
        ElMessage.error(response.msg || '获取历史数据失败')
        historyData.value = []
        totalItems.value = 0
      }
    } catch (error) {
      console.error('获取历史数据失败:', error)
      ElMessage.error('获取历史数据失败，请稍后重试')
      historyData.value = []
      totalItems.value = 0
    } finally {
      historyLoading.value = false
    }
  }

  // 查询数据
  const searchData = () => {
    // 如果查询按钮已禁用，不执行查询
    if (historyQueryButtonDisabled.value) {
      ElMessage.info('查询条件未变化，无需重复查询')
      return
    }

    // 获取当前查询参数
    const currentParams = {
      projectId: selectedProjectId.value,
      equipmentId: selectedEquipmentId.value,
      beginTime: dateRange.value?.[0] || undefined,
      endTime: dateRange.value?.[1] || undefined,
      pageSize: pageSize.value,
      pageNum: 1 // 查询时重置为第一页
    }

    // 检查查询参数是否与上次相同
    if (isSameHistoryQueryParams(currentParams, lastHistoryQueryParams.value)) {
      ElMessage.info('查询条件未变化')
      historyQueryButtonDisabled.value = true
      return
    }

    console.log('查询日期范围：', dateRange.value)

    // 保存当前查询参数
    lastHistoryQueryParams.value = { ...currentParams }

    // 禁用查询按钮
    historyQueryButtonDisabled.value = true

    currentPage.value = 1 // 重置到第一页
    fetchHistoryData() // 重新获取历史数据
  }

  // 分页大小变化
  const handleSizeChange = (size) => {
    pageSize.value = size
    currentPage.value = 1 // 重置到第一页
    fetchHistoryData() // 重新获取数据
  }

  // 页码变化
  const handleCurrentChange = (page) => {
    currentPage.value = page
    fetchHistoryData() // 重新获取数据
  }

  // 导出数据
  const exportData = async () => {
    // 检查是否选择了项目
    if (!selectedProjectId.value) {
      ElMessage.warning('请先选择项目')
      return
    }

    try {
      // 准备导出参数
      const exportParams = {
        projectId: selectedProjectId.value,
        timeType: 1 // 默认按周导出
      }

      // 如果选择了具体设备，添加设备ID参数
      if (selectedEquipmentId.value) {
        exportParams.equipmentId = selectedEquipmentId.value
      }

      // 如果有选择日期范围，添加时间参数
      if (dateRange.value && dateRange.value.length === 2) {
        const [startDate, endDate] = dateRange.value
        exportParams.beginTime = startDate
        exportParams.endTime = endDate
      }

      ElMessage.info('正在导出数据，请稍候...')

      // 调用导出API
      const blob = await exportWaterQualityData(exportParams)

      // 创建下载链接
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')

      // 设置文件名称
      const fileName = `水质监测数据_${new Date().toISOString().split('T')[0]}.xlsx`

      // 下载文件
      link.href = url
      link.setAttribute('download', fileName)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // 清理URL对象
      URL.revokeObjectURL(url)

      ElMessage.success('数据导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('导出失败，请稍后重试')
    }
  }

  // 组件挂载时初始化
  onMounted(() => {
    console.log('水质监测页面已挂载，触发初始化自动查询')
    // 页面加载时立即触发自动查询
    autoSearch()
  })
</script>

<style lang="scss" scoped>
  .air-quality-container {
    padding: 20px;

    .page-header {
      margin-bottom: 20px;

      h1 {
        margin: 0;
        font-size: 24px;
        color: var(--art-text-gray-900); // 修改标题颜色为主题变量
      }
    }

    .content-section {
      margin-bottom: 20px;

      .chart-row {
        margin-top: 20px;
        margin-bottom: 20px;
      }

      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .filter-controls {
        display: flex;
        gap: 10px;
        align-items: center;
      }

      .data-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;

        .data-item {
          flex: 1;
          min-width: 140px;
          padding: 15px;
          text-align: center;
          background-color: var(--art-gray-100); // 修改背景色为主题变量
          border-radius: 8px;

          .item-value {
            margin-bottom: 8px;
            font-size: 24px;
            font-weight: bold;

            &.good {
              color: rgb(var(--art-success)); // 修改颜色为主题变量
            }

            &.normal {
              color: rgb(var(--art-warning)); // 修改颜色为主题变量
            }

            &.bad {
              color: rgb(var(--art-danger)); // 修改颜色为主题变量
            }
          }

          .item-name {
            font-size: 14px;
            color: var(--art-text-gray-600); // 修改文字颜色为主题变量
          }
        }
      }

      .chart-container {
        height: 300px;
      }

      .pagination-container {
        display: flex;
        justify-content: center;
        margin-top: 20px;
      }

      .chart-card,
      .history-card,
      .overview-card {
        margin-bottom: 0;
      }
    }
  }
</style>
