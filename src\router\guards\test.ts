/**
 * 路由导航守卫测试工具
 * 用于测试和验证路由导航守卫的各种功能
 */

import { NavigationErrorHandler, NavigationErrorType, RouteHealthChecker } from './navigationGuard'
import { routeRetry } from './retryMechanism'

/**
 * 路由守卫测试套件
 */
export class RouteGuardTestSuite {
  private errorHandler = NavigationErrorHandler.getInstance()

  /**
   * 测试错误处理功能
   */
  async testErrorHandling() {
    console.log('🧪 开始测试错误处理功能...')

    const testCases = [
      {
        name: '认证失败',
        errorType: NavigationErrorType.AUTH_FAILED,
        error: new Error('用户未登录')
      },
      {
        name: 'Token过期',
        errorType: NavigationErrorType.TOKEN_EXPIRED,
        error: new Error('Token已过期')
      },
      {
        name: '权限不足',
        errorType: NavigationErrorType.PERMISSION_DENIED,
        error: new Error('权限不足')
      },
      {
        name: '路由不存在',
        errorType: NavigationErrorType.ROUTE_NOT_FOUND,
        error: new Error('路由未找到')
      },
      {
        name: '组件加载失败',
        errorType: NavigationErrorType.COMPONENT_LOAD_FAILED,
        error: new Error('组件加载失败')
      },
      {
        name: '网络错误',
        errorType: NavigationErrorType.NETWORK_ERROR,
        error: new Error('网络连接失败')
      },
      {
        name: '服务器错误',
        errorType: NavigationErrorType.SERVER_ERROR,
        error: new Error('服务器内部错误')
      }
    ]

    for (const testCase of testCases) {
      try {
        console.log(`  测试: ${testCase.name}`)
        const result = await this.errorHandler.handleError(testCase.error, testCase.errorType)
        console.log(`  ✅ ${testCase.name} - 处理结果:`, result)
      } catch (error) {
        console.error(`  ❌ ${testCase.name} - 测试失败:`, error)
      }
    }

    console.log('✅ 错误处理功能测试完成\n')
  }

  /**
   * 测试重试机制
   */
  async testRetryMechanism() {
    console.log('🧪 开始测试重试机制...')

    // 测试成功的操作
    try {
      console.log('  测试: 成功操作（无需重试）')
      await routeRetry.retryGetMenuData(
        async () => {
          console.log('    模拟成功获取菜单数据')
          return Promise.resolve()
        },
        {
          maxRetries: 3,
          retryDelay: 100,
          showProgress: false
        }
      )
      console.log('  ✅ 成功操作测试通过')
    } catch (error) {
      console.error('  ❌ 成功操作测试失败:', error)
    }

    // 测试失败后重试的操作
    try {
      console.log('  测试: 失败操作（需要重试）')
      let attemptCount = 0
      await routeRetry.retryGetMenuData(
        async () => {
          attemptCount++
          console.log(`    第${attemptCount}次尝试`)
          if (attemptCount < 3) {
            throw new Error('模拟失败')
          }
          console.log('    第3次尝试成功')
          return Promise.resolve()
        },
        {
          maxRetries: 3,
          retryDelay: 100,
          showProgress: false
        }
      )
      console.log('  ✅ 重试机制测试通过')
    } catch (error) {
      console.error('  ❌ 重试机制测试失败:', error)
    }

    // 测试最终失败的操作
    try {
      console.log('  测试: 最终失败操作（重试后仍失败）')
      await routeRetry.retryGetMenuData(
        async () => {
          throw new Error('模拟持续失败')
        },
        {
          maxRetries: 2,
          retryDelay: 100,
          showProgress: false
        }
      )
      console.log('  ❌ 应该失败但却成功了')
    } catch (error) {
      console.log('  ✅ 最终失败测试通过 - 正确抛出错误')
    }

    console.log('✅ 重试机制测试完成\n')
  }

  /**
   * 测试路由健康检查
   */
  async testRouteHealthCheck() {
    console.log('🧪 开始测试路由健康检查...')

    const testRoutes = [
      {
        name: '正常路由',
        route: {
          name: 'TestRoute',
          path: '/test',
          matched: [{ path: '/test', components: { default: () => Promise.resolve({}) } }],
          meta: {}
        }
      },
      {
        name: '无名称路由',
        route: {
          name: '',
          path: '/test-no-name',
          matched: [{ path: '/test-no-name', components: { default: () => Promise.resolve({}) } }],
          meta: {}
        }
      },
      {
        name: '无匹配路由',
        route: {
          name: 'NoMatchRoute',
          path: '/no-match',
          matched: [],
          meta: {}
        }
      },
      {
        name: '需要认证的路由（未登录）',
        route: {
          name: 'AuthRoute',
          path: '/auth-required',
          matched: [{ path: '/auth-required', components: { default: () => Promise.resolve({}) } }],
          meta: { requiresAuth: true }
        }
      }
    ]

    for (const testRoute of testRoutes) {
      try {
        console.log(`  测试: ${testRoute.name}`)
        const healthResult = await RouteHealthChecker.checkRouteHealth(testRoute.route as any)
        console.log(
          `  结果: 健康=${healthResult.isHealthy}, 问题=${healthResult.issues.length}, 警告=${healthResult.warnings.length}`
        )

        if (healthResult.issues.length > 0) {
          console.log(`    问题: ${healthResult.issues.join(', ')}`)
        }
        if (healthResult.warnings.length > 0) {
          console.log(`    警告: ${healthResult.warnings.join(', ')}`)
        }

        console.log(`  ✅ ${testRoute.name} - 检查完成`)
      } catch (error) {
        console.error(`  ❌ ${testRoute.name} - 检查失败:`, error)
      }
    }

    console.log('✅ 路由健康检查测试完成\n')
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始运行路由守卫测试套件...\n')

    try {
      await this.testErrorHandling()
      await this.testRetryMechanism()
      await this.testRouteHealthCheck()

      console.log('🎉 所有测试完成！')
    } catch (error) {
      console.error('❌ 测试套件运行失败:', error)
    }
  }

  /**
   * 重置错误计数（用于测试）
   */
  resetErrorCount() {
    this.errorHandler.resetErrorCount()
    console.log('🔄 错误计数已重置')
  }

  /**
   * 获取当前错误计数
   */
  getErrorCount() {
    const count = this.errorHandler.getErrorCount()
    console.log(`📊 当前错误计数: ${count}`)
    return count
  }
}

/**
 * 创建测试套件实例
 */
export function createRouteGuardTestSuite(): RouteGuardTestSuite {
  return new RouteGuardTestSuite()
}

/**
 * 快速测试函数（在浏览器控制台中使用）
 */
export function quickTest() {
  const testSuite = createRouteGuardTestSuite()
  testSuite.runAllTests()
}

// 在开发环境下，将测试函数挂载到全局对象上
if (process.env.NODE_ENV === 'development') {
  ;(window as any).routeGuardTest = {
    quickTest,
    createTestSuite: createRouteGuardTestSuite
  }

  console.log('🔧 路由守卫测试工具已加载，使用 window.routeGuardTest.quickTest() 运行测试')
}
