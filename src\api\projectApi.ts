import api from '@/utils/http'

// 项目性能指标接口
export interface ProjectPerformance {
  id: number
  projectId: number
  name: string
  targetValue: string
  status: string | null
  delFlag: string
  createBy: string
  createTime: string
  updateBy: string
  updateTime: string | null
  remark: string | null
}

// 项目亮点特色接口
export interface ProjectFeature {
  id: number
  projectId: number
  title: string
  featureContent: string
  pic: string
  picRemark: string
  status: string | null
  delFlag: string
  createBy: string
  createTime: string
  updateBy: string
  updateTime: string | null
  remark: string | null
}

// 项目详情接口
export interface ProjectInfo {
  id: number
  sourceId: string | null
  code: string | null // 项目编号
  name: string
  projectStatus: string
  projectFunction: string
  projectScale: string
  projectArea: string
  projectAddr: string
  orgComp: string
  designComp: string
  sgComp: string
  designRemark: string
  addr: string
  longitude: string
  latitude: string
  pic: string
  picRemark: string
  areaId?: string // 区域ID
  energyId?: number // 能耗ID，用于实时能耗接口查询
  status: string | null
  delFlag: string
  createBy: string
  createTime: string
  updateBy: string
  updateTime: string | null
  remark: string | null
  zsProjectPerformanceList: ProjectPerformance[]
  zsProjectFeatureList: ProjectFeature[]
}

// 接口返回数据结构
export interface ProjectListResponse {
  total: number
  rows: ProjectInfo[]
  code: number
  msg: string
}

/**
 * 获取工程项目列表
 * @param params 查询参数
 * @returns 项目列表
 */
export function getProjectList(params: {
  pageNum: number
  pageSize: number
  name?: string
  projectStatus?: string
}) {
  return api.get<ProjectListResponse>({
    url: '/basedata/projectinfo/list',
    params
  })
}

/**
 * 获取项目详情
 * @param id 项目ID
 * @returns 项目详情
 */
export function getProjectDetail(id: number) {
  return api.get<{
    code: number
    msg: string
    data: ProjectInfo
  }>({
    url: `/basedata/projectinfo/${id}`
  })
}

// 新增项目的请求参数接口
export interface CreateProjectRequest {
  code?: string // 项目编号
  name: string
  projectStatus?: string
  projectFunction?: string
  projectScale?: string
  projectArea?: string
  projectAddr?: string
  orgComp?: string
  designComp?: string
  sgComp?: string
  designRemark?: string
  addr?: string
  longitude?: string
  latitude?: string
  pic?: string
  picRemark?: string
  areaId?: string // 区域ID
  zsProjectPerformanceList?: Array<{
    name: string
    targetValue: string
  }>
  zsProjectFeatureList?: Array<{
    title: string
    featureContent: string
    pic?: string
    picRemark?: string
  }>
}

// 修改项目的请求参数接口
export interface UpdateProjectRequest extends CreateProjectRequest {
  id: number
}

/**
 * 新增项目
 * @param data 项目数据
 * @returns 新增结果
 */
export function createProject(data: CreateProjectRequest) {
  return api.post<{
    code: number
    msg: string
  }>({
    url: '/basedata/projectinfo/',
    data
  })
}

/**
 * 修改项目
 * @param data 项目数据
 * @returns 修改结果
 */
export function updateProject(data: UpdateProjectRequest) {
  return api.put<{
    code: number
    msg: string
  }>({
    url: '/basedata/projectinfo/',
    data
  })
}

/**
 * 删除项目
 * @param id 项目ID
 * @returns 删除结果
 */
export function deleteProject(id: number) {
  return api.del<{
    code: number
    msg: string
  }>({
    url: `/basedata/projectinfo/${id}`
  })
}

// 项目选择器数据接口
export interface ProjectOption {
  id: number
  name: string
}

// 项目选择器接口返回数据结构
export interface ProjectSelectResponse {
  msg: string
  code: number
  data: ProjectOption[]
}

// 项目选择器数据接口
export interface ProjectOption {
  id: number
  name: string
}

// 项目选择器接口返回数据结构
export interface ProjectSelectResponse {
  msg: string
  code: number
  data: ProjectOption[]
}

/**
 * 获取所有项目信息（用于下拉选择）
 * @returns 项目选择列表
 */
export function getProjectSelectList() {
  return api.get<ProjectSelectResponse>({
    url: '/search/getProjectList'
  })
}
