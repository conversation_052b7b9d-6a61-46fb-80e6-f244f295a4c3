import { ref, computed } from 'vue'
import { useUserStore } from '@/store/modules/user'
import { getUserInfo } from '@/api/usersApi'
import type { UserInfoResponse, UserInfo } from '@/types/store'

export function useUser() {
  const userStore = useUserStore()
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性 - 从 Pinia store 中读取用户信息
  const userInfo = computed(() => userStore.getUserInfo)

  const displayName = computed(() => {
    return userInfo.value.nickName || userInfo.value.userName || '未设置'
  })

  const displayEmail = computed(() => {
    return userInfo.value.email || '未设置邮箱'
  })

  const displayDepartment = computed(() => {
    return userInfo.value.dept?.deptName || '未分配部门'
  })

  const displayRole = computed(() => {
    return userInfo.value.roles?.[0]?.roleName || '普通用户'
  })

  const avatarUrl = computed(() => {
    return '@/assets/img/login/logo.png'
  })

  const isAdmin = computed(() => {
    return userInfo.value.admin || false
  })

  // 刷新用户信息（从API重新获取并更新到Pinia）
  const fetchUserInfo = async (): Promise<UserInfo | null> => {
    loading.value = true
    error.value = null

    try {
      const response = (await getUserInfo()) as UserInfoResponse

      if (response.code === 200 && response.user) {
        // 更新store中的用户信息
        userStore.setUserInfo(response.user)
        console.log('用户信息已刷新:', response.user)
        return response.user
      } else {
        error.value = response.msg || '获取用户信息失败'
        return null
      }
    } catch (err) {
      error.value = '获取用户信息失败'
      console.error('获取用户信息失败:', err)
      return null
    } finally {
      loading.value = false
    }
  }

  // 检查是否有用户信息，如果没有则尝试获取
  const ensureUserInfo = async (): Promise<UserInfo | null> => {
    // 如果Pinia中已有用户信息，直接返回
    if (userInfo.value.userId) {
      return userInfo.value as UserInfo
    }

    // 如果没有用户信息，尝试获取
    return await fetchUserInfo()
  }

  // 格式化性别显示
  const formatGender = (sex: string | undefined): string => {
    if (!sex) return '未设置'
    return sex === '1' ? '男' : sex === '2' ? '女' : '未设置'
  }

  // 格式化日期显示
  const formatDate = (dateString: string | undefined): string => {
    if (!dateString) return '未知时间'
    try {
      return new Date(dateString).toLocaleString('zh-CN')
    } catch {
      return '未知时间'
    }
  }

  return {
    // 响应式数据
    loading,
    error,
    userInfo,

    // 计算属性
    displayName,
    displayEmail,
    displayDepartment,
    displayRole,
    avatarUrl,
    isAdmin,

    // 方法
    fetchUserInfo, // 强制刷新用户信息
    ensureUserInfo, // 确保有用户信息（优先使用缓存）
    formatGender,
    formatDate
  }
}
