// 定义月度碳排放环比图表配置
export function getMonthComparisonOption(
  currentMonth,
  lastMonth,
  currentData,
  lastData,
  xAxisData
) {
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function (params) {
        const day = params[0].axisValue
        const current = params[0].data.toFixed(2)
        const last = params[1].data.toFixed(2)
        const diff = (params[0].data - params[1].data).toFixed(2)
        const rate = (((params[0].data - params[1].data) / params[1].data) * 100).toFixed(2)

        const sign = diff >= 0 ? '+' : ''
        const rateSymbol = rate >= 0 ? '↑' : '↓'

        return `${day}<br/>
                ${currentMonth}: ${current} kgCO₂<br/>
                ${lastMonth}: ${last} kgCO₂<br/>
                环比变化: ${sign}${diff} kgCO₂<br/>
                环比增长率: ${sign}${rate}% ${rateSymbol}`
      }
    },
    legend: {
      data: [currentMonth, lastMonth],
      top: '40px'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '80px',
      containLabel: true
    },
    toolbox: {
      feature: {
        saveAsImage: {
          title: '保存为图片'
        },
        dataZoom: {
          title: {
            zoom: '区域缩放',
            back: '区域还原'
          }
        },
        magicType: {
          type: ['line', 'bar'],
          title: {
            line: '切换为折线图',
            bar: '切换为柱状图'
          }
        },
        restore: {
          title: '还原'
        }
      }
    },
    dataZoom: [
      {
        type: 'slider',
        show: true,
        start: 0,
        end: 100
      },
      {
        type: 'inside',
        start: 0,
        end: 100
      }
    ],
    xAxis: [
      {
        type: 'category',
        data: xAxisData,
        axisTick: {
          alignWithLabel: true
        },
        axisLabel: {
          rotate: xAxisData.length > 12 ? 45 : 0
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '碳排放量(kgCO₂)',
        splitLine: {
          lineStyle: {
            type: 'dashed'
          }
        }
      }
    ],
    series: [
      {
        name: currentMonth,
        type: 'bar',
        barWidth: '40%',
        data: currentData,
        itemStyle: {
          color: '#5470c6'
        },
        markPoint: {
          data: [
            { type: 'max', name: '最大值' },
            { type: 'min', name: '最小值' }
          ]
        }
      },
      {
        name: lastMonth,
        type: 'bar',
        barWidth: '40%',
        data: lastData,
        itemStyle: {
          color: '#fac858'
        },
        markPoint: {
          data: [
            { type: 'max', name: '最大值' },
            { type: 'min', name: '最小值' }
          ]
        }
      }
    ]
  }
}

// 生成模拟数据
export const generateMockData = (count, type = 'normal') => {
  let baseValue = Math.random() * 200 + 100
  let data = []

  // 根据不同类型生成不同特征的数据
  switch (type) {
    case 'year':
      // 年度数据：2024-2028，五个年份数据
      return [320, 332, 301, 334, 390] // 五个年份的固定示例数据
    case 'month':
      // 月度数据：1-12月
      return [110, 132, 101, 134, 90, 230, 210, 120, 132, 101, 134, 90]
    case 'day':
      // 日数据：30天
      for (let i = 0; i < Math.min(count, 30); i++) {
        let value = baseValue + Math.random() * 50 - 25
        // 确保不为负
        value = Math.max(0, value)
        data.push(parseFloat(value.toFixed(2)))
      }
      return data
    default:
      // 默认生成随机数据
      for (let i = 0; i < count; i++) {
        let value = baseValue + Math.random() * 50 - 25
        // 确保不为负
        value = Math.max(0, value)
        data.push(parseFloat(value.toFixed(2)))
      }
      return data
  }
}

// 生成日标签
export const generateDayLabels = (days = 30) => {
  return Array.from({ length: Math.min(days, 30) }, (_, i) => `${i + 1}日`)
}

// 生成月标签
export const generateMonthLabels = () => {
  return ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
}

// 生成年标签
export const generateYearLabels = () => {
  return ['2024年', '2025年', '2026年', '2027年', '2028年']
}

// 获取上一个周期
export const getPreviousPeriod = (dateStr, granularity) => {
  const date = new Date(dateStr)

  switch (granularity) {
    case 'year':
      date.setFullYear(date.getFullYear() - 1)
      break
    case 'month':
      date.setMonth(date.getMonth() - 1)
      break
    case 'day':
      date.setDate(date.getDate() - 1)
      break
  }

  return date
}

// 格式化日期标签
export const formatDateLabel = (date, granularity) => {
  if (!(date instanceof Date)) {
    date = new Date(date)
  }

  switch (granularity) {
    case 'year':
      return `${date.getFullYear()}年`
    case 'month':
      return `${date.getFullYear()}年${date.getMonth() + 1}月`
    case 'day':
      return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`
    default:
      return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`
  }
}
