/**
 * 图片处理工具函数
 * 用于处理项目图片的URL拼接和显示
 */

// 图片根地址
export const IMAGE_BASE_URL = 'http://117.72.111.29/prod-api'

/**
 * 获取完整的图片URL
 * @param relativePath 相对路径
 * @returns 完整的图片URL
 */
export function getFullImageUrl(relativePath: string | undefined | null): string {
  if (!relativePath) return ''
  if (relativePath.startsWith('http')) return relativePath
  return `${IMAGE_BASE_URL}${relativePath}`
}

/**
 * 从项目数据中获取图片路径
 * 优先使用 rawData.pic，然后是 image 字段
 * @param project 项目数据
 * @returns 图片相对路径
 */
export function getProjectImagePath(project: any): string {
  return project?.rawData?.pic || project?.image || ''
}

/**
 * 获取项目的完整图片URL
 * @param project 项目数据
 * @returns 完整的图片URL
 */
export function getProjectImageUrl(project: any): string {
  const imagePath = getProjectImagePath(project)
  return getFullImageUrl(imagePath)
}

/**
 * 获取项目图片说明
 * 优先使用 rawData.picRemark，然后是项目名称
 * @param project 项目数据
 * @returns 图片说明
 */
export function getProjectImageCaption(project: any): string {
  return project?.rawData?.picRemark || project?.name || ''
}
