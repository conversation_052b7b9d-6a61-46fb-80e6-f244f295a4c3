import { fourDotsSpinnerSvg } from '@/assets/svg/loading'
import { MenuListType } from '@/types/menu'
import { processRoute } from '@/utils/menu'
import { ElLoading } from 'element-plus'
import api from '@/utils/http'
import AppConfig from '@/config'
import staticRouteData from '@/router/本地路由.json'

// 菜单接口
export const menuService = {
  // 获取菜单列表，根据配置决定从本地JSON文件或后端API获取
  async getMenuList(): Promise<{ menuList: MenuListType[]; closeLoading: () => void }> {
    const loading = ElLoading.service({
      lock: true,
      background: 'rgba(0, 0, 0, 0)',
      svg: fourDotsSpinnerSvg,
      svgViewBox: '0 0 40 40'
    })

    try {
      let response: {
        code: number
        msg: string
        data: MenuListType[]
      }

      // 根据配置决定数据来源
      if (AppConfig.routeConfig.useStaticRoutes) {
        // 使用静态路由数据
        console.log('使用静态路由数据')
        response = staticRouteData as any
      } else {
        // 调用后端getRouters接口获取路由数据
        console.log('使用本地路由接口')
        response = await api.get<{
          code: number
          msg: string
          data: MenuListType[]
        }>({
          url: '/getRouters'
        })
      }

      // 检查响应格式
      if (!response || typeof response !== 'object') {
        throw new Error('路由数据格式错误')
      }

      if (response.code !== 200) {
        throw new Error(response.msg || '获取路由数据失败')
      }

      if (!Array.isArray(response.data)) {
        throw new Error('路由数据格式错误，期望数组格式')
      }

      // 处理后的菜单数据
      const processedMenuList: MenuListType[] = response.data.map((route) => processRoute(route))

      return {
        menuList: processedMenuList,
        closeLoading: () => loading.close()
      }
    } catch (error: any) {
      loading.close()

      // 详细的错误日志
      console.error('获取路由配置失败:', {
        error,
        status: error?.response?.status,
        statusText: error?.response?.statusText,
        data: error?.response?.data,
        source: AppConfig.routeConfig.useStaticRoutes ? '静态JSON文件' : '动态接口'
      })

      // 根据不同错误类型提供不同的错误信息
      if (AppConfig.routeConfig.useStaticRoutes) {
        // 静态路由错误处理
        throw new Error(`静态路由数据加载失败: ${error?.message || '未知错误'}`)
      } else {
        // 本地路由错误处理
        if (error?.response?.status === 401) {
          const authError = new Error('认证失败，请重新登录') as any
          authError.response = error.response
          throw authError
        } else if (error?.response?.status === 403) {
          throw new Error('权限不足，无法获取路由配置')
        } else if (error?.response?.status === 404) {
          throw new Error('路由配置接口不存在，请检查后端服务')
        } else if (error?.code === 'NETWORK_ERROR' || !error?.response) {
          throw new Error('网络连接失败，请检查网络连接')
        } else {
          throw new Error(`获取路由配置失败: ${error?.message || '未知错误'}`)
        }
      }
    }
  }
}
