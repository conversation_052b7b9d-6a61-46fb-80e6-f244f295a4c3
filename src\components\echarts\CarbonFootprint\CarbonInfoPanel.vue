<template>
  <div class="carbon-info-panel page-content">
    <div class="info-section" v-for="(section, index) in infoSections" :key="index">
      <div class="section-title">
        <div class="title-icon" :style="{ backgroundColor: section.color }">
          <el-icon><component :is="section.icon" /></el-icon>
        </div>
        <h3>{{ section.title }}</h3>
      </div>
      <div class="section-content">
        <p>{{ section.content }}</p>
        <div class="key-points" v-if="section.keyPoints && section.keyPoints.length">
          <div class="key-point" v-for="(point, pidx) in section.keyPoints" :key="pidx">
            <div class="point-bullet"></div>
            <div class="point-text">{{ point }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import {
    QuestionFilled,
    OfficeBuilding,
    DataAnalysis,
    TrendCharts
  } from '@element-plus/icons-vue'

  // 组件名称定义
  defineOptions({
    name: 'CarbonInfoPanel'
  })

  const infoSections = ref([
    {
      title: '什么是碳足迹?',
      color: '#3AA1FF',
      icon: QuestionFilled,
      content:
        '碳足迹是指个人、组织、产品、建筑物或服务在其整个生命周期中直接或间接产生的温室气体排放总量，通常以二氧化碳当量(CO₂e)表示。衡量碳足迹有助于识别排放热点，制定减排策略，评估气候影响并设定可持续发展目标。',
      keyPoints: [
        '直接排放：来自于拥有或控制的排放源',
        '间接排放：来自于使用购买的电力、热力或蒸汽',
        '其他间接排放：来自于组织活动但发生在其价值链中的排放'
      ]
    },
    {
      title: '建筑碳排放构成',
      color: '#FFB444',
      icon: OfficeBuilding,
      content:
        '建筑碳排放主要包括建造阶段的隐含碳排放和使用阶段的运行碳排放。在建筑的整个生命周期中，运行碳排放通常占总排放的60-80%，主要来源于能源消耗和水资源使用。',
      keyPoints: [
        '电力消耗：空调、照明、插座设备、动力设备等用电',
        '水资源使用：生活用水、绿化灌溉等用水',
        '其他能源：燃气、柴油等燃料使用'
      ]
    },
    {
      title: '碳足迹计算方法',
      color: '#4ECB73',
      icon: DataAnalysis,
      content:
        '碳足迹计算基于活动数据(如电力消耗量)乘以相应的排放因子。排放因子反映每单位活动产生的碳排放量，如电网排放因子表示每千瓦时电力产生的碳排放。不同地区、不同能源组合的排放因子差异很大。',
      keyPoints: [
        '活动数据收集：能耗计量、水表记录、燃料消耗统计',
        '排放因子选择：根据地区电网结构、能源类型确定',
        '碳排放计算：活动数据 × 排放因子 = 碳排放量'
      ]
    },
    {
      title: '减少建筑碳足迹的措施',
      color: '#FF7043',
      icon: TrendCharts,
      content:
        '降低建筑碳足迹需要综合采取技术和管理措施。从能源效率提升到可再生能源使用，从用水管理到用户行为改变，多管齐下才能实现显著减排。',
      keyPoints: [
        '能源效率提升：高效设备更换、围护结构优化、智能控制系统',
        '可再生能源应用：光伏发电、地源热泵、风能利用',
        '用水效率提升：节水器具、雨水收集、中水回用',
        '智能化管理：能耗监测、智能调节、预测性维护',
        '用户行为改变：节能意识培训、激励机制、反馈显示'
      ]
    }
  ])
</script>

<style scoped>
  .carbon-info-panel {
    display: flex;
    flex-direction: column;
    gap: 24px;
    width: 100%;
    padding: 20px;
    background-color: var(--art-main-bg-color);
    border: 1px solid var(--art-border-color);
    border-radius: 8px;
    box-shadow: var(--art-box-shadow-xs);
  }

  .info-section {
    padding-bottom: 20px;
    border-bottom: 1px dashed var(--art-border-color);
  }

  .info-section:last-child {
    padding-bottom: 0;
    border-bottom: none;
  }

  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }

  .title-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    margin-right: 12px;
    border-radius: 6px;
  }

  .title-icon .el-icon {
    font-size: 18px;
    color: rgb(255 255 255 / 90%);
  }

  .section-title h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--art-text-gray-800);
  }

  .section-content {
    padding-left: 44px;
  }

  .section-content p {
    margin: 0 0 16px;
    font-size: 14px;
    line-height: 1.6;
    color: var(--art-text-gray-600);
  }

  .key-points {
    margin-top: 12px;
  }

  .key-point {
    display: flex;
    margin-bottom: 8px;
  }

  .point-bullet {
    flex-shrink: 0;
    width: 6px;
    height: 6px;
    margin-top: 8px;
    margin-right: 10px;
    background-color: var(--el-color-primary);
    border-radius: 50%;
  }

  .point-text {
    font-size: 14px;
    line-height: 1.5;
    color: var(--art-text-gray-600);
  }
</style>
