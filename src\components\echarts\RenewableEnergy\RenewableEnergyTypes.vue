<template>
  <div class="chart-container" ref="chartContainer"></div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted } from 'vue'
  import * as echarts from 'echarts'

  const chartContainer = ref(null)
  let chart = null

  // 可再生能源类型数据
  const energyTypesData = {
    // 中国各类可再生能源装机容量占比（2023年）
    china: [
      { value: 609, name: '太阳能', itemStyle: { color: '#FFA500' } },
      { value: 420, name: '风能', itemStyle: { color: '#1E90FF' } },
      { value: 415, name: '水能', itemStyle: { color: '#32CD32' } },
      { value: 59, name: '其他', itemStyle: { color: '#9370DB' } }
    ],
    // 全球各类可再生能源装机容量占比（2023年）
    global: [
      { value: 1250, name: '太阳能', itemStyle: { color: '#FFA500' } },
      { value: 1010, name: '风能', itemStyle: { color: '#1E90FF' } },
      { value: 1250, name: '水能', itemStyle: { color: '#32CD32' } },
      { value: 200, name: '其他', itemStyle: { color: '#9370DB' } }
    ]
  }

  // 初始化图表
  const initChart = () => {
    if (chartContainer.value) {
      chart = echarts.init(chartContainer.value)
      updateChart('china')

      // 监听窗口大小变化，调整图表大小
      window.addEventListener('resize', () => {
        chart.resize()
      })
    }
  }

  // 更新图表数据
  const updateChart = (region) => {
    if (!chart) return

    const data = region === 'china' ? energyTypesData.china : energyTypesData.global
    const title =
      region === 'china' ? '中国可再生能源类型分布 (2023)' : '全球可再生能源类型分布 (2023)'

    const option = {
      title: {
        text: title,
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c}吉瓦 ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 10,
        top: 'center',
        data: data.map((item) => item.name)
      },
      series: [
        {
          name: '装机容量',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: true,
            formatter: '{b}: {d}%'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 14,
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: true
          },
          data: data
        }
      ]
    }

    chart.setOption(option)
  }

  // 导出更新图表的方法，供父组件使用
  const changeRegion = (region) => {
    updateChart(region)
  }

  // 组件挂载时初始化图表
  onMounted(() => {
    initChart()
  })

  // 组件卸载时销毁图表，释放资源
  onUnmounted(() => {
    if (chart) {
      chart.dispose()
      chart = null
    }
    window.removeEventListener('resize', () => {
      if (chart) {
        chart.resize()
      }
    })
  })

  // 导出给父组件使用的方法
  defineExpose({
    changeRegion
  })
</script>

<style scoped lang="scss">
  .chart-container {
    width: 100%;
    height: 400px;
  }
</style>
