<template>
  <div class="carbon-emission-data">
    <div class="data-card">
      <div class="card-header">
        <h3>碳足迹统计</h3>
      </div>
      <div class="card-content">
        <div class="data-item">
          <div class="total-emission">
            <div class="emission-value">{{ formatNumber(totalEmission) }} <span>kgCO₂</span></div>
            <div class="emission-trend" :class="emissionTrend === 'up' ? 'up' : 'down'">
              {{ emissionChange }}% {{ emissionTrend === 'up' ? '↑' : '↓' }}
            </div>
          </div>
          <div class="emission-caption">本月碳排放总量</div>
        </div>
        <div class="data-grid">
          <div v-for="(item, index) in emissionData" :key="index" class="grid-item">
            <div class="grid-item-header">
              <div class="grid-item-label">{{ item.label }}</div>
              <div class="grid-item-percent">{{ item.percent }}%</div>
            </div>
            <div class="grid-item-value">{{ formatNumber(item.value) }}</div>
            <div class="grid-item-unit">kgCO₂</div>
          </div>
        </div>
      </div>
    </div>

    <div class="data-card">
      <div class="card-header">
        <h3>减排建议</h3>
      </div>
      <div class="card-content">
        <div class="suggestion-list">
          <div v-for="(item, index) in reductionSuggestions" :key="index" class="suggestion-item">
            <div class="suggestion-icon" :style="{ backgroundColor: item.color }">
              <i class="suggestion-icon-inner"></i>
            </div>
            <div class="suggestion-content">
              <div class="suggestion-title">{{ item.title }}</div>
              <div class="suggestion-desc">{{ item.description }}</div>
              <div class="suggestion-impact"
                >预计减排: <span>{{ item.impact }}</span></div
              >
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'

  // 组件名称定义
  defineOptions({
    name: 'CarbonEmissionData'
  })

  // 模拟数据 - 实际项目中可从API获取
  const totalEmission = ref(126.8)
  const emissionChange = ref(5.2)
  const emissionTrend = ref('down')

  const emissionData = ref([
    { label: '电力消耗', value: 87.5, unit: 'kgCO₂', percent: 69 },
    { label: '水资源使用', value: 26.3, unit: 'kgCO₂', percent: 21 },
    { label: '空调系统', value: 32.4, unit: 'kgCO₂', percent: 26 },
    { label: '照明系统', value: 18.2, unit: 'kgCO₂', percent: 14 },
    { label: '办公设备', value: 12.6, unit: 'kgCO₂', percent: 10 },
    { label: '其他排放', value: 37.3, unit: 'kgCO₂', percent: 29 }
  ])

  const reductionSuggestions = ref([
    {
      title: '优化空调运行策略',
      description: '提高空调温度设定值1-2°C，节能效果明显，对舒适度影响较小',
      impact: '约减排3.5吨CO₂e/月',
      color: '#FF7043'
    },
    {
      title: '照明系统节能改造',
      description: '更换为LED节能灯具，并在非必要区域安装感应式开关',
      impact: '约减排2.1吨CO₂e/月',
      color: '#FFCA28'
    },
    {
      title: '办公设备节能管理',
      description: '设置电脑、打印机等设备自动休眠，非工作时间完全断电',
      impact: '约减排1.8吨CO₂e/月',
      color: '#FFEE58'
    },
    {
      title: '绿色能源替代',
      description: '屋顶光伏系统建设，部分替代传统电网供电',
      impact: '约减排8.5吨CO₂e/月',
      color: '#4ECB73'
    }
  ])

  // 格式化数字，保留1位小数
  const formatNumber = (num: number) => {
    return num.toFixed(1)
  }
</script>

<style scoped>
  .carbon-emission-data {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
  }

  .data-card {
    width: 100%;
    overflow: hidden;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgb(0 0 0 / 8%);
  }

  .card-header {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
  }

  .card-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }

  .card-content {
    padding: 20px;
  }

  .data-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 30px;
  }

  .total-emission {
    display: flex;
    gap: 10px;
    align-items: baseline;
  }

  .emission-value {
    font-size: 28px;
    font-weight: 700;
    color: #333;
  }

  .emission-value span {
    font-size: 16px;
    font-weight: 400;
    color: #666;
  }

  .emission-caption {
    margin-top: 5px;
    font-size: 14px;
    color: #999;
  }

  .emission-trend {
    padding: 2px 8px;
    font-size: 14px;
    font-weight: 600;
    border-radius: 12px;
  }

  .emission-trend.up {
    color: #f56c6c;
    background-color: rgb(245 108 108 / 10%);
  }

  .emission-trend.down {
    color: #67c23a;
    background-color: rgb(103 194 58 / 10%);
  }

  .data-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 20px;
  }

  .grid-item {
    display: flex;
    flex-direction: column;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 8px;
  }

  .grid-item-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
  }

  .grid-item-label {
    font-size: 14px;
    color: #666;
  }

  .grid-item-percent {
    font-size: 14px;
    font-weight: 500;
    color: #409eff;
  }

  .grid-item-value {
    margin-bottom: 4px;
    font-size: 22px;
    font-weight: 600;
    color: #333;
  }

  .grid-item-unit {
    font-size: 12px;
    color: #999;
  }

  .suggestion-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .suggestion-item {
    display: flex;
    padding: 16px;
    background-color: #f9f9f9;
    border-radius: 6px;
  }

  .suggestion-icon {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    margin-right: 16px;
    border-radius: 8px;
  }

  .suggestion-icon-inner {
    width: 18px;
    height: 18px;
    background-color: rgb(255 255 255 / 80%);
    border-radius: 4px;
  }

  .suggestion-content {
    flex-grow: 1;
  }

  .suggestion-title {
    margin-bottom: 6px;
    font-size: 15px;
    font-weight: 600;
    color: #333;
  }

  .suggestion-desc {
    margin-bottom: 8px;
    font-size: 13px;
    color: #666;
  }

  .suggestion-impact {
    font-size: 12px;
    color: #999;
  }

  .suggestion-impact span {
    font-weight: 600;
    color: #67c23a;
  }

  @media (width >= 768px) {
    .data-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }
</style>
