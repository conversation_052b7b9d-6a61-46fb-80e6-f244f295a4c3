<template>
  <div class="project-detail">
    <!-- 非编辑模式 - 详细信息展示（仅查看模式显示） -->
    <template v-if="mode === 'view'">
      <!-- 重要信息顶部突出显示 -->
      <div class="important-info-section">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-highlight">
              <div class="info-highlight-label"> <i class="el-icon-document"></i> 项目名称 </div>
              <div class="info-highlight-value">
                {{ form.name }}
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-highlight">
              <div class="info-highlight-label">
                <i class="el-icon-office-building"></i> 建筑功能
              </div>
              <div class="info-highlight-value">
                {{ form.function }}
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-highlight">
              <div class="info-highlight-label"> <i class="el-icon-coordinate"></i> 项目状态 </div>
              <div class="info-highlight-value">
                <el-tag :type="form.isConnected ? 'success' : 'warning'">
                  {{ form.isConnected ? '已接入平台' : '未接入平台' }}
                </el-tag>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 使用选项卡分类显示详细信息 -->
      <el-tabs type="border-card" class="info-tabs">
        <el-tab-pane>
          <template #label>
            <div class="tab-label"><i class="el-icon-info"></i> 项目概况</div>
          </template>
          <div class="tab-content">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="info-box">
                  <el-descriptions :column="1" border>
                    <el-descriptions-item label="项目编号" label-class-name="info-label">
                      <i class="el-icon-document info-icon"></i>
                      {{ form.code || '暂无' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="占地面积" label-class-name="info-label">
                      <i class="el-icon-full-screen info-icon"></i>
                      {{ formatAreaValue(form.scale) }}m²
                    </el-descriptions-item>
                    <el-descriptions-item label="建筑面积" label-class-name="info-label">
                      <i class="el-icon-office-building info-icon"></i>
                      {{ formatAreaValue(form.area) }}m²
                    </el-descriptions-item>
                    <el-descriptions-item label="建筑功能" label-class-name="info-label">
                      <i class="el-icon-school info-icon"></i>
                      {{ form.function }}
                    </el-descriptions-item>
                    <el-descriptions-item label="项目地址" label-class-name="info-label">
                      <i class="el-icon-location info-icon"></i>
                      {{ form.location }}
                    </el-descriptions-item>
                    <el-descriptions-item label="所属区域" label-class-name="info-label">
                      <i class="el-icon-place info-icon"></i>
                      {{ getAreaLabel(form.areaId) || '暂无' }}
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-box">
                  <el-descriptions :column="1" border>
                    <el-descriptions-item label="建设单位" label-class-name="info-label">
                      <i class="el-icon-user info-icon"></i>
                      {{ form.constructor }}
                    </el-descriptions-item>
                    <el-descriptions-item label="施工单位" label-class-name="info-label">
                      <i class="el-icon-s-cooperation info-icon"></i>
                      {{ form.contractor || '暂无' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="设计单位" label-class-name="info-label">
                      <i class="el-icon-edit info-icon"></i>
                      {{ form.designer }}
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>

        <el-tab-pane>
          <template #label>
            <div class="tab-label"> <i class="el-icon-star-on"></i> 设计理念 </div>
          </template>
          <div class="tab-content">
            <div class="content-box">
              <p>{{ form.designConcept || '暂无设计理念描述' }}</p>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane>
          <template #label>
            <div class="tab-label"><i class="el-icon-trophy"></i> 特色亮点</div>
          </template>
          <div class="tab-content">
            <div v-if="typeof form.highlights === 'string'" class="content-box">
              <p>{{ form.highlights }}</p>
            </div>
            <div v-else class="highlights-container">
              <div
                v-for="(highlight, index) in form.highlights"
                :key="index"
                class="highlight-card"
              >
                <div class="highlight-card-header">
                  <div class="highlight-title">
                    {{ highlight.title || `亮点${index + 1}` }}
                  </div>
                </div>
                <div class="highlight-content">
                  <p>{{ highlight.content }}</p>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane>
          <template #label>
            <div class="tab-label"> <i class="el-icon-data-analysis"></i> 项目绩效 </div>
          </template>
          <div class="tab-content">
            <div v-if="typeof form.performance === 'string'" class="content-box">
              <p>{{ form.performance }}</p>
            </div>
            <div v-else class="performance-container">
              <template v-if="hasValidPerformanceData">
                <div
                  v-for="(item, index) in validPerformanceData"
                  :key="index"
                  class="performance-card"
                >
                  <div class="performance-card-header">
                    <div class="performance-title">
                      {{ item.label || `指标${index + 1}` }}
                    </div>
                  </div>
                  <div class="performance-content">
                    <div class="performance-value">{{ item.value || item }}</div>
                  </div>
                </div>
              </template>
              <template v-else>
                <div class="empty-performance">
                  <p>暂无绩效指标数据</p>
                </div>
              </template>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane>
          <template #label>
            <div class="tab-label"> <i class="el-icon-location"></i> 地理位置 </div>
          </template>
          <div class="tab-content">
            <div class="map-container">
              <div id="map-container" class="amap-container"></div>
              <div class="address-info">
                <p> <i class="el-icon-location-information"></i> 地址：{{ form.location }} </p>
                <p v-if="form.coordinates">
                  <i class="el-icon-coordinate"></i> 坐标：{{ form.coordinates.lng }},
                  {{ form.coordinates.lat }}
                </p>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane>
          <template #label>
            <div class="tab-label"> <i class="el-icon-picture"></i> 项目图片 </div>
          </template>
          <div class="tab-content">
            <div class="project-image-container">
              <el-image
                :src="getFullImageUrl(form.image)"
                :preview-src-list="[getFullImageUrl(form.image)]"
                fit="contain"
                class="project-image-large"
              >
                <template #error>
                  <div class="image-error">
                    <el-icon><Picture /></el-icon>
                    <span>图片加载失败</span>
                  </div>
                </template>
              </el-image>
              <div class="image-caption">
                {{ form.imageCaption || form.name }}
              </div>

              <!-- 图片缩略图列表 -->
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>

      <!-- 操作按钮 - 隐藏编辑按钮 -->
      <!-- <div class="actions">
        <el-button type="primary" @click="startEdit" class="action-btn">
          <el-icon class="el-icon--left"><Edit /></el-icon>编辑
        </el-button>
      </div> -->
    </template>

    <!-- 编辑模式（编辑和新增模式都显示） -->
    <template v-if="mode === 'edit' || mode === 'add'">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <el-tabs type="border-card">
          <el-tab-pane label="基本信息">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="项目编号" prop="code">
                  <el-input
                    v-model="form.code"
                    placeholder="请输入项目编号"
                    :disabled="mode === 'edit'"
                  />
                  <div v-if="mode === 'edit'" class="form-tip"></div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目名称" prop="name">
                  <el-input v-model="form.name" placeholder="请输入项目名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="占地面积" prop="scale">
                  <el-input
                    v-model="form.scale"
                    placeholder="请输入建筑面积（m²）"
                    type="number"
                    step="1"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="建筑面积" prop="area">
                  <el-input
                    v-model="form.area"
                    placeholder="请输入建筑面积（m²）"
                    type="number"
                    step="1"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="建筑功能" prop="function">
                  <el-input v-model="form.function" placeholder="请输入建筑功能" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目地址" prop="location">
                  <el-input v-model="form.location" placeholder="请输入项目地址" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="所属区域" prop="areaId">
                  <el-select v-model="form.areaId" placeholder="请选择所属区域" clearable>
                    <el-option
                      v-for="area in areaOptions"
                      :key="area.key"
                      :label="area.label"
                      :value="area.key"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>

          <el-tab-pane label="建设信息">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="建设单位" prop="constructor">
                  <el-input v-model="form.constructor" placeholder="请输入建设单位" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="施工单位" prop="contractor">
                  <el-input v-model="form.contractor" placeholder="请输入施工单位" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="设计单位" prop="designer">
                  <el-input v-model="form.designer" placeholder="请输入设计单位" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="平台状态">
                  <el-switch
                    v-model="form.isConnected"
                    active-text="已接入平台"
                    inactive-text="未接入平台"
                  ></el-switch>
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>

          <el-tab-pane label="设计理念">
            <el-form-item label="设计理念描述">
              <el-input
                type="textarea"
                v-model="form.designConcept"
                :rows="6"
                placeholder="请输入项目设计理念"
              />
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="项目绩效">
            <div
              v-for="(item, index) in form.performance"
              :key="index"
              class="performance-edit-item"
            >
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item :label="'指标名称'">
                    <el-input v-model="item.label" placeholder="请输入指标名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="'指标数值'">
                    <el-input v-model="item.value" placeholder="请输入指标数值" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-button type="danger" @click="removePerformance(index)" size="small">
                删除此指标
              </el-button>
            </div>
            <div class="add-performance">
              <el-button type="primary" @click="addPerformance" size="small">
                添加绩效指标
              </el-button>
            </div>
          </el-tab-pane>

          <el-tab-pane label="特色亮点">
            <div
              v-for="(highlight, index) in form.highlights"
              :key="index"
              class="highlight-edit-item"
            >
              <el-card class="highlight-edit-card" shadow="hover">
                <template #header>
                  <div class="highlight-card-header">
                    <span>亮点 {{ index + 1 }}</span>
                    <el-button
                      type="danger"
                      size="small"
                      @click="removeHighlight(index)"
                      :disabled="form.highlights.length <= 1"
                    >
                      删除
                    </el-button>
                  </div>
                </template>

                <el-row :gutter="20">
                  <el-col :span="24">
                    <el-form-item label="亮点标题">
                      <el-input
                        v-model="highlight.title"
                        placeholder="请输入亮点标题"
                        maxlength="50"
                        show-word-limit
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="亮点内容">
                      <el-input
                        type="textarea"
                        v-model="highlight.content"
                        :rows="4"
                        placeholder="请输入亮点详细描述"
                        maxlength="500"
                        show-word-limit
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-card>
            </div>

            <div class="add-highlight">
              <el-button type="primary" @click="addHighlight" size="small">
                <el-icon class="el-icon--left"><Plus /></el-icon>
                添加特色亮点
              </el-button>
            </div>
          </el-tab-pane>

          <el-tab-pane label="地理位置">
            <el-form-item label="坐标">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-input
                    v-model="form.coordinates.lng"
                    placeholder="请输入经度 (-180 ~ 180)"
                    type="number"
                    step="0.000001"
                    @blur="validateAndFormatCoordinate('lng')"
                  >
                    <template #prepend>经度</template>
                  </el-input>
                </el-col>
                <el-col :span="12">
                  <el-input
                    v-model="form.coordinates.lat"
                    placeholder="请输入纬度 (-90 ~ 90)"
                    type="number"
                    step="0.000001"
                    @blur="validateAndFormatCoordinate('lat')"
                  >
                    <template #prepend>纬度</template>
                  </el-input>
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="goToCoordinates">去到经纬度</el-button>
            </el-form-item>
            <div class="map-edit-container">
              <div id="map-edit-container" class="amap-container"></div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="项目图片">
            <div class="image-section">
              <div class="image-preview">
                <el-image
                  :src="getFullImageUrl(form.image)"
                  :preview-src-list="[getFullImageUrl(form.image)]"
                  fit="cover"
                  :initial-index="0"
                >
                  <template #error>
                    <div class="image-error">
                      <el-icon><Picture /></el-icon>
                      <span>图片加载失败</span>
                    </div>
                  </template>
                </el-image>
              </div>
              <el-form-item label="图片说明">
                <el-input v-model="form.imageCaption" placeholder="请输入图片说明文字" />
              </el-form-item>
              <el-upload
                class="image-upload"
                :action="uploadAction"
                :headers="uploadHeaders"
                name="file"
                :show-file-list="false"
                :on-success="handleImageUpload"
                :on-error="handleImageUploadError"
                accept="image/*"
              >
                <el-button type="primary" class="upload-btn">
                  <el-icon class="el-icon--left"><Upload /></el-icon>更换图片
                </el-button>
                <div class="upload-tip"> 推荐尺寸: 1200 x 800px，支持 jpg、png 格式 </div>
              </el-upload>
            </div>
          </el-tab-pane>
        </el-tabs>

        <div class="actions">
          <el-button @click="cancelEdit" class="action-btn">
            <el-icon class="el-icon--left"><Close /></el-icon>取消
          </el-button>
          <el-button type="primary" @click="saveChanges" class="action-btn">
            <el-icon class="el-icon--left"><Check /></el-icon>保存
          </el-button>
        </div>
      </el-form>
    </template>
  </div>
</template>

<script>
  import { ref, reactive, onMounted, onBeforeUnmount, watch, computed } from 'vue'
  import { Close, Check, Picture, Upload, Edit, Plus } from '@element-plus/icons-vue'
  import { ElMessage } from 'element-plus'
  import { updateProject, createProject } from '@/api/projectApi'
  import { useUserStore } from '@/store/modules/user'
  import { getFullImageUrl } from '@/utils/imageUtils'
  import { getAreaDict } from '@/api/dictApi'

  export default {
    name: 'ProjectDetail',
    components: {
      Close,
      Check,
      Picture,
      Upload,
      Edit,
      Plus
    },
    props: {
      project: {
        type: Object,
        required: true
      },
      mode: {
        type: String,
        default: 'view', // 'view', 'edit', 'add'
        validator: (value) => ['view', 'edit', 'add'].includes(value)
      }
    },
    emits: ['update', 'refresh', 'close', 'startEdit'],
    setup(props, { emit }) {
      // 用户store
      const userStore = useUserStore()

      // 根据mode初始化编辑状态
      const isEditing = ref(props.mode === 'edit' || props.mode === 'add')
      const formRef = ref(null)

      // 表单验证规则
      const rules = reactive({
        name: [
          { required: true, message: '请输入项目名称', trigger: 'blur' },
          { min: 2, max: 100, message: '项目名称长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        scale: [
          { required: true, message: '请输入占地面积', trigger: 'blur' },
          { pattern: /^\d+$/, message: '占地面积必须为正整数（单位：m²）', trigger: 'blur' }
        ],
        area: [
          { required: true, message: '请输入建筑面积', trigger: 'blur' },
          {
            pattern: /^\d+$/,
            message: '建筑面积必须为正整数（单位：m²）',
            trigger: 'blur'
          }
        ],
        function: [
          { required: true, message: '请输入建筑功能', trigger: 'blur' },
          { min: 2, max: 100, message: '建筑功能长度在 2 到 100 个字符', trigger: 'blur' }
        ],

        constructor: [
          { required: true, message: '请输入建设单位', trigger: 'blur' },
          { min: 2, max: 100, message: '建设单位长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        contractor: [
          { required: false, message: '请输入施工单位', trigger: 'blur' },
          { min: 2, max: 100, message: '施工单位长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        designer: [
          { required: true, message: '请输入设计单位', trigger: 'blur' },
          { min: 2, max: 100, message: '设计单位长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        areaId: [{ required: true, message: '请选择所属区域', trigger: 'change' }]
      })

      // 区域字典数据
      const areaOptions = ref([])

      let map = null
      let marker = null
      let mapEdit = null
      let markerEdit = null
      let amapLoaded = ref(false)

      // 处理传入的项目数据
      const processProjectData = (project) => {
        // 处理亮点数据，可能是字符串格式或者已经是数组格式
        let highlights = project.details?.highlights || project.highlights
        if (typeof highlights === 'string') {
          // 如果是字符串，尝试将其转换为数组格式
          const highlightItems = highlights.split('\n').filter((item) => item.trim())
          highlights = highlightItems.map((item, index) => {
            return {
              title: `亮点${index + 1}`,
              content: item
            }
          })
        } else if (!Array.isArray(highlights)) {
          highlights = [
            {
              title: '',
              content: ''
            }
          ]
        }

        // 新增模式下确保至少有一个空的亮点项
        if (props.mode === 'add' && highlights.length === 0) {
          highlights = [
            {
              title: '',
              content: ''
            }
          ]
        }

        // 处理绩效数据，可能是字符串格式或者已经是数组格式
        let performance = project.details?.performance || project.performance
        if (typeof performance === 'string') {
          // 如果是字符串，尝试将其转换为数组格式
          const performanceItems = performance.split('\n').filter((item) => item.trim())
          performance = performanceItems.map((item, index) => {
            // 尝试分解为 label 和 value
            const parts = item.split('：').length > 1 ? item.split('：') : item.split(':')
            if (parts.length > 1) {
              return {
                label: parts[0].replace(/^\d+\.\s*/, ''), // 去掉前面的数字编号
                value: parts[1].trim()
              }
            }
            return {
              label: `指标${index + 1}`,
              value: item
            }
          })
        } else if (!Array.isArray(performance)) {
          performance = [{ label: '', value: '' }]
        }

        // 新增模式下确保至少有一个空的绩效项
        if (props.mode === 'add' && performance.length === 0) {
          performance = [{ label: '', value: '' }]
        }

        // 处理项目图片
        const projectImages = project.details?.images || [project.image]

        // 根据项目地址生成坐标（如果没有提供坐标）
        let projectCoordinates = project.details?.coordinates

        // 如果没有坐标，则根据不同项目设置不同的默认坐标
        if (!projectCoordinates) {
          if (project.location.includes('宜兴')) {
            projectCoordinates = { lng: 119.5029, lat: 31.1609 } // 宜兴坐标
          } else if (project.location.includes('南京')) {
            projectCoordinates = { lng: 118.796877, lat: 32.060255 } // 南京坐标
          } else if (project.location.includes('苏州')) {
            projectCoordinates = { lng: 120.585316, lat: 31.298886 } // 苏州坐标
          } else if (project.location.includes('徐州')) {
            projectCoordinates = { lng: 117.284124, lat: 34.205768 } // 徐州坐标
          } else {
            projectCoordinates = { lng: 119.827884, lat: 31.346137 } // 默认坐标
          }
        }

        return {
          // 基本信息
          code: project.rawData?.code || project.code || '', // 项目编号
          name: project.name || '',
          scale: project.rawData?.projectScale || project.scale || '',
          area: project.rawData?.projectArea || project.area || '',
          function: project.function || '',
          location: project.location || '',
          constructor: project.constructor || '',
          contractor: project.contractor || '',
          designer: project.designer || '',
          isConnected: project.isConnected || false,
          areaId: project.rawData?.areaId || project.areaId || '', // 区域ID

          // 详细信息
          designConcept: project.details?.designConcept || '',
          highlights: highlights,
          performance: performance,

          // 图片相关 - 优先使用 rawData.pic，然后是 project.image
          image: project.rawData?.pic || project.image || '',
          images: projectImages,
          imageCaption: project.rawData?.picRemark || project.name || '',

          // 地理位置
          coordinates: projectCoordinates
        }
      }

      // 创建表单数据
      const form = reactive(processProjectData(props.project))

      // 加载高德地图脚本
      const loadAmapScript = () => {
        if (window.AMap) {
          amapLoaded.value = true
          initMap()
          return
        }

        const script = document.createElement('script')
        script.type = 'text/javascript'
        script.async = true
        // 建议在实际项目中使用自己申请的key
        script.src =
          'https://webapi.amap.com/maps?v=2.0&key=add518eb345447ea017fd3c2fb2b5a45&plugin=AMap.Geocoder'
        script.onload = () => {
          amapLoaded.value = true
          initMap()
        }
        script.onerror = () => {
          console.error('高德地图脚本加载失败')
          ElMessage.error('地图加载失败，请刷新页面重试')
        }
        document.head.appendChild(script)
      }

      // 重置地图实例
      const resetMap = () => {
        if (map) {
          map.destroy()
          map = null
          marker = null
        }
        initMap()
      }

      // 初始化地图
      const initMap = () => {
        if (!amapLoaded.value || !window.AMap) return

        // 确保DOM已挂载
        setTimeout(() => {
          // 初始化展示地图
          if (document.getElementById('map-container') && !map) {
            try {
              map = new AMap.Map('map-container', {
                zoom: 15,
                center: [form.coordinates.lng, form.coordinates.lat]
              })

              marker = new AMap.Marker({
                position: [form.coordinates.lng, form.coordinates.lat],
                title: form.name
              })

              map.add(marker)

              // 添加地图控件
              map.plugin(['AMap.ToolBar', 'AMap.Scale'], () => {
                map.addControl(new AMap.ToolBar())
                map.addControl(new AMap.Scale())
              })
            } catch (error) {
              console.error('地图初始化失败:', error)
            }
          }
        }, 300)
      }

      // 初始化编辑地图
      const initEditMap = () => {
        if (!amapLoaded.value || !window.AMap) return

        if (isEditing.value && document.getElementById('map-edit-container') && !mapEdit) {
          setTimeout(() => {
            try {
              mapEdit = new AMap.Map('map-edit-container', {
                zoom: 15,
                center: [form.coordinates.lng, form.coordinates.lat]
              })

              markerEdit = new AMap.Marker({
                position: [form.coordinates.lng, form.coordinates.lat],
                title: form.name,
                draggable: true
              })

              mapEdit.add(markerEdit)

              // 标记拖动结束后更新坐标
              markerEdit.on('dragend', () => {
                const position = markerEdit.getPosition()
                form.coordinates.lng = position.lng
                form.coordinates.lat = position.lat
              })

              // 添加地图控件
              mapEdit.plugin(['AMap.ToolBar', 'AMap.Scale'], () => {
                mapEdit.addControl(new AMap.ToolBar())
                mapEdit.addControl(new AMap.Scale())
              })
            } catch (error) {
              console.error('编辑地图初始化失败:', error)
            }
          }, 300)
        }
      }

      // 去到经纬度
      const goToCoordinates = () => {
        // 验证经纬度是否有效
        const lng = parseFloat(form.coordinates.lng)
        const lat = parseFloat(form.coordinates.lat)

        if (isNaN(lng) || isNaN(lat)) {
          ElMessage.warning('请先输入有效的经纬度坐标')
          return
        }

        if (lng < -180 || lng > 180) {
          ElMessage.warning('经度范围应在 -180 到 180 之间')
          return
        }

        if (lat < -90 || lat > 90) {
          ElMessage.warning('纬度范围应在 -90 到 90 之间')
          return
        }

        if (!amapLoaded.value || !window.AMap) {
          ElMessage.error('地图尚未加载完成，请稍后再试')
          return
        }

        console.log('跳转到坐标:', lng, lat)

        try {
          // 更新地图中心和标记位置
          if (mapEdit) {
            mapEdit.setCenter([lng, lat])
            markerEdit.setPosition([lng, lat])
            ElMessage.success(`已跳转到坐标：${lng}, ${lat}`)
          } else {
            ElMessage.warning('地图未初始化，请稍后再试')
          }
        } catch (error) {
          console.error('跳转到坐标异常:', error)
          ElMessage.error('跳转到坐标功能异常')
        }
      }

      // 开始编辑
      const startEdit = () => {
        // 通知父组件切换到编辑模式
        emit('startEdit')
      }

      // 取消编辑
      const cancelEdit = () => {
        // 直接关闭对话框
        emit('close')
      }

      // 保存更改
      const saveChanges = async () => {
        // 表单验证
        if (!formRef.value) return

        try {
          await formRef.value.validate()
        } catch (error) {
          ElMessage.error('请检查表单填写是否正确')
          return
        }

        // 验证坐标格式
        if (!validateCoordinates()) {
          return
        }

        try {
          // 构建API请求数据
          const submitData = {
            code: form.code, // 项目编号
            name: form.name,
            projectStatus: form.isConnected ? '1' : '0',
            projectFunction: form.function,
            projectScale: form.scale,
            projectArea: form.area, // 使用表单中的建筑面积
            projectAddr: form.location,
            orgComp: form.constructor,
            designComp: form.designer,
            sgComp: form.contractor,
            designRemark: form.designConcept,
            addr: form.location,
            longitude: form.coordinates.lng.toString(),
            latitude: form.coordinates.lat.toString(),
            pic: form.image,
            picRemark: form.imageCaption,
            areaId: form.areaId, // 区域ID
            zsProjectPerformanceList: form.performance.map((p) => ({
              name: p.label,
              targetValue: p.value
            })),
            zsProjectFeatureList: form.highlights.map((h) => ({
              title: h.title,
              featureContent: h.content
            }))
          }

          let response
          const isNewProject = !props.project.id || props.mode === 'add'

          if (isNewProject) {
            // 新增项目
            response = await createProject(submitData)
          } else {
            // 更新项目
            submitData.id = props.project.id
            response = await updateProject(submitData)
          }

          if (response.code === 200) {
            ElMessage.success(isNewProject ? '项目新增成功' : '项目更新成功')

            // 保存成功后关闭对话框
            emit('close')
            // 通知父组件刷新数据
            emit('refresh')
          } else {
            ElMessage.error(response.msg || (isNewProject ? '项目新增失败' : '项目更新失败'))
          }
        } catch (error) {
          console.error('保存失败:', error)
          ElMessage.error('保存失败，请稍后重试')
        }
      }

      // 监听项目变化，更新表单数据和地图
      watch(
        () => props.project,
        (newProject) => {
          Object.assign(form, processProjectData(newProject))
          resetMap()
        },
        { deep: true }
      )

      // 格式化面积值显示
      const formatAreaValue = (value) => {
        if (!value || value === '暂无' || value === '') return '暂无'

        // 如果是字符串，尝试提取数字
        if (typeof value === 'string') {
          // 移除所有非数字和小数点的字符
          const numStr = value.replace(/[^\d.]/g, '')
          if (numStr === '') return '暂无'
          const numValue = parseFloat(numStr)
          return isNaN(numValue) ? '暂无' : Math.round(numValue).toString()
        }

        // 如果是数字，直接处理
        const numValue = parseFloat(value)
        return isNaN(numValue) ? '暂无' : Math.round(numValue).toString()
      }

      // 计算有效的绩效数据
      const validPerformanceData = computed(() => {
        if (!form.performance || !Array.isArray(form.performance)) return []
        return form.performance.filter((item) => {
          // 过滤掉空的指标项
          return item && (item.label?.trim() || item.value?.trim())
        })
      })

      // 检查是否有有效的绩效数据
      const hasValidPerformanceData = computed(() => {
        return validPerformanceData.value.length > 0
      })

      // 验证坐标格式
      const validateCoordinates = () => {
        const lng = parseFloat(form.coordinates.lng)
        const lat = parseFloat(form.coordinates.lat)

        if (isNaN(lng) || isNaN(lat)) {
          ElMessage.error('坐标格式不正确，请输入有效的数字')
          return false
        }

        if (lng < -180 || lng > 180) {
          ElMessage.error('经度范围应在 -180 到 180 之间')
          return false
        }

        if (lat < -90 || lat > 90) {
          ElMessage.error('纬度范围应在 -90 到 90 之间')
          return false
        }

        return true
      }

      // 格式化坐标显示
      const formatCoordinate = (value, type = 'lng') => {
        const num = parseFloat(value)
        if (isNaN(num)) return value
        return num.toFixed(6)
      }

      // 验证并格式化坐标输入
      const validateAndFormatCoordinate = (type) => {
        const value = type === 'lng' ? form.coordinates.lng : form.coordinates.lat
        const num = parseFloat(value)

        if (isNaN(num)) {
          ElMessage.warning(`请输入有效的${type === 'lng' ? '经度' : '纬度'}数值`)
          return
        }

        if (type === 'lng') {
          if (num < -180 || num > 180) {
            ElMessage.warning('经度范围应在 -180 到 180 之间')
            form.coordinates.lng = Math.max(-180, Math.min(180, num)).toFixed(6)
          } else {
            form.coordinates.lng = num.toFixed(6)
          }
        } else {
          if (num < -90 || num > 90) {
            ElMessage.warning('纬度范围应在 -90 到 90 之间')
            form.coordinates.lat = Math.max(-90, Math.min(90, num)).toFixed(6)
          } else {
            form.coordinates.lat = num.toFixed(6)
          }
        }

        // 如果地图已初始化，更新地图中心和标记位置
        if (mapEdit && markerEdit) {
          const newCenter = [parseFloat(form.coordinates.lng), parseFloat(form.coordinates.lat)]
          mapEdit.setCenter(newCenter)
          markerEdit.setPosition(newCenter)
        }
      }

      // 获取区域字典数据
      const fetchAreaOptions = async () => {
        try {
          const response = await getAreaDict()
          if (response.code === 200) {
            areaOptions.value = response.data || []
          } else {
            console.error('获取区域字典数据失败:', response.msg)
          }
        } catch (error) {
          console.error('获取区域字典数据失败:', error)
        }
      }

      // 根据区域ID获取区域标签
      const getAreaLabel = (areaId) => {
        if (!areaId || !areaOptions.value.length) return ''
        const area = areaOptions.value.find((item) => item.key === areaId)
        return area ? area.label : ''
      }

      // 组件挂载后加载地图脚本和区域数据
      onMounted(() => {
        loadAmapScript()
        fetchAreaOptions()
        // 延迟初始化编辑地图，等待DOM挂载
        setTimeout(() => {
          initEditMap()
        }, 500)
      })

      // 组件销毁前清理地图
      onBeforeUnmount(() => {
        if (map) {
          map.destroy()
          map = null
          marker = null
        }

        if (mapEdit) {
          mapEdit.destroy()
          mapEdit = null
          markerEdit = null
        }
      })

      // 添加绩效指标
      const addPerformance = () => {
        form.performance.push({ label: '', value: '' })
      }

      // 删除绩效指标
      const removePerformance = (index) => {
        form.performance.splice(index, 1)
        // 确保至少有一个绩效指标
        if (form.performance.length === 0) {
          form.performance.push({ label: '', value: '' })
        }
      }

      // 添加特色亮点
      const addHighlight = () => {
        form.highlights.push({
          title: '',
          content: ''
        })
      }

      // 删除特色亮点
      const removeHighlight = (index) => {
        form.highlights.splice(index, 1)
        // 确保至少有一个特色亮点
        if (form.highlights.length === 0) {
          form.highlights.push({
            title: '',
            content: ''
          })
        }
      }

      // 上传配置
      const uploadAction = computed(() => `${import.meta.env.VITE_API_URL}/common/upload`)
      const uploadHeaders = computed(() => ({
        Authorization: `Bearer ${userStore.accessToken}`
      }))

      // 处理图片上传
      const handleImageUpload = (response) => {
        if (response.code === 200 && response.fileName) {
          // 存储相对路径到表单数据
          form.image = response.fileName
          ElMessage.success('图片上传成功')
        } else {
          ElMessage.error('图片上传失败')
        }
      }

      // 处理图片上传错误
      const handleImageUploadError = (error) => {
        console.error('图片上传失败:', error)
        ElMessage.error('图片上传失败，请稍后重试')
      }

      // 预览图片
      const previewImage = (index) => {
        // 使用Element Plus的图片预览API
        const imgInstance = document.querySelector('.project-image-large .el-image__inner')
        if (imgInstance && imgInstance.__vue__) {
          imgInstance.__vue__.clickHandler(index)
        }
      }

      return {
        form,
        formRef,
        rules,
        isEditing,
        mode: props.mode,
        areaOptions,
        getAreaLabel,
        formatAreaValue,
        validPerformanceData,
        hasValidPerformanceData,
        validateCoordinates,
        formatCoordinate,
        validateAndFormatCoordinate,
        uploadAction,
        uploadHeaders,
        getFullImageUrl,

        startEdit,
        cancelEdit,
        saveChanges,
        goToCoordinates,
        handleImageUpload,
        handleImageUploadError,
        addPerformance,
        removePerformance,
        addHighlight,
        removeHighlight,
        previewImage
      }
    }
  }
</script>

<style scoped>
  .project-detail {
    padding: 24px;
    background-color: var(--art-main-bg-color);
  }

  /* 重要信息部分样式 */
  .important-info-section {
    margin-bottom: 20px;
  }

  .info-highlight {
    height: 100%;
    padding: 15px;
    background: var(--art-main-bg-color);
    border-radius: 8px;
    box-shadow: var(--art-box-shadow-xs);
    transition: all 0.3s;
  }

  .info-highlight:hover {
    box-shadow: var(--art-box-shadow-sm);
    transform: translateY(-2px);
  }

  .info-highlight-label {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
    color: var(--art-text-gray-500);
  }

  .info-highlight-label i {
    margin-right: 5px;
    color: rgb(var(--art-primary));
  }

  .info-highlight-value {
    font-size: 16px;
    font-weight: 600;
    color: var(--art-text-gray-900);
  }

  /* 选项卡样式 */
  .info-tabs {
    margin-top: 45px;
    margin-bottom: 20px;
  }

  .tab-label {
    display: flex;
    align-items: center;
  }

  .tab-label i {
    margin-right: 5px;
  }

  .tab-content {
    padding: 20px;
  }

  /* 项目概况卡片样式 */
  .info-box {
    height: 100%;
    margin-bottom: 15px;
    overflow: hidden;
    background: var(--art-main-bg-color);
    border-radius: 8px;
    box-shadow: var(--art-box-shadow-xs);
    transition: all 0.3s;
  }

  .info-box:hover {
    box-shadow: var(--art-box-shadow-sm);
  }

  /* 设计理念样式 */
  .content-box {
    padding: 20px;
    line-height: 1.6;
    background: var(--art-main-bg-color);
    border-radius: 8px;
    box-shadow: var(--art-box-shadow-xs);
  }

  .content-box p {
    margin-bottom: 1em;
    text-align: left;
  }

  /* 特色亮点样式 - 新版本 */
  .highlights-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    width: 100%;
  }

  .highlight-card {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    background: var(--art-main-bg-color);
    border-radius: 8px;
    box-shadow: var(--art-box-shadow-xs);
    transition: all 0.3s;
  }

  .highlight-card:hover {
    box-shadow: var(--art-box-shadow-sm);
    transform: translateY(-2px);
  }

  .highlight-card-header {
    padding: 15px;
    font-size: 14px;
    text-align: left;
    background-color: var(--art-gray-200);
    border-bottom: 1px solid var(--art-border-color);
  }

  .highlight-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--art-text-gray-800);
  }

  .highlight-content {
    flex: 1;
    padding: 15px;
  }

  .highlight-content p {
    margin-bottom: 15px;
    line-height: 1.6;
    text-align: left;
  }

  .highlight-image {
    margin-top: 15px;
    text-align: left;
  }

  .highlight-image .el-image {
    max-width: 100%;
    max-height: 200px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
  }

  /* 项目绩效样式 - 更新为卡片式 */
  .performance-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 20px;
    width: 100%;
  }

  .empty-performance {
    grid-column: 1 / -1;
    padding: 40px 20px;
    font-size: 14px;
    color: var(--art-text-gray-500);
    text-align: center;
  }

  .empty-performance p {
    margin: 0;
  }

  /* 坐标输入提示样式 */
  .coordinate-tip {
    padding: 8px 12px;
    margin-top: 8px;
    background-color: var(--art-gray-100);
    border-left: 3px solid var(--art-primary-color);
    border-radius: 4px;
  }

  .performance-card {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    background: var(--art-main-bg-color);
    border-radius: 6px;
    box-shadow: var(--art-box-shadow-xs);
    transition: all 0.3s;
  }

  .performance-card:hover {
    box-shadow: var(--art-box-shadow-sm);
    transform: translateY(-2px);
  }

  .performance-card-header {
    padding: 12px;
    background-color: var(--art-gray-200);
    border-bottom: 1px solid var(--art-border-color);
  }

  .performance-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--art-text-gray-800);
    text-align: left;
  }

  .performance-content {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: center;
    padding: 16px;
  }

  .performance-value {
    font-size: 15px;
    color: var(--art-text-gray-700);
    text-align: left;
  }

  /* 项目图片样式 */
  .project-image-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    align-items: center;
  }

  .project-image-large {
    width: 100%;
    height: 400px;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
  }

  .image-caption {
    font-size: 14px;
    font-weight: 500;
    color: var(--art-text-gray-600);
    text-align: left;
  }

  /* 缩略图样式 */
  .image-thumbnails {
    width: 100%;
    margin-top: 20px;
  }

  .thumbnail-title {
    margin-bottom: 10px;
    font-size: 14px;
    color: #606266;
  }

  .thumbnail-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
  }

  .thumbnail-item {
    width: 100px;
    height: 80px;
    overflow: hidden;
    cursor: pointer;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
    transition: all 0.3s;
  }

  .thumbnail-item:hover {
    box-shadow: 0 4px 8px rgb(0 0 0 / 15%);
    transform: translateY(-3px);
  }

  .thumbnail-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .thumbnail-error {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #909399;
    background-color: #f5f7fa;
  }

  /* 表单编辑部分样式 */
  .image-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .image-preview {
    width: 100%;
    max-width: 600px;
    height: 400px;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
  }

  .image-preview .el-image {
    width: 100%;
    height: 100%;
    transition: transform 0.3s ease;
  }

  .image-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--art-text-gray-500);
    background-color: var(--art-gray-200);
  }

  .image-error .el-icon {
    margin-bottom: 12px;
    font-size: 48px;
  }

  .image-upload {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .upload-btn {
    display: flex;
    gap: 5px;
    align-items: center;
  }

  .upload-tip {
    font-size: 12px;
    line-height: 1.4;
    color: var(--art-text-gray-500);
  }

  .actions {
    display: flex;
    gap: 16px;
    justify-content: flex-end;
    padding-top: 20px;
    border-top: 1px solid var(--art-border-color);
  }

  .action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 100px;
  }

  /* 描述列表样式 */
  :deep(.info-label) {
    background-color: var(--art-gray-200) !important;
  }

  .info-icon {
    margin-right: 5px;
    color: rgb(var(--art-primary));
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: var(--art-text-gray-600);
  }

  :deep(.el-form-item.is-required .el-form-item__label::before) {
    color: rgb(var(--art-error));
  }

  :deep(.el-input__wrapper) {
    box-shadow: 0 0 0 1px var(--art-border-color) inset;
    transition: all 0.2s;
  }

  :deep(.el-input__wrapper:hover) {
    box-shadow: 0 0 0 1px var(--art-gray-400) inset;
  }

  :deep(.el-input__wrapper.is-focus) {
    box-shadow: 0 0 0 1px rgb(var(--art-primary)) inset;
  }

  /* 地图容器样式 */
  .amap-container {
    width: 100%;
    height: 400px;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
  }

  .map-container {
    width: 100%;
  }

  .address-info {
    padding: 15px;
    margin-top: 15px;
    background-color: var(--art-gray-200);
    border-radius: 8px;
  }

  .address-info p {
    display: flex;
    align-items: center;
    margin: 8px 0;
    color: var(--art-text-gray-700);
  }

  .address-info i {
    margin-right: 8px;
    color: #409eff;
  }

  .map-edit-container {
    margin-top: 20px;
  }

  .map-tip {
    margin-top: 10px;
    font-size: 12px;
    color: var(--art-text-gray-500);
  }

  /* 高亮编辑区域 */
  .highlight-edit-item,
  .performance-edit-item {
    padding: 20px;
    margin-bottom: 20px;
    background-color: var(--art-main-bg-color);
    border: 1px dashed var(--art-border-dashed-color);
    border-radius: 8px;
  }

  /* 特色亮点编辑样式 */
  .highlight-edit-item {
    padding: 0;
    background: none;
    border: none;
  }

  .highlight-edit-card {
    border: 1px solid #e4e7ed;
    border-radius: 8px;
  }

  .highlight-edit-card .highlight-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    font-weight: 600;
    color: #303133;
    background-color: transparent;
    border-bottom: none;
  }

  .add-highlight {
    padding: 20px;
    margin-top: 20px;
    text-align: center;
    background-color: #fafafa;
    border: 2px dashed #dcdfe6;
    border-radius: 8px;
    transition: all 0.3s ease;
  }

  .add-highlight:hover {
    background-color: #f0f9ff;
    border-color: #409eff;
  }

  .add-performance {
    padding: 20px;
    margin-top: 20px;
    text-align: center;
    background-color: #fafafa;
    border: 2px dashed #dcdfe6;
    border-radius: 8px;
    transition: all 0.3s ease;
  }

  .add-performance:hover {
    background-color: #f0f9ff;
    border-color: #409eff;
  }

  /* 表单提示样式 */
  .form-tip {
    margin-top: 4px;
    font-size: 12px;
    line-height: 1.2;
    color: var(--el-color-info);
  }
</style>
