<template>
  <div ref="chartContainer" style="width: 100%; height: 300px"></div>
</template>

<script setup>
  import { ref, onMounted, watch, inject } from 'vue'
  import * as echarts from 'echarts'

  // 从父组件接收时间范围参数
  const props = defineProps({
    timeRange: {
      type: String,
      default: 'day'
    }
  })

  // 引用父组件的airQualityData和trendData
  const airQualityData = inject('airQualityData')
  const trendData = inject('trendData')

  const chartContainer = ref(null)
  let chart = null

  // 获取图表数据
  const getChartData = () => {
    // 如果有真实趋势数据，使用真实数据
    if (trendData && trendData.value && trendData.value.timeList && trendData.value.dataList) {
      console.log('使用真实PM趋势数据:', trendData.value)
      const times = trendData.value.timeList
      const values = trendData.value.dataList.map((item) => item.ph2_5 || 0)
      const pm10Values = trendData.value.dataList.map((item) => item.ph10 || 0)

      return { times, values, pm10Values }
    }

    // 否则使用模拟数据
    console.log('使用模拟PM数据，timeRange:', props.timeRange)
    return generateData(props.timeRange)
  }

  // 模拟不同时间范围的数据（作为备用）
  const generateData = (range) => {
    const times = []
    const values = []
    const pm10Values = []

    // 根据选择的时间范围生成数据
    if (range === 'day') {
      for (let i = 0; i < 24; i++) {
        times.push(`${i}:00`)
        // 使用airQualityData中的PM2.5值作为基准，生成波动数据
        const pm25Base = parseInt(airQualityData.find((item) => item.name === 'PM2.5').value)
        const pm10Base = parseInt(airQualityData.find((item) => item.name === 'PM10').value)

        // 生成随机波动
        const pm25Random = Math.floor(pm25Base * (0.8 + Math.random() * 0.4))
        const pm10Random = Math.floor(pm10Base * (0.8 + Math.random() * 0.4))

        values.push(pm25Random)
        pm10Values.push(pm10Random)
      }
    } else if (range === 'week') {
      const days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
      for (let i = 0; i < 7; i++) {
        times.push(days[i])
        const pm25Base = parseInt(airQualityData.find((item) => item.name === 'PM2.5').value)
        const pm10Base = parseInt(airQualityData.find((item) => item.name === 'PM10').value)

        const pm25Random = Math.floor(pm25Base * (0.7 + Math.random() * 0.6))
        const pm10Random = Math.floor(pm10Base * (0.7 + Math.random() * 0.6))

        values.push(pm25Random)
        pm10Values.push(pm10Random)
      }
    } else if (range === 'month') {
      for (let i = 1; i <= 30; i++) {
        times.push(`${i}日`)
        const pm25Base = parseInt(airQualityData.find((item) => item.name === 'PM2.5').value)
        const pm10Base = parseInt(airQualityData.find((item) => item.name === 'PM10').value)

        const pm25Random = Math.floor(pm25Base * (0.6 + Math.random() * 0.8))
        const pm10Random = Math.floor(pm10Base * (0.6 + Math.random() * 0.8))

        values.push(pm25Random)
        pm10Values.push(pm10Random)
      }
    }

    return { times, values, pm10Values }
  }

  // 初始化图表
  const initChart = () => {
    if (chart) {
      chart.dispose()
    }

    chart = echarts.init(chartContainer.value)
    updateChart()
  }

  // 更新图表数据
  const updateChart = () => {
    const { times, values, pm10Values } = getChartData()

    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: ['PM2.5', 'PM10'],
        bottom: 0,
        left: 'center'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: times,
        axisLabel: {
          rotate: props.timeRange === 'month' ? 45 : 0
        }
      },
      yAxis: {
        type: 'value',
        name: '浓度(μg/m³)'
      },
      series: [
        {
          name: 'PM2.5',
          type: 'line',
          data: values,
          smooth: true,
          lineStyle: {
            width: 3,
            color: '#67C23A'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(103, 194, 58, 0.5)' },
              { offset: 1, color: 'rgba(103, 194, 58, 0.1)' }
            ])
          }
        },
        {
          name: 'PM10',
          type: 'line',
          data: pm10Values,
          smooth: true,
          lineStyle: {
            width: 3,
            color: '#E6A23C'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(230, 162, 60, 0.5)' },
              { offset: 1, color: 'rgba(230, 162, 60, 0.1)' }
            ])
          }
        }
      ]
    }

    chart.setOption(option)
  }

  // 监听时间范围变化
  watch(
    () => props.timeRange,
    () => {
      updateChart()
    }
  )

  // 监听趋势数据变化
  watch(
    () => trendData?.value,
    () => {
      if (chart) {
        updateChart()
      }
    },
    { deep: true }
  )

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    if (chart) {
      chart.resize()
    }
  })

  onMounted(() => {
    initChart()
  })
</script>

<style scoped></style>
