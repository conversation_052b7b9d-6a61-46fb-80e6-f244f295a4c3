<template>
  <div class="carbon-emission-comparison">
    <!-- 头部筛选区域 -->
    <div class="filter-container">
      <div class="filter-row">
        <div class="filter-item">
          <span class="filter-label">监测项目:</span>
          <MultiProjectSelector
            v-model="selectedProjects"
            placeholder="选择监测项目"
            clearable
            class="filter-select"
            @change="handleProjectChange"
            @loaded="handleProjectListLoaded"
          />
        </div>
        <div class="filter-item">
          <span class="filter-label">时间粒度：</span>
          <el-radio-group
            v-model="timeGranularity"
            class="time-granularity-buttons"
            @change="handleTimeGranularityChange"
          >
            <el-radio-button label="day">日</el-radio-button>
            <el-radio-button label="month">月</el-radio-button>
            <el-radio-button label="year">年</el-radio-button>
          </el-radio-group>
        </div>
        <div class="filter-item date-filter">
          <span class="filter-label">日期选择：</span>
          <div class="date-picker-group">
            <!-- 日期和月份使用范围选择器 -->
            <el-date-picker
              v-if="timeGranularity !== 'year'"
              v-model="dateRange"
              :type="dateRangeType"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :format="timeGranularity === 'month' ? 'YYYY-MM' : 'YYYY-MM-DD'"
              :value-format="timeGranularity === 'month' ? 'YYYY-MM' : 'YYYY-MM-DD'"
              class="date-picker"
              @change="handleDateRangeChange"
            />
            <!-- 年份使用单选选择器 -->
            <el-date-picker
              v-else
              v-model="yearValue"
              type="year"
              placeholder="选择年份"
              value-format="YYYY"
              class="date-picker"
              @change="handleYearChange"
            />
          </div>
        </div>
        <div class="filter-item">
          <span class="filter-label">数据类型：</span>
          <el-radio-group
            v-model="isUnitArea"
            class="data-type-buttons"
            @change="handleDataTypeChange"
          >
            <el-radio-button :label="true">碳排放强度</el-radio-button>
            <el-radio-button :label="false">碳排放总量</el-radio-button>
          </el-radio-group>
        </div>
        <div class="filter-actions">
          <el-button
            type="primary"
            @click="handleSearch"
            :loading="loading"
            :disabled="queryButtonDisabled"
            >查询</el-button
          >
        </div>
      </div>
    </div>

    <!-- 对比曲线图表 -->
    <el-card class="chart-card" shadow="hover">
      <div class="chart-header">
        <h3>{{ chartTitle }}</h3>
      </div>
      <div ref="chartRef" class="chart-container"></div>
    </el-card>

    <!-- 对比数据表格 -->
    <el-card class="table-card" shadow="hover">
      <div class="card-header">
        <h3>{{ tableTitleText }}</h3>
        <div class="header-actions">
          <el-button size="small" type="success" @click="exportData">
            <el-icon><Document /></el-icon>导出数据
          </el-button>
        </div>
      </div>

      <ArtTable
        :data="tableData"
        :loading="loading"
        :border="false"
        :highlight-current-row="false"
        :total="total"
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30]"
        :pagination-size="'default'"
        :pagination-align="'right'"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        class="comparison-table"
      >
        <el-table-column prop="period" label="日期"></el-table-column>
        <el-table-column
          v-if="selectedProjectsData.length > 0"
          :prop="'data_' + selectedProjectsData[0].id"
          :label="selectedProjectsData[0].name + '(' + unitText + ')'"
        >
          <template #default="scope">
            {{
              scope.row['data_' + selectedProjectsData[0].id] !== undefined
                ? scope.row['data_' + selectedProjectsData[0].id].toFixed(2)
                : '0.00'
            }}
          </template>
        </el-table-column>
      </ArtTable>
    </el-card>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
  import { useSettingStore } from '@/store/modules/setting'
  import { SystemThemeEnum } from '@/enums/appEnum'
  import * as echarts from 'echarts'
  import { ElMessage } from 'element-plus'
  import { getCarbonComparisonReport } from '@/api/carbon-emission'
  import MultiProjectSelector from '@/components/MultiProjectSelector.vue'

  import { getProjectComparisonOption } from '@/components/echarts/CarbonEmission/Comparison.js'

  // 状态管理
  const loading = ref(false)
  const selectedProjects = ref([]) // 选中的项目ID列表
  const selectedProjectsData = ref([]) // 选中的项目详细信息列表
  const timeGranularity = ref('day') // 默认日粒度
  const dateRange = ref([]) // 默认时间范围，将在初始化时设置
  const yearValue = ref(null) // 年份单选值
  const total = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(10)
  const tableData = ref([])
  const isCarbon = ref(true) // true表示碳排放，false表示能耗(标煤)
  const isUnitArea = ref(true) // true表示碳排放强度，false表示碳排放总量
  const chartRef = ref(null)
  let chart = null
  const settingStore = useSettingStore()
  const isDark = computed(() => settingStore.systemThemeType === SystemThemeEnum.DARK)

  const isInitialized = ref(false) // 标记是否已初始化

  // 查询状态管理 - 防重复查询
  const lastQueryParams = ref(null)
  const queryButtonDisabled = ref(false) // 查询按钮状态

  // 比较查询参数是否相同
  const isSameQueryParams = (params1, params2) => {
    if (!params1 || !params2) return false
    return (
      JSON.stringify(params1.projectIdList) === JSON.stringify(params2.projectIdList) &&
      params1.beginTime === params2.beginTime &&
      params1.endTime === params2.endTime &&
      params1.timeType === params2.timeType &&
      params1.dataType === params2.dataType
    )
  }

  // 计算属性：根据时间粒度计算对应的日期控件类型
  const datePickerType = computed(() => {
    switch (timeGranularity.value) {
      case 'year':
        return 'year'
      case 'month':
        return 'month'
      case 'day':
        return 'date'
      default:
        return 'month'
    }
  })

  // 计算属性：根据时间粒度计算对应的日期控件类型
  const dateRangeType = computed(() => {
    switch (timeGranularity.value) {
      case 'year':
        return 'year' // 年份使用单选，不是范围选择
      case 'month':
        return 'monthrange'
      case 'day':
        return 'daterange'
      default:
        return 'monthrange'
    }
  })

  // 计算属性：图表标题
  const chartTitle = computed(() => {
    const typeText = isCarbon.value ? '碳排放' : '能耗(标煤)'
    const unitAreaText = isUnitArea.value ? '强度' : '总量'
    let periodText = ''

    switch (timeGranularity.value) {
      case 'year':
        periodText = '年度'
        break
      case 'month':
        periodText = '月度'
        break
      case 'day':
        periodText = '日'
        break
      default:
        periodText = '日'
    }

    const projectName =
      selectedProjectsData.value.length > 0 ? selectedProjectsData.value[0].name : ''
    return `${projectName}${periodText}${typeText}${unitAreaText}趋势图`
  })

  // 计算属性：表格标题
  const tableTitleText = computed(() => {
    const typeText = isCarbon.value ? '碳排放' : '能耗'
    const unitAreaText = isUnitArea.value ? '强度' : '总量'
    const projectName =
      selectedProjectsData.value.length > 0 ? selectedProjectsData.value[0].name : ''

    return `${projectName}${typeText}${unitAreaText}详细数据`
  })

  // 计算属性：单位文本
  const unitText = computed(() => {
    if (isCarbon.value) {
      return isUnitArea.value ? 'kgCO₂/m²' : 'kgCO₂'
    } else {
      return isUnitArea.value ? 'kgce/m²' : '标煤'
    }
  })

  // 防抖处理函数
  let searchTimeout = null
  const debouncedAutoSearch = () => {
    if (searchTimeout) {
      clearTimeout(searchTimeout)
    }
    searchTimeout = setTimeout(() => {
      autoSearch()
    }, 500) // 500ms防抖延迟
  }

  // 自动查询方法
  const autoSearch = async () => {
    // 如果没有选择项目，不发请求
    if (selectedProjects.value.length === 0) {
      console.log('没有选择项目，不发请求')
      return
    }

    console.log('自动查询参数:', {
      selectedProjects: selectedProjects.value,
      timeGranularity: timeGranularity.value,
      dateRange: dateRange.value,
      yearValue: yearValue.value,
      isUnitArea: isUnitArea.value
    })
    await fetchData()
  }

  // 设置默认时间范围：上个月的30天
  const setDefaultDateRange = () => {
    const now = new Date()
    const endDate = new Date(now.getFullYear(), now.getMonth(), 0) // 上个月的最后一天
    const startDate = new Date(endDate)
    startDate.setDate(endDate.getDate() - 29) // 往前推30天（包含结束日期）

    const formatDate = (date) => {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    }

    dateRange.value = [formatDate(startDate), formatDate(endDate)]
    console.log('设置默认时间范围:', dateRange.value)
  }

  // 处理项目列表加载完成
  const handleProjectListLoaded = (projectList) => {
    console.log('项目列表加载成功:', projectList)

    // 自动选择第一个项目并初始化查询
    if (projectList.length > 0 && !isInitialized.value) {
      selectedProjects.value = [projectList[0].id]
      selectedProjectsData.value = [projectList[0]]
      console.log('自动选择第一个项目:', projectList[0])

      // 设置默认时间范围
      setDefaultDateRange()

      // 标记已初始化
      isInitialized.value = true

      // 自动执行查询
      setTimeout(() => {
        fetchData()
      }, 100) // 稍微延迟确保所有状态都已更新
    }
  }

  // 初始化
  onMounted(() => {
    initChart()
    // 如果项目列表为空，先设置默认时间范围
    if (dateRange.value.length === 0) {
      setDefaultDateRange()
    }
    window.addEventListener('resize', handleResize)
  })

  // 组件卸载时清理
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    if (chart) {
      chart.dispose()
      chart = null
    }
  })

  // 监听窗口大小变化
  const handleResize = () => {
    chart && chart.resize()
  }

  // 监听项目选择变化
  watch(
    selectedProjects,
    (newValue) => {
      if (newValue && newValue.length > 0) {
        // 重置分页
        currentPage.value = 1
        console.log('项目选择变化:', newValue)
        // 如果不是初始化阶段，项目选择变化时不自动查询，等待用户点击查询按钮
      } else {
        // 清空表格数据
        tableData.value = []
        // 更新图表显示空状态
        updateChart()
      }
    },
    { deep: true }
  )

  // 监听主题变化，更新图表
  watch(isDark, () => {
    if (chart) {
      // 销毁旧实例，防止 ECharts 缓存旧的颜色配置
      chart.dispose()
      chart = null
      // 重新初始化并更新图表
      initChart() // initChart 内部会调用 updateChart
    }
  })

  // 监听查询条件变化，启用查询按钮
  watch(
    [selectedProjects, timeGranularity, dateRange, yearValue, isUnitArea],
    (newVal, oldVal) => {
      // 当查询条件发生变化时，启用查询按钮
      if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
        console.log('查询条件变化，启用查询按钮:', {
          selectedProjects: selectedProjects.value,
          timeGranularity: timeGranularity.value,
          dateRange: dateRange.value,
          yearValue: yearValue.value,
          isUnitArea: isUnitArea.value
        })
        queryButtonDisabled.value = false
        // 清空上次查询参数，允许重新查询
        lastQueryParams.value = null
      }
    },
    { deep: true }
  )

  // 初始化图表
  const initChart = () => {
    if (chartRef.value) {
      chart = echarts.init(chartRef.value)
      updateChart()
    }
  }

  // 更新图表数据
  const updateChart = () => {
    if (!chart) return

    if (selectedProjectsData.value.length === 0 || tableData.value.length === 0) {
      // 如果没有选择项目或没有数据，显示空图表
      chart.setOption({
        title: {
          text: '暂无数据',
          left: 'center',
          top: 'center',
          textStyle: {
            color: '#999',
            fontSize: 16
          }
        },
        xAxis: {
          show: false
        },
        yAxis: {
          show: false
        },
        series: []
      })
      return
    }

    try {
      // 从表格数据中提取图表数据
      const xAxisData = tableData.value.map((row) => row.period)

      // 为每个项目生成系列数据
      const projectsData = selectedProjectsData.value.map((project) => {
        const data = tableData.value.map((row) => row[`data_${project.id}`] || 0)
        return {
          name: project.name,
          data: data
        }
      })

      console.log('图表数据:', { xAxisData, projectsData })

      // 设置图表配置
      const option = getProjectComparisonOption(
        projectsData,
        xAxisData,
        isUnitArea.value,
        isCarbon.value ? 'carbon' : 'energy'
      )

      chart.setOption(option, true)
    } catch (error) {
      console.error('图表更新错误:', error)
      // 出错时显示错误信息
      chart.setOption({
        title: {
          text: '图表数据加载失败',
          left: 'center',
          top: 'center',
          textStyle: {
            color: '#f56c6c',
            fontSize: 16
          }
        },
        xAxis: {
          show: false
        },
        yAxis: {
          show: false
        },
        series: []
      })
    }
  }

  // 切换数据类型（碳排放/能耗）
  const toggleDataType = () => {
    updateChart()
    fetchData()
    ElMessage.success(`已切换到${isCarbon.value ? '碳排放' : '能耗(标煤)'}视图`)
  }

  // 处理项目变化
  const handleProjectChange = (projectList) => {
    console.log('项目选择变化:', projectList)
    // 保存项目详细信息
    selectedProjectsData.value = projectList
    // 清空上次查询参数，允许重新查询
    lastQueryParams.value = null
    queryButtonDisabled.value = false
    // 项目变化时自动发送请求（使用防抖）
    if (projectList && projectList.length > 0) {
      debouncedAutoSearch()
    }
  }

  // 处理时间粒度变化
  const handleTimeGranularityChange = () => {
    console.log('时间粒度变化:', timeGranularity.value)
    // 清空上次查询参数，允许重新查询
    lastQueryParams.value = null
    queryButtonDisabled.value = false
    // 时间粒度变化时自动发送请求（使用防抖）
    if (selectedProjects.value.length > 0) {
      debouncedAutoSearch()
    }
  }

  // 处理数据类型变化
  const handleDataTypeChange = () => {
    console.log('数据类型变化:', isUnitArea.value ? '碳排放强度' : '碳排放总量')
    // 清空上次查询参数，允许重新查询
    lastQueryParams.value = null
    queryButtonDisabled.value = false
    // 数据类型变化时自动发送请求（使用防抖）
    if (selectedProjects.value.length > 0) {
      debouncedAutoSearch()
    }
    ElMessage.success(`已切换到${isUnitArea.value ? '碳排放强度' : '碳排放总量'}视图`)
  }

  // 切换单位面积模式（保持向后兼容）
  const toggleUnitAreaMode = () => {
    handleDataTypeChange()
  }

  // 获取数据
  const fetchData = async () => {
    if (selectedProjects.value.length === 0) {
      ElMessage.warning('请至少选择一个监测项目')
      tableData.value = [] // 清空表格数据
      return
    }

    // 验证时间选择
    if (timeGranularity.value === 'year') {
      if (!yearValue.value) {
        ElMessage.warning('请选择年份')
        return
      }
    } else {
      if (!dateRange.value || dateRange.value.length !== 2) {
        ElMessage.warning('请选择完整的日期范围')
        return
      }
    }

    loading.value = true

    try {
      // 构建API请求参数
      const timeTypeMap = {
        day: 0,
        month: 1,
        year: 2
      }

      // 构建时间参数
      let beginTime, endTime
      if (timeGranularity.value === 'year' && yearValue.value) {
        // 年份选择：只发送年份值，格式：2025
        beginTime = yearValue.value
        endTime = yearValue.value
      } else if (dateRange.value && dateRange.value.length === 2) {
        // 日期和月份选择：保持原有格式
        // 日：2025-06-23，月：2025-06
        beginTime = dateRange.value[0]
        endTime = dateRange.value[1]
      }

      const params = {
        projectIdList: selectedProjects.value,
        beginTime,
        endTime,
        timeType: timeTypeMap[timeGranularity.value],
        dataType: isUnitArea.value ? '1' : '0' // 0:碳排放总量，1:碳排放强度
      }

      console.log('发送碳排放对比API请求，参数:', params)

      // 调用API
      const response = await getCarbonComparisonReport(params)

      console.log('碳排放对比API响应:', response)

      if (response.code === 200 && response.data) {
        const { timeList, dataList } = response.data

        // 转换为表格数据格式
        const data = timeList.map((time, timeIndex) => {
          const row = {
            period: time
          }

          // 为每个项目添加数据
          selectedProjectsData.value.forEach((project) => {
            // 从dataList中找到对应时间点和项目的数据
            const timeData = dataList[timeIndex] || []
            const projectData = timeData.find((item) => item.projectName === project.name)

            if (projectData) {
              // 根据数据类型选择显示的值
              row[`data_${project.id}`] = isUnitArea.value
                ? projectData.strength
                : projectData.quantity
            } else {
              row[`data_${project.id}`] = 0
            }
          })

          return row
        })

        tableData.value = data
        total.value = data.length

        // 更新图表
        updateChart()

        // 保存当前查询参数
        lastQueryParams.value = { ...params }
        // 禁用查询按钮（因为已经查询过相同条件）
        queryButtonDisabled.value = true

        console.log('✅ 数据处理成功，表格数据:', tableData.value)
      } else {
        throw new Error(response.msg || 'API返回数据为空')
      }
    } catch (error) {
      console.error('获取碳排放对比数据失败:', error)
      ElMessage.error('获取数据失败，请稍后重试')
      tableData.value = [] // 出错时清空表格数据
    } finally {
      loading.value = false
    }
  }

  // 处理筛选查询
  const handleSearch = () => {
    if (selectedProjects.value.length === 0) {
      ElMessage.warning('请至少选择一个监测项目')
      return
    }

    // 如果查询按钮已禁用，不执行查询
    if (queryButtonDisabled.value) {
      ElMessage.info('查询条件未变化，无需重复查询')
      return
    }

    // 构建当前查询参数用于比较
    const timeTypeMap = {
      day: 0,
      month: 1,
      year: 2
    }

    let beginTime, endTime
    if (timeGranularity.value === 'year' && yearValue.value) {
      // 年份选择：只发送年份值，格式：2025
      beginTime = yearValue.value
      endTime = yearValue.value
    } else if (dateRange.value && dateRange.value.length === 2) {
      // 日期和月份选择：保持原有格式
      // 日：2025-06-23，月：2025-06
      beginTime = dateRange.value[0]
      endTime = dateRange.value[1]
    }

    const currentParams = {
      projectIdList: selectedProjects.value,
      beginTime,
      endTime,
      timeType: timeTypeMap[timeGranularity.value],
      dataType: isUnitArea.value ? '1' : '0'
    }

    // 检查查询参数是否与上次相同
    if (isSameQueryParams(currentParams, lastQueryParams.value)) {
      ElMessage.info('查询条件未变化')
      queryButtonDisabled.value = true
      return
    }

    fetchData()
  }

  // 分页处理
  const handleSizeChange = (size) => {
    pageSize.value = size
  }

  const handleCurrentChange = (page) => {
    currentPage.value = page
  }

  // 导出数据（CSV格式）
  const exportData = () => {
    if (tableData.value.length === 0) {
      ElMessage.warning('暂无数据可导出')
      return
    }

    // 生成CSV表头
    let csvContent = '日期'
    selectedProjectsData.value.forEach((project) => {
      csvContent += `,${project.name}(${unitText.value})`
    })
    csvContent += '\n'

    // 生成CSV数据
    tableData.value.forEach((row) => {
      csvContent += row.period

      // 添加每个项目的数据
      selectedProjectsData.value.forEach((project) => {
        const projectId = project.id
        csvContent += `,${row[`data_${projectId}`].toFixed(2)}`
      })

      csvContent += '\n'
    })

    // 创建并下载CSV文件
    const encodedUri = encodeURI('data:text/csv;charset=utf-8,' + csvContent)
    const link = document.createElement('a')
    link.setAttribute('href', encodedUri)

    const typeText = isCarbon.value ? '碳排放' : '能耗'
    const unitAreaText = isUnitArea.value ? '单位面积' : ''

    // 生成文件名
    let fileName
    if (timeGranularity.value === 'year' && yearValue.value) {
      fileName = `${unitAreaText}${typeText}_对比数据_${yearValue.value}.csv`
    } else if (dateRange.value && dateRange.value.length === 2) {
      fileName = `${unitAreaText}${typeText}_对比数据_${dateRange.value[0].substring(
        0,
        10
      )}_${dateRange.value[1].substring(0, 10)}.csv`
    } else {
      fileName = `${unitAreaText}${typeText}_对比数据.csv`
    }

    link.setAttribute('download', fileName)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success('数据导出成功')
  }

  // 处理日期范围变化
  const handleDateRangeChange = () => {
    if (dateRange.value && dateRange.value.length === 2) {
      console.log('日期范围变化:', dateRange.value)
      // 清空上次查询参数，允许重新查询
      lastQueryParams.value = null
      queryButtonDisabled.value = false
      // 日期变化时自动发送请求（使用防抖）
      if (selectedProjects.value.length > 0) {
        debouncedAutoSearch()
      }
    }
  }

  // 处理年份变化
  const handleYearChange = () => {
    if (yearValue.value) {
      console.log('年份变化:', yearValue.value)
      // 清空上次查询参数，允许重新查询
      lastQueryParams.value = null
      queryButtonDisabled.value = false
      // 年份变化时自动发送请求（使用防抖）
      if (selectedProjects.value.length > 0) {
        debouncedAutoSearch()
      }
    }
  }
</script>

<style lang="scss" scoped>
  .carbon-emission-comparison {
    padding: 20px;
    color: var(--el-text-color-primary);
    background-color: var(--el-bg-color);

    .page-title {
      margin-bottom: 20px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    .filter-container {
      padding: 16px;
      margin-bottom: 16px;
      border-radius: 8px;
      box-shadow: var(--el-box-shadow-light);

      .filter-row {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        align-items: center;
      }

      .filter-item {
        display: flex;
        align-items: center;
        white-space: nowrap;

        .filter-label {
          margin-right: 8px;
          font-size: 14px;
          color: var(--el-text-color-regular);
        }

        .filter-select {
          width: 200px;
        }

        .time-granularity-buttons,
        .data-type-buttons {
          margin-left: 0;
        }

        &.date-filter {
          .date-picker-group {
            .date-picker {
              width: 300px;
            }
          }
        }
      }

      .filter-actions {
        margin-left: auto;
      }
    }

    .filter-card {
      margin-bottom: 20px;

      .filter-form {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        margin-bottom: 10px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .chart-card {
      margin-bottom: 20px;

      .chart-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 15px;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: var(--el-text-color-primary);
        }
      }

      .chart-container {
        width: 100%;
        height: 400px;
      }
    }

    .table-card {
      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 15px;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: var(--el-text-color-primary);
        }
      }

      .comparison-table {
        width: 100%;
        margin: 0 auto;

        :deep(.table-pagination) {
          margin-top: 20px;
        }

        :deep(.el-table) {
          overflow: hidden;
          border-radius: 6px;

          th.el-table__cell {
            padding: 12px 0;
            font-size: 14px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            text-align: center;
            background-color: var(--el-fill-color-extra-light);
          }

          td.el-table__cell {
            padding: 14px 0;
            color: var(--el-text-color-regular);
            text-align: center;
            transition: background-color 0.2s ease;
          }

          tr.el-table__row {
            transition: all 0.2s ease;

            &:hover {
              background-color: var(--el-fill-color-light);
            }
          }

          // 移除垂直分隔线
          .el-table__cell {
            border-right: none !important;
          }
        }
      }
    }

    /* 黑夜模式特殊样式 */
    :global(.dark) {
      .carbon-emission-comparison {
        .filter-container,
        .chart-card,
        .table-card {
          background-color: var(--el-bg-color-overlay);
          border: 1px solid var(--el-border-color);
        }

        .comparison-table {
          :deep(.el-table) {
            background-color: var(--el-bg-color-overlay);

            th.el-table__cell {
              background-color: var(--el-fill-color);
            }
          }
        }
      }
    }
  }
</style>
