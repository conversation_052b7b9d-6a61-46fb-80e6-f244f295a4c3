<template>
  <el-select
    v-model="selectedValue"
    :placeholder="placeholder"
    :clearable="clearable"
    :disabled="disabled"
    :loading="loading"
    filterable
    @change="handleChange"
    @clear="handleClear"
    style="width: 180px"
  >
    <el-option
      v-for="project in projectList"
      :key="project.id"
      :label="project.name"
      :value="project.id"
    />
  </el-select>
</template>

<script setup lang="ts">
  import { ref, onMounted, computed } from 'vue'
  import { ElMessage } from 'element-plus'
  import { getProjectSelectList, type ProjectOption } from '@/api/projectApi'

  // 组件属性定义
  interface Props {
    modelValue?: number | null
    placeholder?: string
    clearable?: boolean
    disabled?: boolean
    defaultProjectId?: number // 新增默认项目ID属性
  }

  // 组件事件定义
  interface Emits {
    (e: 'update:modelValue', value: number | null): void
    (e: 'change', projectInfo: ProjectOption | null): void
    (e: 'loaded', projectList: ProjectOption[]): void
  }

  // 定义属性和事件
  const props = withDefaults(defineProps<Props>(), {
    modelValue: null,
    placeholder: '请选择项目',
    clearable: true,
    disabled: false
  })

  const emit = defineEmits<Emits>()

  // 响应式数据
  const projectList = ref<ProjectOption[]>([])
  const loading = ref(false)

  // 计算属性：双向绑定的值
  const selectedValue = computed({
    get: () => props.modelValue,
    set: (value: number | null) => emit('update:modelValue', value)
  })

  // 获取项目列表
  const fetchProjectList = async () => {
    try {
      loading.value = true
      const response = await getProjectSelectList()

      if (response.code === 200) {
        projectList.value = response.data || []
        // 触发加载完成事件
        emit('loaded', projectList.value)
      } else {
        ElMessage.error(response.msg || '获取项目列表失败')
        projectList.value = []
      }
    } catch (error) {
      console.error('获取项目列表失败:', error)
      ElMessage.error('获取项目列表失败，请稍后重试')
      projectList.value = []
    } finally {
      loading.value = false
    }
  }

  // 处理选择变化
  const handleChange = (value: number | null) => {
    const selectedProject = value ? projectList.value.find((p) => p.id === value) : null
    emit('change', selectedProject || null)
  }

  // 处理清空
  const handleClear = () => {
    emit('change', null)
  }

  // 组件挂载时获取项目列表
  onMounted(() => {
    fetchProjectList()
  })

  // 暴露刷新方法和项目列表，供父组件调用
  defineExpose({
    refresh: fetchProjectList,
    projectList
  })
</script>

<style scoped>
  /* 如果需要自定义样式可以在这里添加 */
</style>
