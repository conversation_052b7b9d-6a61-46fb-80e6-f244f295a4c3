import api from '@/utils/http'

// 可再生能源配置相关接口

/**
 * 可再生能源配置查询参数
 */
export interface RenewableEnergyConfigQueryParams {
  projectId?: number | string
  pageSize: number
  pageNum: number
}

/**
 * 可再生能源配置数据
 */
export interface RenewableEnergyConfig {
  id?: number
  projectId: number
  projectName?: string
  tyngf: string // 太阳能光伏装机容量
  tynrsjrq: string // 太阳能热水集热器面积
  dyrb: string // 地源热泵制冷/热量
  kqyrb: string // 空气源热泵制热量
  createTime?: string
  updateTime?: string
  createBy?: string
  updateBy?: string
  remark?: string
}

/**
 * 可再生能源配置列表响应
 */
export interface RenewableEnergyConfigListResponse {
  msg: string
  code: number
  total: number
  rows: RenewableEnergyConfig[]
}

/**
 * 新增可再生能源配置请求参数
 */
export interface CreateRenewableEnergyConfigRequest {
  projectId: number
  tyngf: string
  tynrsjrq: string
  dyrb: string
  kqyrb: string
}

/**
 * 修改可再生能源配置请求参数
 */
export interface UpdateRenewableEnergyConfigRequest {
  id: number
  projectId: number
  tyngf: string
  tynrsjrq: string
  dyrb: string
  kqyrb: string
}

/**
 * 通用响应接口
 */
export interface CommonResponse {
  msg: string
  code: number
  data?: any
}

/**
 * 获取可再生能源配置列表
 * @param params 查询参数
 * @returns 可再生能源配置列表
 */
export function getRenewableEnergyConfigList(
  params: RenewableEnergyConfigQueryParams
): Promise<RenewableEnergyConfigListResponse> {
  return api.get({
    url: '/basedata/energyrenewableconfig/list',
    params
  })
}

/**
 * 新增可再生能源配置
 * @param data 配置数据
 * @returns 操作结果
 */
export function createRenewableEnergyConfig(
  data: CreateRenewableEnergyConfigRequest
): Promise<CommonResponse> {
  return api.post({
    url: '/basedata/energyrenewableconfig/',
    data
  })
}

/**
 * 修改可再生能源配置
 * @param data 配置数据
 * @returns 操作结果
 */
export function updateRenewableEnergyConfig(
  data: UpdateRenewableEnergyConfigRequest
): Promise<CommonResponse> {
  return api.put({
    url: '/basedata/energyrenewableconfig/',
    data
  })
}

/**
 * 删除可再生能源配置
 * @param id 配置ID
 * @returns 操作结果
 */
export function deleteRenewableEnergyConfig(id: number): Promise<CommonResponse> {
  return api.del({
    url: `/basedata/energyrenewableconfig/${id}`
  })
}
