{"msg": "操作成功", "code": 200, "permissions": ["*:*:*"], "roles": ["admin"], "user": {"createBy": "admin", "createTime": "2025-04-28 11:21:57", "updateBy": null, "updateTime": null, "remark": "管理员", "params": {"@type": "java.util.HashMap"}, "userId": 1, "deptId": 103, "userName": "admin", "nickName": "若依", "email": "<EMAIL>", "phonenumber": "15888888888", "sex": "1", "avatar": null, "password": "$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2", "status": "0", "delFlag": "0", "loginIp": "*************", "loginDate": "2025-06-03T11:14:49.000+08:00", "dept": {"createBy": null, "createTime": null, "updateBy": null, "updateTime": null, "remark": null, "params": {"@type": "java.util.HashMap"}, "deptId": 103, "parentId": 101, "ancestors": "0,100,101", "deptName": "研发部门", "orderNum": 1, "leader": "zhai", "phone": null, "email": null, "status": "0", "delFlag": null, "parentName": null, "children": []}, "roles": [{"createBy": null, "createTime": null, "updateBy": null, "updateTime": null, "remark": null, "params": {"@type": "java.util.HashMap"}, "roleId": 1, "roleName": "超级管理员", "roleKey": "admin", "roleSort": 1, "dataScope": "1", "menuCheckStrictly": false, "deptCheckStrictly": false, "status": "0", "delFlag": null, "flag": false, "menuIds": null, "deptIds": null, "permissions": null, "admin": true}], "roleIds": null, "postIds": null, "roleId": 1, "admin": true}}