import request from '@/utils/http'

// 能耗分析相关接口

/**
 * 能耗分析查询参数
 */
export interface EnergyAnalysisParams {
  projectId: string | number
  buildingId?: string | number
  beginTime: string
  endTime: string
  timeType: number // 0-日 1-月 2-年
}

/**
 * 能耗类型数据项
 */
export interface EnergyTypeItem {
  name: string
  value: number
}

/**
 * 能耗分项数据项
 */
export interface EnergyItemData {
  name: string
  value: number
}

/**
 * 能耗趋势线数据
 */
export interface EnergyLineData {
  name: string
  dataList: number[]
}

/**
 * 能耗分析响应数据
 */
export interface EnergyAnalysisResponse {
  msg: string
  code: number
  data: {
    energyTypeList: EnergyTypeItem[]
    energyItemList: EnergyItemData[]
    energyLineDataList: EnergyLineData[]
    timeList: string[]
  }
}

/**
 * 获取能耗分析数据
 */
export function getEnergyAnalysis(params: EnergyAnalysisParams): Promise<EnergyAnalysisResponse> {
  return request.get({
    url: '/energy/energyconsume/getEnergyAnalysis',
    params
  })
}
