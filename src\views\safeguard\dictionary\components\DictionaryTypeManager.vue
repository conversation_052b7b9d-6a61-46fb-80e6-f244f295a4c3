<template>
  <div class="dictionary-type-manager">
    <!-- 查询条件区域 -->
    <div class="filter-section">
      <el-form :model="queryParams" inline>
        <el-form-item label="字典名称:">
          <el-input v-model="queryParams.dictName" placeholder="请输入字典名称" clearable />
        </el-form-item>
        <el-form-item label="字典类型:">
          <el-input v-model="queryParams.dictType" placeholder="请输入字典类型" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :icon="Search">查询</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 字典类型表格 -->
    <div class="table-section">
      <div class="table-operation">
        <el-button type="primary" @click="handleAdd" :icon="Plus">新增字典类型</el-button>
      </div>

      <ArtTable
        :data="tableData"
        :loading="loading"
        row-key="dictId"
        :border="false"
        :highlight-current-row="false"
        :total="totalCount"
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        class="dict-table"
      >
        <el-table-column prop="index" label="序号" align="center" width="80" />
        <el-table-column prop="dictName" label="字典名称" align="center" />
        <el-table-column prop="dictType" label="字典类型" align="center" />
        <el-table-column prop="remark" label="备注" align="center">
          <template #default="scope">
            <span>{{ scope.row.remark || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" align="center">
          <template #default="scope">
            <span>{{ formatDate(scope.row.createTime) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="240" align="center">
          <template #default="scope">
            <div class="operation-btns">
              <el-button
                link
                size="small"
                @click="handleViewData(scope.row)"
                title="查看数据"
                class="operation-btn view-btn"
              >
                <el-icon><List /></el-icon>
              </el-button>
              <el-button
                link
                size="small"
                @click="handleEdit(scope.row)"
                title="编辑"
                class="operation-btn edit-btn"
              >
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button
                link
                size="small"
                @click="handleDelete(scope.row)"
                title="删除"
                class="operation-btn delete-btn"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
      </ArtTable>
    </div>

    <!-- 新增/编辑字典类型对话框 -->
    <el-dialog
      :title="dialogType === 'add' ? '新增字典类型' : '编辑字典类型'"
      v-model="dialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="字典名称" prop="dictName">
          <el-input v-model="form.dictName" placeholder="请输入字典名称" />
        </el-form-item>
        <el-form-item label="字典类型" prop="dictType">
          <el-input v-model="form.dictType" placeholder="请输入字典类型" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitLoading"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Search, Edit, Delete, Plus, List } from '@element-plus/icons-vue'
  import { getDictTypeList, createDictType, updateDictType, deleteDictType } from '@/api/dictApi.ts'

  // 定义组件事件
  const emit = defineEmits(['view-data', 'data-updated', 'all-types-updated'])

  // 查询参数
  const queryParams = reactive({
    dictName: '',
    dictType: '',
    pageNum: 1,
    pageSize: 10
  })

  // 表格数据和分页
  const tableData = ref([])
  const loading = ref(false)
  const totalCount = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(10)

  // 对话框相关
  const dialogVisible = ref(false)
  const dialogType = ref('add')
  const formRef = ref(null)
  const submitLoading = ref(false)

  // 表单数据
  const form = reactive({
    dictId: null,
    dictName: '',
    dictType: '',
    remark: ''
  })

  // 表单验证规则
  const rules = {
    dictName: [
      { required: true, message: '请输入字典名称', trigger: 'blur' },
      { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
    ],
    dictType: [
      { required: true, message: '请输入字典类型', trigger: 'blur' },
      { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
    ]
  }

  // 格式化日期
  const formatDate = (dateString) => {
    if (!dateString) return '-'
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  }

  // 获取全部字典类型数据（用于下拉选择）
  const fetchAllDictTypes = async () => {
    try {
      const response = await getDictTypeList({
        pageSize: 99,
        pageNum: 1
      })
      if (response.code === 200 || response.code === 1) {
        const allData = response.rows || []
        // 通知父组件全部字典类型数据
        emit('all-types-updated', allData)
      }
    } catch (error) {
      console.error('获取全部字典类型失败:', error)
    }
  }

  // 获取字典类型列表
  const fetchData = async () => {
    loading.value = true
    try {
      const params = {
        dictName: queryParams.dictName || undefined,
        dictType: queryParams.dictType || undefined,
        pageSize: pageSize.value,
        pageNum: currentPage.value
      }

      const response = await getDictTypeList(params)
      if (response.code === 200 || response.code === 1) {
        const data = response.rows || []
        tableData.value = data.map((item, index) => ({
          ...item,
          index: (currentPage.value - 1) * pageSize.value + index + 1
        }))
        totalCount.value = response.total || 0

        // 通知父组件数据已更新
        emit('data-updated', data)

        // 同时获取全部字典类型数据用于下拉选择
        fetchAllDictTypes()
      } else {
        ElMessage.error(response.msg || '获取字典类型列表失败')
        tableData.value = []
        totalCount.value = 0
      }
    } catch (error) {
      console.error('获取字典类型列表失败:', error)
      // 如果接口不存在，使用模拟数据
      if (error.response?.status === 404) {
        ElMessage.warning('字典类型接口暂未实现，使用模拟数据')
        const mockData = [
          {
            dictId: 1,
            dictName: '能源类型',
            dictType: 'yw_nhlx',
            remark: '用于定义系统中的能源类型',
            createTime: '2024-01-01 10:00:00'
          },
          {
            dictId: 2,
            dictName: '设备类型',
            dictType: 'sblx',
            remark: '用于定义设备分类',
            createTime: '2024-01-02 10:00:00'
          },
          {
            dictId: 3,
            dictName: '设备状态',
            dictType: 'sbzt',
            remark: '用于定义设备运行状态',
            createTime: '2024-01-03 10:00:00'
          }
        ]
        tableData.value = mockData.map((item, index) => ({
          ...item,
          index: index + 1
        }))
        totalCount.value = mockData.length
        emit('data-updated', mockData)
        // 模拟数据情况下也发送全部数据
        emit('all-types-updated', mockData)
      } else {
        ElMessage.error('获取字典类型列表失败，请稍后重试')
        tableData.value = []
        totalCount.value = 0
      }
    } finally {
      loading.value = false
    }
  }

  // 搜索
  const handleSearch = () => {
    currentPage.value = 1
    fetchData()
  }

  // 重置表单
  const resetForm = () => {
    queryParams.dictName = ''
    queryParams.dictType = ''
    currentPage.value = 1
    fetchData()
  }

  // 分页处理
  const handleCurrentChange = (val) => {
    currentPage.value = val
    fetchData()
  }

  const handleSizeChange = (val) => {
    pageSize.value = val
    currentPage.value = 1
    fetchData()
  }

  // 新增
  const handleAdd = () => {
    dialogType.value = 'add'
    resetFormData()
    dialogVisible.value = true
  }

  // 编辑
  const handleEdit = (row) => {
    dialogType.value = 'edit'
    resetFormData()
    form.dictId = row.dictId
    form.dictName = row.dictName
    form.dictType = row.dictType
    form.remark = row.remark || ''
    dialogVisible.value = true
  }

  // 删除
  const handleDelete = (row) => {
    ElMessageBox.confirm(`确认删除字典类型 "${row.dictName}" 吗？删除后将无法恢复！`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        try {
          const response = await deleteDictType(row.dictId)
          if (response.code === 200) {
            ElMessage.success(`删除字典类型 "${row.dictName}" 成功`)
            fetchData()
          } else {
            ElMessage.error(response.msg || '删除字典类型失败')
          }
        } catch (error) {
          console.error('删除字典类型失败:', error)
          ElMessage.error('删除字典类型失败')
        }
      })
      .catch(() => {})
  }

  // 查看数据
  const handleViewData = (row) => {
    emit('view-data', row)
  }

  // 重置表单数据
  const resetFormData = () => {
    if (formRef.value) {
      formRef.value.resetFields()
    }
    form.dictId = null
    form.dictName = ''
    form.dictType = ''
    form.remark = ''
  }

  // 提交表单
  const submitForm = () => {
    if (!formRef.value) return

    formRef.value.validate(async (valid) => {
      if (valid) {
        submitLoading.value = true
        try {
          const formData = {
            dictName: form.dictName,
            dictType: form.dictType,
            remark: form.remark
          }

          if (dialogType.value === 'add') {
            try {
              const response = await createDictType(formData)
              if (response.code === 200) {
                ElMessage.success('新增字典类型成功')
                dialogVisible.value = false
                fetchData()
              } else {
                ElMessage.error(response.msg || '新增字典类型失败')
              }
            } catch (error) {
              if (error.response?.status === 404) {
                ElMessage.success('新增字典类型成功（模拟）')
                dialogVisible.value = false
                fetchData()
              } else {
                throw error
              }
            }
          } else {
            try {
              const requestData = {
                dictId: form.dictId,
                ...formData
              }
              const response = await updateDictType(requestData)
              if (response.code === 200) {
                ElMessage.success('编辑字典类型成功')
                dialogVisible.value = false
                fetchData()
              } else {
                ElMessage.error(response.msg || '编辑字典类型失败')
              }
            } catch (error) {
              if (error.response?.status === 404) {
                ElMessage.success('编辑字典类型成功（模拟）')
                dialogVisible.value = false
                fetchData()
              } else {
                throw error
              }
            }
          }
        } catch (error) {
          console.error(
            dialogType.value === 'add' ? '新增字典类型失败:' : '编辑字典类型失败:',
            error
          )
          ElMessage.error(dialogType.value === 'add' ? '新增字典类型失败' : '编辑字典类型失败')
        } finally {
          submitLoading.value = false
        }
      } else {
        ElMessage.warning('请填写完整的表单信息')
        return false
      }
    })
  }

  // 暴露方法给父组件
  defineExpose({
    fetchData
  })

  // 组件挂载时初始化数据
  onMounted(() => {
    fetchData()
    fetchAllDictTypes()
  })
</script>

<style lang="scss" scoped>
  .dictionary-type-manager {
    // 筛选条件区域样式
    .filter-section {
      padding: 20px;
      margin-bottom: 20px;
      background-color: var(--art-main-bg-color);
      border: 1px solid var(--art-border-color);
      border-radius: 4px;
      box-shadow: var(--art-box-shadow-xs);

      .el-form {
        display: flex;
        flex-wrap: wrap;

        .el-form-item {
          margin-right: 10px;
          margin-bottom: 10px;

          // 按钮间距
          .el-button + .el-button {
            margin-left: 10px;
          }
        }
      }

      :deep(.el-input) {
        width: 180px;
      }
    }

    // 表格区域样式
    .table-section {
      padding: 20px;
      background-color: var(--art-main-bg-color);
      border: 1px solid var(--art-border-color);
      border-radius: 4px;
      box-shadow: var(--art-box-shadow-xs);

      // 表格操作区域
      .table-operation {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 15px;

        .el-button {
          margin-right: 10px;
        }
      }

      // 确保ArtTable组件有足够的显示空间
      :deep(.art-table) {
        height: auto;
        min-height: 400px;
      }

      // 自定义操作按钮样式
      .operation-btns {
        display: flex;
        gap: 12px;
        justify-content: center;
      }

      .operation-btn {
        width: 32px !important;
        height: 32px !important;
        padding: 6px !important;
        margin: 0 !important;
        line-height: 1 !important;
        border: none !important;
        border-radius: 4px !important;
        box-shadow: 0 2px 4px rgb(0 0 0 / 10%) !important;
        transition: all 0.3s ease !important;
      }

      .operation-btn:hover {
        box-shadow: 0 4px 8px rgb(0 0 0 / 15%) !important;
        transform: translateY(-2px) !important;
      }

      .view-btn {
        background-color: #e6f7ff !important;
      }

      .view-btn .el-icon {
        font-size: 16px;
        color: #409eff !important;
      }

      .edit-btn {
        background-color: #e6f7ff !important;
      }

      .edit-btn .el-icon {
        font-size: 16px;
        color: #409eff !important;
      }

      .delete-btn {
        background-color: #fff1f0 !important;
      }

      .delete-btn .el-icon {
        font-size: 16px;
        color: #f56c6c !important;
      }

      // 修复el-table__empty-block高度不断增长问题
      .dict-table :deep(.el-table__empty-block) {
        height: auto !important;
        min-height: 60px;
        max-height: 400px;
        overflow: hidden;
      }

      // 确保空表格状态下的布局稳定
      .dict-table :deep(.el-table__body-wrapper) {
        height: auto !important;
        min-height: 200px;
      }
    }
  }

  // 对话框样式
  :deep(.el-dialog) {
    .el-dialog__header {
      background-color: var(--art-main-bg-color);
      border-bottom: 1px solid var(--art-border-color);
    }

    .el-dialog__body {
      padding: 20px;
    }

    .el-form-item__label {
      font-weight: 500;
    }
  }
</style>
