<template>
  <div class="markdown-renderer" :class="{ 'dark-mode': isDark }">
    <div class="markdown-content" v-html="renderedContent"></div>
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted, ref, watch } from 'vue'
  import { useSettingStore } from '@/store/modules/setting'
  import { storeToRefs } from 'pinia'
  import MarkdownIt from 'markdown-it'
  import hljs from 'highlight.js'
  import 'highlight.js/styles/atom-one-light.css'
  import 'highlight.js/styles/atom-one-dark.css'

  // 获取主题设置
  const settingStore = useSettingStore()
  const { isDark } = storeToRefs(settingStore)

  // 定义 props
  const props = defineProps<{
    content: string
  }>()

  // 创建 markdown-it 实例
  const md = new MarkdownIt({
    html: true,
    linkify: true,
    typographer: true,
    highlight: function (str: string, lang: string) {
      if (lang && hljs.getLanguage(lang)) {
        try {
          const highlighted = hljs.highlight(str, { language: lang, ignoreIllegals: true }).value
          return `<pre class="hljs"><code class="language-${lang}">${highlighted}</code></pre>`
        } catch (__) {}
      }
      return `<pre class="hljs"><code>${md.utils.escapeHtml(str)}</code></pre>`
    }
  })

  // 渲染 Markdown 内容
  const renderedContent = computed(() => {
    if (!props.content) return ''
    return md.render(props.content)
  })

  // 在组件挂载后处理代码块
  onMounted(() => {
    addCopyButtons()
  })

  // 监听内容变化，更新代码块
  watch(
    () => props.content,
    () => {
      setTimeout(() => {
        addCopyButtons()
      }, 0)
    }
  )

  // 添加复制按钮到代码块
  const addCopyButtons = () => {
    const codeBlocks = document.querySelectorAll('.markdown-renderer pre.hljs')

    codeBlocks.forEach((block: Element) => {
      // 避免重复添加按钮
      if (block.querySelector('.copy-button')) return

      const button = document.createElement('button')
      button.className = 'copy-button'
      button.textContent = '复制'
      button.addEventListener('click', () => {
        const code = block.querySelector('code')
        if (code) {
          navigator.clipboard
            .writeText(code.textContent || '')
            .then(() => {
              button.textContent = '已复制!'
              setTimeout(() => {
                button.textContent = '复制'
              }, 2000)
            })
            .catch((err) => {
              console.error('复制失败:', err)
              button.textContent = '复制失败'
              setTimeout(() => {
                button.textContent = '复制'
              }, 2000)
            })
        }
      })

      block.appendChild(button)
    })
  }
</script>

<style lang="scss" scoped>
  .markdown-renderer {
    width: 100%;
    overflow-wrap: break-word;

    &.dark-mode {
      :deep(pre.hljs) {
        background-color: #282c34;

        .copy-button {
          color: #e6e6e6;
          background-color: #3a3f4b;

          &:hover {
            background-color: #4a4f5a;
          }
        }
      }

      :deep(code:not(pre code)) {
        color: #e6e6e6;
        background-color: rgb(255 255 255 / 10%);
      }

      :deep(blockquote) {
        background-color: rgb(255 255 255 / 5%);
        border-left-color: #4a4f5a;
      }

      :deep(table) {
        th {
          background-color: #3a3f4b;
        }

        tr:nth-child(2n) {
          background-color: rgb(255 255 255 / 5%);
        }
      }
    }
  }

  .markdown-content {
    :deep(p) {
      margin-bottom: 16px;
      line-height: 1.6;
    }

    :deep(h1, h2, h3, h4, h5, h6) {
      margin-top: 24px;
      margin-bottom: 16px;
      font-weight: 600;
      line-height: 1.25;
    }

    :deep(h1) {
      font-size: 2em;
    }

    :deep(h2) {
      font-size: 1.5em;
    }

    :deep(h3) {
      font-size: 1.25em;
    }

    :deep(ul, ol) {
      padding-left: 2em;
      margin-bottom: 16px;
    }

    :deep(li) {
      margin-bottom: 4px;
    }

    :deep(pre.hljs) {
      position: relative;
      padding: 16px;
      margin-bottom: 16px;
      overflow: auto;
      border-radius: 6px;

      .copy-button {
        position: absolute;
        top: 8px;
        right: 8px;
        padding: 4px 8px;
        font-size: 12px;
        color: #333;
        cursor: pointer;
        background-color: #e6e6e6;
        border: none;
        border-radius: 4px;
        opacity: 0;
        transition: opacity 0.2s;

        &:hover {
          background-color: #d6d6d6;
        }
      }

      &:hover .copy-button {
        opacity: 1;
      }
    }

    :deep(code:not(pre code)) {
      padding: 0.2em 0.4em;
      margin: 0;
      font-size: 85%;
      background-color: rgb(0 0 0 / 5%);
      border-radius: 3px;
    }

    :deep(blockquote) {
      padding: 0 1em;
      margin-bottom: 16px;
      color: #6a737d;
      border-left: 0.25em solid #dfe2e5;
    }

    :deep(table) {
      display: block;
      width: 100%;
      margin-bottom: 16px;
      overflow: auto;
      border-collapse: collapse;

      th,
      td {
        padding: 6px 13px;
        border: 1px solid #dfe2e5;
      }

      th {
        font-weight: 600;
        background-color: #f6f8fa;
      }

      tr:nth-child(2n) {
        background-color: #f6f8fa;
      }
    }

    :deep(img) {
      box-sizing: content-box;
      max-width: 100%;
    }

    :deep(hr) {
      height: 0.25em;
      padding: 0;
      margin: 24px 0;
      background-color: #e1e4e8;
      border: 0;
    }
  }
</style>
