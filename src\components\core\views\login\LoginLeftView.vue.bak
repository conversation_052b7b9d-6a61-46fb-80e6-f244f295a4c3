<template>
  <div class="left-view">
    <div class="logo">
      <img src="@imgs/login/logo.png" alt="logo" class="logo-img" />
      <h1 class="title">{{ AppConfig.systemInfo.name }}</h1>
    </div>
    <img class="left-bg" src="@imgs/login/lf_bg.png" />
    <img class="left-img" src="@imgs/login/lf_icon2.png" />
  </div>
</template>

<script setup lang="ts">
import AppConfig from '@/config'
</script>

<style lang="scss" scoped>
@use '@/views/login/index' as login;

.logo-img {
  height: 40px;
  margin-right: 10px;
}
</style>
