<template>
  <div class="page-content data-overview">
    <!-- 操作按钮区域 -->
    <div class="action-bar">
      <div class="action-buttons">
        <el-button type="primary" @click="exportData" :loading="exportLoading">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
        <el-button @click="showFilterDialog = true">
          <el-icon><Filter /></el-icon>
          筛选
        </el-button>
      </div>
    </div>

    <!-- 关键指标卡片区域 -->
    <div class="metrics-cards">
      <div class="metric-card">
        <div class="metric-header">
          <span class="metric-title">今年累计碳排放量</span>
          <el-icon class="info-icon"><InfoFilled /></el-icon>
        </div>
        <div class="metric-value">
          <span class="value">{{ formatNumber(totalEmission) }}</span>
          <span class="unit">kgCO₂</span>
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-header">
          <span class="metric-title">今年累计碳排放强度</span>
          <el-icon class="info-icon"><InfoFilled /></el-icon>
        </div>
        <div class="metric-value">
          <span class="value">{{ emissionIntensity }}</span>
          <span class="unit">kgCO₂/m²</span>
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-header">
          <span class="metric-title">碳排放达标率</span>
          <el-icon class="info-icon"><InfoFilled /></el-icon>
        </div>
        <div class="metric-value">
          <div class="progress-container">
            <el-progress
              :percentage="complianceRate"
              :stroke-width="8"
              :show-text="false"
              color="#5D87FF"
            />
            <span class="progress-text">{{ complianceRate }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-container">
      <!-- 第一行：饼图和柱状图 -->
      <div class="chart-row">
        <div class="chart-card">
          <div class="chart-header">
            <h3>碳排放来源分布</h3>
          </div>
          <div class="chart-content">
            <div ref="pieChartRef" class="chart-wrapper"></div>
          </div>
        </div>

        <div class="chart-card">
          <div class="chart-header">
            <h3>网点碳排放情况</h3>
            <div class="chart-legend">
              <span class="legend-item">
                <span class="legend-color" style="background-color: #5d87ff"></span>
                碳排放量
              </span>
              <span class="legend-item">
                <span class="legend-color" style="background-color: #60c041"></span>
                同比变化
              </span>
            </div>
          </div>
          <div class="chart-content">
            <div ref="barChartRef" class="chart-wrapper"></div>
          </div>
        </div>
      </div>

      <!-- 第二行：趋势图 -->
      <div class="chart-row">
        <div class="chart-card full-width">
          <div class="chart-header">
            <h3>碳排放趋势分析</h3>
            <div class="chart-legend">
              <span class="legend-item">
                <span class="legend-color" style="background-color: #5d87ff"></span>
                累计排放量
              </span>
              <span class="legend-item">
                <span class="legend-color" style="background-color: #60c041"></span>
                碳排放强度
              </span>
            </div>
          </div>
          <div class="chart-content">
            <div ref="trendChartRef" class="chart-wrapper"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选对话框 -->
    <el-dialog v-model="showFilterDialog" title="数据筛选" width="500px">
      <el-form :model="filterForm" label-width="100px">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="网点">
          <el-select v-model="filterForm.branch" placeholder="请选择网点" clearable>
            <el-option label="全部网点" value=""></el-option>
            <el-option label="分行A" value="branchA"></el-option>
            <el-option label="分行B" value="branchB"></el-option>
            <el-option label="分行C" value="branchC"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showFilterDialog = false">取消</el-button>
          <el-button type="primary" @click="applyFilter">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted, nextTick } from 'vue'
  import { ElMessage } from 'element-plus'
  import { Download, Filter, InfoFilled } from '@element-plus/icons-vue'
  import * as echarts from 'echarts'
  import * as XLSX from 'xlsx'

  // 响应式数据
  const totalEmission = ref(511234.12)
  const emissionIntensity = ref(12.7)
  const complianceRate = ref(54)
  const exportLoading = ref(false)
  const showFilterDialog = ref(false)

  // 图表引用
  const pieChartRef = ref<HTMLElement>()
  const barChartRef = ref<HTMLElement>()
  const trendChartRef = ref<HTMLElement>()

  // 图表实例
  let pieChart: echarts.ECharts | null = null
  let barChart: echarts.ECharts | null = null
  let trendChart: echarts.ECharts | null = null

  // 筛选表单
  const filterForm = ref({
    dateRange: [],
    branch: ''
  })

  // 模拟数据
  const mockData = {
    // 饼图数据 - 碳排放来源分布（百分比保持不变，但基于511.23吨总量）
    pieData: [
      { name: '用电', value: 45, color: '#5D87FF' },
      { name: '供暖', value: 25, color: '#60C041' },
      { name: '交通', value: 15, color: '#F9901F' },
      { name: '办公', value: 10, color: '#FF80C8' },
      { name: '其他', value: 5, color: '#38C0FC' }
    ],
    // 柱状图数据 - 网点碳排放情况（调整为合理分配，总和接近511吨）
    barData: {
      categories: ['分行A', '分行B', '分行C', '分行D', '分行E'],
      emissions: [102, 120, 85, 115, 89], // 总计511吨
      changes: [8, -2, 12, -5, 15] // 同比变化保持不变
    },
    // 趋势图数据（调整累计排放量为合理增长到511.23吨）
    trendData: {
      months: [
        '1月',
        '2月',
        '3月',
        '4月',
        '5月',
        '6月',
        '7月',
        '8月',
        '9月',
        '10月',
        '11月',
        '12月'
      ],
      cumulative: [
        42,
        87,
        135,
        186,
        241,
        298,
        358,
        421,
        456,
        485,
        498,
        511 // 从42吨增长到511吨
      ],
      intensity: [10.5, 10.8, 11.2, 11.5, 11.8, 12.0, 12.2, 12.4, 12.5, 12.6, 12.7, 12.8] // 强度数据保持不变
    }
  }

  // 格式化数字
  const formatNumber = (num: number): string => {
    return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
  }

  // 初始化饼图
  const initPieChart = () => {
    if (!pieChartRef.value) return

    pieChart = echarts.init(pieChartRef.value)

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c}% ({d}%)'
      },
      legend: {
        bottom: 10,
        left: 'center',
        data: mockData.pieData.map((item) => item.name)
      },
      series: [
        {
          name: '碳排放来源',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '45%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 8,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: true,
            formatter: '{b}: {c}%'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 14,
              fontWeight: 'bold'
            }
          },
          data: mockData.pieData.map((item) => ({
            name: item.name,
            value: item.value,
            itemStyle: { color: item.color }
          }))
        }
      ]
    }

    pieChart.setOption(option)
  }

  // 初始化柱状图
  const initBarChart = () => {
    if (!barChartRef.value) return

    barChart = echarts.init(barChartRef.value)

    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999'
          }
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          data: mockData.barData.categories,
          axisPointer: {
            type: 'shadow'
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          name: '碳排放量',
          position: 'left',
          axisLabel: {
            formatter: '{value} t'
          }
        },
        {
          type: 'value',
          name: '同比变化',
          position: 'right',
          axisLabel: {
            formatter: '{value} %'
          }
        }
      ],
      series: [
        {
          name: '碳排放量',
          type: 'bar',
          data: mockData.barData.emissions,
          itemStyle: {
            color: '#5D87FF'
          }
        },
        {
          name: '同比变化',
          type: 'line',
          yAxisIndex: 1,
          data: mockData.barData.changes,
          itemStyle: {
            color: '#60C041'
          },
          lineStyle: {
            color: '#60C041'
          }
        }
      ]
    }

    barChart.setOption(option)
  }

  // 初始化趋势图
  const initTrendChart = () => {
    if (!trendChartRef.value) return

    trendChart = echarts.init(trendChartRef.value)

    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: mockData.trendData.months
      },
      yAxis: [
        {
          type: 'value',
          name: '累计排放量(t)',
          position: 'left',
          axisLabel: {
            formatter: '{value}'
          }
        },
        {
          type: 'value',
          name: '碳排放强度',
          position: 'right',
          axisLabel: {
            formatter: '{value}'
          }
        }
      ],
      series: [
        {
          name: '累计排放量',
          type: 'line',
          data: mockData.trendData.cumulative,
          smooth: true,
          itemStyle: {
            color: '#5D87FF'
          },
          lineStyle: {
            color: '#5D87FF'
          }
        },
        {
          name: '碳排放强度',
          type: 'line',
          yAxisIndex: 1,
          data: mockData.trendData.intensity,
          smooth: true,
          itemStyle: {
            color: '#60C041'
          },
          lineStyle: {
            color: '#60C041'
          }
        }
      ]
    }

    trendChart.setOption(option)
  }

  // 窗口大小变化处理
  const handleResize = () => {
    pieChart?.resize()
    barChart?.resize()
    trendChart?.resize()
  }

  // 导出数据
  const exportData = async () => {
    try {
      exportLoading.value = true

      // 准备导出数据
      const exportDataList = [
        {
          指标: '今年累计碳排放量',
          数值: totalEmission.value,
          单位: 'kgCO₂'
        },
        {
          指标: '今年累计碳排放强度',
          数值: emissionIntensity.value,
          单位: 'kgCO₂/m²'
        },
        {
          指标: '碳排放达标率',
          数值: complianceRate.value,
          单位: '%'
        }
      ]

      // 添加饼图数据
      mockData.pieData.forEach((item) => {
        exportDataList.push({
          指标: `碳排放来源-${item.name}`,
          数值: item.value,
          单位: '%'
        })
      })

      // 添加柱状图数据
      mockData.barData.categories.forEach((category, index) => {
        exportDataList.push({
          指标: `${category}-碳排放量`,
          数值: mockData.barData.emissions[index],
          单位: 't'
        })
        exportDataList.push({
          指标: `${category}-同比变化`,
          数值: mockData.barData.changes[index],
          单位: '%'
        })
      })

      // 创建工作簿
      const wb = XLSX.utils.book_new()
      const ws = XLSX.utils.json_to_sheet(exportDataList)
      XLSX.utils.book_append_sheet(wb, ws, '数据总览')

      // 生成文件名
      const fileName = `数据总览_${new Date().toISOString().split('T')[0]}.xlsx`

      // 下载文件
      XLSX.writeFile(wb, fileName)

      ElMessage.success('数据导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('导出失败，请重试')
    } finally {
      exportLoading.value = false
    }
  }

  // 应用筛选
  const applyFilter = () => {
    // 这里可以根据筛选条件重新加载数据
    console.log('应用筛选:', filterForm.value)
    showFilterDialog.value = false
    ElMessage.success('筛选条件已应用')
  }

  // 生命周期
  onMounted(async () => {
    await nextTick()
    initPieChart()
    initBarChart()
    initTrendChart()
    window.addEventListener('resize', handleResize)
  })

  onUnmounted(() => {
    pieChart?.dispose()
    barChart?.dispose()
    trendChart?.dispose()
    window.removeEventListener('resize', handleResize)
  })
</script>

<style lang="scss" scoped>
  @import './style';
</style>
