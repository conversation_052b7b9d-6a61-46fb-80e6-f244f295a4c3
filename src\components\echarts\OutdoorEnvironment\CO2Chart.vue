<template>
  <div ref="chartContainer" style="width: 100%; height: 300px"></div>
</template>

<script setup>
  import { ref, onMounted, watch, inject } from 'vue'
  import * as echarts from 'echarts'

  // 从父组件接收时间范围参数
  const props = defineProps({
    timeRange: {
      type: String,
      default: 'day'
    }
  })

  // 引用父组件的airQualityData
  const airQualityData = inject('airQualityData')

  const chartContainer = ref(null)
  let chart = null

  // 模拟不同时间范围的数据
  const generateData = (range) => {
    const times = []
    const co2Values = []
    const tvocValues = []

    // 根据选择的时间范围生成数据
    if (range === 'day') {
      for (let i = 0; i < 24; i++) {
        times.push(`${i}:00`)
        // 使用airQualityData中的CO2值作为基准，生成波动数据
        const co2Base = parseInt(airQualityData.find((item) => item.name === 'CO2').value)
        const tvocBase = parseFloat(airQualityData.find((item) => item.name === 'TVOC').value)

        // 生成随机波动
        const co2Random = Math.floor(co2Base * (0.9 + Math.random() * 0.2))
        const tvocRandom = parseFloat((tvocBase * (0.9 + Math.random() * 0.2)).toFixed(2))

        co2Values.push(co2Random)
        tvocValues.push(tvocRandom)
      }
    } else if (range === 'week') {
      const days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
      for (let i = 0; i < 7; i++) {
        times.push(days[i])
        const co2Base = parseInt(airQualityData.find((item) => item.name === 'CO2').value)
        const tvocBase = parseFloat(airQualityData.find((item) => item.name === 'TVOC').value)

        const co2Random = Math.floor(co2Base * (0.85 + Math.random() * 0.3))
        const tvocRandom = parseFloat((tvocBase * (0.85 + Math.random() * 0.3)).toFixed(2))

        co2Values.push(co2Random)
        tvocValues.push(tvocRandom)
      }
    } else if (range === 'month') {
      for (let i = 1; i <= 30; i++) {
        times.push(`${i}日`)
        const co2Base = parseInt(airQualityData.find((item) => item.name === 'CO2').value)
        const tvocBase = parseFloat(airQualityData.find((item) => item.name === 'TVOC').value)

        const co2Random = Math.floor(co2Base * (0.8 + Math.random() * 0.4))
        const tvocRandom = parseFloat((tvocBase * (0.8 + Math.random() * 0.4)).toFixed(2))

        co2Values.push(co2Random)
        tvocValues.push(tvocRandom)
      }
    }

    return { times, co2Values, tvocValues }
  }

  // 初始化图表
  const initChart = () => {
    if (chart) {
      chart.dispose()
    }

    chart = echarts.init(chartContainer.value)
    updateChart()
  }

  // 更新图表数据
  const updateChart = () => {
    const { times, co2Values, tvocValues } = generateData(props.timeRange)

    const option = {
      title: {
        text: '气体污染物趋势',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        },
        left: 'center',
        top: 10
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: ['CO2', 'TVOC'],
        bottom: 0,
        left: 'center'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: times,
        axisLabel: {
          rotate: props.timeRange === 'month' ? 45 : 0
        }
      },
      yAxis: [
        {
          type: 'value',
          name: 'CO2(ppm)',
          position: 'left',
          axisLine: {
            lineStyle: {
              color: '#5470C6'
            }
          }
        },
        {
          type: 'value',
          name: 'TVOC(mg/m³)',
          position: 'right',
          axisLine: {
            lineStyle: {
              color: '#91CC75'
            }
          },
          splitLine: {
            show: false
          }
        }
      ],
      series: [
        {
          name: 'CO2',
          type: 'line',
          data: co2Values,
          smooth: true,
          lineStyle: {
            width: 3,
            color: '#5470C6'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(84, 112, 198, 0.5)' },
              { offset: 1, color: 'rgba(84, 112, 198, 0.1)' }
            ])
          }
        },
        {
          name: 'TVOC',
          type: 'line',
          yAxisIndex: 1,
          data: tvocValues,
          smooth: true,
          lineStyle: {
            width: 3,
            color: '#91CC75'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(145, 204, 117, 0.5)' },
              { offset: 1, color: 'rgba(145, 204, 117, 0.1)' }
            ])
          }
        }
      ]
    }

    chart.setOption(option)
  }

  // 监听时间范围变化
  watch(
    () => props.timeRange,
    () => {
      updateChart()
    }
  )

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    if (chart) {
      chart.resize()
    }
  })

  onMounted(() => {
    initChart()
  })
</script>

<style scoped></style>
