<template>
  <div class="chart-wrapper">
    <!-- 添加标题栏 -->
    <div class="chart-header">
      <h3 class="chart-title">
        <el-icon><OfficeBuilding /></el-icon>
        {{ buildingName }} - 总体碳排放情况
      </h3>
    </div>
    <div class="chart-body">
      <div class="chart-container" ref="chartRef"></div>
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-mask">
        <el-icon class="loading-icon"><Loading /></el-icon>
        <span class="loading-text">数据加载中...</span>
      </div>
      <!-- 无数据提示 -->
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted, defineProps, watch, computed, nextTick } from 'vue'
  import * as echarts from 'echarts'
  import { OfficeBuilding, Loading } from '@element-plus/icons-vue'
  import { ElMessage } from 'element-plus'
  import { useSettingStore } from '@/store/modules/setting' // 导入设置store

  // 获取主题设置
  const settingsStore = useSettingStore()

  // 定义组件事件
  const emit = defineEmits(['timeUnitChange'])

  // 接收外部传入的数据
  const props = defineProps({
    emissionData: {
      type: Array,
      default: () => []
    },
    reductionData: {
      type: Array,
      default: () => []
    },
    actualData: {
      type: Array,
      default: () => []
    },
    xAxisData: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: '碳排放情况'
    },
    buildingName: {
      type: String,
      default: '项目'
    },
    buildingId: {
      type: Number,
      default: 1
    },
    chartColor: {
      type: String,
      default: '#409EFF'
    }
  })

  // 图表DOM引用
  const chartRef = ref(null)
  // 图表实例
  let chartInstance = null
  // 控制加载状态
  const loading = ref(false)

  // 判断数据是否为空
  const isEmpty = computed(() => {
    return !props.xAxisData || props.xAxisData.length === 0
  })

  // 处理时间单位变化 - 由父组件处理数据获取
  const handleTimeUnitChange = (value) => {
    // 通知父组件时间单位发生变化，由父组件负责获取新数据
    // 这里只负责显示加载状态
    loading.value = true

    // 发送事件给父组件
    emit('timeUnitChange', value)
  }

  // 更新图表数据的方法（供父组件调用）
  const updateChart = (data) => {
    if (!data) {
      console.warn('GenericTotalChart: 未接收到数据')
      loading.value = false
      return
    }

    loading.value = true

    // 处理来自父组件的真实API数据
    let newEmissionData = []
    let newReductionData = []
    let newActualData = []
    let newXAxisData = []

    if (data.values && data.xAxis && data.reductionData && data.actualData) {
      // 处理API转换后的完整数据格式
      newXAxisData = data.xAxis
      newEmissionData = data.values
      newReductionData = data.reductionData.map((val) => -Math.abs(val)) // 减排数据显示为负值
      newActualData = data.actualData
    } else if (data.emissionData && data.reductionData && data.actualData && data.xAxisData) {
      // 处理详细数据格式
      newXAxisData = data.xAxisData
      newEmissionData = data.emissionData
      newReductionData = data.reductionData.map((val) => -Math.abs(val))
      newActualData = data.actualData
    } else {
      console.warn('GenericTotalChart: 数据格式不完整，需要包含完整的排放数据', data)
      loading.value = false
      return
    }

    // 数据验证 - 只检查时间轴数据
    if (newXAxisData.length === 0) {
      console.warn('GenericTotalChart: 接收到空时间轴数据')
      loading.value = false
      return
    }

    // 确保图表实例和DOM元素都存在
    nextTick(() => {
      if (!chartInstance && chartRef.value) {
        // 如果图表实例不存在，先创建
        chartInstance = echarts.init(chartRef.value, null)
      }

      if (chartInstance && chartRef.value) {
        // 使用主题化配置更新图表
        const themedOption = getThemedChartOption({
          xAxisData: newXAxisData,
          emissionData: newEmissionData,
          reductionData: newReductionData,
          actualData: newActualData
        })
        chartInstance.setOption(themedOption, true)
      }

      loading.value = false
    })
  }

  // 完成数据加载的方法（供父组件调用）
  const finishLoading = () => {
    loading.value = false
  }

  // 获取主题化的图表配置
  const getThemedChartOption = (currentData) => {
    const isDark = settingsStore.isDark

    // 直接定义固定颜色，不依赖CSS变量
    // 文本和轴线颜色
    const textColor = isDark ? '#D1D5DB' : '#1F2937'
    const axisLineColor = isDark ? '#6B7280' : '#4B5563'
    const splitLineColor = isDark ? '#374151' : '#E5E7EB'
    const legendTextColor = isDark ? '#D1D5DB' : '#374151'
    const tooltipBgColor = isDark ? '#111827' : '#FFFFFF'
    const tooltipBorderColor = '#E5E7EB'

    // 系列颜色定义 - 使用固定值
    const colorBlue = '#5D87FF'
    const colorGreen = '#13DEB9'
    const colorOrange = '#FFAB1F'

    // 直接定义RGB值
    const blueRgb = '93, 135, 255'
    const successRgb = '19, 222, 185'
    const warningRgb = '255, 171, 31'

    return {
      title: {
        show: false
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: tooltipBgColor,
        borderColor: tooltipBorderColor,
        textStyle: {
          color: textColor
        },
        formatter: function (params) {
          let result = params[0].axisValue + '<br/>'
          params.forEach((item) => {
            const value = Math.abs(item.value) // 显示绝对值
            const unit = 'kg·CO₂'
            const prefix = item.value < 0 ? '减排: ' : ''
            result += item.marker + ' ' + prefix + item.seriesName + ': ' + value + unit + '<br/>'
          })
          return result
        }
      },
      legend: {
        bottom: 10,
        data: ['碳排放总量', '光伏碳减排', '实际碳排放量'],
        textStyle: {
          color: legendTextColor
        }
      },

      grid: {
        left: '6%',
        right: '4%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        splitLine: {
          show: false
        },
        axisLabel: {
          interval: 0,
          color: textColor
        },
        axisLine: {
          lineStyle: {
            color: axisLineColor
          }
        },
        data: currentData.xAxisData
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: textColor,
          formatter: function (value) {
            if (value === 0) {
              return `{carbon|碳中和}`
            }
            return value
          },
          rich: {
            carbon: {
              color: textColor,
              fontWeight: 'bold',
              padding: [0, 0, 0, 10]
            }
          }
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: axisLineColor
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            color: splitLineColor
          }
        },
        axisTick: {
          show: true,
          lineStyle: {
            color: axisLineColor
          }
        },
        min: function () {
          const maxReduction = Math.abs(Math.min(...(currentData.reductionData || [0])))
          return -Math.max(Math.ceil(maxReduction * 1.1), 10) // 负值，为减排留出空间
        },
        max: function () {
          const maxEmission = Math.max(...(currentData.emissionData || [0]))
          const maxActual = Math.max(...(currentData.actualData || [0]))
          const maxValue = Math.max(maxEmission, maxActual)
          return Math.max(Math.ceil(maxValue * 1.1), 100) // 添加10%的缓冲空间
        }
      },
      series: [
        {
          name: '碳排放总量',
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            color: colorBlue,
            borderColor: colorBlue,
            borderWidth: 2
          },
          lineStyle: {
            width: 3,
            color: colorBlue
          },
          areaStyle: {
            opacity: 0.3,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: `${colorBlue}66` },
              { offset: 1, color: `${colorBlue}11` }
            ])
          },
          data: currentData.emissionData,
          markLine: {
            silent: true,
            lineStyle: {
              color: axisLineColor,
              type: 'dashed',
              width: 1
            },
            data: [{ yAxis: 0, label: { formatter: '零排放线' } }]
          }
        },
        {
          name: '光伏碳减排',
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            color: colorGreen,
            borderColor: colorGreen,
            borderWidth: 2
          },
          lineStyle: {
            width: 2,
            color: colorGreen
          },
          areaStyle: {
            opacity: 0.3,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: `${colorGreen}66` },
              { offset: 1, color: `${colorGreen}11` }
            ])
          },
          data: currentData.reductionData
        },
        {
          name: '实际碳排放量',
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            color: colorOrange,
            borderColor: colorOrange,
            borderWidth: 2
          },
          lineStyle: {
            width: 2,
            color: colorOrange
          },
          areaStyle: {
            opacity: 0.3,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: `${colorOrange}66` },
              { offset: 1, color: `${colorOrange}11` }
            ])
          },
          data: currentData.actualData
        }
      ]
    }
  }

  // 初始化图表
  const initChart = () => {
    if (chartRef.value) {
      // 创建图表实例时指定主题为null，使用我们自定义的主题
      chartInstance = echarts.init(chartRef.value, null)
      const initialData = {
        xAxisData: props.xAxisData,
        emissionData: props.emissionData,
        reductionData: props.reductionData,
        actualData: props.actualData
      }
      const option = getThemedChartOption(initialData)
      chartInstance.setOption(option, true) // 使用true确保完全替换
      window.addEventListener('resize', handleResize)
    }
  }

  // 处理窗口大小变化
  const handleResize = () => {
    if (chartInstance) {
      chartInstance.resize()
    }
  }

  // 监听主题变化
  watch(
    () => settingsStore.systemThemeType,
    (newThemeType) => {
      if (chartInstance && chartRef.value) {
        // 主题变化时，获取当前数据并重建图表
        let currentChartData = {
          xAxisData: [],
          emissionData: [],
          reductionData: []
        }
        try {
          const currentOption = chartInstance.getOption()
          if (
            currentOption &&
            currentOption.xAxis &&
            currentOption.xAxis[0] &&
            currentOption.series
          ) {
            currentChartData.xAxisData = currentOption.xAxis[0].data || props.xAxisData
            currentChartData.emissionData = currentOption.series[0]
              ? currentOption.series[0].data
              : props.emissionData
            currentChartData.reductionData = currentOption.series[1]
              ? currentOption.series[1].data
              : props.reductionData
          } else {
            // 如果无法从实例获取，则回退到props
            currentChartData.xAxisData = props.xAxisData
            currentChartData.emissionData = props.emissionData
            currentChartData.reductionData = props.reductionData
          }
        } catch (e) {
          console.error('Error getting chart option for theming:', e)
          // 发生错误时，回退到props
          currentChartData.xAxisData = props.xAxisData
          currentChartData.emissionData = props.emissionData
          currentChartData.reductionData = props.reductionData
        }

        // 销毁并重新创建图表
        chartInstance.dispose()
        chartInstance = echarts.init(chartRef.value, null)

        // 使用新主题重建图表
        const option = getThemedChartOption(currentChartData)
        chartInstance.setOption(option, true) // true 表示不合并，完全替换
      }
    },
    { immediate: false } // 初始化时由 initChart 处理主题
  )

  // 组件挂载后初始化图表
  onMounted(() => {
    nextTick(() => {
      // 先初始化图表
      initChart()
    })
  })

  // 组件卸载前清理资源
  onUnmounted(() => {
    if (chartInstance) {
      chartInstance.dispose()
      chartInstance = null
    }
    window.removeEventListener('resize', handleResize)
  })

  // 暴露方法给父组件
  defineExpose({
    updateChart,
    handleTimeUnitChange,
    finishLoading
  })
</script>

<style lang="scss" scoped>
  .chart-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: var(--art-main-bg-color); // 使用主题背景色
    border-radius: 4px;
    box-shadow: var(--art-root-card-box-shadow); // 使用主题阴影

    .chart-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      border-bottom: 1px solid var(--art-border-color); // 使用主题边框颜色

      .chart-title {
        display: flex;
        align-items: center;
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--art-text-gray-800); // 使用主题文字颜色

        .el-icon {
          margin-right: 8px;
          font-size: 18px;
        }
      }

      .time-filter {
        display: flex;
        align-items: center;
      }
    }

    .chart-body {
      position: relative;
      padding: 16px;

      .chart-container {
        width: 100%;
        height: 350px;
      }

      .loading-mask {
        position: absolute;
        inset: 0;
        z-index: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: rgba(var(--art-main-bg-color-rgb), 0.7); // 使用主题背景色带透明度

        .loading-icon {
          font-size: 24px;
          color: rgb(var(--art-primary)); // 使用主题主色
          animation: loading-rotate 2s linear infinite;
        }

        .loading-text {
          margin-top: 8px;
          font-size: 14px;
          color: var(--art-text-gray-600); // 使用主题文字颜色
        }
      }

      .empty-data {
        position: absolute;
        inset: 0;
        z-index: 1;
        display: flex;
        align-items: center;
        justify-content: center;

        .empty-desc {
          margin: 8px 0 0;
          color: var(--art-text-gray-500); // 使用主题文字颜色
        }

        .empty-tip {
          margin: 4px 0 0;
          font-size: 12px;
          color: var(--art-text-gray-400); // 使用主题文字颜色
        }
      }
    }
  }

  @keyframes loading-rotate {
    0% {
      transform: rotate(0);
    }

    100% {
      transform: rotate(360deg);
    }
  }
</style>
