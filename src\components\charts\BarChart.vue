<template>
  <div ref="chartRef" :style="{ width: '100%', height: height }"></div>
</template>

<script setup>
  import { ref, onMounted, watch, onUnmounted, nextTick } from 'vue'
  import * as echarts from 'echarts'

  const props = defineProps({
    data: {
      type: Array,
      required: true,
      default: () => []
    },
    height: {
      type: String,
      default: '400px'
    },
    title: {
      type: String,
      default: ''
    }
  })

  const chartRef = ref(null)
  let chart = null

  const initChart = () => {
    if (!chartRef.value) return

    try {
      // Dispose existing chart instance if it exists
      if (chart) {
        chart.dispose()
      }

      // Initialize new chart
      chart = echarts.init(chartRef.value)

      // Check if data is valid
      if (!props.data || props.data.length === 0) {
        console.warn('柱状图数据为空')
        return
      }

      // Extract data for chart
      const categories = props.data.map((item) => item.name)
      const values = props.data.map((item) => item.value)

      // Chart options
      const option = {
        title: {
          text: props.title,
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: categories,
          axisLabel: {
            interval: 0,
            rotate: categories.length > 5 ? 30 : 0
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '能耗值',
            type: 'bar',
            barWidth: '60%',
            data: values,
            itemStyle: {
              color: function (params) {
                // Generate different colors for different bars
                const colorList = [
                  '#5470c6',
                  '#91cc75',
                  '#fac858',
                  '#ee6666',
                  '#73c0de',
                  '#3ba272',
                  '#fc8452',
                  '#9a60b4'
                ]
                return colorList[params.dataIndex % colorList.length]
              }
            },
            label: {
              show: true,
              position: 'top'
            }
          }
        ]
      }

      // Set chart options and render
      chart.setOption(option)
    } catch (error) {
      console.error('初始化柱状图失败:', error)
    }
  }

  // Initialize chart when component is mounted
  onMounted(() => {
    nextTick(() => {
      initChart()
    })
  })

  // Update chart when data changes
  watch(
    () => props.data,
    (newVal) => {
      if (newVal && newVal.length > 0) {
        nextTick(() => {
          initChart()
        })
      }
    },
    { deep: true }
  )

  // Handle window resize
  const handleResize = () => {
    if (chart) {
      chart.resize()
    }
  }

  window.addEventListener('resize', handleResize)

  // Clean up when component is unmounted
  onUnmounted(() => {
    if (chart) {
      chart.dispose()
      chart = null
    }
    window.removeEventListener('resize', handleResize)
  })
</script>

<style scoped>
  /* 确保图表容器有足够的高度 */
</style>
