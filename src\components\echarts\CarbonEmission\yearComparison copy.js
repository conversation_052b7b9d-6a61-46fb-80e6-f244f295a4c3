// 定义月度碳排放环比图表配置
export function getMonthComparisonOption(
  currentMonth,
  lastMonth,
  currentData,
  lastData,
  xAxisData
) {
  return {
    title: {
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function (params) {
        // 使用参数索引获取数据
        const day = params[0].axisValue
        const current = params[0].data.toFixed(2)
        const last = params[1].data.toFixed(2)
        const diff = (params[0].data - params[1].data).toFixed(2)
        const rate = (((params[0].data - params[1].data) / params[1].data) * 100).toFixed(2)

        const sign = diff >= 0 ? '+' : ''
        const rateSymbol = rate >= 0 ? '↑' : '↓'

        return `${day}<br/>
                    ${currentMonth}: ${current} 吨<br/>
                    ${lastMonth}: ${last} 吨<br/>
                    同比变化: ${sign}${diff} 吨<br/>
                    同比增长率: ${sign}${rate}% ${rateSymbol}`
      }
    },
    legend: {
      data: [currentMonth, lastMonth],
      top: '40px'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '80px',
      containLabel: true
    },
    toolbox: {
      feature: {
        saveAsImage: {
          title: '保存为图片'
        },
        dataZoom: {
          title: {
            zoom: '区域缩放',
            back: '区域还原'
          }
        },
        magicType: {
          type: ['line', 'bar'],
          title: {
            line: '切换为折线图',
            bar: '切换为柱状图'
          }
        },
        restore: {
          title: '还原'
        }
      }
    },
    dataZoom: [
      {
        type: 'slider',
        show: true,
        start: 0,
        end: 100
      },
      {
        type: 'inside',
        start: 0,
        end: 100
      }
    ],
    xAxis: [
      {
        type: 'category',
        data: xAxisData,
        axisTick: {
          alignWithLabel: true
        },
        axisLabel: {
          rotate: xAxisData.length > 12 ? 45 : 0
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '碳排放量(kgCO₂)',
        splitLine: {
          lineStyle: {
            type: 'dashed'
          }
        }
      }
    ],
    series: [
      {
        name: currentMonth,
        type: 'bar',
        barWidth: '40%',
        data: currentData,
        itemStyle: {
          color: '#fac858' // 黄色
        },
        markPoint: {
          data: [
            { type: 'max', name: '最大值' },
            { type: 'min', name: '最小值' }
          ]
        }
      }
    ]
  }
}

// 生成模拟数据
export function generateMockData(count) {
  return Array.from(
    { length: count },
    () => Math.floor(Math.random() * 500) + 500 // 生成500-1000范围内的随机数
  )
}

// 根据天数生成X轴标签
export function generateDayLabels(days) {
  return Array.from({ length: days }, (_, i) => `${i + 1}日`)
}

// 根据年月生成日期标签
export function generateMonthLabels() {
  return Array.from({ length: 12 }, (_, i) => `${i + 1}月`)
}

// 根据当前时间获取上一年度或上一月度
export function getPreviousPeriod(date, type = 'month') {
  const currentDate = new Date(date)

  if (type === 'month') {
    // 获取上个月
    currentDate.setMonth(currentDate.getMonth() - 1)
  } else if (type === 'year') {
    // 获取去年同期
    currentDate.setFullYear(currentDate.getFullYear() - 1)
  } else if (type === 'day') {
    // 获取前一天
    currentDate.setDate(currentDate.getDate() - 1)
  }

  return currentDate
}

// 格式化日期为易读字符串
export function formatDateLabel(date, type = 'month') {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()

  if (type === 'year') {
    return `${year}年`
  } else if (type === 'month') {
    return `${year}年${month.toString().padStart(2, '0')}月`
  } else if (type === 'day') {
    return `${year}年${month.toString().padStart(2, '0')}月${day.toString().padStart(2, '0')}日`
  }

  return `${year}年${month.toString().padStart(2, '0')}月`
}
