<template>
  <div ref="chartRef" class="chart-container"></div>
</template>

<script setup>
  import { ref, onMounted, watch, onUnmounted } from 'vue'
  import * as echarts from 'echarts'

  const props = defineProps({
    title: {
      type: String,
      required: true
    },
    data: {
      type: Array,
      required: true
    },
    options: {
      type: Object,
      default: () => ({})
    },
    // 新增：直接传递 x 轴数据
    xAxisData: {
      type: Array,
      default: () => []
    },
    // 新增：直接传递 y 轴数据
    yAxisData: {
      type: Array,
      default: () => []
    },
    // 新增：数据单位
    unit: {
      type: String,
      default: 'kWh'
    }
  })

  const chartRef = ref(null)
  let myChart = null

  // 生成图表配置
  const generateChartOption = () => {
    // 优先使用传递的 xAxisData 和 yAxisData，否则使用 data 数组
    const xData = props.xAxisData.length > 0 ? props.xAxisData : props.data.map((item) => item.date)
    const yData =
      props.yAxisData.length > 0
        ? props.yAxisData
        : props.data.map((item) => parseFloat(item.value))
    const chartUnit = props.unit || (props.data[0] && props.data[0].unit) || 'kWh'

    return {
      title: {
        text: props.title,
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        formatter: function (params) {
          const param = params[0]
          return `${param.name}<br/>${param.seriesName}: ${param.value} ${chartUnit}`
        },
        backgroundColor: 'rgba(50, 50, 50, 0.9)',
        borderColor: '#409EFF',
        borderWidth: 1,
        textStyle: {
          color: '#fff'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '8%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: xData,
        boundaryGap: false,
        axisLine: {
          lineStyle: {
            color: '#E4E7ED'
          }
        },
        axisLabel: {
          color: '#606266',
          fontSize: 12
        }
      },
      yAxis: {
        type: 'value',
        name: chartUnit,
        nameLocation: 'end',
        nameTextStyle: {
          color: '#606266',
          fontSize: 12
        },
        axisLine: {
          lineStyle: {
            color: '#E4E7ED'
          }
        },
        axisLabel: {
          color: '#606266',
          fontSize: 12,
          formatter: (value) => `${value}`
        },
        splitLine: {
          lineStyle: {
            color: '#F2F6FC',
            type: 'dashed'
          }
        }
      },
      series: [
        {
          name: props.title,
          type: 'line',
          data: yData,
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          areaStyle: {
            opacity: 0.1,
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#409EFF' },
                { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
              ]
            }
          },
          lineStyle: {
            width: 3,
            color: '#409EFF'
          },
          itemStyle: {
            color: '#409EFF',
            borderWidth: 2,
            borderColor: '#fff'
          }
        }
      ]
    }
  }

  const initChart = () => {
    if (myChart) {
      myChart.dispose()
    }

    myChart = echarts.init(chartRef.value)
    const options = generateChartOption()
    myChart.setOption(options)
  }

  // 监听数据变化
  watch(
    () => [props.data, props.title, props.options, props.xAxisData, props.yAxisData, props.unit],
    () => {
      initChart()
    },
    { deep: true }
  )

  // 监听窗口大小变化
  const handleResize = () => {
    myChart?.resize()
  }

  onMounted(() => {
    initChart()
    window.addEventListener('resize', handleResize)

    // 监听容器大小变化
    const resizeObserver = new ResizeObserver(() => {
      myChart?.resize()
    })

    if (chartRef.value) {
      resizeObserver.observe(chartRef.value)
    }
  })

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    myChart?.dispose()
  })
</script>

<style scoped>
  .chart-container {
    box-sizing: border-box;
    width: 100%;
    height: 400px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px 0 rgb(0 0 0 / 5%);
  }
</style>
