<template>
  <div class="building-management-container page-content">
    <!-- 查询条件区域 -->
    <div class="filter-section">
      <el-form :model="queryParams" inline>
        <el-form-item label="项目名称:">
          <ProjectSelector
            ref="projectSelectorRef"
            v-model="selectedProjectId"
            placeholder="请选择项目"
            @change="handleProjectChange"
            @loaded="handleProjectListLoaded"
          />
        </el-form-item>

        <el-form-item label="楼栋名称:">
          <el-input v-model="queryParams.buildingName" placeholder="请输入楼栋名称" clearable />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch" :icon="Search">查询</el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button type="success" @click="handleExport" :loading="exporting" :icon="Download"
            >导出数据</el-button
          >
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <div class="table-operation">
        <el-button type="primary" @click="handleAdd" :icon="Plus">新增楼栋</el-button>
      </div>

      <ArtTable
        :data="tableData"
        :loading="loading"
        row-key="id"
        :border="false"
        :highlight-current-row="false"
        :total="totalCount"
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        class="building-table"
      >
        <el-table-column prop="index" label="序号" align="center" width="80" />
        <el-table-column prop="name" label="楼栋名称" align="center">
          <template #default="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="floorCount" label="楼层数量" align="center">
          <template #default="scope">
            <span>{{ scope.row.zsFloorList?.length || 0 }}层</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" align="center">
          <template #default="scope">
            <span>{{ formatDate(scope.row.createTime) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="180" align="center">
          <template #default="scope">
            <div class="operation-btns">
              <el-button
                link
                size="small"
                @click="handleDetail(scope.row)"
                title="查看"
                class="operation-btn view-btn"
              >
                <el-icon><Search /></el-icon>
              </el-button>
              <el-button
                link
                size="small"
                @click="handleEdit(scope.row)"
                title="编辑"
                class="operation-btn edit-btn"
              >
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button
                link
                size="small"
                @click="handleDelete(scope.row)"
                title="删除"
                class="operation-btn delete-btn"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
      </ArtTable>
    </div>

    <!-- 新增/编辑楼栋对话框 -->
    <el-dialog
      :title="dialogType === 'add' ? '新增楼栋' : '编辑楼栋'"
      v-model="dialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="buildingForm"
        :rules="buildingRules"
        ref="buildingFormRef"
        label-width="100px"
        style="max-height: 500px; overflow-y: auto"
      >
        <el-form-item label="楼栋名称" prop="name">
          <el-input v-model="buildingForm.name" placeholder="请输入楼栋名称" />
        </el-form-item>
        <el-form-item label="楼层信息" prop="zsFloorList">
          <div class="floor-list">
            <div v-for="(floor, index) in buildingForm.zsFloorList" :key="index" class="floor-item">
              <el-input
                v-model="floor.name"
                placeholder="请输入楼层名称"
                style="width: 200px; margin-right: 10px"
              />
              <el-button
                type="danger"
                size="small"
                @click="removeFloor(index)"
                :disabled="buildingForm.zsFloorList.length <= 1"
              >
                删除
              </el-button>
            </div>
            <el-button type="primary" size="small" @click="addFloor" style="margin-top: 10px">
              添加楼层
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitBuildingForm" :loading="submitLoading"
            >确定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted, nextTick } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Search, Edit, Delete, Plus, Download } from '@element-plus/icons-vue'
  import ProjectSelector from '@/components/ProjectSelector.vue'
  import {
    getBuildingList,
    createBuilding,
    updateBuilding,
    deleteBuilding
  } from '@/api/buildingApi'

  // 项目选择相关数据
  const selectedProjectId = ref(null) // 初始为空，等待获取项目列表后设置第一个
  const selectedProjectInfo = ref(null)
  const projectSelectorRef = ref()

  // 查询参数
  const queryParams = reactive({
    buildingName: '',
    pageNum: 1,
    pageSize: 10
  })

  // 表格数据和分页相关
  const tableData = ref([])
  const loading = ref(false)
  const exporting = ref(false)
  const totalCount = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(10)

  // 对话框相关
  const dialogVisible = ref(false)
  const dialogType = ref('add') // 'add' 或 'edit'
  const buildingFormRef = ref(null)
  const submitLoading = ref(false)

  // 楼栋表单
  const buildingForm = reactive({
    id: null,
    name: '',
    zsFloorList: [{ name: '' }]
  })

  // 表单验证规则
  const buildingRules = {
    name: [
      { required: true, message: '请输入楼栋名称', trigger: 'blur' },
      { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
    ],
    zsFloorList: [
      {
        validator: (_, value, callback) => {
          if (!value || value.length === 0) {
            callback(new Error('至少需要一个楼层'))
          } else {
            const hasEmptyFloor = value.some((floor) => !floor.name || floor.name.trim() === '')
            if (hasEmptyFloor) {
              callback(new Error('楼层名称不能为空'))
            } else {
              callback()
            }
          }
        },
        trigger: 'blur'
      }
    ]
  }

  // 格式化日期
  const formatDate = (dateString) => {
    if (!dateString) return '-'
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  }

  // 获取表格数据
  const fetchData = async () => {
    if (!selectedProjectId.value) {
      console.log('没有选择项目，不发请求')
      return
    }

    loading.value = true
    try {
      const response = await getBuildingList(selectedProjectId.value)

      if (response.code === 200) {
        let filteredData = response.data || []

        // 根据楼栋名称筛选
        if (queryParams.buildingName) {
          filteredData = filteredData.filter((item) => item.name.includes(queryParams.buildingName))
        }

        // 添加序号
        tableData.value = filteredData.map((item, index) => ({
          ...item,
          index: index + 1
        }))
        totalCount.value = filteredData.length
      } else {
        ElMessage.error(response.msg || '获取楼栋列表失败')
        tableData.value = []
        totalCount.value = 0
      }
    } catch (error) {
      console.error('获取楼栋列表失败:', error)
      ElMessage.error('获取楼栋列表失败，请稍后重试')
      tableData.value = []
      totalCount.value = 0
    } finally {
      loading.value = false
    }
  }

  // 导出数据
  const handleExport = async () => {
    if (totalCount.value === 0) {
      ElMessage.warning('暂无数据可导出')
      return
    }

    ElMessageBox.confirm('确认导出所有数据?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        exporting.value = true
        try {
          // 模拟导出延迟
          await new Promise((resolve) => setTimeout(resolve, 1000))
          ElMessage.success('导出成功')
        } catch (error) {
          console.error('导出失败:', error)
          ElMessage.error('导出失败')
        } finally {
          exporting.value = false
        }
      })
      .catch(() => {})
  }

  // 处理项目变化
  const handleProjectChange = (projectInfo) => {
    selectedProjectInfo.value = projectInfo
    console.log('选中的项目:', projectInfo)
    // 注意：这里不自动发请求，只有点击查询按钮时才发请求
  }

  // 监听项目选择器组件的项目列表加载完成
  const handleProjectListLoaded = (projectList) => {
    // 当项目列表加载完成后，自动选择第一个项目并发请求
    if (projectList && projectList.length > 0) {
      const firstProject = projectList[0]
      selectedProjectId.value = firstProject.id
      selectedProjectInfo.value = firstProject
      console.log('自动选择第一个项目:', firstProject)
      // 自动发请求获取数据
      nextTick(() => {
        fetchData()
      })
    }
  }

  // 重置表单
  const resetForm = () => {
    // 重置为第一个项目
    if (projectSelectorRef.value && projectSelectorRef.value.projectList?.length > 0) {
      const firstProject = projectSelectorRef.value.projectList[0]
      selectedProjectId.value = firstProject.id
      selectedProjectInfo.value = firstProject
    } else {
      selectedProjectId.value = null
      selectedProjectInfo.value = null
    }

    Object.keys(queryParams).forEach((key) => {
      if (key !== 'pageNum' && key !== 'pageSize') {
        queryParams[key] = ''
      }
    })
    queryParams.pageNum = 1
    fetchData()
  }

  // 搜索按钮点击事件
  const handleSearch = () => {
    // 如果没有选择项目，不发请求
    if (!selectedProjectId.value) {
      console.log('没有选择项目，不发请求')
      return
    }

    console.log('查询参数:', {
      projectId: selectedProjectId.value,
      ...queryParams
    })
    queryParams.pageNum = 1
    fetchData()
  }

  // 页码改变事件
  const handleCurrentChange = (val) => {
    queryParams.pageNum = val
    fetchData()
  }

  // 分页大小改变事件
  const handleSizeChange = (val) => {
    pageSize.value = val
    queryParams.pageSize = val
    fetchData()
  }

  // 添加楼栋
  const handleAdd = () => {
    dialogType.value = 'add'
    resetBuildingForm()
    dialogVisible.value = true
  }

  // 编辑楼栋
  const handleEdit = (row) => {
    dialogType.value = 'edit'
    resetBuildingForm()
    // 填充表单数据
    buildingForm.id = row.id
    buildingForm.name = row.name
    buildingForm.zsFloorList =
      row.zsFloorList && row.zsFloorList.length > 0
        ? row.zsFloorList.map((floor) => ({ id: floor.id, name: floor.name }))
        : [{ name: '' }]
    dialogVisible.value = true
  }

  // 重置楼栋表单
  const resetBuildingForm = () => {
    if (buildingFormRef.value) {
      buildingFormRef.value.resetFields()
    }
    buildingForm.id = null
    buildingForm.name = ''
    buildingForm.zsFloorList = [{ name: '' }]
  }

  // 添加楼层
  const addFloor = () => {
    buildingForm.zsFloorList.push({ name: '' })
  }

  // 删除楼层
  const removeFloor = (index) => {
    if (buildingForm.zsFloorList.length > 1) {
      buildingForm.zsFloorList.splice(index, 1)
    }
  }

  // 提交楼栋表单
  const submitBuildingForm = () => {
    if (!buildingFormRef.value) return

    buildingFormRef.value.validate(async (valid) => {
      if (valid) {
        if (!selectedProjectId.value) {
          ElMessage.error('请先选择项目')
          return
        }

        submitLoading.value = true
        try {
          const formData = {
            name: buildingForm.name,
            zsFloorList: buildingForm.zsFloorList.filter((floor) => floor.name.trim() !== '')
          }

          if (dialogType.value === 'add') {
            // 新增操作
            const requestData = {
              projectId: selectedProjectId.value,
              ...formData
            }
            const response = await createBuilding(requestData)
            if (response.code === 200) {
              ElMessage.success('新增楼栋成功')
              dialogVisible.value = false
              fetchData()
            } else {
              ElMessage.error(response.msg || '新增楼栋失败')
            }
          } else {
            // 编辑操作
            const requestData = {
              id: buildingForm.id,
              ...formData
            }
            const response = await updateBuilding(requestData)
            if (response.code === 200) {
              ElMessage.success('编辑楼栋成功')
              dialogVisible.value = false
              fetchData()
            } else {
              ElMessage.error(response.msg || '编辑楼栋失败')
            }
          }
        } catch (error) {
          console.error(dialogType.value === 'add' ? '新增楼栋失败:' : '编辑楼栋失败:', error)
          ElMessage.error(dialogType.value === 'add' ? '新增楼栋失败' : '编辑楼栋失败')
        } finally {
          submitLoading.value = false
        }
      } else {
        ElMessage.warning('请填写完整的表单信息')
        return false
      }
    })
  }

  // 详情按钮点击事件
  const handleDetail = (row) => {
    const floorNames = row.zsFloorList?.map((floor) => floor.name).join('、') || '无'
    ElMessage.info(`楼栋名称：${row.name}，楼层信息：${floorNames}`)
    // 这里可以实现详情查看逻辑，如弹出对话框或跳转到详情页面
  }

  // 删除单个楼栋
  const handleDelete = (row) => {
    ElMessageBox.confirm(`确认删除楼栋 "${row.name}" 吗？删除后将无法恢复！`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        try {
          const response = await deleteBuilding(row.id)
          if (response.code === 200) {
            ElMessage.success(`删除楼栋 "${row.name}" 成功`)
            fetchData()
          } else {
            ElMessage.error(response.msg || '删除楼栋失败')
          }
        } catch (error) {
          console.error('删除楼栋失败:', error)
          ElMessage.error('删除楼栋失败')
        }
      })
      .catch(() => {})
  }

  // 组件挂载时初始化数据
  onMounted(() => {
    // 初始化时不需要立即获取数据，等待项目选择器加载完成后自动获取
  })
</script>

<style lang="scss" scoped>
  .building-management-container {
    padding: 20px;

    // 筛选条件区域样式
    .filter-section {
      padding: 20px;
      margin-bottom: 20px;
      background-color: var(--art-main-bg-color);
      border: 1px solid var(--art-border-color);
      border-radius: 4px;
      box-shadow: var(--art-box-shadow-xs);

      .el-form {
        display: flex;
        flex-wrap: wrap;

        .el-form-item {
          margin-right: 10px;
          margin-bottom: 10px;

          // 按钮间距
          .el-button + .el-button {
            margin-left: 10px;
          }
        }
      }

      :deep(.el-select) {
        width: 180px;
      }

      :deep(.el-input) {
        width: 180px;
      }
    }

    // 表格区域样式
    .table-section {
      padding: 20px;
      background-color: var(--art-main-bg-color);
      border: 1px solid var(--art-border-color);
      border-radius: 4px;
      box-shadow: var(--art-box-shadow-xs);

      // 表格操作区域
      .table-operation {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 15px;

        .el-button {
          margin-right: 10px;
        }
      }

      // 确保ArtTable组件有足够的显示空间
      :deep(.art-table) {
        height: auto;
        min-height: 400px;
      }

      // 自定义操作按钮样式
      .operation-btns {
        display: flex;
        gap: 12px;
        justify-content: center;
      }

      .operation-btn {
        width: 32px !important;
        height: 32px !important;
        padding: 6px !important;
        margin: 0 !important;
        line-height: 1 !important; /* 确保图标垂直居中 */
        border: none !important;
        border-radius: 4px !important; /* 方形边框 */
        box-shadow: 0 2px 4px rgb(0 0 0 / 10%) !important; /* 添加阴影效果 */
        transition: all 0.3s ease !important; /* 添加过渡效果 */
      }

      .operation-btn:hover {
        box-shadow: 0 4px 8px rgb(0 0 0 / 15%) !important; /* 悬停时增强阴影 */
        transform: translateY(-2px) !important; /* 悬停时上移效果 */
      }

      .view-btn {
        background-color: #e6f7ff !important; /* 浅蓝色背景 */
      }

      .view-btn .el-icon {
        font-size: 16px;
        color: #409eff !important; /* 蓝色图标 */
      }

      .edit-btn {
        background-color: #e6f7ff !important; /* 浅蓝色背景 */
      }

      .edit-btn .el-icon {
        font-size: 16px;
        color: #409eff !important; /* 蓝色图标 */
      }

      .delete-btn {
        background-color: #fff1f0 !important; /* 浅红色背景 */
      }

      .delete-btn .el-icon {
        font-size: 16px;
        color: #f56c6c !important; /* 红色图标 */
      }

      // 修复el-table__empty-block高度不断增长问题
      .building-table :deep(.el-table__empty-block) {
        height: auto !important;
        min-height: 60px;
        max-height: 400px;
        overflow: hidden;
      }

      // 确保空表格状态下的布局稳定
      .building-table :deep(.el-table__body-wrapper) {
        height: auto !important;
        min-height: 200px;
      }
    }

    // 楼层列表样式
    .floor-list {
      .floor-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
      }
    }
  }
</style>
