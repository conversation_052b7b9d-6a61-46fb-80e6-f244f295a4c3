import { ProjectInfo } from '@/api/projectApi'

// 项目图片资源
import image1 from '@/assets/img/demo-project-overview/image1.png'
import image2 from '@/assets/img/demo-project-overview/image2.png'
import image3 from '@/assets/img/demo-project-overview/image3.png'
import image4 from '@/assets/img/demo-project-overview/image4.png'
import image5 from '@/assets/img/demo-project-overview/image5.png'
import image6 from '@/assets/img/demo-project-overview/image6.png'
import image7 from '@/assets/img/demo-project-overview/image7.png'
import image8 from '@/assets/img/demo-project-overview/image8.png'

// 图片数组
const projectImages = [image1, image2, image3, image4, image5, image6, image7, image8]

/**
 * 将接口返回的项目数据映射为组件需要的格式
 * @param apiProject 接口返回的项目数据
 * @returns 组件需要的项目数据格式
 */
export function mapProjectData(apiProject: ProjectInfo) {
  // 处理占地面积信息 - 优先使用总建筑面积，如果没有则使用用地面积
  const scaleValue = apiProject.projectScale || apiProject.projectArea || ''

  // 构建完整的规模描述用于详情显示
  const scaleDescription = apiProject.projectArea
    ? `用地面积${apiProject.projectArea}万㎡，总建筑面积${apiProject.projectScale}万㎡`
    : `总建筑面积${apiProject.projectScale}万㎡`

  // 处理项目亮点
  const highlights =
    apiProject.zsProjectFeatureList?.map((feature) => feature.featureContent).join('\n') || ''

  // 处理项目性能指标
  const performance =
    apiProject.zsProjectPerformanceList?.map((perf) => perf.targetValue).join('\n') || ''

  // 处理项目图片 - 根据项目ID循环使用图片资源
  const imageIndex = (apiProject.id - 1) % projectImages.length
  const image = projectImages[imageIndex]

  // 判断是否已接入 - 根据项目状态判断
  const isConnected = apiProject.projectStatus === '1'

  return {
    id: apiProject.id,
    code: apiProject.code, // 项目编号
    name: apiProject.name,
    image: image,
    scale: scaleValue, // 使用纯数字值用于卡片显示
    scaleDescription: scaleDescription, // 完整描述用于详情显示
    function: apiProject.projectFunction,
    location: apiProject.addr,
    constructor: apiProject.orgComp,
    contractor: apiProject.sgComp,
    designer: apiProject.designComp,
    isConnected: isConnected,
    areaId: apiProject.areaId, // 区域ID
    energyId: apiProject.energyId, // 能耗ID，用于实时能耗接口查询
    details: {
      designConcept: apiProject.designRemark,
      highlights: highlights,
      performance: performance
    },
    // 地理位置信息
    coordinates: {
      longitude: parseFloat(apiProject.longitude) || 0,
      latitude: parseFloat(apiProject.latitude) || 0
    },
    // 原始数据，用于详情页面
    rawData: apiProject
  }
}

/**
 * 批量映射项目数据
 * @param apiProjects 接口返回的项目数组
 * @returns 映射后的项目数组
 */
export function mapProjectList(apiProjects: ProjectInfo[]) {
  return apiProjects.map((project) => mapProjectData(project))
}
