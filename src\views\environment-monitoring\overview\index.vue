<template>
  <div class="monitoring-overview-modern">
    <!-- 简化的头部筛选区域 -->
    <div class="content-section">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card class="filter-card">
            <template #header>
              <div class="card-header">
                <span>环境监测概览</span>
                <div class="filter-controls">
                  <ProjectBuildingSelector
                    ref="projectBuildingSelectorRef"
                    v-model="projectBuildingSelection"
                    @change="handleProjectBuildingChange"
                    @project-loaded="handleProjectListLoaded"
                  />
                  <el-button type="primary" @click="handleSearch" :disabled="queryButtonDisabled"
                    >查询</el-button
                  >
                  <el-button @click="handleReset">重置</el-button>
                </div>
              </div>
            </template>
            <div class="status-overview">
              <div class="overview-grid" :class="gridLayoutClass">
                <div
                  v-for="(device, index) in deviceStatusList"
                  :key="device.id"
                  class="status-card-modern"
                  :class="`status-${device.status}`"
                  :style="{ animationDelay: `${index * 0.1}s` }"
                >
                  <div class="card-content">
                    <div class="device-header">
                      <div class="device-icon-modern">
                        <div class="icon-bg" :class="`bg-${device.status}`"></div>
                        <i class="iconfont-sys" v-html="device.icon"></i>
                      </div>
                      <div class="device-meta">
                        <h3>{{ device.name }}</h3>
                        <div class="status-indicator" :class="device.status">
                          <div class="indicator-dot"></div>
                          <span>{{ device.statusText }}</span>
                        </div>
                      </div>
                    </div>

                    <div class="device-value-section">
                      <div class="primary-value">
                        <span class="value">{{ device.value }}</span>
                        <span class="unit">{{ device.unit }}</span>
                      </div>
                    </div>

                    <div class="device-stats-modern">
                      <div class="stat-card total">
                        <div class="stat-icon">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            fill="currentColor"
                          >
                            <path
                              d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"
                            />
                          </svg>
                        </div>
                        <div class="stat-info">
                          <span class="stat-value">{{ device.value }}</span>
                          <span class="stat-label">设备总数</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 现代化设备状态概览 -->

    <!-- 现代化监测仪表盘矩阵 -->
    <div class="dashboard-matrix">
      <div class="matrix-grid">
        <!-- 舒适度监测 -->
        <div class="monitoring-card comfort-card">
          <div class="card-header-modern">
            <div class="header-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path
                  d="M15 13V5c0-1.66-1.34-3-3-3S9 3.34 9 5v8c-1.21.91-2 2.37-2 4 0 2.76 2.24 5 5 5s5-2.24 5-5c0-1.63-.79-3.09-2-4zm-4-2V5c0-.55.45-1 1-1s1 .45 1 1v6h-2z"
                />
              </svg>
            </div>
            <div class="header-content">
              <h3>舒适度监测</h3>
              <p>Temperature & Humidity</p>
            </div>
            <div class="header-status" :class="comfortData.level">
              <div class="status-dot"></div>
              <span>{{ comfortData.level }}</span>
            </div>
          </div>
          <div class="card-body-modern">
            <div class="gauge-section">
              <div class="gauge-container-modern" ref="comfortGaugeRef"></div>
              <div class="gauge-overlay">
                <div class="gauge-center-value">
                  <span class="value">{{ comfortData.level || '暂无' }}</span>
                  <span class="label">舒适度</span>
                </div>
              </div>
            </div>
            <div class="metrics-grid">
              <div class="metric-item">
                <div class="metric-icon temp">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                    <path
                      d="M15 13V5c0-1.66-1.34-3-3-3S9 3.34 9 5v8c-1.21.91-2 2.37-2 4 0 2.76 2.24 5 5 5s5-2.24 5-5c0-1.63-.79-3.09-2-4zm-4-2V5c0-.55.45-1 1-1s1 .45 1 1v6h-2z"
                    />
                  </svg>
                </div>
                <div class="metric-data">
                  <span class="metric-value">{{ comfortData.temperature }}</span>
                  <span class="metric-unit">℃</span>
                  <span class="metric-label">温度</span>
                </div>
              </div>
              <div class="metric-item">
                <div class="metric-icon humidity">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                    <path
                      d="M12 2c-5.33 4.55-8 8.48-8 11.8 0 4.98 3.8 8.2 8 8.2s8-3.22 8-8.2C20 10.48 17.33 6.55 12 2z"
                    />
                  </svg>
                </div>
                <div class="metric-data">
                  <span class="metric-value">{{ comfortData.humidity }}</span>
                  <span class="metric-unit">%</span>
                  <span class="metric-label">湿度</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 有害气体监测 -->
        <div class="monitoring-card gas-card">
          <div class="card-header-modern">
            <div class="header-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path
                  d="M18.5 12c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM5 9v6h4l5 5V4L9 9H5z"
                />
              </svg>
            </div>
            <div class="header-content">
              <h3>有害气体监测</h3>
              <p>Air Quality Index</p>
            </div>
            <div class="header-status" :class="gasData.level">
              <div class="status-dot"></div>
              <span>{{ gasData.level }}</span>
            </div>
          </div>
          <div class="card-body-modern">
            <div class="gauge-section">
              <div class="gauge-container-modern" ref="gasGaugeRef"></div>
              <div class="gauge-overlay">
                <div class="gauge-center-value">
                  <span class="value">{{ gasData.level || '暂无' }}</span>
                  <span class="label">空气质量</span>
                </div>
              </div>
            </div>
            <div class="metrics-grid">
              <div class="metric-item">
                <div class="metric-icon tvoc">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                    <path
                      d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"
                    />
                  </svg>
                </div>
                <div class="metric-data">
                  <span class="metric-value">{{ gasData.tvoc }}</span>
                  <span class="metric-unit">mg/m³</span>
                  <span class="metric-label">TVOC</span>
                </div>
              </div>
              <div class="metric-item">
                <div class="metric-icon co2">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                    <path
                      d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"
                    />
                  </svg>
                </div>
                <div class="metric-data">
                  <span class="metric-value">{{ gasData.co2 }}</span>
                  <span class="metric-unit">ppm</span>
                  <span class="metric-label">CO₂</span>
                </div>
              </div>
              <div class="metric-item">
                <div class="metric-icon formaldehyde">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                    <path
                      d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                    />
                  </svg>
                </div>
                <div class="metric-data">
                  <span class="metric-value">{{ gasData.formaldehyde }}</span>
                  <span class="metric-unit">mg/m³</span>
                  <span class="metric-label">甲醛</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 颗粒物监测 -->
        <div class="monitoring-card particle-card">
          <div class="card-header-modern">
            <div class="header-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path
                  d="M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96z"
                />
              </svg>
            </div>
            <div class="header-content">
              <h3>颗粒物监测</h3>
              <p>Particle Matter</p>
            </div>
            <div class="header-status" :class="particleData.level">
              <div class="status-dot"></div>
              <span>{{ particleData.level }}</span>
            </div>
          </div>
          <div class="card-body-modern">
            <div class="gauge-section">
              <div class="gauge-container-modern" ref="particleGaugeRef"></div>
              <div class="gauge-overlay">
                <div class="gauge-center-value">
                  <span class="value">{{ particleData.level || '暂无' }}</span>
                  <span class="label">颗粒物</span>
                </div>
              </div>
            </div>
            <div class="metrics-grid">
              <div class="metric-item">
                <div class="metric-icon pm25">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                    <path
                      d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"
                    />
                  </svg>
                </div>
                <div class="metric-data">
                  <span class="metric-value">{{ particleData.pm25 }}</span>
                  <span class="metric-unit">μg/m³</span>
                  <span class="metric-label">PM2.5</span>
                </div>
              </div>
              <div class="metric-item">
                <div class="metric-icon pm10">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                    <path
                      d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"
                    />
                  </svg>
                </div>
                <div class="metric-data">
                  <span class="metric-value">{{ particleData.pm10 }}</span>
                  <span class="metric-unit">μg/m³</span>
                  <span class="metric-label">PM10</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 噪声监测 -->
        <div class="monitoring-card noise-card">
          <div class="card-header-modern">
            <div class="header-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path
                  d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"
                />
              </svg>
            </div>
            <div class="header-content">
              <h3>噪声监测</h3>
              <p>Sound Level</p>
            </div>
            <div class="header-status" :class="noiseData.level">
              <div class="status-dot"></div>
              <span>{{ noiseData.level }}</span>
            </div>
          </div>
          <div class="card-body-modern">
            <div class="gauge-section">
              <div class="gauge-container-modern" ref="noiseGaugeRef"></div>
              <div class="gauge-overlay">
                <div class="gauge-center-value">
                  <span class="value">{{ noiseData.level || '暂无' }}</span>
                  <span class="label">噪声</span>
                </div>
              </div>
            </div>
            <div class="metrics-grid">
              <div class="metric-item full-width">
                <div class="metric-icon noise">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                    <path
                      d="M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z"
                    />
                  </svg>
                </div>
                <div class="metric-data">
                  <span class="metric-value">{{ noiseData.currentNoise }}</span>
                  <span class="metric-unit">dB</span>
                  <span class="metric-label">当前噪声</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 水质监测 - 扩展卡片 -->
        <div class="monitoring-card water-card wide-card">
          <div class="card-header-modern">
            <div class="header-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path
                  d="M12 2c-5.33 4.55-8 8.48-8 11.8 0 4.98 3.8 8.2 8 8.2s8-3.22 8-8.2C20 10.48 17.33 6.55 12 2z"
                />
              </svg>
            </div>
            <div class="header-content">
              <h3>水质监测</h3>
              <p>Water Quality Analysis</p>
            </div>
            <div class="header-status" :class="waterData.level">
              <div class="status-dot"></div>
              <span>{{ waterData.level }}</span>
            </div>
          </div>
          <div class="card-body-modern">
            <div class="water-content">
              <div class="gauge-section">
                <div class="gauge-container-modern" ref="waterGaugeRef"></div>
                <div class="gauge-overlay">
                  <div class="gauge-center-value">
                    <span class="value">{{ waterData.level || '暂无' }}</span>
                    <span class="label">水质</span>
                  </div>
                </div>
              </div>
              <div class="water-metrics">
                <div class="water-metric">
                  <div class="metric-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                      <path
                        d="M15 13V5c0-1.66-1.34-3-3-3S9 3.34 9 5v8c-1.21.91-2 2.37-2 4 0 2.76 2.24 5 5 5s5-2.24 5-5c0-1.63-.79-3.09-2-4zm-4-2V5c0-.55.45-1 1-1s1 .45 1 1v6h-2z"
                      />
                    </svg>
                  </div>
                  <div class="metric-info">
                    <span class="metric-value">{{ waterData.temperature }}</span>
                    <span class="metric-unit">℃</span>
                    <span class="metric-label">水温</span>
                  </div>
                </div>
                <div class="water-metric">
                  <div class="metric-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                      <path
                        d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                      />
                    </svg>
                  </div>
                  <div class="metric-info">
                    <span class="metric-value">{{ waterData.ph }}</span>
                    <span class="metric-label">pH值</span>
                  </div>
                </div>
                <div class="water-metric">
                  <div class="metric-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                      <path
                        d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"
                      />
                    </svg>
                  </div>
                  <div class="metric-info">
                    <span class="metric-value">{{ waterData.do }}</span>
                    <span class="metric-unit">mg/L</span>
                    <span class="metric-label">溶解氧</span>
                  </div>
                </div>
                <div class="water-metric">
                  <div class="metric-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                      <path
                        d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"
                      />
                    </svg>
                  </div>
                  <div class="metric-info">
                    <span class="metric-value">{{ waterData.cod }}</span>
                    <span class="metric-unit">mg/L</span>
                    <span class="metric-label">COD</span>
                  </div>
                </div>
                <div class="water-metric">
                  <div class="metric-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                      <path
                        d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                      />
                    </svg>
                  </div>
                  <div class="metric-info">
                    <span class="metric-value">{{ waterData.nh3n }}</span>
                    <span class="metric-unit">mg/L</span>
                    <span class="metric-label">氨氮</span>
                  </div>
                </div>
                <div class="water-metric">
                  <div class="metric-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                      <path
                        d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"
                      />
                    </svg>
                  </div>
                  <div class="metric-info">
                    <span class="metric-value">{{ waterData.waterLevel }}</span>
                    <span class="metric-unit">m</span>
                    <span class="metric-label">水位</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 室外环境监测 - 扩展卡片 -->
        <div class="monitoring-card outdoor-card wide-card">
          <div class="card-header-modern">
            <div class="header-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path
                  d="M6.76 4.84l-1.8-1.79-1.41 1.41 1.79 1.79 1.42-1.41zM4 10.5H1v2h3v-2zm9-9.95h-2V3.5h2V.55zm7.45 3.91l-1.41-1.41-1.79 1.79 1.41 1.41 1.79-1.79zm-3.21 13.7l1.79 1.8 1.41-1.41-1.8-1.79-1.4 1.4zM20 10.5v2h3v-2h-3zm-8-5c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm-1 16.95h2V19.5h-2v2.95zm-7.45-3.91l1.41 1.41 1.79-1.8-1.41-1.41-1.79 1.8z"
                />
              </svg>
            </div>
            <div class="header-content">
              <h3>室外环境监测</h3>
              <p>Outdoor Environment</p>
            </div>
            <div class="header-status" :class="outdoorData.level">
              <div class="status-dot"></div>
              <span>{{ outdoorData.level }}</span>
            </div>
          </div>
          <div class="card-body-modern">
            <div class="outdoor-content">
              <div class="gauge-section">
                <div class="gauge-container-modern" ref="outdoorGaugeRef"></div>
                <div class="gauge-overlay">
                  <div class="gauge-center-value">
                    <span class="value">{{ outdoorData.level || '暂无' }}</span>
                    <span class="label">环境质量</span>
                  </div>
                </div>
              </div>
              <div class="outdoor-metrics">
                <div class="outdoor-metric">
                  <div class="metric-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                      <path
                        d="M15 13V5c0-1.66-1.34-3-3-3S9 3.34 9 5v8c-1.21.91-2 2.37-2 4 0 2.76 2.24 5 5 5s5-2.24 5-5c0-1.63-.79-3.09-2-4zm-4-2V5c0-.55.45-1 1-1s1 .45 1 1v6h-2z"
                      />
                    </svg>
                  </div>
                  <div class="metric-info">
                    <span class="metric-value">{{ outdoorData.temperature }}</span>
                    <span class="metric-unit">℃</span>
                    <span class="metric-label">温度</span>
                  </div>
                </div>
                <div class="outdoor-metric">
                  <div class="metric-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                      <path
                        d="M12 2c-5.33 4.55-8 8.48-8 11.8 0 4.98 3.8 8.2 8 8.2s8-3.22 8-8.2C20 10.48 17.33 6.55 12 2z"
                      />
                    </svg>
                  </div>
                  <div class="metric-info">
                    <span class="metric-value">{{ outdoorData.humidity }}</span>
                    <span class="metric-unit">%</span>
                    <span class="metric-label">湿度</span>
                  </div>
                </div>
                <div class="outdoor-metric">
                  <div class="metric-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                      <path
                        d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"
                      />
                    </svg>
                  </div>
                  <div class="metric-info">
                    <span class="metric-value">{{ outdoorData.pm25 }}</span>
                    <span class="metric-unit">μg/m³</span>
                    <span class="metric-label">PM2.5</span>
                  </div>
                </div>
                <div class="outdoor-metric">
                  <div class="metric-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                      <path
                        d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"
                      />
                    </svg>
                  </div>
                  <div class="metric-info">
                    <span class="metric-value">{{ outdoorData.pm10 }}</span>
                    <span class="metric-unit">μg/m³</span>
                    <span class="metric-label">PM10</span>
                  </div>
                </div>
                <div class="outdoor-metric">
                  <div class="metric-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                      <path
                        d="M6 8.83V7h4.17L12 8.83 6 8.83zM4 5v2l6 6H8.83L4 8.83V5zm16 7c0 .55-.45 1-1 1s-1-.45-1-1 .45-1 1-1 1 .45 1 1zm0-8c0 .55-.45 1-1 1s-1-.45-1-1 .45-1 1-1 1 .45 1 1zm-8 8c0 .55-.45 1-1 1s-1-.45-1-1 .45-1 1-1 1 .45 1 1z"
                      />
                    </svg>
                  </div>
                  <div class="metric-info">
                    <span class="metric-value">{{ outdoorData.windDirection }}</span>
                    <span class="metric-label">风向</span>
                  </div>
                </div>
                <div class="outdoor-metric">
                  <div class="metric-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                      <path
                        d="M2.5 15h4c.83 0 1.5.67 1.5 1.5S7.33 18 6.5 18H2.5c-.28 0-.5-.22-.5-.5s.22-.5.5-.5zm0-5h7c.83 0 1.5.67 1.5 1.5S10.33 13 9.5 13h-7c-.28 0-.5-.22-.5-.5s.22-.5.5-.5zm18-3H10c-.28 0-.5-.22-.5-.5s.22-.5.5-.5h10.5c.28 0 .5.22.5.5s-.22.5-.5.5z"
                      />
                    </svg>
                  </div>
                  <div class="metric-info">
                    <span class="metric-value">{{ outdoorData.windSpeed }}</span>
                    <span class="metric-unit">m/s</span>
                    <span class="metric-label">风速</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted, reactive, onUnmounted, nextTick, computed, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import * as echarts from 'echarts'
  import ProjectBuildingSelector from '@/components/ProjectBuildingSelector.vue'
  import { getEnvironmentOverview, getLevelInfo } from '@/api/environment-monitoring/overviewApi'
  import { useSettingStore } from '@/store/modules/setting'
  import { SystemThemeEnum } from '@/enums/appEnum'

  // 获取主题设置
  const settingStore = useSettingStore()
  const isDark = computed(() => settingStore.systemThemeType === SystemThemeEnum.DARK)

  // 计算网格布局类 - 根据设备卡片数量动态调整布局对齐方式
  const gridLayoutClass = computed(() => {
    const visibleDeviceCount = deviceStatusList.value.length
    if (visibleDeviceCount === 1) {
      // 1个卡片：水平居中显示
      return 'grid-layout-single'
    } else if (visibleDeviceCount === 2) {
      // 2个卡片：左右对齐（一个靠左，一个靠右）
      return 'grid-layout-double'
    } else {
      // 3个或更多卡片：保持当前的网格布局
      return 'grid-layout-multiple'
    }
  })

  // 项目楼栋选择相关数据
  const projectBuildingSelectorRef = ref()
  const projectBuildingSelection = ref({ projectId: null, buildingId: null })

  // 概览数据
  const overviewData = ref(null)
  const loading = ref(false)

  // 设备状态数据
  const deviceStatusList = ref([])

  // 仪表盘引用
  const comfortGaugeRef = ref(null)
  const gasGaugeRef = ref(null)
  const particleGaugeRef = ref(null)
  const noiseGaugeRef = ref(null)
  const waterGaugeRef = ref(null)
  const outdoorGaugeRef = ref(null)

  // 图表实例存储
  const chartInstances = ref({
    comfort: null,
    gas: null,
    particle: null,
    noise: null,
    water: null,
    outdoor: null
  })

  // 仪表盘数据
  const comfortData = reactive({
    value: 0,
    level: '',
    temperature: 0,
    humidity: 0,
    visible: false
  })

  const gasData = reactive({
    value: 0,
    level: '',
    tvoc: undefined,
    co2: undefined,
    formaldehyde: undefined,
    visible: false
  })

  const particleData = reactive({
    value: 0,
    level: '',
    pm25: 0,
    pm10: 0,
    visible: false
  })

  const noiseData = reactive({
    value: 0,
    level: '',
    currentNoise: 0,
    visible: false
  })

  const waterData = reactive({
    value: 0,
    level: '',
    temperature: undefined,
    ph: undefined,
    do: undefined,
    cod: undefined,
    nh3n: undefined,
    waterLevel: undefined,
    visible: false
  })

  const outdoorData = reactive({
    value: 0,
    level: '',
    temperature: 0,
    humidity: 0,
    pm25: 0,
    pm10: 0,
    windDirection: '',
    windSpeed: 0,
    visible: false
  })

  // 现代化仪表盘初始化
  const initModernGaugeChart = (
    el,
    value,
    _name, // 使用下划线前缀表示参数未使用
    colorTheme = 'default'
  ) => {
    if (!el) return null

    // 现代化配色方案 - 支持主题切换
    const colorSchemes = {
      default: {
        excellent: ['#10b981', '#065f46'],
        good: ['#22c55e', '#166534'],
        moderate: ['#f59e0b', '#92400e'],
        poor: ['#ef4444', '#991b1b']
      },
      comfort: {
        excellent: ['#06b6d4', '#0e7490'],
        good: ['#10b981', '#065f46'],
        moderate: ['#f59e0b', '#92400e'],
        poor: ['#ef4444', '#991b1b']
      },
      air: {
        excellent: ['#8b5cf6', '#5b21b6'],
        good: ['#06b6d4', '#0e7490'],
        moderate: ['#f59e0b', '#92400e'],
        poor: ['#ef4444', '#991b1b']
      },
      particle: {
        excellent: ['#ec4899', '#be185d'],
        good: ['#8b5cf6', '#5b21b6'],
        moderate: ['#f59e0b', '#92400e'],
        poor: ['#ef4444', '#991b1b']
      },
      noise: {
        excellent: ['#22c55e', '#166534'],
        good: ['#84cc16', '#365314'],
        moderate: ['#f59e0b', '#92400e'],
        poor: ['#ef4444', '#991b1b']
      },
      water: {
        excellent: ['#0ea5e9', '#0c4a6e'],
        good: ['#06b6d4', '#0e7490'],
        moderate: ['#f59e0b', '#92400e'],
        poor: ['#ef4444', '#991b1b']
      },
      outdoor: {
        excellent: ['#fbbf24', '#d97706'],
        good: ['#10b981', '#065f46'],
        moderate: ['#f59e0b', '#92400e'],
        poor: ['#ef4444', '#991b1b']
      }
    }

    const colors = colorSchemes[colorTheme] || colorSchemes.default

    // 根据数值返回对应文字等级和颜色
    const getValueInfo = (val) => {
      if (val >= 90) return { text: '优秀', color: colors.excellent }
      if (val >= 70) return { text: '好', color: colors.good }
      if (val >= 50) return { text: '一般', color: colors.moderate }
      return { text: '差', color: colors.poor }
    }

    const valueInfo = getValueInfo(value)
    const chart = echarts.init(el)

    const option = {
      backgroundColor: 'transparent',
      series: [
        // 外圈装饰环
        {
          type: 'gauge',
          startAngle: 200,
          endAngle: -20,
          min: 0,
          max: 100,
          radius: '85%',
          center: ['50%', '55%'],
          splitNumber: 0,
          axisLine: {
            lineStyle: {
              width: 2,
              color: [[1, isDark.value ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)']]
            }
          },
          axisTick: { show: false },
          splitLine: { show: false },
          axisLabel: { show: false },
          pointer: { show: false },
          detail: { show: false },
          data: []
        },
        // 主仪表盘
        {
          type: 'gauge',
          startAngle: 200,
          endAngle: -20,
          min: 0,
          max: 100,
          radius: '75%',
          center: ['50%', '55%'],
          splitNumber: 0,
          axisLine: {
            lineStyle: {
              width: 8,
              color: [
                [
                  value / 100,
                  {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 0,
                    colorStops: [
                      { offset: 0, color: valueInfo.color[1] },
                      { offset: 1, color: valueInfo.color[0] }
                    ]
                  }
                ],
                [1, isDark.value ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)']
              ]
            }
          },
          pointer: {
            show: true,
            length: '65%',
            width: 3,
            offsetCenter: [0, '5%'],
            itemStyle: {
              color: valueInfo.color[0],
              shadowColor: 'rgba(0,0,0,0.3)',
              shadowBlur: 5,
              shadowOffsetY: 2
            }
          },
          axisTick: { show: false },
          splitLine: { show: false },
          axisLabel: { show: false },
          detail: { show: false },
          data: [{ value: value }]
        },
        // 内圈进度环
        {
          type: 'gauge',
          startAngle: 200,
          endAngle: -20,
          min: 0,
          max: 100,
          radius: '60%',
          center: ['50%', '55%'],
          splitNumber: 0,
          axisLine: {
            lineStyle: {
              width: 4,
              color: [
                [value / 100, valueInfo.color[0]],
                [1, isDark.value ? 'rgba(255,255,255,0.03)' : 'rgba(0,0,0,0.03)']
              ]
            }
          },
          pointer: { show: false },
          axisTick: { show: false },
          splitLine: { show: false },
          axisLabel: { show: false },
          detail: { show: false },
          data: []
        }
      ],
      animation: true,
      animationDuration: 2000,
      animationEasing: 'cubicOut'
    }

    chart.setOption(option)

    // 响应式处理
    const resizeHandler = () => {
      chart.resize()
    }
    window.addEventListener('resize', resizeHandler)

    // 返回图表实例和清理函数
    return {
      chart,
      destroy: () => {
        window.removeEventListener('resize', resizeHandler)
        chart.dispose()
      }
    }
  }

  // 更新初始化方法，使用现代化图表
  const initGaugeChart = (el, value, name, chartKey) => {
    const themeMap = {
      舒适度: 'comfort',
      空气质量: 'air',
      颗粒物: 'particle',
      噪声: 'noise',
      水质: 'water',
      环境质量: 'outdoor'
    }

    // 销毁旧图表
    if (chartInstances.value[chartKey]) {
      chartInstances.value[chartKey].destroy()
    }

    // 创建新图表
    const chartInstance = initModernGaugeChart(el, value, name, themeMap[name] || 'default')
    chartInstances.value[chartKey] = chartInstance

    return chartInstance
  }

  // 查询状态管理 - 防重复查询
  const lastQueryParams = ref(null)
  const queryButtonDisabled = ref(false) // 查询按钮状态

  // 比较查询参数是否相同
  const isSameQueryParams = (params1, params2) => {
    if (!params1 || !params2) return false
    return params1.projectId === params2.projectId && params1.buildingId === params2.buildingId
  }

  // 防抖处理函数
  let searchTimeout = null
  const debouncedAutoSearch = () => {
    if (searchTimeout) {
      clearTimeout(searchTimeout)
    }
    searchTimeout = setTimeout(() => {
      autoSearch()
    }, 500) // 500ms防抖延迟
  }

  // 自动查询方法
  const autoSearch = async () => {
    // 如果没有选择项目，不发请求
    if (!projectBuildingSelection.value.projectId) {
      console.log('没有选择项目，不发请求')
      return
    }

    console.log('自动查询参数:', projectBuildingSelection.value)
    await fetchOverviewData()
  }

  // 处理项目楼栋变化
  const handleProjectBuildingChange = (data) => {
    console.log('项目楼栋选择变化:', data)
    // 清空上次查询参数，允许重新查询
    lastQueryParams.value = null
    queryButtonDisabled.value = false
    // 项目或楼栋变化时自动发送请求（使用防抖）
    if (data.projectId) {
      debouncedAutoSearch()
    }
  }

  // 监听项目列表加载完成
  const handleProjectListLoaded = (projectList) => {
    console.log('项目列表加载完成:', projectList)
    // 当项目列表加载完成后，自动发请求获取第一个项目的数据
    if (projectList && projectList.length > 0) {
      nextTick(() => {
        autoSearch()
      })
    }
  }

  // 获取环境监测概览数据
  const fetchOverviewData = async () => {
    if (!projectBuildingSelection.value.projectId) {
      console.log('没有选择项目，不发请求')
      return
    }

    try {
      loading.value = true
      const params = {
        projectId: projectBuildingSelection.value.projectId,
        buildingId: projectBuildingSelection.value.buildingId
      }

      console.log('查询参数:', params)
      const response = await getEnvironmentOverview(params)

      if (response.code === 200) {
        overviewData.value = response.data
        updateDisplayData(response.data)
        console.log('获取概览数据成功:', response.data)

        // 保存当前查询参数
        lastQueryParams.value = { ...params }
        // 禁用查询按钮（因为已经查询过相同条件）
        queryButtonDisabled.value = true
      } else {
        ElMessage.error(response.msg || '获取概览数据失败')
      }
    } catch (error) {
      console.error('获取概览数据失败:', error)
      ElMessage.error('获取概览数据失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  // 处理搜索
  const handleSearch = () => {
    // 如果查询按钮已禁用，不执行查询
    if (queryButtonDisabled.value) {
      ElMessage.info('查询条件未变化，无需重复查询')
      return
    }

    // 获取当前查询参数
    const currentParams = {
      projectId: projectBuildingSelection.value.projectId,
      buildingId: projectBuildingSelection.value.buildingId
    }

    // 检查查询参数是否与上次相同
    if (isSameQueryParams(currentParams, lastQueryParams.value)) {
      ElMessage.info('查询条件未变化')
      queryButtonDisabled.value = true
      return
    }

    fetchOverviewData()
  }

  // 处理重置
  const handleReset = () => {
    if (projectBuildingSelectorRef.value) {
      projectBuildingSelectorRef.value.reset()
    }
    // 清空查询缓存，允许重新查询
    lastQueryParams.value = null
    queryButtonDisabled.value = false
    // 重置后自动查询
    nextTick(() => {
      autoSearch()
    })
  }

  // 更新显示数据
  const updateDisplayData = (data) => {
    // 更新设备状态列表
    if (data.equipInfoList && data.equipInfoList.length > 0) {
      deviceStatusList.value = data.equipInfoList.map((item, index) => ({
        id: String(index + 1),
        name: item.name,
        icon: getDeviceIcon(item.name),
        value: item.num.toString(),
        unit: '台',
        status: item.status === '正常' ? 'normal' : 'warning',
        statusText: item.status
      }))
    } else {
      // 设置默认设备状态列表，保持显示
      deviceStatusList.value = [
        {
          id: '1',
          name: '室外气象站',
          icon: '&#xe824;',
          value: '--',
          unit: '台',
          status: 'warning',
          statusText: '暂无数据'
        },
        {
          id: '2',
          name: '室内七合一',
          icon: '&#xe71d;',
          value: '--',
          unit: '台',
          status: 'warning',
          statusText: '暂无数据'
        },
        {
          id: '3',
          name: '水质监测',
          icon: '&#xe6fa;',
          value: '--',
          unit: '台',
          status: 'warning',
          statusText: '暂无数据'
        }
      ]
    }

    // 更新温湿度监测数据
    if (data.ssdMonitor) {
      const levelInfo = getLevelInfo(data.ssdMonitor.level)
      comfortData.value = levelInfo.value
      comfortData.level = levelInfo.text
      comfortData.temperature = data.ssdMonitor.wd
      comfortData.humidity = data.ssdMonitor.sd
      comfortData.visible = true
    } else {
      // 设置默认值，保持组件可见
      comfortData.value = 0
      comfortData.level = '暂无数据'
      comfortData.temperature = '--'
      comfortData.humidity = '--'
      comfortData.visible = true
    }

    // 更新有害气体监测数据
    if (data.yhqtMonitor) {
      const levelInfo = getLevelInfo(data.yhqtMonitor.level)
      gasData.value = levelInfo.value
      gasData.level = levelInfo.text
      gasData.tvoc = data.yhqtMonitor.tvoc
      gasData.co2 = data.yhqtMonitor.co2
      gasData.formaldehyde = data.yhqtMonitor.formaldehyde
      gasData.visible = true
    } else {
      // 设置默认值，保持组件可见
      gasData.value = 0
      gasData.level = '暂无数据'
      gasData.tvoc = '--'
      gasData.co2 = '--'
      gasData.formaldehyde = '--'
      gasData.visible = true
    }

    // 更新颗粒物监测数据
    if (data.klwMonitor) {
      const levelInfo = getLevelInfo(data.klwMonitor.level)
      particleData.value = levelInfo.value
      particleData.level = levelInfo.text
      particleData.pm25 = data.klwMonitor.pm2_5
      particleData.pm10 = data.klwMonitor.pm10
      particleData.visible = true
    } else {
      // 设置默认值，保持组件可见
      particleData.value = 0
      particleData.level = '暂无数据'
      particleData.pm25 = '--'
      particleData.pm10 = '--'
      particleData.visible = true
    }

    // 更新噪声监测数据
    if (data.zsMonitor) {
      const levelInfo = getLevelInfo(data.zsMonitor.level)
      noiseData.value = levelInfo.value
      noiseData.level = levelInfo.text
      noiseData.currentNoise = data.zsMonitor.zs
      noiseData.visible = true
    } else {
      // 设置默认值，保持组件可见
      noiseData.value = 0
      noiseData.level = '暂无数据'
      noiseData.currentNoise = '--'
      noiseData.visible = true
    }

    // 更新水质监测数据
    if (data.waterMonitor) {
      const levelInfo = getLevelInfo(data.waterMonitor.level)
      waterData.value = levelInfo.value
      waterData.level = levelInfo.text
      waterData.temperature = data.waterMonitor.wd
      waterData.ph = data.waterMonitor.ph
      waterData.do = data.waterMonitor.doValue
      waterData.cod = data.waterMonitor.cod
      waterData.nh3n = data.waterMonitor.ad
      waterData.waterLevel = data.waterMonitor.waterLevel
      waterData.visible = true
    } else {
      // 设置默认值，保持组件可见
      waterData.value = 0
      waterData.level = '暂无数据'
      waterData.temperature = '--'
      waterData.ph = '--'
      waterData.do = '--'
      waterData.cod = '--'
      waterData.nh3n = '--'
      waterData.waterLevel = '--'
      waterData.visible = true
    }

    // 更新室外环境监测数据
    if (data.swMonitor) {
      const levelInfo = getLevelInfo(data.swMonitor.level)
      outdoorData.value = levelInfo.value
      outdoorData.level = levelInfo.text
      outdoorData.temperature = data.swMonitor.wd
      outdoorData.humidity = data.swMonitor.sd
      outdoorData.pm25 = data.swMonitor.pm2_5
      outdoorData.pm10 = data.swMonitor.pm10
      outdoorData.windDirection = data.swMonitor.fx
      outdoorData.windSpeed = data.swMonitor.fs
      outdoorData.visible = true
    } else {
      // 设置默认值，保持组件可见
      outdoorData.value = 0
      outdoorData.level = '暂无数据'
      outdoorData.temperature = '--'
      outdoorData.humidity = '--'
      outdoorData.pm25 = '--'
      outdoorData.pm10 = '--'
      outdoorData.windDirection = '--'
      outdoorData.windSpeed = '--'
      outdoorData.visible = true
    }

    // 重新初始化图表
    nextTick(() => {
      initAllCharts()
    })
  }

  // 获取设备图标
  const getDeviceIcon = (deviceName) => {
    if (deviceName.includes('室外') || deviceName.includes('气象')) {
      return '&#xe824;' // 天气图标
    } else if (deviceName.includes('室内') || deviceName.includes('七合一')) {
      return '&#xe71d;' // 灯泡图标
    } else if (deviceName.includes('水质')) {
      return '&#xe6fa;' // 水的图标
    }
    return '&#xe824;' // 默认图标
  }

  // 初始化所有图表
  const initAllCharts = () => {
    if (comfortGaugeRef.value) {
      initGaugeChart(comfortGaugeRef.value, comfortData.value, '舒适度', 'comfort')
    }
    if (gasGaugeRef.value) {
      initGaugeChart(gasGaugeRef.value, gasData.value, '空气质量', 'gas')
    }
    if (particleGaugeRef.value) {
      initGaugeChart(particleGaugeRef.value, particleData.value, '颗粒物', 'particle')
    }
    if (noiseGaugeRef.value) {
      initGaugeChart(noiseGaugeRef.value, noiseData.value, '噪声', 'noise')
    }
    if (waterGaugeRef.value) {
      initGaugeChart(waterGaugeRef.value, waterData.value, '水质', 'water')
    }
    if (outdoorGaugeRef.value) {
      initGaugeChart(outdoorGaugeRef.value, outdoorData.value, '环境质量', 'outdoor')
    }
  }

  // 监听主题变化，更新图表
  const updateChartsTheme = () => {
    // 重新初始化所有图表以应用新主题
    nextTick(() => {
      initAllCharts()
    })
  }

  // 监听项目楼栋选择变化
  watch(
    projectBuildingSelection,
    (newVal, oldVal) => {
      // 当项目或楼栋选择发生变化时，启用查询按钮
      if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
        console.log('项目楼栋选择变化，启用查询按钮:', newVal)
        queryButtonDisabled.value = false
        // 清空上次查询参数，允许重新查询
        lastQueryParams.value = null
      }
    },
    { deep: true }
  )

  // 监听主题变化
  watch(isDark, () => {
    updateChartsTheme()
  })

  // 页面挂载后初始化图表
  onMounted(() => {
    // 初始化时先设置默认可见性和测试数据
    comfortData.visible = true
    comfortData.level = '好'
    comfortData.value = 80
    comfortData.temperature = 23.5
    comfortData.humidity = 65

    gasData.visible = true
    gasData.level = '优秀'
    gasData.value = 95
    gasData.tvoc = 58
    gasData.co2 = 420
    // gasData.formaldehyde = undefined // 不设置甲醛，测试条件显示

    particleData.visible = true
    particleData.level = '一般'
    particleData.value = 60
    particleData.pm25 = 45
    particleData.pm10 = 32

    noiseData.visible = true // 确保噪声监测始终显示
    noiseData.level = '暂无数据'
    noiseData.value = 0
    noiseData.currentNoise = '--'

    waterData.visible = true
    waterData.level = '差'
    waterData.value = 30
    waterData.temperature = 18
    waterData.ph = 7.2
    waterData.do = 5.8
    waterData.cod = 12
    waterData.nh3n = 0.15
    // waterData.waterLevel = undefined // 不设置水位，测试条件显示

    outdoorData.visible = true
    outdoorData.level = '好'
    outdoorData.value = 80
    outdoorData.temperature = 26.5
    outdoorData.humidity = 58
    outdoorData.pm25 = 22
    outdoorData.pm10 = 45
    outdoorData.windDirection = '东北'
    outdoorData.windSpeed = 2.7

    // 设置测试设备数据
    deviceStatusList.value = [
      {
        id: '1',
        name: '室外气象站',
        icon: '&#xe824;',
        value: '2',
        unit: '台',
        status: 'normal',
        statusText: '正常'
      },
      {
        id: '2',
        name: '室内七合一',
        icon: '&#xe71d;',
        value: '2',
        unit: '台',
        status: 'normal',
        statusText: '正常'
      },
      {
        id: '3',
        name: '水质监测',
        icon: '&#xe6fa;',
        value: '2',
        unit: '台',
        status: 'normal',
        statusText: '正常'
      }
    ]

    // 初始化图表
    nextTick(() => {
      initAllCharts()
    })

    // 监听主题变化
    window.addEventListener('themeChange', updateChartsTheme)
  })

  // 在组件卸载时移除事件监听和销毁图表
  onUnmounted(() => {
    window.removeEventListener('themeChange', updateChartsTheme)

    // 清理防抖定时器
    if (searchTimeout) {
      clearTimeout(searchTimeout)
    }

    // 销毁所有图表实例
    Object.values(chartInstances.value).forEach((instance) => {
      if (instance && instance.destroy) {
        instance.destroy()
      }
    })
  })
</script>

<style lang="scss" scoped>
  @import 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap';

  .monitoring-overview-modern {
    min-height: 100vh;
    padding: 20px;
    font-family:
      Inter,
      -apple-system,
      BlinkMacSystemFont,
      'Segoe UI',
      Roboto,
      sans-serif;
    color: var(--el-text-color-primary);
    background: var(--el-bg-color);
    animation: fadeIn 0.8s ease-out;

    // 简化的内容区域
    .content-section {
      margin-bottom: 20px;

      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .filter-controls {
        display: flex;
        gap: 10px;
        align-items: center;
      }

      .filter-card {
        margin-bottom: 20px;
      }
    }

    // 现代化设备状态概览
    .status-overview {
      width: 100%;
      padding: 0 24px 24px;
      margin: 0;

      .overview-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(360px, 1fr));
        gap: 24px;

        // 动态布局：根据卡片数量调整对齐方式，保持卡片尺寸不变

        // 单个卡片时：水平居中显示，卡片最大
        &.grid-layout-single {
          display: flex;
          align-items: flex-start;
          justify-content: center;

          .status-card-modern {
            flex: 0 0 auto;
            width: clamp(400px, 50%, 800px); // 1个卡片：最小400px，理想50%，最大800px
          }
        }

        // 两个卡片时：使用网格布局确保对称
        &.grid-layout-double {
          display: grid;
          grid-template-columns: 1fr 1fr; // 两列等宽
          gap: 24px; // 卡片间距
          align-items: start;

          .status-card-modern {
            justify-self: center; // 在列中居中
            width: 100%; // 在网格中占满列宽
            min-width: 0; // 移除最小宽度限制，让卡片能够收缩
            max-width: min(600px, 100%); // 限制最大宽度，不超过列宽
          }
        }

        // 三个或更多卡片时：保持原有的默认网格布局不变
        &.grid-layout-multiple {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(360px, 1fr)); // 保持原有设置
          justify-content: start;
        }

        .status-card-modern {
          position: relative;
          padding: 24px;
          overflow: hidden;
          backdrop-filter: blur(20px);
          border: 1px solid var(--el-border-color-light);
          border-radius: 16px;
          box-shadow: var(--el-box-shadow-light);
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          animation: slideUp 0.6s ease-out;
          animation-fill-mode: both;

          &:hover {
            box-shadow: var(--el-box-shadow);
            transform: translateY(-8px);

            .card-background {
              opacity: 1;
              transform: scale(1.1);
            }
          }

          .card-background {
            position: absolute;
            top: 0;
            right: 0;
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #667eea20, #764ba220);
            border-radius: 50%;
            opacity: 0.6;
            transition: all 0.4s ease;
            transform: translateX(40px) translateY(-40px) scale(1);
          }

          .card-content {
            position: relative;
            z-index: 2;

            .device-header {
              display: flex;
              gap: 16px;
              align-items: center;
              margin-bottom: 24px;

              .device-icon-modern {
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 56px;
                height: 56px;

                .icon-bg {
                  position: absolute;
                  width: 100%;
                  height: 100%;
                  border-radius: 16px;
                  opacity: 0.1;

                  &.bg-normal {
                    background: linear-gradient(135deg, #10b981, #059669);
                  }

                  &.bg-warning {
                    background: linear-gradient(135deg, #f59e0b, #d97706);
                  }

                  &.bg-danger {
                    background: linear-gradient(135deg, #ef4444, #dc2626);
                  }
                }

                .iconfont-sys {
                  position: relative;
                  z-index: 1;
                  font-size: 28px;
                }
              }

              .device-meta {
                flex: 1;

                h3 {
                  margin: 0 0 8px;
                  font-size: 18px;
                  font-weight: 700;
                  color: var(--el-text-color-primary);
                  letter-spacing: -0.01em;
                }

                .status-indicator {
                  display: flex;
                  gap: 8px;
                  align-items: center;

                  .indicator-dot {
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    animation: pulse 2s infinite;
                  }

                  span {
                    font-size: 12px;
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 0.1em;
                  }

                  &.normal {
                    color: #10b981;

                    .indicator-dot {
                      background: #10b981;
                    }
                  }

                  &.warning {
                    color: #f59e0b;

                    .indicator-dot {
                      background: #f59e0b;
                    }
                  }

                  &.danger {
                    color: #ef4444;

                    .indicator-dot {
                      background: #ef4444;
                    }
                  }
                }
              }
            }

            .device-value-section {
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-bottom: 24px;

              .primary-value {
                .value {
                  font-size: 32px;
                  font-weight: 800;
                  line-height: 1;
                  color: var(--el-text-color-primary);
                }

                .unit {
                  margin-left: 4px;
                  font-size: 16px;
                  font-weight: 500;
                  color: var(--el-text-color-regular);
                }
              }
            }

            .device-stats-modern {
              display: flex;
              justify-content: center;

              .stat-card {
                display: flex;
                gap: 12px;
                align-items: center;
                padding: 16px;
                background: var(--el-fill-color-extra-light);
                border-radius: 12px;
                transition: all 0.3s ease;

                &:hover {
                  background: var(--el-fill-color-light);
                }

                .stat-icon {
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  width: 32px;
                  height: 32px;
                  border-radius: 8px;

                  svg {
                    width: 16px;
                    height: 16px;
                  }
                }

                &.total .stat-icon {
                  color: #10b981;
                  background: rgb(16 185 129 / 10%);
                }

                .stat-info {
                  .stat-value {
                    display: block;
                    font-size: 18px;
                    font-weight: 700;
                    line-height: 1;
                    color: var(--el-text-color-primary);
                  }

                  .stat-label {
                    margin-top: 2px;
                    font-size: 12px;
                    color: var(--el-text-color-regular);
                  }
                }
              }
            }
          }
        }
      }
    }

    // 现代化监测仪表盘矩阵
    .dashboard-matrix {
      max-width: 100%;
      margin: 0;

      .matrix-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;

        .monitoring-card {
          overflow: hidden;
          backdrop-filter: blur(20px);
          border: 1px solid var(--el-border-color-light);
          border-radius: 16px;
          box-shadow: var(--el-box-shadow-light);
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          animation: slideUp 0.6s ease-out;
          animation-fill-mode: both;

          &:hover {
            box-shadow: var(--el-box-shadow);
            transform: translateY(-4px);
          }

          &.wide-card {
            grid-column: span 2;
          }

          .card-header-modern {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 24px 28px 20px;
            border-bottom: 1px solid var(--el-border-color-lighter);

            .header-icon {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 40px;
              height: 40px;
              color: white;
              background: linear-gradient(135deg, #667eea, #764ba2);
              border-radius: 12px;
              box-shadow: 0 4px 16px rgb(102 126 234 / 30%);

              svg {
                width: 20px;
                height: 20px;
              }
            }

            .header-content {
              flex: 1;
              margin-left: 16px;

              h3 {
                margin: 0 0 4px;
                font-size: 18px;
                font-weight: 700;
                color: var(--el-text-color-primary);
                letter-spacing: -0.01em;
              }

              p {
                margin: 0;
                font-size: 12px;
                font-weight: 500;
                color: var(--el-text-color-regular);
                text-transform: uppercase;
                letter-spacing: 0.1em;
              }
            }

            .header-status {
              display: flex;
              gap: 8px;
              align-items: center;
              padding: 6px 12px;
              font-size: 12px;
              font-weight: 600;
              border-radius: 8px;

              .status-dot {
                width: 6px;
                height: 6px;
                border-radius: 50%;
                animation: pulse 2s infinite;
              }

              &.excellent {
                color: #10b981;
                background: rgb(16 185 129 / 10%);

                .status-dot {
                  background: #10b981;
                }
              }

              &.comfort {
                color: #10b981;
                background: rgb(16 185 129 / 10%);

                .status-dot {
                  background: #10b981;
                }
              }

              &.good {
                color: #22c55e;
                background: rgb(34 197 94 / 10%);

                .status-dot {
                  background: #22c55e;
                }
              }

              &.moderate {
                color: #f59e0b;
                background: rgb(245 158 11 / 10%);

                .status-dot {
                  background: #f59e0b;
                }
              }

              // 中文等级支持
              &.优秀 {
                color: #10b981;
                background: rgb(16 185 129 / 10%);

                .status-dot {
                  background: #10b981;
                }
              }

              &.好 {
                color: #22c55e;
                background: rgb(34 197 94 / 10%);

                .status-dot {
                  background: #22c55e;
                }
              }

              &.一般 {
                color: #f59e0b;
                background: rgb(245 158 11 / 10%);

                .status-dot {
                  background: #f59e0b;
                }
              }

              &.差 {
                color: #ef4444;
                background: rgb(239 68 68 / 10%);

                .status-dot {
                  background: #ef4444;
                }
              }
            }
          }

          .card-body-modern {
            padding: 5px 28px;

            .gauge-section {
              position: relative;
              margin-bottom: 24px;

              .gauge-container-modern {
                width: 100%;
                height: 200px;
              }

              .gauge-overlay {
                position: absolute;
                top: 50%;
                left: 50%;
                text-align: center;
                pointer-events: none;
                transform: translate(-50%, -50%);

                .gauge-center-value {
                  .value {
                    display: block;
                    font-size: 18px;
                    font-weight: 700;
                    line-height: 1;
                    color: var(--el-text-color-primary);
                  }

                  .label {
                    margin-top: 4px;
                    font-size: 11px;
                    font-weight: 600;
                    color: var(--el-text-color-regular);
                    text-transform: uppercase;
                    letter-spacing: 0.1em;
                  }
                }
              }
            }

            .metrics-grid {
              display: grid;
              grid-template-columns: repeat(2, 1fr);
              gap: 16px;

              .metric-item {
                display: flex;
                gap: 12px;
                align-items: center;
                padding: 16px;
                background: var(--el-fill-color-extra-light);
                border-radius: 12px;
                transition: all 0.3s ease;

                &.full-width {
                  grid-column: 1 / -1;
                }

                &:hover {
                  background: var(--el-fill-color-light);
                  transform: translateY(-2px);
                }

                .metric-icon {
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  width: 32px;
                  height: 32px;
                  color: #667eea;
                  background: rgb(102 126 234 / 10%);
                  border-radius: 8px;

                  svg {
                    width: 16px;
                    height: 16px;
                  }

                  &.temp {
                    color: #ef4444;
                    background: rgb(239 68 68 / 10%);
                  }

                  &.humidity {
                    color: #3b82f6;
                    background: rgb(59 130 246 / 10%);
                  }

                  &.tvoc,
                  &.co2,
                  &.formaldehyde {
                    color: #f59e0b;
                    background: rgb(245 158 11 / 10%);
                  }

                  &.pm25,
                  &.pm10 {
                    color: #a855f7;
                    background: rgb(168 85 247 / 10%);
                  }

                  &.noise {
                    color: #22c55e;
                    background: rgb(34 197 94 / 10%);
                  }
                }

                .metric-data {
                  flex: 1;

                  .metric-value {
                    font-size: 16px;
                    font-weight: 700;
                    line-height: 1;
                    color: var(--el-text-color-primary);
                  }

                  .metric-unit {
                    margin-left: 2px;
                    font-size: 11px;
                    color: var(--el-text-color-regular);
                  }

                  .metric-label {
                    display: block;
                    margin-top: 4px;
                    font-size: 11px;
                    font-weight: 600;
                    color: var(--el-text-color-regular);
                    text-transform: uppercase;
                    letter-spacing: 0.1em;
                  }
                }
              }
            }

            // 水质监测特殊布局
            .water-content {
              display: grid;
              grid-template-columns: 180px 1fr;
              gap: 20px;
              align-items: start;

              .water-metrics {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 12px;

                .water-metric {
                  display: flex;
                  gap: 12px;
                  align-items: center;
                  padding: 16px;
                  background: var(--el-fill-color-extra-light);
                  border-radius: 12px;
                  transition: all 0.3s ease;

                  &:hover {
                    background: var(--el-fill-color-light);
                    transform: translateY(-2px);
                  }

                  .metric-icon {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 32px;
                    height: 32px;
                    color: #3b82f6;
                    background: rgb(59 130 246 / 10%);
                    border-radius: 8px;

                    svg {
                      width: 16px;
                      height: 16px;
                    }
                  }

                  .metric-info {
                    .metric-value {
                      font-size: 16px;
                      font-weight: 700;
                      line-height: 1;
                      color: var(--el-text-color-primary);
                    }

                    .metric-unit {
                      margin-left: 2px;
                      font-size: 11px;
                      color: var(--el-text-color-regular);
                    }

                    .metric-label {
                      display: block;
                      margin-top: 4px;
                      font-size: 11px;
                      font-weight: 600;
                      color: var(--el-text-color-regular);
                      text-transform: uppercase;
                      letter-spacing: 0.1em;
                    }
                  }
                }
              }
            }

            // 室外环境监测特殊布局
            .outdoor-content {
              display: grid;
              grid-template-columns: 180px 1fr;
              gap: 20px;
              align-items: start;

              .outdoor-metrics {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 12px;

                .outdoor-metric {
                  display: flex;
                  gap: 12px;
                  align-items: center;
                  padding: 16px;
                  background: var(--el-fill-color-extra-light);
                  border-radius: 12px;
                  transition: all 0.3s ease;

                  &:hover {
                    background: var(--el-fill-color-light);
                    transform: translateY(-2px);
                  }

                  .metric-icon {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 32px;
                    height: 32px;
                    color: #fbbf24;
                    background: rgb(251 191 36 / 10%);
                    border-radius: 8px;

                    svg {
                      width: 16px;
                      height: 16px;
                    }
                  }

                  .metric-info {
                    .metric-value {
                      font-size: 16px;
                      font-weight: 700;
                      line-height: 1;
                      color: var(--el-text-color-primary);
                    }

                    .metric-unit {
                      margin-left: 2px;
                      font-size: 11px;
                      color: var(--el-text-color-regular);
                    }

                    .metric-label {
                      display: block;
                      margin-top: 4px;
                      font-size: 11px;
                      font-weight: 600;
                      color: var(--el-text-color-regular);
                      text-transform: uppercase;
                      letter-spacing: 0.1em;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    // 响应式设计
    @media (width <= 1400px) {
      .dashboard-matrix .matrix-grid {
        grid-template-columns: repeat(3, 1fr);

        .monitoring-card.wide-card {
          grid-column: span 3;
        }
      }
    }

    @media (width <= 1200px) {
      .modern-header .header-content {
        flex-direction: column;
        gap: 20px;
        align-items: stretch;

        .header-right .filter-panel {
          flex-wrap: wrap;
        }
      }

      .dashboard-matrix .matrix-grid {
        grid-template-columns: repeat(2, 1fr);

        .monitoring-card.wide-card {
          grid-column: span 2;
        }
      }

      // 在中等屏幕上调整卡片大小
      .status-overview .overview-grid {
        &.grid-layout-single .status-card-modern {
          width: clamp(350px, 60%, 700px); // 中屏单卡片：稍小一些
        }

        &.grid-layout-double {
          grid-template-columns: 1fr; // 中屏改为单列垂直排列
          gap: 20px;
          justify-items: center; // 卡片在列中居中

          .status-card-modern {
            width: clamp(320px, 70%, 600px); // 中屏双卡片：垂直排列时可以更大
            max-width: min(600px, 100%); // 确保不超出容器
          }
        }

        &.grid-layout-multiple {
          grid-template-columns: repeat(auto-fit, minmax(360px, 1fr)); // 保持原有设置
        }
      }
    }

    @media (width <= 900px) {
      .dashboard-matrix .matrix-grid {
        grid-template-columns: 1fr;

        .monitoring-card.wide-card {
          grid-column: 1;
        }
      }
    }

    @media (width <= 768px) {
      .modern-header .header-content {
        padding: 20px;

        .header-right .filter-panel {
          flex-direction: column;
          gap: 16px;

          .filter-group {
            .modern-select {
              min-width: 100%;
            }
          }

          .filter-actions {
            margin-top: 0;

            .action-btn {
              flex: 1;
              justify-content: center;
            }
          }
        }
      }

      .status-overview,
      .dashboard-matrix {
        padding: 0 16px 20px;
      }

      .status-overview .overview-grid {
        grid-template-columns: 1fr;

        // 在小屏幕上，所有布局都改为垂直排列，但仍保持大小差异
        &.grid-layout-single,
        &.grid-layout-double,
        &.grid-layout-multiple {
          display: flex;
          flex-direction: column;
          gap: 16px;
          align-items: center;
        }

        // 小屏幕上的卡片大小差异
        &.grid-layout-single .status-card-modern {
          width: 100%; // 单卡片：充满宽度
          max-width: 500px; // 但有最大宽度限制
        }

        &.grid-layout-double .status-card-modern {
          width: 95%; // 双卡片：稍小一些
          max-width: 450px;
        }

        &.grid-layout-multiple .status-card-modern {
          width: 100%; // 多卡片：在小屏幕上充满宽度，保持原有行为
        }
      }

      .dashboard-matrix .matrix-grid {
        grid-template-columns: 1fr;
      }
    }

    // 动画定义
    @keyframes fadeIn {
      from {
        opacity: 0;
      }

      to {
        opacity: 1;
      }
    }

    @keyframes slideUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }

      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes pulse {
      0%,
      100% {
        opacity: 1;
      }

      50% {
        opacity: 0.5;
      }
    }

    // 主题适配的背景纹理
    .monitoring-overview-modern {
      // 添加微妙的背景纹理
      &::before {
        position: fixed;
        top: 0;
        left: 0;
        z-index: -1;
        width: 100%;
        height: 100%;
        pointer-events: none;
        content: '';
        background: radial-gradient(
          circle at 50% 50%,
          var(--el-color-primary-light-9) 0%,
          transparent 50%
        );
      }
    }

    // 黑夜模式特殊样式
    :global(.dark) {
      .monitoring-overview-modern {
        &::before {
          background: radial-gradient(
            circle at 50% 50%,
            var(--el-color-primary-dark-2) 0%,
            transparent 50%
          );
        }

        .status-card-modern,
        .monitoring-card {
          background: var(--el-bg-color-overlay);
          border-color: var(--el-border-color);
        }

        .card-background {
          background: linear-gradient(
            135deg,
            var(--el-color-primary-light-7),
            var(--el-color-primary-light-8)
          );
        }
      }
    }
  }
</style>
