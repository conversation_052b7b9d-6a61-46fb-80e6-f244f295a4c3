<template>
  <div class="chart-container" ref="chartContainer"></div>
</template>

<script setup>
  import { ref, onMounted, watch, nextTick } from 'vue'
  import * as echarts from 'echarts'

  const props = defineProps({
    timeRange: {
      type: String,
      default: 'day'
    },
    chartData: {
      type: Object,
      default: () => ({
        xAxisData: [],
        temperatureData: [],
        humidityData: []
      })
    }
  })

  const chartContainer = ref(null)
  let chart = null

  // 初始化图表
  const initChart = () => {
    if (!chartContainer.value) return

    // 如果图表已经存在，销毁它以避免重复创建
    if (chart) {
      chart.dispose()
    }

    // 创建新的图表实例
    chart = echarts.init(chartContainer.value)

    // 设置图表选项
    const option = {
      tooltip: {
        trigger: 'axis',
        formatter: function (params) {
          let result = params[0].axisValue + '<br/>'
          params.forEach((param) => {
            let marker = `<span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${param.color};"></span>`
            let value = param.seriesName === '温度' ? param.value + '°C' : param.value + '%'
            result += marker + param.seriesName + ': ' + value + '<br/>'
          })
          return result
        }
      },
      legend: {
        data: ['温度', '湿度'],
        bottom: 0
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: props.chartData.xAxisData,
        axisLine: {
          lineStyle: {
            color: '#999'
          }
        },
        axisLabel: {
          color: '#666'
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '温度(°C)',
          nameTextStyle: {
            color: '#666'
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#409EFF'
            }
          },
          axisLabel: {
            formatter: '{value}',
            color: '#666'
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(224, 230, 241, 0.8)'
            }
          }
        },
        {
          type: 'value',
          name: '湿度(%)',
          nameTextStyle: {
            color: '#666'
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#67C23A'
            }
          },
          axisLabel: {
            formatter: '{value}',
            color: '#666'
          },
          splitLine: {
            show: false
          }
        }
      ],
      series: [
        {
          name: '温度',
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          showSymbol: false,
          data: props.chartData.temperatureData,
          itemStyle: {
            color: '#409EFF'
          },
          lineStyle: {
            width: 2
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(64, 158, 255, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(64, 158, 255, 0.1)'
              }
            ])
          }
        },
        {
          name: '湿度',
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          showSymbol: false,
          yAxisIndex: 1,
          data: props.chartData.humidityData,
          itemStyle: {
            color: '#67C23A'
          },
          lineStyle: {
            width: 2
          }
        }
      ]
    }

    // 应用选项
    chart.setOption(option)

    // 添加响应式调整
    window.addEventListener('resize', () => {
      if (chart) {
        chart.resize()
      }
    })
  }

  // 更新图表
  const updateChart = () => {
    if (!chart) {
      initChart()
      return
    }

    chart.setOption({
      xAxis: {
        data: props.chartData.xAxisData
      },
      series: [
        {
          data: props.chartData.temperatureData
        },
        {
          data: props.chartData.humidityData
        }
      ]
    })
  }

  // 监听图表数据变化
  watch(
    () => props.chartData,
    () => {
      nextTick(() => {
        updateChart()
      })
    },
    { deep: true }
  )

  // 监听时间范围变化
  watch(
    () => props.timeRange,
    () => {
      nextTick(() => {
        updateChart()
      })
    }
  )

  // 组件挂载时初始化图表
  onMounted(() => {
    initChart()
  })
</script>

<style scoped lang="scss">
  .chart-container {
    width: 100%;
    height: 350px;
  }
</style>
