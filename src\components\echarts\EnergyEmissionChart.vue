<template>
  <div :id="chartId" class="energy-emission-chart"></div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted, watch } from 'vue'
  import * as echarts from 'echarts'

  // 使用props接收数据
  const props = defineProps({
    chartData: {
      type: Array,
      required: true
    }
  })

  // 生成唯一的图表ID，避免多个图表实例冲突
  const chartId = `energy-emission-chart-${Date.now()}`
  // 图表实例
  const chartInstance = ref(null)

  // 处理窗口大小变化
  const handleResize = () => {
    if (chartInstance.value) {
      chartInstance.value.resize()
    }
  }

  // 初始化图表
  const initChart = () => {
    // 获取DOM元素
    const chartDom = document.getElementById(chartId)
    if (!chartDom) return

    // 创建图表实例
    chartInstance.value = echarts.init(chartDom)

    // 更新图表数据
    updateChart()

    // 添加窗口大小变化的监听器
    window.addEventListener('resize', handleResize)
  }

  // 更新图表数据
  const updateChart = () => {
    if (!chartInstance.value) return

    // 过滤掉总计项，只展示各类能源数据
    const data = props.chartData
      .filter((item) => item.type !== '总计')
      .map((item) => ({
        name: item.type,
        value: parseFloat(item.emission.replace(/,/g, ''))
      }))

    // 获取所有能源类型的唯一颜色
    const colors = ['#1e88e5', '#f44336', '#ff9800', '#4caf50', '#9c27b0']

    // 设置图表选项
    const option = {
      title: {
        text: '能源碳排放分析',
        left: 'center',
        top: 10,
        textStyle: {
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} kg CO₂ ({d}%)'
      },
      legend: {
        orient: 'horizontal',
        bottom: 10,
        data: data.map((item) => item.name)
      },
      series: [
        {
          name: '碳排放量',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '45%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 4,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '14',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: data,
          color: colors
        }
      ]
    }

    // 使用配置项设置图表
    chartInstance.value.setOption(option)
  }

  // 监听数据变化
  watch(
    () => props.chartData,
    () => {
      updateChart()
    },
    { deep: true }
  )

  // 组件挂载时初始化图表
  onMounted(() => {
    initChart()
    // 延迟执行一次resize，确保图表正确渲染
    setTimeout(() => {
      handleResize()
    }, 200)
  })

  // 组件卸载时销毁图表实例
  onUnmounted(() => {
    if (chartInstance.value) {
      chartInstance.value.dispose()
      chartInstance.value = null
    }
    window.removeEventListener('resize', handleResize)
  })
</script>

<style scoped>
  .energy-emission-chart {
    width: 100%;
    height: 300px;
    min-height: 300px;
  }
</style>
