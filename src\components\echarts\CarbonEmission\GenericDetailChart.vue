<template>
  <div class="chart-wrapper">
    <div class="chart-header">
      <h3 class="chart-title">
        <el-icon><OfficeBuilding /></el-icon>
        {{ buildingName }} - 分项碳排放情况
      </h3>
    </div>
    <div class="chart-body">
      <div :id="chartId" class="chart-container"></div>
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-mask">
        <el-icon class="loading-icon"><Loading /></el-icon>
        <span class="loading-text">数据加载中...</span>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted, watch, nextTick, computed, onBeforeUnmount, defineProps } from 'vue'
  import { ElMessage } from 'element-plus'
  import * as echarts from 'echarts/core'
  import { BarChart } from 'echarts/charts'
  import {
    TitleComponent,
    TooltipComponent,
    GridComponent,
    LegendComponent,
    DataZoomComponent
  } from 'echarts/components'
  import { CanvasRenderer } from 'echarts/renderers'
  import { Loading, OfficeBuilding } from '@element-plus/icons-vue'

  // 注册必要的组件
  echarts.use([
    BarChart,
    TitleComponent,
    TooltipComponent,
    GridComponent,
    LegendComponent,
    DataZoomComponent,
    CanvasRenderer
  ])

  // 定义props
  const props = defineProps({
    buildingId: {
      type: Number,
      required: true
    },
    buildingName: {
      type: String,
      default: '未知建筑'
    }
  })

  // 生成唯一的chart ID
  const chartId = computed(() => `building-detail-chart-${props.buildingId}`)

  // 图表颜色配置
  const CHART_COLORS = [
    '#409EFF', // 蓝色 - 用电
    '#E6A23C', // 黄色 - 用水
    '#F56C6C', // 红色 - 用气
    '#67C23A', // 绿色 - 光伏
    '#909399' // 灰色 - 其他
  ]

  // 本地状态
  const chartInstance = ref(null)
  const loading = ref(false) // 默认不加载，等待父组件传入数据
  const chartData = ref({})
  const legendSelected = ref({
    用电: true,
    用水: true,
    用气: true,
    光伏: true
  })

  const isEmpty = computed(() => {
    return (
      !chartData.value.series ||
      chartData.value.series.length === 0 ||
      !chartData.value.xAxis ||
      chartData.value.xAxis.length === 0
    )
  })

  // 格式化数字显示（添加千位分隔符）
  const formatNumber = (num) => {
    if (!num && num !== 0) return ''
    return parseFloat(num).toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })
  }

  // 添加更新图表的方法，供父组件调用
  const updateChart = (data) => {
    if (!data) return

    loading.value = true

    // 更新图表数据
    chartData.value = {
      xAxis: data.xAxis || [],
      legend: data.legend || [],
      series: data.series || []
    }

    // 在下一次DOM更新后更新图表
    nextTick(() => {
      if (!chartInstance.value) {
        initChart()
      }
      renderChart()
      loading.value = false
    })
  }

  // 初始化图表
  const initChart = () => {
    if (chartInstance.value) {
      chartInstance.value.dispose()
    }

    // 获取DOM元素
    const chartDom = document.getElementById(chartId.value)
    if (!chartDom) {
      console.error(`找不到图表DOM元素: ${chartId.value}`)
      return
    }

    // 创建图表实例
    chartInstance.value = echarts.init(chartDom)
  }

  // 渲染图表
  const renderChart = () => {
    if (!chartInstance.value || isEmpty.value) return

    const { xAxis = [], legend = [], series = [] } = chartData.value

    // 保存当前图例选中状态
    if (chartInstance.value) {
      const currentOption = chartInstance.value.getOption()
      if (
        currentOption &&
        currentOption.legend &&
        currentOption.legend[0] &&
        currentOption.legend[0].selected
      ) {
        Object.keys(currentOption.legend[0].selected).forEach((key) => {
          if (legendSelected.value.hasOwnProperty(key)) {
            legendSelected.value[key] = currentOption.legend[0].selected[key]
          }
        })
      }
    }

    const option = {
      color: CHART_COLORS,
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderColor: '#eee',
        borderWidth: 1,
        padding: [10, 15],
        textStyle: {
          color: '#333',
          fontSize: 13
        },
        formatter: function (params) {
          let html = `<div style="font-weight:500;margin-bottom:5px;color:#333">${params[0].name}</div>`

          params.forEach((item) => {
            if (item.value === undefined || item.value === null) return

            // 处理显示值
            let displayValue = item.value
            let displayUnit = 'kgCO₂'

            // 特殊处理光伏
            if (item.seriesName === '光伏') {
              // 显示为正值，但表示减排
              displayValue = Math.abs(item.value)
              displayUnit = 'kgCO₂ (减排)'
            }

            html += `
            <div style="display:flex;align-items:center;margin-bottom:3px;">
              <span style="display:inline-block;width:10px;height:10px;background:${
                item.color
              };margin-right:8px;border-radius:50%"></span>
              <span style="margin-right:8px;font-size:13px;color:#666">${item.seriesName}:</span>
              <span style="font-weight:bold;color:#333">${formatNumber(
                displayValue
              )} ${displayUnit}</span>
            </div>
          `
          })

          return html
        },
        axisPointer: {
          type: 'shadow',
          shadowStyle: {
            color: 'rgba(0, 0, 0, 0.05)'
          }
        }
      },
      legend: {
        data: legend,
        icon: 'circle',
        bottom: 0,
        left: 'center',
        itemWidth: 8,
        itemHeight: 8,
        textStyle: {
          color: '#606266',
          fontSize: 12
        },
        selected: legendSelected.value
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '13%',
        top: '12%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: xAxis,
        axisLine: {
          lineStyle: {
            color: '#E0E0E0'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          fontSize: 12,
          color: '#606266',
          margin: 12
        }
      },
      yAxis: {
        type: 'value',
        name: 'kg·CO₂',
        nameTextStyle: {
          color: '#909399',
          fontSize: 12,
          padding: [0, 0, 0, 10]
        },
        splitLine: {
          lineStyle: {
            color: '#F2F6FC',
            type: 'dashed'
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          fontSize: 12,
          color: '#606266',
          margin: 10,
          formatter: function (value) {
            if (value >= 1000) {
              return (value / 1000).toFixed(1) + 'k'
            }
            return value
          }
        }
      },
      series: series.map((item, index) => ({
        name: item.name,
        type: 'bar',
        barWidth: '20%',
        emphasis: {
          focus: 'series'
        },
        data: item.data
      }))
    }

    chartInstance.value.setOption(option)
  }

  // 窗口大小变化时，重新调整图表大小
  const handleResize = () => {
    if (chartInstance.value) {
      chartInstance.value.resize()
    }
  }

  // 挂载阶段加载数据
  onMounted(() => {
    // 初始化图表
    nextTick(() => {
      initChart()
      // 加载初始数据
      // loadChartData().then(() => {
      //   renderChart()
      // })
    })

    // 监听窗口大小变化
    window.addEventListener('resize', handleResize)
  })

  // 卸载阶段清理
  onBeforeUnmount(() => {
    window.removeEventListener('resize', handleResize)
    if (chartInstance.value) {
      chartInstance.value.dispose()
      chartInstance.value = null
    }
  })

  // 暴露方法给父组件
  defineExpose({
    updateChart
  })
</script>

<style lang="scss" scoped>
  .chart-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: var(--art-main-bg-color); // 使用主题背景色
    border-radius: 4px;
    box-shadow: var(--art-root-card-box-shadow); // 使用主题阴影

    .chart-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      border-bottom: 1px solid var(--art-border-color); // 使用主题边框颜色

      .chart-title {
        display: flex;
        align-items: center;
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--art-text-gray-800); // 使用主题文字颜色

        .el-icon {
          margin-right: 8px;
          font-size: 18px;
        }
      }

      .chart-actions {
        display: flex;
        gap: 8px;
        align-items: center;
      }
    }

    .chart-body {
      position: relative;
      padding: 16px;

      .chart-container {
        width: 100%;
        height: 350px;
      }

      .loading-mask {
        position: absolute;
        inset: 0;
        z-index: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: rgba(
          var(--art-main-bg-color-rgb, 255, 255, 255),
          0.7
        ); // 使用主题背景色带透明度

        .loading-icon {
          font-size: 24px;
          color: rgb(var(--art-primary)); // 使用主题主色
          animation: loading-rotate 2s linear infinite;
        }

        .loading-text {
          margin-top: 8px;
          font-size: 14px;
          color: var(--art-text-gray-600); // 使用主题文字颜色
        }
      }

      .empty-data {
        position: absolute;
        inset: 0;
        z-index: 1;
        display: flex;
        align-items: center;
        justify-content: center;

        .empty-desc {
          margin: 8px 0 0;
          color: var(--art-text-gray-500); // 使用主题文字颜色
        }

        .empty-tip {
          margin: 4px 0 0;
          font-size: 12px;
          color: var(--art-text-gray-400); // 使用主题文字颜色
        }
      }
    }
  }

  @keyframes loading-rotate {
    0% {
      transform: rotate(0);
    }

    100% {
      transform: rotate(360deg);
    }
  }
</style>
