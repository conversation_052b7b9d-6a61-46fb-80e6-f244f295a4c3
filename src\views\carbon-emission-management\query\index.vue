<template>
  <div class="query-container page-content">
    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <el-form :inline="true" :model="filterForm" ref="filterFormRef">
        <!-- 项目和楼栋选择 -->
        <ProjectBuildingSelector
          v-model="projectBuildingSelection"
          :auto-select-first="true"
          @change="handleProjectBuildingChange"
          @project-loaded="handleProjectListLoaded"
          custom-class="inline-selector"
        />
        <el-form-item label="查询字段:" prop="energyType">
          <el-select v-model="filterForm.energyType" placeholder="请选择查询字段" clearable>
            <el-option
              v-for="item in energyTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="时间:" prop="dateRange">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item class="button-group">
          <el-button type="primary" @click="handleQuery">
            <el-icon><Search /></el-icon> 查询
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon> 重置
          </el-button>
          <el-button type="success" @click="handleExport">
            <el-icon><Download /></el-icon> 导出列表
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 表格和操作区域 -->
    <el-card class="table-card">
      <!-- 使用 ArtTable 替代 el-table 和 el-pagination -->
      <ArtTable
        :data="tableData"
        :loading="loading"
        row-key="id"
        :border="false"
        :highlight-current-row="false"
        :total="totalCount"
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        index
        class="query-table"
      >
        <el-table-column prop="projectName" label="项目名称" align="center" />
        <el-table-column
          v-if="showBuildingColumn"
          prop="buildingName"
          label="楼栋"
          align="center"
        />
        <el-table-column label="时间" align="center">
          <template #default="{ row }">
            {{ formatReportTime(row.reportTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="elecQuantity" label="电(kWh)" align="center" />
        <el-table-column prop="waterQuantity" label="水(t)" align="center" />
        <el-table-column prop="co2Quantity" label="碳排放(kgCO₂)" align="center" />
      </ArtTable>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted, nextTick, watch } from 'vue'
  import { ElMessage, FormInstance } from 'element-plus'
  import { Search, Refresh, Download } from '@element-plus/icons-vue'
  import ProjectBuildingSelector from '@/components/ProjectBuildingSelector.vue'
  import { getCarbonSearchReport, type CarbonSearchItem } from '@/api/carbon-emission'
  import { getEnergyTypeDict } from '@/api/dictApi'
  // ArtTable组件已在全局注册，无需导入

  // 定义接口或类型
  interface FilterForm {
    energyType: string
    dateRange: [string, string] | undefined
  }

  interface Option {
    value: string
    label: string
  }

  // 筛选表单的引用
  const filterFormRef = ref<FormInstance>()
  const loading = ref(false) // 表格加载状态

  // 项目楼栋选择相关数据
  const projectBuildingSelection = ref({
    projectId: null,
    buildingId: null
  })
  const selectedProjectInfo = ref<any>(null)
  const selectedBuildingInfo = ref<any>(null)

  // 控制楼栋列显示
  const showBuildingColumn = ref(false)

  // 获取默认日期范围（今天往前数30天）
  const getDefaultDateRange = (): [string, string] => {
    const today = new Date()
    const thirtyDaysAgo = new Date(today)
    thirtyDaysAgo.setDate(today.getDate() - 30)

    const formatDate = (date: Date): string => {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    }

    return [formatDate(thirtyDaysAgo), formatDate(today)]
  }

  // 筛选表单数据
  const filterForm = reactive<FilterForm>({
    energyType: '',
    dateRange: getDefaultDateRange() // 默认时间范围：今天往前数30天
  })

  // 表格数据
  const tableData = ref<CarbonSearchItem[]>([])
  const totalCount = ref(0)

  // 查询字段选项
  const energyTypeOptions = ref<Option[]>([])

  // 获取查询字段字典数据
  const fetchEnergyTypeOptions = async () => {
    try {
      const response = await getEnergyTypeDict()
      if (response.code === 200) {
        energyTypeOptions.value = response.data.map((item) => ({
          value: item.key,
          label: item.label
        }))
        // 默认选中碳排放字段
        const carbonEmissionOption = energyTypeOptions.value.find(
          (item) => item.label.includes('碳排放') || item.label.includes('碳')
        )
        if (carbonEmissionOption && !filterForm.energyType) {
          filterForm.energyType = carbonEmissionOption.value
        }
      } else {
        console.error('获取查询字段失败:', response.msg)
        // 使用默认数据作为备选
        energyTypeOptions.value = [
          { value: '0', label: '耗水' },
          { value: '1', label: '耗电' },
          { value: '2', label: '耗气' },
          { value: '3', label: '光伏' }
        ]
      }
    } catch (error) {
      console.error('获取查询字段失败:', error)
      // 使用默认数据作为备选
      energyTypeOptions.value = [
        { value: '0', label: '耗水' },
        { value: '1', label: '耗电' },
        { value: '2', label: '耗气' },
        { value: '3', label: '光伏' }
      ]
    }
  }

  // 分页配置
  const pagination = reactive({
    currentPage: 1,
    pageSize: 10
  })

  // 处理项目楼栋变化
  const handleProjectBuildingChange = (data: any) => {
    selectedProjectInfo.value = data.projectInfo
    selectedBuildingInfo.value = data.buildingInfo
    console.log('项目楼栋选择变化:', data)

    // 控制楼栋列显示：当选择了具体楼栋时显示楼栋列，否则隐藏
    showBuildingColumn.value = !!data.buildingId

    // 如果选择了项目，自动查询数据
    if (data.projectId) {
      // 重置分页到第一页
      pagination.currentPage = 1
      nextTick(() => {
        fetchCarbonSearchData()
      })
    } else {
      // 如果没有选择项目，清空表格数据
      tableData.value = []
      totalCount.value = 0
    }
  }

  // 监听项目列表加载完成
  const handleProjectListLoaded = (projectList: any[]) => {
    console.log('项目列表加载完成:', projectList)
    // 当项目列表加载完成且自动选择了第一个项目后，自动发送查询请求
    if (projectList && projectList.length > 0) {
      const firstProject = projectList[0]
      console.log('自动选择第一个项目:', firstProject)
      // 使用nextTick确保项目选择状态已更新
      nextTick(() => {
        // 自动发送查询请求
        if (projectBuildingSelection.value.projectId) {
          console.log('自动发送初始化查询请求')
          fetchCarbonSearchData()
        }
      })
    }
  }

  // 获取碳排放数据查询列表
  const fetchCarbonSearchData = async () => {
    // 如果没有选择项目，不发请求
    if (!projectBuildingSelection.value.projectId) {
      console.log('没有选择项目，不发请求')
      ElMessage.warning('请先选择项目')
      return
    }

    try {
      loading.value = true
      const params = {
        projectId: projectBuildingSelection.value.projectId,
        buildingId: projectBuildingSelection.value.buildingId || undefined,
        energyType: filterForm.energyType || undefined,
        beginTime: filterForm.dateRange?.[0] || undefined,
        endTime: filterForm.dateRange?.[1] || undefined,
        pageSize: pagination.pageSize,
        pageNum: pagination.currentPage
      }

      console.log('发送碳排放数据查询请求，参数:', params)
      const response = await getCarbonSearchReport(params)
      console.log('碳排放数据查询响应:', response)

      if (response.code === 200) {
        tableData.value = response.rows || []
        totalCount.value = response.total || 0
        console.log('✅ 获取碳排放数据成功:', tableData.value)
        // 调试：查看第一条数据的结构
        if (tableData.value.length > 0) {
          console.log('第一条数据结构:', tableData.value[0])
          console.log('reportTime字段值:', tableData.value[0].reportTime)
        }
      } else {
        ElMessage.error(response.msg || '获取碳排放数据失败')
        tableData.value = []
        totalCount.value = 0
      }
    } catch (error) {
      console.error('获取碳排放数据失败:', error)
      ElMessage.error('获取碳排放数据失败，请稍后重试')
      tableData.value = []
      totalCount.value = 0
    } finally {
      loading.value = false
    }
  }

  // 查询处理
  const handleQuery = () => {
    pagination.currentPage = 1
    fetchCarbonSearchData()
  }

  // 重置处理
  const handleReset = () => {
    // 重置项目楼栋选择
    projectBuildingSelection.value = {
      projectId: null,
      buildingId: null
    }
    selectedProjectInfo.value = null
    selectedBuildingInfo.value = null

    // 重置筛选表单
    filterFormRef.value?.resetFields()
    filterForm.energyType = ''
    filterForm.dateRange = getDefaultDateRange() // 使用动态计算的默认日期范围

    // 重置分页
    pagination.currentPage = 1

    // 清空表格数据
    tableData.value = []
    totalCount.value = 0

    ElMessage.info('筛选条件已重置。')
  }

  // 导出列表处理 - 暂时占位
  const handleExport = () => {
    ElMessage.info('导出列表功能待实现。')
  }

  // 分页大小改变处理
  const handleSizeChange = (val: number) => {
    pagination.pageSize = val
    pagination.currentPage = 1
    // 重新获取数据
    if (projectBuildingSelection.value.projectId) {
      fetchCarbonSearchData()
    }
  }

  // 当前页改变处理
  const handleCurrentChange = (val: number) => {
    pagination.currentPage = val
    // 重新获取数据
    if (projectBuildingSelection.value.projectId) {
      fetchCarbonSearchData()
    }
  }

  // 监听查询字段变化，自动触发查询
  watch(
    () => filterForm.energyType,
    (newValue, oldValue) => {
      console.log('查询字段变化:', { oldValue, newValue })
      // 只有在有项目选择的情况下才自动查询
      if (projectBuildingSelection.value.projectId) {
        // 重置分页到第一页
        pagination.currentPage = 1
        nextTick(() => {
          fetchCarbonSearchData()
        })
      }
    }
  )

  // 监听日期范围变化，自动触发查询
  watch(
    () => filterForm.dateRange,
    (newValue, oldValue) => {
      console.log('日期范围变化:', { oldValue, newValue })
      // 只有在有项目选择的情况下才自动查询
      if (projectBuildingSelection.value.projectId) {
        // 重置分页到第一页
        pagination.currentPage = 1
        nextTick(() => {
          fetchCarbonSearchData()
        })
      }
    },
    { deep: true }
  )

  // 格式化报告时间，支持多种格式：YYYYMMDD、时间戳、YYYY-MM-DD
  const formatReportTime = (reportTime: string | number | undefined | null) => {
    console.log('formatReportTime 输入:', reportTime, '类型:', typeof reportTime)

    if (!reportTime && reportTime !== 0) {
      return '--'
    }

    // 如果是数字类型（可能是时间戳或YYYYMMDD格式的数字）
    if (typeof reportTime === 'number') {
      const numStr = String(reportTime)

      // 如果是8位数字，当作YYYYMMDD处理
      if (numStr.length === 8) {
        const year = numStr.substring(0, 4)
        const month = numStr.substring(4, 6)
        const day = numStr.substring(6, 8)
        return `${year}-${month}-${day}`
      }

      // 如果是13位数字，当作毫秒时间戳处理
      if (numStr.length === 13) {
        const date = new Date(reportTime)
        if (!isNaN(date.getTime())) {
          return date.toISOString().split('T')[0] // 返回 YYYY-MM-DD 格式
        }
      }

      // 如果是10位数字，当作秒时间戳处理
      if (numStr.length === 10) {
        const date = new Date(reportTime * 1000)
        if (!isNaN(date.getTime())) {
          return date.toISOString().split('T')[0] // 返回 YYYY-MM-DD 格式
        }
      }
    }

    // 转换为字符串处理
    const timeStr = String(reportTime).trim()

    // 如果是8位数字格式 YYYYMMDD
    if (timeStr.length === 8 && /^\d{8}$/.test(timeStr)) {
      const year = timeStr.substring(0, 4)
      const month = timeStr.substring(4, 6)
      const day = timeStr.substring(6, 8)
      return `${year}-${month}-${day}`
    }

    // 如果已经是 YYYY-MM-DD 格式，直接返回
    if (/^\d{4}-\d{2}-\d{2}$/.test(timeStr)) {
      return timeStr
    }

    // 其他情况返回原值或 --
    return timeStr || '--'
  }

  onMounted(() => {
    // 初始化时获取查询字段字典数据
    fetchEnergyTypeOptions()
  })
</script>

<style scoped>
  :deep(.el-form-item--large) {
    margin-bottom: 0 !important;
    margin-left: 10px !important;
  }

  .query-container {
    padding: 20px;
    background-color: #f0f2f5;
  }

  .filter-card {
    margin-bottom: 20px;
    border-radius: 4px;
  }

  .table-card {
    min-height: 400px;
    border-radius: 4px;
  }

  /* 确保ArtTable组件有足够的显示空间 */
  .table-card :deep(.art-table) {
    height: auto;
    min-height: 400px;
  }

  .table-actions {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 15px;
  }

  .el-form-item {
    margin-right: 15px;
  }

  .el-select,
  .el-input,
  .el-date-picker {
    width: 220px;
  }

  /* 移除表格宽度限制，让表格自适应 */
  .query-table {
    width: 100%;
  }

  /* 修复el-table__empty-block高度不断增长问题 */
  .query-table :deep(.el-table__empty-block) {
    height: auto !important;
    min-height: 60px;
    max-height: 400px;
    overflow: hidden;
  }

  /* 确保空表格状态下的布局稳定 */
  .query-table :deep(.el-table__body-wrapper) {
    height: auto !important;
    min-height: 200px;
  }

  .el-button .el-icon + span {
    margin-left: 5px;
  }

  /* 新增样式 */
  .el-radio-group {
    margin-right: 10px;
  }

  /* ProjectBuildingSelector 内联样式 */
  .inline-selector {
    display: flex;
    gap: 15px;
    align-items: center;
  }

  .inline-selector .filter-item {
    display: flex;
    align-items: center;
    margin-right: 15px;
  }

  .inline-selector .filter-label {
    margin-right: 8px;
    white-space: nowrap;
  }

  .inline-selector .filter-select {
    width: 180px;
  }

  /* 确保表单元素在一行显示不换行 */
  .filter-card .el-form {
    position: relative;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    padding-right: 300px; /* 为按钮组预留空间 */
    padding-bottom: 5px;
    overflow-x: auto;
  }

  .filter-card .el-form::-webkit-scrollbar {
    height: 6px;
  }

  .filter-card .el-form::-webkit-scrollbar-thumb {
    background: #c0c4cc;
    border-radius: 3px;
  }

  .filter-card .el-form::-webkit-scrollbar-track {
    background: #f0f2f5;
  }

  /* 按钮组靠右显示 */
  .button-group {
    position: absolute !important;
    right: 20px;
    margin-right: 0 !important;
  }

  /* 确保按钮之间有合适的间距 */
  .button-group .el-button {
    margin-left: 8px;
  }

  .button-group .el-button:first-child {
    margin-left: 0;
  }
</style>
