<template>
  <div class="building-energy-monitoring page-content">
    <!-- 头部筛选区域 -->
    <div class="filter-container">
      <div class="filter-row">
        <!-- 项目和楼栋选择器 -->
        <ProjectBuildingSelector
          ref="projectBuildingSelectorRef"
          v-model="projectBuildingSelection"
          @change="handleProjectBuildingChange"
          @project-loaded="handleProjectListLoaded"
        />
        <div class="filter-item">
          <span class="filter-label">时间</span>
          <el-radio-group
            v-model="dateType"
            class="time-granularity-buttons"
            @change="handleDateTypeChange"
          >
            <el-radio-button label="day">日</el-radio-button>
            <el-radio-button label="month">月</el-radio-button>
            <el-radio-button label="year">年</el-radio-button>
          </el-radio-group>
        </div>
        <div class="filter-item date-filter">
          <span class="filter-label">日期选择：</span>
          <div class="date-picker-group">
            <el-date-picker
              v-if="dateType === 'day'"
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="date-picker"
              @change="handleDateRangeChange"
            />
            <el-date-picker
              v-if="dateType === 'month'"
              v-model="dateRange"
              type="monthrange"
              range-separator="至"
              start-placeholder="开始月份"
              end-placeholder="结束月份"
              class="date-picker"
              @change="handleDateRangeChange"
            />
            <el-date-picker
              v-if="dateType === 'year'"
              v-model="yearRange"
              type="yearrange"
              range-separator="至"
              start-placeholder="开始年份"
              end-placeholder="结束年份"
              class="date-picker"
              value-format="YYYY"
              @change="handleYearRangeChange"
            />
          </div>
        </div>
        <div class="filter-actions">
          <el-button
            type="success"
            @click="exportData"
            :loading="exportLoading"
            :disabled="exportLoading || tableData.length === 0"
          >
            <template v-if="!exportLoading">
              <el-icon><Download /></el-icon>
              导出
            </template>
            <template v-else>
              {{ exportProgressText }}
            </template>
          </el-button>
        </div>
      </div>
    </div>

    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="menu-card" :data-type="getCardType">
          <!-- 添加级联选择器 -->

          <el-menu
            mode="vertical"
            :default-active="activeMenu"
            @select="handleMenuSelect"
            class="monitoring-menu"
            v-loading="equipmentTypesLoading"
            element-loading-text="加载设备类型..."
          >
            <!-- 如果有接口数据，按分类显示 -->
            <template v-if="equipmentTypes.length > 0">
              <!-- 用电数据分类 -->
              <el-menu-item-group title="用电数据" v-if="electricityItems.length > 0">
                <el-menu-item v-for="item in electricityItems" :key="item.key" :index="item.key">
                  <el-icon><component :is="getIconByKey(item.key)" /></el-icon>
                  <span>{{ item.label }}</span>
                </el-menu-item>
              </el-menu-item-group>

              <!-- 用水数据分类 -->
              <el-menu-item-group title="用水数据" v-if="waterItems.length > 0">
                <el-menu-item v-for="item in waterItems" :key="item.key" :index="item.key">
                  <el-icon><component :is="getIconByKey(item.key)" /></el-icon>
                  <span>{{ item.label }}</span>
                </el-menu-item>
              </el-menu-item-group>

              <!-- 用气数据分类 -->
              <el-menu-item-group title="用气数据" v-if="gasItems.length > 0">
                <el-menu-item v-for="item in gasItems" :key="item.key" :index="item.key">
                  <el-icon><component :is="getIconByKey(item.key)" /></el-icon>
                  <span>{{ item.label }}</span>
                </el-menu-item>
              </el-menu-item-group>

              <!-- 其他数据分类 -->
              <el-menu-item-group title="其他数据" v-if="otherItems.length > 0">
                <el-menu-item v-for="item in otherItems" :key="item.key" :index="item.key">
                  <el-icon><component :is="getIconByKey(item.key)" /></el-icon>
                  <span>{{ item.label }}</span>
                </el-menu-item>
              </el-menu-item-group>
            </template>

            <!-- 如果接口数据为空，显示默认菜单 -->
            <template v-if="equipmentTypes.length === 0 && !equipmentTypesLoading">
              <!-- 用电数据分类 -->
              <el-menu-item-group title="用电数据">
                <el-menu-item index="0" v-if="showBuildingTotalElectricity">
                  <el-icon><Odometer /></el-icon>
                  <span>建筑总用电量</span>
                </el-menu-item>
                <el-menu-item index="1">
                  <el-icon><Odometer /></el-icon>
                  <span>建筑空调用电数据</span>
                </el-menu-item>
                <el-menu-item index="2">
                  <el-icon><ReadingLamp /></el-icon>
                  <span>建筑照明用电数据</span>
                </el-menu-item>
                <el-menu-item index="3">
                  <el-icon><Lightning /></el-icon>
                  <span>建筑特殊用电数据</span>
                </el-menu-item>
                <el-menu-item index="4">
                  <el-icon><OfficeBuilding /></el-icon>
                  <span>建筑动力用电数据</span>
                </el-menu-item>
              </el-menu-item-group>

              <!-- 用水数据分类 -->
              <el-menu-item-group title="用水数据">
                <el-menu-item index="5">
                  <el-icon><Bowl /></el-icon>
                  <span>建筑用水数据</span>
                </el-menu-item>
              </el-menu-item-group>

              <!-- 用气数据分类 -->
              <el-menu-item-group title="用气数据">
                <el-menu-item index="6">
                  <el-icon><Smoking /></el-icon>
                  <span>建筑用气数据</span>
                </el-menu-item>
              </el-menu-item-group>

              <!-- 其他数据分类 -->
              <el-menu-item-group title="其他数据">
                <el-menu-item index="7">
                  <el-icon><HotWater /></el-icon>
                  <span>其他数据</span>
                </el-menu-item>
              </el-menu-item-group>
            </template>
          </el-menu>
        </el-card>
      </el-col>

      <el-col :span="18">
        <el-card class="data-card" :data-type="getCardType">
          <template #header>
            <div class="data-header">
              <span>
                <el-icon>
                  <component :is="getChartIcon"></component>
                </el-icon>
                {{ currentTitle }}
              </span>
            </div>
          </template>

          <div class="data-content">
            <div class="content-wrapper">
              <div
                class="chart-body"
                v-loading="energyDataLoading"
                element-loading-text="加载数据中..."
              >
                <BuildingEnergyChart
                  :title="currentTitle"
                  :data="tableData"
                  :x-axis-data="chartData.xAxisData"
                  :y-axis-data="chartData.yAxisData"
                  :unit="chartData.unit"
                  :options="{
                    menuType: activeMenu,
                    unit: getUnit
                  }"
                  style="width: 100%; height: 100%"
                />
              </div>
              <!-- 用水数据展示 -->
              <template v-if="isWaterData">
                <div class="table-container">
                  <el-table
                    :data="tableData.slice((currentPage - 1) * pageSize, currentPage * pageSize)"
                    style="width: 100%; margin-top: 20px"
                    border
                    stripe
                  >
                    <el-table-column prop="date" :label="dateColumnLabel" align="center" />
                    <el-table-column prop="value" label="数值" align="center" />
                    <el-table-column prop="unit" label="单位" align="center" />
                  </el-table>
                  <div class="pagination">
                    <el-pagination
                      v-model:currentPage="currentPage"
                      v-model:page-size="pageSize"
                      :page-sizes="[10, 20, 50, 100]"
                      layout="total, sizes, prev, pager, next, jumper"
                      :total="tableData.length"
                      @size-change="handleSizeChange"
                      @current-change="handleCurrentChange"
                    />
                  </div>
                </div>
              </template>

              <!-- 用气数据展示 -->
              <template v-if="isGasData">
                <div class="table-container">
                  <el-table
                    :data="tableData.slice((currentPage - 1) * pageSize, currentPage * pageSize)"
                    style="width: 100%; margin-top: 20px"
                    border
                    stripe
                  >
                    <el-table-column prop="date" :label="dateColumnLabel" align="center" />
                    <el-table-column prop="value" label="数值" align="center" />
                    <el-table-column prop="unit" label="单位" align="center" />
                  </el-table>
                  <div class="pagination">
                    <el-pagination
                      v-model:currentPage="currentPage"
                      v-model:page-size="pageSize"
                      :page-sizes="[10, 20, 50, 100]"
                      layout="total, sizes, prev, pager, next, jumper"
                      :total="tableData.length"
                      @size-change="handleSizeChange"
                      @current-change="handleCurrentChange"
                    />
                  </div>
                </div>
              </template>

              <!-- 其他数据展示 -->
              <template v-if="isOtherData">
                <div class="table-container">
                  <el-table
                    :data="tableData.slice((currentPage - 1) * pageSize, currentPage * pageSize)"
                    style="width: 100%; margin-top: 20px"
                    border
                    stripe
                  >
                    <el-table-column prop="date" :label="dateColumnLabel" align="center" />
                    <el-table-column prop="value" label="数值" align="center" />
                    <el-table-column prop="unit" label="单位" align="center" />
                  </el-table>
                  <div class="pagination">
                    <el-pagination
                      v-model:currentPage="currentPage"
                      v-model:page-size="pageSize"
                      :page-sizes="[10, 20, 50, 100]"
                      layout="total, sizes, prev, pager, next, jumper"
                      :total="tableData.length"
                      @size-change="handleSizeChange"
                      @current-change="handleCurrentChange"
                    />
                  </div>
                </div>
              </template>

              <!-- 用电数据保持原有的表格展示 -->
              <template v-if="isElectricityData">
                <div class="table-container">
                  <el-table
                    :data="tableData.slice((currentPage - 1) * pageSize, currentPage * pageSize)"
                    style="width: 100%; margin-top: 20px"
                    border
                    stripe
                  >
                    <el-table-column prop="date" :label="dateColumnLabel" align="center" />
                    <el-table-column prop="value" label="数值" align="center" />
                    <el-table-column prop="unit" label="单位" align="center" />
                  </el-table>
                  <div class="pagination">
                    <el-pagination
                      v-model:currentPage="currentPage"
                      v-model:page-size="pageSize"
                      :page-sizes="[10, 20, 50, 100]"
                      layout="total, sizes, prev, pager, next, jumper"
                      :total="tableData.length"
                      @size-change="handleSizeChange"
                      @current-change="handleCurrentChange"
                    />
                  </div>
                </div>
              </template>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted } from 'vue'
  import BuildingEnergyChart from '@/components/charts/BuildingEnergyChart.vue'
  // 导入Element Plus图标
  import {
    Odometer, // 总用电量
    Lightning, // 特殊用电
    ReadingLamp, // 照明插座
    OfficeBuilding, // 动力用电
    Bowl, // 用水
    Smoking, // 用气
    HotWater, // 供冷供热
    Download // 下载
  } from '@element-plus/icons-vue'

  import { formatDate, menuTitles } from '@/components/echarts/buildingEnergyMonitoring'
  import { ElMessage } from 'element-plus'
  // 导入xlsx库用于Excel导出
  import * as XLSX from 'xlsx'
  import ProjectBuildingSelector from '@/components/ProjectBuildingSelector.vue'
  import { nextTick } from 'vue'
  // 导入设备类型接口和能耗数据接口
  import { getEquipmentTypeDict, getEnergyData } from '@/api/energy-monitoring/queryApi'

  const activeMenu = ref('0') // 默认选择建筑总用电量
  const dateType = ref('day') // 默认为日
  // 设置默认日期范围：今天的前七天
  const today = new Date()
  const sevenDaysAgo = new Date()
  sevenDaysAgo.setDate(today.getDate() - 6) // 前6天加上今天共7天
  const dateRange = ref([sevenDaysAgo, today])

  // 年份范围选择
  const yearRange = ref([])

  // 新增：图表数据
  const chartData = ref({
    xAxisData: [],
    yAxisData: [],
    unit: 'kWh'
  })

  // 设备类型数据
  const equipmentTypes = ref([])
  const equipmentTypesLoading = ref(false)

  // 能耗数据加载状态
  const energyDataLoading = ref(false)

  // 建筑总用电量显示控制
  const showBuildingTotalElectricity = ref(true)

  // 缓存上一次的查询参数，用于防重复请求
  const lastQueryParams = ref(null)

  // 请求防抖标识
  const isRequesting = ref(false)

  // 比较查询参数是否相同的工具函数
  const isSameQueryParams = (params1, params2) => {
    if (!params1 || !params2) return false

    return (
      params1.projectId === params2.projectId &&
      params1.buildingId === params2.buildingId &&
      params1.beginTime === params2.beginTime &&
      params1.endTime === params2.endTime &&
      params1.timeType === params2.timeType &&
      params1.equipmentType === params2.equipmentType
    )
  }

  // 项目和楼栋选择器相关数据
  const projectBuildingSelectorRef = ref()
  const projectBuildingSelection = ref({
    projectId: null,
    buildingId: null
  })
  const selectedProjectInfo = ref(null)
  const selectedBuildingInfo = ref(null)

  // 计算属性：按分类组织设备类型
  const electricityItems = computed(() => {
    return equipmentTypes.value.filter((item) => {
      // 如果是建筑总用电量，需要检查显示控制
      if (item.key === 'building_total_electricity' || item.key === '0') {
        return showBuildingTotalElectricity.value
      }

      return [
        'building_hvac_electricity',
        'building_lighting_electricity',
        'building_special_electricity',
        'building_power_electricity',
        '1',
        '2',
        '3',
        '4'
      ].includes(item.key)
    })
  })

  const waterItems = computed(() => {
    return equipmentTypes.value.filter((item) => ['building_water', '5'].includes(item.key))
  })

  const gasItems = computed(() => {
    return equipmentTypes.value.filter((item) => ['building_gas', '6'].includes(item.key))
  })

  const otherItems = computed(() => {
    return equipmentTypes.value.filter((item) => ['building_other', '7'].includes(item.key))
  })

  // 计算属性：判断当前选中的数据类型
  const isElectricityData = computed(() => {
    return [
      'building_total_electricity',
      'building_hvac_electricity',
      'building_lighting_electricity',
      'building_special_electricity',
      'building_power_electricity',
      '0',
      '1',
      '2',
      '3',
      '4'
    ].includes(activeMenu.value)
  })

  const isWaterData = computed(() => {
    return ['building_water', '5'].includes(activeMenu.value)
  })

  const isGasData = computed(() => {
    return ['building_gas', '6'].includes(activeMenu.value)
  })

  const isOtherData = computed(() => {
    return ['building_other', '7'].includes(activeMenu.value)
  })

  const currentTitle = computed(() => {
    const prefix = dateType.value === 'day' ? '日' : dateType.value === 'month' ? '月' : '年'

    // 优先使用接口返回的设备类型名称
    const equipmentType = equipmentTypes.value.find((item) => item.key === activeMenu.value)
    if (equipmentType) {
      return prefix + equipmentType.label + '数据'
    }

    // 如果接口数据中没有找到，使用默认标题
    return prefix + (menuTitles[activeMenu.value] || '设备数据')
  })

  const dateColumnLabel = computed(() => {
    return dateType.value === 'day' ? '日期' : dateType.value === 'month' ? '月份' : '年份'
  })

  const tableData = ref([])

  // 添加导出相关的状态变量
  const exportLoading = ref(false)
  const exportProgressText = ref('')

  // 修改导出数据方法
  const exportData = async () => {
    try {
      exportLoading.value = true
      exportProgressText.value = '准备数据...'

      // 准备要导出的数据
      const exportDataList = tableData.value.map((item) => ({
        [dateColumnLabel.value]: item.date,
        数值: item.value,
        单位: item.unit
      }))

      // 创建工作簿
      const wb = XLSX.utils.book_new()

      // 转换数据为工作表
      const ws = XLSX.utils.json_to_sheet(exportDataList)

      // 将工作表添加到工作簿
      XLSX.utils.book_append_sheet(wb, ws, currentTitle.value)

      exportProgressText.value = '生成文件...'

      // 生成文件名 - 使用已导入的 formatDate 函数
      const fileName = `${currentTitle.value}_${formatDate(new Date(), 'day')}.xlsx`

      // 将工作簿写入文件并下载
      exportProgressText.value = '下载中...'
      XLSX.writeFile(wb, fileName)

      // 导出成功
      ElMessage.success('数据导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('导出失败，请重试')
    } finally {
      // 重置状态
      exportLoading.value = false
      exportProgressText.value = ''
    }
  }

  const handleDateTypeChange = () => {
    // 根据不同的日期类型设置默认日期范围
    switch (dateType.value) {
      case 'day':
        // 设置为今天的前七天（前6天加上今天共7天）
        const today = new Date()
        const sevenDaysAgo = new Date()
        sevenDaysAgo.setDate(today.getDate() - 6)
        dateRange.value = [sevenDaysAgo, today]
        break
      case 'month':
        dateRange.value = [new Date(new Date().setMonth(new Date().getMonth() - 6)), new Date()]
        break
      case 'year':
        // 年份模式下，设置默认年份范围为当前年份
        const currentYear = new Date().getFullYear().toString()
        yearRange.value = [currentYear, currentYear]
        break
    }
    // 日期类型变化时强制查询
    forceQuery()
  }

  // 处理日期范围变化
  const handleDateRangeChange = (dateRangeValue) => {
    if (dateRangeValue && dateRangeValue.length === 2) {
      // 日期范围变化时自动触发查询
      forceQuery()
    }
  }

  // 处理年份范围选择变化
  const handleYearRangeChange = (yearRangeValue) => {
    if (yearRangeValue && yearRangeValue.length === 2) {
      // 年份范围变化时自动触发查询
      forceQuery()
    }
  }

  const handleMenuSelect = (index) => {
    // 防止重复点击相同的菜单项
    if (index === activeMenu.value) {
      console.log('菜单项未变化，跳过重复请求')
      return
    }

    activeMenu.value = index
    handleQuery()
  }

  const handleQuery = () => {
    // 防止在请求进行中重复点击
    if (isRequesting.value) {
      console.log('请求进行中，跳过重复请求')
      return
    }

    // 构建当前查询参数
    let beginTime, endTime

    if (dateType.value === 'year' && yearRange.value && yearRange.value.length === 2) {
      // 年份范围选择：如果开始和结束年份相同，只发送一个年份值
      if (yearRange.value[0] === yearRange.value[1]) {
        // 相同年份时，只发送一个值
        beginTime = yearRange.value[0]
        endTime = yearRange.value[0]
      } else {
        // 不同年份时，发送范围
        beginTime = yearRange.value[0]
        endTime = yearRange.value[1]
      }
    } else {
      // 其他模式使用日期范围
      beginTime = getTimeValue(dateRange.value[0])
      endTime = getTimeValue(dateRange.value[1])
    }

    const currentParams = {
      projectId: projectBuildingSelection.value.projectId,
      buildingId: projectBuildingSelection.value.buildingId || undefined,
      beginTime,
      endTime,
      timeType: getTimeType(),
      equipmentType: activeMenu.value
    }

    // 检查查询参数是否与上次相同
    if (isSameQueryParams(currentParams, lastQueryParams.value)) {
      console.log('查询参数未变化，跳过重复请求:', currentParams)
      ElMessage.info('查询条件未变化')
      return
    }

    // 调用真实接口获取数据
    fetchEnergyData()
  }

  // 强制查询方法（忽略缓存，用于特殊情况）
  const forceQuery = () => {
    // 清空缓存的查询参数，强制执行查询
    lastQueryParams.value = null
    handleQuery()
  }

  // 处理项目和楼栋选择变化
  const handleProjectBuildingChange = async (data) => {
    selectedProjectInfo.value = data.projectInfo
    selectedBuildingInfo.value = data.buildingInfo

    // 当项目变化时，先检测建筑总用电量数据
    if (data.projectId) {
      await checkBuildingTotalElectricityData()
      // 然后自动重新查询数据（使用强制查询，因为项目/楼栋变化必须重新获取数据）
      forceQuery()
    }
  }

  // 监听项目列表加载完成
  const handleProjectListLoaded = (projectList) => {
    // 当项目列表加载完成后，如果有默认选中的项目，自动获取数据
    if (projectList && projectList.length > 0) {
      // 延迟一下，等待项目选择器设置默认值
      nextTick(async () => {
        if (projectBuildingSelection.value.projectId) {
          // 先检测建筑总用电量数据
          await checkBuildingTotalElectricityData()
          // 然后获取数据
          forceQuery()
        }
      })
    }
  }

  // 获取设备类型数据
  const fetchEquipmentTypes = async () => {
    try {
      equipmentTypesLoading.value = true

      const response = await getEquipmentTypeDict()

      if (response.code === 200) {
        equipmentTypes.value = response.data || []

        // 如果有数据，设置默认选中第一个
        if (equipmentTypes.value.length > 0) {
          activeMenu.value = equipmentTypes.value[0].key
        }
      } else {
        console.error('设备类型数据获取失败:', response.msg)
        ElMessage.error(response.msg || '设备类型数据获取失败')
      }
    } catch (error) {
      console.error('获取设备类型数据失败:', error)
      ElMessage.error('获取设备类型数据失败，请稍后重试')
    } finally {
      equipmentTypesLoading.value = false
    }
  }

  // 检测建筑总用电量数据是否存在
  const checkBuildingTotalElectricityData = async () => {
    try {
      // 如果没有选择项目，直接返回
      if (!projectBuildingSelection.value.projectId) {
        return
      }

      // 构建检测请求参数
      const today = new Date()
      const sevenDaysAgo = new Date()
      sevenDaysAgo.setDate(today.getDate() - 6)

      const params = {
        projectId: projectBuildingSelection.value.projectId,
        buildingId: projectBuildingSelection.value.buildingId || undefined,
        beginTime: formatDate(sevenDaysAgo, 'day'),
        endTime: formatDate(today, 'day'),
        timeType: 0, // 日视图
        equipmentType: '0' // 建筑总用电量
      }

      console.log('检测建筑总用电量数据参数:', params)

      const response = await getEnergyData(params)

      if (response.code === 200 && response.data) {
        // 检查 dataList 是否为空数组
        const hasData = response.data.dataList && response.data.dataList.length > 0
        showBuildingTotalElectricity.value = hasData

        console.log('建筑总用电量数据检测结果:', {
          dataList: response.data.dataList,
          hasData,
          showBuildingTotalElectricity: showBuildingTotalElectricity.value
        })

        // 如果建筑总用电量没有数据且当前选中的是建筑总用电量，需要切换到其他选项
        if (
          !hasData &&
          (activeMenu.value === '0' || activeMenu.value === 'building_total_electricity')
        ) {
          // 直接使用设备类型数据寻找第一个可用的菜单项，避免循环依赖
          const availableElectricityItems = equipmentTypes.value.filter((item) =>
            [
              'building_hvac_electricity',
              'building_lighting_electricity',
              'building_special_electricity',
              'building_power_electricity',
              '1',
              '2',
              '3',
              '4'
            ].includes(item.key)
          )
          const availableWaterItems = equipmentTypes.value.filter((item) =>
            ['building_water', '5'].includes(item.key)
          )
          const availableGasItems = equipmentTypes.value.filter((item) =>
            ['building_gas', '6'].includes(item.key)
          )
          const availableOtherItems = equipmentTypes.value.filter((item) =>
            ['building_other', '7'].includes(item.key)
          )

          const allAvailableItems = [
            ...availableElectricityItems,
            ...availableWaterItems,
            ...availableGasItems,
            ...availableOtherItems
          ]

          if (allAvailableItems.length > 0) {
            activeMenu.value = allAvailableItems[0].key
            console.log('自动切换到可用菜单项:', allAvailableItems[0])
          } else {
            // 如果没有其他可用项，使用默认的建筑空调用电
            activeMenu.value = '1'
          }
        }
      } else {
        console.warn('建筑总用电量数据检测失败:', response.msg)
        // 检测失败时默认显示
        showBuildingTotalElectricity.value = true
      }
    } catch (error) {
      console.error('检测建筑总用电量数据失败:', error)
      // 检测失败时默认显示
      showBuildingTotalElectricity.value = true
    }
  }

  // 获取能耗数据
  const fetchEnergyData = async () => {
    try {
      // 设置请求状态
      energyDataLoading.value = true
      isRequesting.value = true

      // 构建请求参数
      let beginTime, endTime

      if (dateType.value === 'year' && yearRange.value && yearRange.value.length === 2) {
        // 年份范围选择：如果开始和结束年份相同，只发送一个年份值
        if (yearRange.value[0] === yearRange.value[1]) {
          // 相同年份时，只发送一个值
          beginTime = yearRange.value[0]
          endTime = yearRange.value[0]
        } else {
          // 不同年份时，发送范围
          beginTime = yearRange.value[0]
          endTime = yearRange.value[1]
        }
      } else {
        // 其他模式使用日期范围
        beginTime = getTimeValue(dateRange.value[0])
        endTime = getTimeValue(dateRange.value[1])
      }

      const params = {
        projectId: projectBuildingSelection.value.projectId,
        buildingId: projectBuildingSelection.value.buildingId || undefined,
        beginTime,
        endTime,
        timeType: getTimeType(),
        equipmentType: activeMenu.value
      }

      // 缓存当前查询参数
      lastQueryParams.value = { ...params }

      // 如果没有选择项目，不发请求
      if (!params.projectId) {
        chartData.value = {
          xAxisData: [],
          yAxisData: [],
          unit: getUnit.value
        }
        tableData.value = []
        return
      }

      const response = await getEnergyData(params)

      if (response.code === 200 && response.data) {
        // 更新图表数据
        chartData.value = {
          xAxisData: response.data.timeList || [],
          yAxisData: response.data.dataList || [],
          unit: response.data.unit || getUnit.value
        }

        // 更新表格数据（保持向后兼容）
        tableData.value = (response.data.timeList || []).map((time, index) => ({
          date: time,
          value: (response.data.dataList[index] || 0).toString(),
          unit: response.data.unit || getUnit.value
        }))
      } else {
        console.error('能耗数据获取失败:', response.msg)
        ElMessage.error(response.msg || '数据获取失败')

        // 清空数据
        chartData.value = {
          xAxisData: [],
          yAxisData: [],
          unit: getUnit.value
        }
        tableData.value = []
      }
    } catch (error) {
      console.error('获取能耗数据失败:', error)
      ElMessage.error('获取能耗数据失败，请稍后重试')

      // 清空数据
      chartData.value = {
        xAxisData: [],
        yAxisData: [],
        unit: getUnit.value
      }
      tableData.value = []
    } finally {
      // 重置请求状态
      energyDataLoading.value = false
      isRequesting.value = false
    }
  }

  // 时间处理辅助函数
  const getTimeValue = (date) => {
    if (!date) return ''

    switch (dateType.value) {
      case 'year':
        return date.getFullYear().toString()
      case 'month':
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
      case 'day':
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
      default:
        return date.getFullYear().toString()
    }
  }

  // 获取时间类型
  const getTimeType = () => {
    switch (dateType.value) {
      case 'day':
        return '0'
      case 'month':
        return '1'
      case 'year':
        return '2'
      default:
        return '2'
    }
  }

  // 组件挂载时初始化图表和获取默认数据
  onMounted(async () => {
    // 先获取设备类型数据
    await fetchEquipmentTypes()

    // 如果没有从接口获取到数据，设置默认选中的菜单项
    if (equipmentTypes.value.length === 0) {
      activeMenu.value = '0'
    }

    // 等待项目选择器加载完成后再获取数据
  })

  // 添加单位计算属性
  const getUnit = computed(() => {
    switch (activeMenu.value) {
      // 用水数据 (5)
      case 'building_water':
      case '5':
        return 'm³'
      // 用气数据 (6)
      case 'building_gas':
      case '6':
        return 'm³'
      // 其他数据 (7)
      case 'building_other':
      case '7':
        return 'kWh'
      // 用电数据（0-4，默认）
      default:
        return 'kWh'
    }
  })

  // 根据key获取对应的图标组件
  const getIconByKey = (key) => {
    switch (key) {
      // 用电数据相关图标 (0-4)
      case 'building_total_electricity':
      case '0':
        return 'Odometer' // 建筑总用电量
      case 'building_hvac_electricity':
      case '1':
        return 'Odometer' // 建筑空调用电数据
      case 'building_lighting_electricity':
      case '2':
        return 'ReadingLamp' // 建筑照明用电数据
      case 'building_special_electricity':
      case '3':
        return 'Lightning' // 建筑特殊用电数据
      case 'building_power_electricity':
      case '4':
        return 'OfficeBuilding' // 建筑动力用电数据
      // 用水数据相关图标 (5)
      case 'building_water':
      case '5':
        return 'Bowl' // 建筑用水数据
      // 用气数据相关图标 (6)
      case 'building_gas':
      case '6':
        return 'Smoking' // 建筑用气数据
      // 其他数据相关图标 (7)
      case 'building_other':
      case '7':
        return 'HotWater' // 其他数据
      default:
        return 'Odometer'
    }
  }

  // 添加图表图标计算属性
  const getChartIcon = computed(() => {
    return getIconByKey(activeMenu.value)
  })

  // 添加卡片类型计算属性
  const getCardType = computed(() => {
    // 根据不同的菜单项返回不同的卡片类型
    switch (activeMenu.value) {
      case 'building_water':
      case 'building_gas':
      case 'building_other':
      case '5': // 用水
      case '6': // 用气
      case '7': // 其他
        return 'energy-data'
      default: // 用电类型 (0-4)
        return 'electricity-data'
    }
  })

  // 分页相关
  const currentPage = ref(1)
  const pageSize = ref(10)

  // 处理页码变化
  const handleCurrentChange = (val) => {
    currentPage.value = val
  }

  // 处理每页条数变化
  const handleSizeChange = (val) => {
    pageSize.value = val
  }

  // 监听年份范围选择变化，自动触发查询
  watch(
    () => yearRange.value,
    (newVal, oldVal) => {
      if (JSON.stringify(newVal) !== JSON.stringify(oldVal) && newVal && newVal.length === 2) {
        console.log('年份范围选择变化:', newVal)
        // 年份范围变化时自动触发查询
        forceQuery()
      }
    },
    { deep: true }
  )
</script>

<style lang="scss" scoped>
  .building-energy-monitoring {
    min-height: calc(100vh - 60px);
    padding: 16px;

    /* 筛选容器样式 - 参考能耗分析页面 */
    .filter-container {
      padding: 16px;
      margin-bottom: 16px;
      background-color: var(--el-bg-color);
      border-radius: 8px;
      box-shadow: var(--el-box-shadow-light);

      .filter-row {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        align-items: center;
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .filter-item {
        display: flex;
        align-items: center;
        margin-right: 0;

        .filter-label {
          margin-right: 8px;
          font-size: 14px;
          color: var(--el-text-color-regular);
          white-space: nowrap;
        }

        .filter-select {
          width: 200px;
        }

        .time-granularity-buttons {
          margin-left: 8px;
        }
      }

      .date-filter {
        .date-picker-group {
          display: flex;

          .date-picker {
            width: 100%;
            max-width: 300px;
          }
        }
      }

      .filter-actions {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
        margin-left: auto;
      }
    }

    /* 主题变量 - 适配白天和黑夜模式 */
    --em-bg-color: var(--art-bg-color, #fafbfc);
    --em-main-bg-color: var(--art-main-bg-color, #fff);
    --em-card-bg-color: var(--art-main-bg-color, #fff);
    --em-hover-bg-color: var(--art-hoverColor, rgb(246 249 252));
    --em-item-bg-color: var(--art-gray-200, #f1f1f4);
    --em-item-hover-bg-color: var(--art-gray-300, #dbdfe9);

    /* 边框和阴影 */
    --em-border-color: var(--art-border-color, #eaebf1);
    --em-box-shadow: var(--art-box-shadow-sm, 0 0.1rem 1rem 0.25rem rgb(0 0 0 / 5%));
    --em-box-shadow-hover: var(--art-box-shadow, 0 0.5rem 1.5rem 0.5rem rgb(0 0 0 / 7.5%));

    /* 文本颜色 */
    --em-text-primary: var(--art-text-gray-900, #071437);
    --em-text-secondary: var(--art-text-gray-600, #78829d);
    --em-text-tertiary: var(--art-text-gray-500, #99a1b7);
    --em-text-quaternary: var(--art-text-gray-400, #c4cada);

    /* 图表和卡片颜色 */
    --em-card-stat-bg: var(--art-gray-200, #f8f9fa);
    --em-card-stat-bg-hover: var(--art-gray-300, #f0f0f0);
    --em-icon-bg-water: linear-gradient(135deg, #03a9f4, #00bcd4);
    --em-icon-bg-gas: linear-gradient(135deg, #ff9800, #ff5722);
    --em-cooling-color: #409eff;
    --em-heating-color: #f56c6c;
    --em-trend-up-color: #f56c6c;
    --em-trend-down-color: #67c23a;
  }

  /* html.dark 状态下的主题变量自动生效 */

  /* 只在左侧菜单卡片中隐藏 ProjectBuildingSelector 组件的标签文字 */
  .menu-card :deep(.project-building-selector .filter-label),
  .menu-card :deep(.project-building-selector .filter-item .filter-label) {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    font-size: 0 !important;
    line-height: 0 !important;
    visibility: hidden !important;
    opacity: 0 !important;
  }

  .el-row {
    display: flex;
    flex: 1;
    height: 100%;
  }

  .el-col {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .menu-card,
  .data-card {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: var(--em-card-bg-color);
    border-radius: 8px;
    box-shadow: var(--em-box-shadow);
    transition: all 0.3s;
  }

  .menu-card:hover,
  .data-card:hover {
    box-shadow: var(--em-box-shadow-hover);
  }

  .card-header,
  .data-header {
    display: flex;
    flex: 0 0 auto;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    border-bottom: 1px solid var(--em-border-color);
  }

  .header-title {
    display: flex;
    gap: 8px;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    color: var(--em-text-primary);
  }

  .header-controls {
    display: flex;
    flex-wrap: nowrap;
    gap: 12px;
    align-items: center;
    min-width: 0;

    /* 隐藏 ProjectBuildingSelector 组件中的标签文字 */
    :deep(.project-building-selector .filter-item .filter-label) {
      display: none !important;
    }
  }

  .project-selector {
    width: 180px;
  }

  .monitoring-menu {
    padding: 10px;
    border-right: none;
  }

  :deep(.el-menu-item-group__title) {
    padding: 10px 20px;
    font-size: 13px;
    font-weight: 600;
    color: var(--em-text-tertiary);
  }

  :deep(.el-menu-item) {
    display: flex;
    gap: 8px;
    align-items: center;
    height: 50px;
    padding-left: 24px !important;
    margin: 4px 0;
    line-height: 50px;
    color: var(--em-text-secondary);
    border-radius: 8px 0 0 8px;
    transition: all 0.3s;
  }

  :deep(.el-menu-item .el-icon) {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    margin-right: 8px;
    font-size: 20px;
    text-align: center;
  }

  :deep(.el-menu-item:hover) {
    background-color: var(--em-hover-bg-color);
    transform: translateX(5px);
  }

  :deep(.el-menu-item.is-active) {
    color: white;
    background-color: #409eff;
  }

  :deep(.el-menu-item.is-active .el-icon) {
    color: white;
  }

  .content-wrapper {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 20px;
  }

  /* 图表容器样式优化 */
  .chart-container {
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    min-height: 500px;
    padding: 0;
    margin-bottom: 20px;
    overflow: hidden;
    background: var(--em-main-bg-color);
    border-radius: 12px;
    box-shadow: var(--em-box-shadow);
    transition: all 0.3s;
  }

  .chart-container:hover {
    box-shadow: var(--em-box-shadow-hover);
  }

  .chart-header {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background: var(--em-hover-bg-color);
    border-bottom: 1px solid var(--em-border-color);
  }

  .chart-title {
    display: flex;
    gap: 8px;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    color: var(--em-text-primary);
  }

  .chart-title i {
    color: #409eff;
  }

  .chart-actions {
    display: flex;
    gap: 10px;
  }

  .chart-body {
    position: relative;
    display: flex;
    flex: 1;
    flex-direction: column;
    min-height: 400px;
    padding: 20px;
  }

  .chart-inner {
    width: 100%;
    height: 100%;
  }

  /* 表格容器样式 */
  .table-container {
    flex-shrink: 0;
    width: 100%;
    padding: 20px;
    background: var(--em-main-bg-color);
    border-radius: 12px;
    box-shadow: var(--em-box-shadow);
    transition: all 0.3s;
  }

  /* 表格样式 */
  :deep(.el-table) {
    width: 100% !important;
    height: 200px;
    margin-top: 20px;
    overflow: hidden;
    border-radius: 8px;

    --el-table-border-color: var(--em-border-color);
    --el-table-header-bg-color: var(--em-hover-bg-color);
    --el-table-row-hover-bg-color: var(--em-hover-bg-color);
  }

  :deep(.el-table__body-wrapper) {
    height: calc(400px - 40px) !important;
    overflow-y: auto !important;
  }

  /* 隐藏表格内部滚动条，但保持滚动功能 */
  :deep(.el-table__body-wrapper::-webkit-scrollbar) {
    width: 0;
    height: 0;
  }

  /* 自定义滚动条 - 只保留菜单卡片的滚动条 */
  .menu-card::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .menu-card::-webkit-scrollbar-thumb {
    background: var(--em-text-quaternary);
    border-radius: 3px;
  }

  .menu-card::-webkit-scrollbar-track {
    background: transparent;
  }

  /* 隐藏数据卡片和内容区域的滚动条 */
  .data-card::-webkit-scrollbar,
  .data-content::-webkit-scrollbar {
    width: 0;
    height: 0;
  }

  /* 添加选择器样式 */
  :deep(.el-select) {
    min-width: 120px;
  }

  :deep(.el-date-editor) {
    width: 240px !important;
  }

  :deep(.el-select .el-input__wrapper),
  :deep(.el-date-editor .el-input__wrapper) {
    width: 100%;
    box-shadow: 0 0 0 1px var(--em-border-color) inset;
  }

  :deep(.el-select-dropdown__item) {
    display: flex;
    gap: 8px;
    align-items: center;
    padding: 0 12px;
  }

  :deep(.el-select-dropdown__item i) {
    width: 16px;
    font-size: 14px;
    text-align: center;
  }

  :deep(.el-button) {
    display: inline-flex;
    gap: 4px;
    align-items: center;
    border-radius: 6px;
    transition: all 0.3s;
  }

  :deep(.el-button:hover) {
    box-shadow: var(--em-box-shadow);
    transform: translateY(-2px);
  }

  :deep(.el-tag) {
    border-radius: 4px;
  }

  /* 移除内容区域的滚动条 */
  .data-card {
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .data-content {
    height: 918px;
    overflow: hidden;
  }

  /* 供冷供热数据卡片样式增强 */
  .temp-card {
    position: relative;
    padding-top: 65px;
    background-color: var(--em-main-bg-color);
  }

  .temp-header {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    display: flex;
    gap: 8px;
    align-items: center;
    padding: 15px 20px;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid var(--em-border-color);
  }

  .temp-header i {
    font-size: 18px;
  }

  .cooling .temp-header {
    color: var(--em-cooling-color);
  }

  .cooling .temp-header i {
    color: var(--em-cooling-color);
  }

  .heating .temp-header {
    color: var(--em-heating-color);
  }

  .heating .temp-header i {
    color: var(--em-heating-color);
  }

  .temp-body {
    width: 100%;
  }

  .temp-value {
    margin-bottom: 15px;
    font-size: 28px;
    font-weight: 600;
    text-align: center;
  }

  .cooling .temp-value {
    color: var(--em-cooling-color);
  }

  .heating .temp-value {
    color: var(--em-heating-color);
  }

  .temp-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-top: 20px;
  }

  .temp-stats .stat-item {
    padding: 10px;
    text-align: center;
    background: var(--em-card-stat-bg);
    border-radius: 8px;
  }

  .temp-stats .label {
    margin-bottom: 5px;
    font-size: 13px;
    color: var(--em-text-tertiary);
  }

  .temp-stats .value {
    font-size: 18px;
    font-weight: 600;
    color: var(--em-text-primary);
  }

  /* 用水数据样式 */
  .water-cards,
  .gas-cards {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-top: 20px;
  }

  .water-card,
  .gas-card {
    display: flex;
    gap: 15px;
    align-items: center;
    padding: 20px;
    background-color: var(--em-card-stat-bg);
    border-radius: 8px;
    transition: all 0.3s;
  }

  .water-card:hover,
  .gas-card:hover {
    background-color: var(--em-card-stat-bg-hover);
    box-shadow: var(--em-box-shadow);
    transform: translateY(-3px);
  }

  .water-icon,
  .gas-icon,
  .quality-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    font-size: 24px;
    border-radius: 50%;
  }

  .water-icon {
    color: white;
    background: var(--em-icon-bg-water);
  }

  .gas-icon {
    color: white;
    background: var(--em-icon-bg-gas);
  }

  .water-info,
  .gas-info,
  .quality-info {
    flex: 1;
  }

  .water-info .label,
  .gas-info .label,
  .quality-info .label {
    margin-bottom: 5px;
    font-size: 14px;
    color: var(--em-text-secondary);
  }

  .water-info .value,
  .gas-info .value,
  .quality-info .value {
    margin-bottom: 3px;
    font-size: 20px;
    font-weight: 600;
    color: var(--em-text-primary);
  }

  .water-info .desc,
  .gas-info .desc,
  .quality-info .desc {
    font-size: 12px;
    color: var(--em-text-tertiary);
  }

  .efficiency-stats {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
    margin-top: 20px;
  }

  .efficiency-item {
    padding: 15px;
    background-color: var(--em-card-stat-bg);
    border-radius: 8px;
    transition: all 0.3s;
  }

  .efficiency-item:hover {
    background-color: var(--em-card-stat-bg-hover);
  }

  .efficiency-item .label {
    margin-bottom: 8px;
    font-size: 14px;
    color: var(--em-text-secondary);
  }

  .efficiency-item .value {
    display: flex;
    gap: 5px;
    align-items: center;
    font-size: 18px;
    font-weight: 600;
    color: var(--em-text-primary);
  }

  .trend-up {
    color: var(--em-trend-up-color);
  }

  .trend-down {
    color: var(--em-trend-down-color);
  }

  .quality-stats {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
    margin-top: 20px;
  }

  .quality-item {
    display: flex;
    gap: 15px;
    align-items: center;
    padding: 15px;
    background-color: var(--em-card-stat-bg);
    border-radius: 8px;
    transition: all 0.3s;
  }

  .quality-item:hover {
    background-color: var(--em-card-stat-bg-hover);
  }

  .quality-icon {
    color: white;
    background: var(--em-icon-bg-water);
  }

  .quality-info .status {
    display: inline-block;
    padding: 2px 8px;
    margin-top: 3px;
    font-size: 12px;
    border-radius: 4px;
  }

  .quality-info .normal {
    color: #67c23a;
    background-color: #f0f9eb;
  }

  .quality-info .abnormal {
    color: #f56c6c;
    background-color: #fef0f0;
  }

  /* 用气数据样式 */
  .gas-carbon .carbon-stats {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
    margin-top: 20px;
  }

  .gas-carbon .carbon-item {
    display: flex;
    gap: 15px;
    align-items: center;
    padding: 15px;
    background-color: var(--em-card-stat-bg);
    border-radius: 8px;
    transition: all 0.3s;
  }

  .gas-carbon .carbon-item:hover {
    background-color: var(--em-card-stat-bg-hover);
  }

  .gas-carbon .carbon-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    font-size: 24px;
    color: white;
    background: var(--em-icon-bg-gas);
    border-radius: 50%;
  }

  .gas-carbon .carbon-info {
    flex: 1;
  }

  .gas-carbon .carbon-info .label {
    margin-bottom: 5px;
    font-size: 14px;
    color: var(--em-text-secondary);
  }

  .gas-carbon .carbon-info .value {
    margin-bottom: 3px;
    font-size: 20px;
    font-weight: 600;
    color: var(--em-text-primary);
  }

  .gas-carbon .carbon-info .desc {
    font-size: 12px;
    color: var(--em-text-tertiary);
  }

  .pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }

  /* 添加导出按钮样式 */
  :deep(.el-button.is-loading) {
    min-width: 120px;
  }

  /* 添加菜单滚动样式 */
  :deep(.el-menu) {
    height: 100%;
    overflow-y: scroll;
    background-color: var(--em-card-bg-color);
    border-right: none;
  }

  :deep(.el-menu::-webkit-scrollbar) {
    width: 6px;
  }

  :deep(.el-menu::-webkit-scrollbar-thumb) {
    background: var(--em-text-quaternary);
    border-radius: 3px;
  }

  :deep(.el-menu::-webkit-scrollbar-track) {
    background: transparent;
  }

  .section-title {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
    color: var(--em-text-primary);
  }

  .section-title i {
    color: var(--em-text-tertiary);
  }

  .energy-distribution {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 450px;
    padding: 20px;
    margin-bottom: 20px;
    background-color: var(--em-main-bg-color);
    border-radius: 8px;
    box-shadow: var(--em-box-shadow);
  }

  .pie-chart {
    position: relative;
    flex: 1;
    width: 100%;
    height: 350px;
  }

  .renewable-stats {
    width: 100%;
    padding: 20px 0;
    overflow: visible;
  }

  .renewable-stats .el-row {
    height: auto;
  }

  .renewable-stats .el-col {
    height: auto;
    min-height: 450px;
  }

  /* 添加级联选择器样式 */
  .building-selector {
    padding: 10px 15px;
    margin-bottom: 10px;
    border-bottom: 1px solid var(--em-border-color);
  }

  .building-selector :deep(.el-cascader) {
    width: 100%;
  }

  .building-selector :deep(.el-input__wrapper) {
    box-shadow: 0 0 0 1px var(--em-border-color) inset;
  }

  .building-selector :deep(.el-cascader__dropdown) {
    max-width: 500px;
  }

  /* Element Plus 图标样式 */
  .building-energy-monitoring .el-icon {
    margin-right: 5px;
    vertical-align: middle;
  }

  .section-title .el-icon {
    margin-right: 8px;
    font-size: 18px;
  }
</style>

<!-- 全局样式，用于隐藏 ProjectBuildingSelector 组件的标签 -->
<style>
  /* 隐藏分项计量页面中 ProjectBuildingSelector 组件的标签文字 */
  .building-energy-monitoring .project-building-selector .filter-label {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    font-size: 0 !important;
    line-height: 0 !important;
    visibility: hidden !important;
    opacity: 0 !important;
  }
</style>
