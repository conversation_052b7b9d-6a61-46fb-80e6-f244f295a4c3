import api from '@/utils/http'

// ========== 碳排放因子库管理相关接口 ==========

/**
 * 碳排放因子库数据项
 */
export interface CarbonFactorItem {
  createBy?: string
  createTime?: string
  updateBy?: string
  updateTime?: string
  remark?: string | null
  id: number
  name: string // 因子名称
  category?: string // 活动大类（不在界面显示）
  energyType: string // 能耗类型
  energyType2?: string // 活动子类（不在界面显示）
  gasType?: string // 气体类型（不在界面显示）
  factorValue: number // 因子数值
  factorUnit: string // 因子单位
  gwpValue?: number // GWP数值（不在界面显示）
  dataSource: string // 数据来源
  sourceYear?: string // 来源年份
  area?: string // 适用区域
  level?: string // 遵循标准
  factorVersion?: string // 因子版本
  status: string // 状态：0-选用，1-非选用
  delFlag?: string
}

/**
 * 碳排放因子库列表查询参数
 */
export interface CarbonFactorQueryParams {
  name?: string // 因子名称
  category?: string // 活动大类
  energyType?: string // 能源类型
  area?: string // 适用区域
  status?: string // 状态
  pageSize: number // 页大小
  pageNum: number // 当前页
}

/**
 * 碳排放因子库列表响应
 */
export interface CarbonFactorListResponse {
  total: number
  rows: CarbonFactorItem[]
  code: number
  msg: string
}

/**
 * 新增/修改碳排放因子库请求参数
 */
export interface CarbonFactorFormData {
  id?: number
  name: string // 因子名称
  category?: string // 活动大类
  energyType: string // 能耗类型
  energyType2?: string // 活动子类
  factorValue: number // 因子数值
  factorUnit: string // 因子单位
  dataSource: string // 数据来源
  sourceYear?: string // 来源年份
  area?: string // 适用区域
  level?: string // 遵循标准
  factorVersion?: string // 因子版本
  status: string // 状态
}

/**
 * 字典数据项
 */
export interface DictDataItem {
  id: number
  key: string
  label: string
}

/**
 * 字典数据响应
 */
export interface DictDataResponse {
  msg: string
  code: number
  data: DictDataItem[]
}

/**
 * 获取碳排放因子库列表
 * @param params 查询参数
 * @returns 因子库列表
 */
export function getCarbonFactorList(
  params: CarbonFactorQueryParams
): Promise<CarbonFactorListResponse> {
  return api.get({
    url: '/carbonemission/carbonfactor/list',
    params
  })
}

/**
 * 新增碳排放因子库
 * @param data 因子数据
 * @returns 新增结果
 */
export function createCarbonFactor(data: CarbonFactorFormData): Promise<{
  code: number
  msg: string
}> {
  return api.post({
    url: '/carbonemission/carbonfactor/',
    data
  })
}

/**
 * 修改碳排放因子库
 * @param data 因子数据
 * @returns 修改结果
 */
export function updateCarbonFactor(data: CarbonFactorFormData & { id: number }): Promise<{
  code: number
  msg: string
}> {
  return api.put({
    url: '/carbonemission/carbonfactor/',
    data
  })
}

/**
 * 删除碳排放因子库
 * @param id 因子ID
 * @returns 删除结果
 */
export function deleteCarbonFactor(id: number): Promise<{
  code: number
  msg: string
}> {
  return api.del({
    url: `/carbonemission/carbonfactor/${id}`
  })
}

/**
 * 获取字典数据
 * @param dictType 字典类型
 * @returns 字典数据
 */
export function getDictData(dictType: string): Promise<DictDataResponse> {
  return api.get({
    url: '/search/getDictDataList',
    params: { dictType }
  })
}

// 字典类型常量
export const DICT_TYPES = {
  ENERGY_TYPE: 'yw_nhlx', // 能耗类型
  CO2_CATEGORY: 'yw_co2_category', // 活动大类
  GAS_TYPE: 'yw_gas_type', // 气体类型
  AREA: 'yw_area' // 适用区域
} as const
