<template>
  <div ref="chartContainer" class="carbon-chart-container"></div>
</template>

<script setup>
  import { ref, onMounted, watch, nextTick, onBeforeUnmount } from 'vue'
  import * as echarts from 'echarts'

  // 图表DOM引用
  const chartContainer = ref(null)
  // 图表实例
  let chart = null
  // ResizeObserver实例
  let resizeObserver = null

  // 从父组件接收的属性
  const props = defineProps({
    predictionData: {
      type: Array,
      default: () => [
        /* 数据保持不变 */
      ]
    },
    scenarioName: {
      type: String,
      default: '基准情景'
    },
    selectedAreas: {
      type: Array,
      default: () => ['A1#', 'A2#', 'B1#', 'B4#']
    },
    emissionTypes: {
      type: Array,
      default: () => ['energy', 'transport', 'water', 'waste']
    },
    // 添加宽度和高度属性，允许父组件控制大小
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '400px'
    }
  })

  // 初始化图表
  const initChart = () => {
    if (chart) {
      chart.dispose()
    }

    if (!chartContainer.value) return

    // 添加延时初始化，确保容器已完全渲染
    nextTick(() => {
      chart = echarts.init(chartContainer.value)
      updateChart()

      // 初始化后手动触发一次resize
      chart.resize()

      // 使用ResizeObserver监听容器尺寸变化
      if (window.ResizeObserver) {
        resizeObserver = new ResizeObserver(() => {
          chart && chart.resize()
        })
        resizeObserver.observe(chartContainer.value)
      }
    })

    // 添加窗口大小变化的监听
    window.addEventListener('resize', handleResize)
  }

  // 窗口大小变化处理函数
  const handleResize = () => {
    chart && chart.resize()
  }

  // 更新图表数据和配置
  const updateChart = () => {
    if (!chart) return

    // 数据准备
    const years = props.predictionData.map((item) => item.year)

    // 创建不同排放类型的数据系列
    const series = []

    // 堆叠区域图 - 各排放类型
    if (props.emissionTypes.includes('energy')) {
      series.push({
        name: '能源碳排放',
        type: 'bar',
        stack: 'total',
        emphasis: { focus: 'series' },
        data: props.predictionData.map((item) => item.energy),
        itemStyle: { color: '#1e88e5' }
      })
    }

    if (props.emissionTypes.includes('transport')) {
      series.push({
        name: '交通碳排放',
        type: 'bar',
        stack: 'total',
        emphasis: { focus: 'series' },
        data: props.predictionData.map((item) => item.transport),
        itemStyle: { color: '#4caf50' }
      })
    }

    if (props.emissionTypes.includes('water')) {
      series.push({
        name: '水资源碳排放',
        type: 'bar',
        stack: 'total',
        emphasis: { focus: 'series' },
        data: props.predictionData.map((item) => item.water),
        itemStyle: { color: '#00bcd4' }
      })
    }

    if (props.emissionTypes.includes('waste')) {
      series.push({
        name: '废弃物碳排放',
        type: 'bar',
        stack: 'total',
        emphasis: { focus: 'series' },
        data: props.predictionData.map((item) => item.waste),
        itemStyle: { color: '#ff9800' }
      })
    }

    // 折线图 - 总排放量
    series.push({
      name: '总碳排放量',
      type: 'line',
      yAxisIndex: 0,
      symbol: 'circle',
      symbolSize: 8,
      itemStyle: { color: '#5470c6' },
      lineStyle: { width: 3 },
      emphasis: { focus: 'series' },
      data: props.predictionData.map((item) => item.total)
    })

    // 折线图 - 同比增长率
    series.push({
      name: '同比增长率',
      type: 'line',
      yAxisIndex: 1,
      symbol: 'emptyCircle',
      symbolSize: 8,
      itemStyle: { color: '#91cc75' },
      lineStyle: { width: 2, type: 'dashed' },
      emphasis: { focus: 'series' },
      data: props.predictionData.map((item) => item.yoy)
    })

    // 配置选项
    const option = {
      title: {
        text: '碳排放预测趋势图',
        left: 'center',
        top: 10,
        textStyle: {
          fontSize: 18,
          fontWeight: 'bold'
        }
      },
      grid: {
        left: '5%',
        right: '8%',
        bottom: '12%',
        top: '25%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function (params) {
          let tooltip = `<div style="font-weight:bold;margin-bottom:8px;">${params[0].axisValue}</div>`
          let total = 0

          // Process stacked bar data
          params.forEach((param) => {
            if (param.seriesType === 'bar') {
              total += param.value
              tooltip += `<div style="display:flex;justify-content:space-between;margin:5px 0;">
            <span style="margin-right:15px;display:inline-flex;align-items:center;">
              <span style="display:inline-block;width:10px;height:10px;background:${
                param.color
              };margin-right:5px;border-radius:50%;"></span>
              ${param.seriesName}:
            </span>
            <span style="font-weight:bold;">${param.value.toFixed(1)} 吨</span>
          </div>`
            }
          })

          // Add total and year-over-year growth
          const totalParam = params.find((p) => p.seriesName === '总碳排放量')
          const yoyParam = params.find((p) => p.seriesName === '同比增长率(%)')

          tooltip += `<div style="margin-top:8px;border-top:1px dashed #ccc;padding-top:8px;">
        <div style="display:flex;justify-content:space-between;margin:5px 0;">
          <span style="font-weight:bold;">总计:</span>
          <span style="font-weight:bold;color:#5470c6;">${
            totalParam ? totalParam.value.toFixed(1) : total.toFixed(1)
          } 吨</span>
        </div>
        <div style="display:flex;justify-content:space-between;margin:5px 0;">
          <span>同比增长:</span>
          <span style="color:${
            yoyParam && yoyParam.value < 0 ? '#67c23a' : '#f56c6c'
          };">${yoyParam ? yoyParam.value.toFixed(1) : '0'}%</span>
        </div>
      </div>`

          return tooltip
        }
      },
      legend: {
        data: [
          '能源碳排放',
          '交通碳排放',
          '水资源碳排放',
          '废弃物碳排放',
          '总碳排放量',
          '同比增长率(%)'
        ],
        top: 40,
        itemWidth: 15,
        itemHeight: 10,
        itemGap: 20,
        textStyle: {
          fontSize: 12
        }
      },

      xAxis: {
        type: 'category',
        data: props.predictionData.map((item) => `${item.year}年`).slice(0, 6),
        axisLabel: {
          interval: 0,
          fontSize: 12,
          margin: 14
        },
        axisTick: {
          alignWithLabel: true
        },
        axisLine: {
          lineStyle: {
            color: '#ddd'
          }
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '碳排放量(吨)',
          min: 0,
          nameTextStyle: {
            padding: [0, 0, 0, -5],
            fontSize: 12
          },
          axisLabel: {
            formatter: '{value} 吨',
            fontSize: 12
          },
          splitLine: {
            lineStyle: {
              type: 'dashed',
              color: '#eee'
            }
          }
        },
        {
          type: 'value',
          name: '同比增长率',
          position: 'right',
          min: -2.5,
          max: 0,
          nameTextStyle: {
            padding: [0, -5, 0, 0],
            fontSize: 12
          },
          axisLabel: {
            formatter: '{value}%',
            fontSize: 12
          },
          splitLine: {
            show: false
          }
        }
      ],
      series: series
    }

    // 应用配置
    chart.setOption(option)
  }

  // 监听属性变化，更新图表
  watch(
    () => [props.predictionData, props.scenarioName, props.selectedAreas, props.emissionTypes],
    () => {
      nextTick(() => {
        updateChart()
      })
    },
    { deep: true }
  )

  // 组件挂载后初始化图表
  onMounted(() => {
    initChart()
  })

  // 组件卸载前清理图表实例
  onBeforeUnmount(() => {
    // 移除事件监听器
    window.removeEventListener('resize', handleResize)

    // 清理ResizeObserver
    if (resizeObserver) {
      resizeObserver.disconnect()
      resizeObserver = null
    }

    // 销毁图表实例
    if (chart) {
      chart.dispose()
      chart = null
    }
  })
</script>

<style scoped>
  .carbon-chart-container {
    width: v-bind('width');
    height: v-bind('height');
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);
  }
</style>
