import request from '@/utils/http'

/**
 * 网络通讯参数配置查询参数
 */
export interface NetworkConfigQueryParams {
  configName?: string
  configKey?: string
  configValue?: string
  configType?: string
  pageSize: number
  pageNum: number
}

/**
 * 网络通讯参数配置数据项
 */
export interface NetworkConfigItem {
  createBy?: string
  createTime?: string
  updateBy?: string
  updateTime?: string | null
  remark?: string
  id?: number
  configName: string
  configKey: string
  configValue: string
  configType: string
  ipAddr?: string
  port?: string
  userName?: string
  pwd?: string
  qos?: string
  businessType?: string
  field1?: string | null
  field2?: string | null
}

/**
 * 网络通讯参数配置查询响应
 */
export interface NetworkConfigListResponse {
  total: number
  rows: NetworkConfigItem[]
  code: number
  msg: string
}

/**
 * 网络通讯参数配置详情响应
 */
export interface NetworkConfigDetailResponse {
  code: number
  msg: string
  data: NetworkConfigItem
}

/**
 * 通用响应接口
 */
export interface CommonResponse {
  code: number
  msg: string
}

/**
 * 新增网络通讯参数配置请求参数
 */
export interface CreateNetworkConfigRequest {
  configName: string
  configKey: string
  configValue: string
  configType: string
  ipAddr?: string
  port?: string
  userName?: string
  pwd?: string
  qos?: string
  remark?: string
}

/**
 * 修改网络通讯参数配置请求参数
 */
export interface UpdateNetworkConfigRequest extends CreateNetworkConfigRequest {
  id: number
}

/**
 * 查询网络通讯参数配置列表
 * @param params 查询参数
 * @returns 网络通讯参数配置列表
 */
export function getNetworkConfigList(
  params: NetworkConfigQueryParams
): Promise<NetworkConfigListResponse> {
  return request.get({
    url: '/system/webconfig/list',
    params
  })
}

/**
 * 获取网络通讯参数配置详细信息
 * @param id 配置ID
 * @returns 网络通讯参数配置详情
 */
export function getNetworkConfigDetail(id: number): Promise<NetworkConfigDetailResponse> {
  return request.get({
    url: `/system/webconfig/${id}`
  })
}

/**
 * 新增网络通讯参数配置
 * @param data 配置数据
 * @returns 操作结果
 */
export function createNetworkConfig(data: CreateNetworkConfigRequest): Promise<CommonResponse> {
  return request.post({
    url: '/system/webconfig/',
    data
  })
}

/**
 * 修改网络通讯参数配置
 * @param data 配置数据
 * @returns 操作结果
 */
export function updateNetworkConfig(data: UpdateNetworkConfigRequest): Promise<CommonResponse> {
  return request.put({
    url: '/system/webconfig/',
    data
  })
}

/**
 * 删除网络通讯参数配置
 * @param id 配置ID
 * @returns 操作结果
 */
export function deleteNetworkConfig(id: number): Promise<CommonResponse> {
  return request.del({
    url: `/system/webconfig/${id}`
  })
}
