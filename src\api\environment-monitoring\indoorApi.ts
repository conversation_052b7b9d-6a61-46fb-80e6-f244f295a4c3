import api from '@/utils/http'

// 室内环境监测相关接口

/**
 * 设备列表查询参数
 */
export interface EquipmentListParams {
  projectId: number | string
  buildingId?: number | string
  floorId?: number | string
  equipmentCategory?: number | string
  equipmentCategorytype?: number | string
}

/**
 * 设备信息
 */
export interface EquipmentInfo {
  id: number
  name: string
  code: string
  equipmentType: string
  // 其他设备字段...
}

/**
 * 设备列表响应
 */
export interface EquipmentListResponse {
  msg: string
  code: number
  data: EquipmentInfo[]
}

/**
 * 室内空气质量数据
 */
export interface IndoorAirQualityData {
  createBy: string
  createTime: string
  updateBy: string
  updateTime: string | null
  remark: string | null
  id: number
  projectId: number
  buildingId: number
  equipmentId: number
  wd: number // 温度
  sd: number // 湿度
  pm25: number // PM2.5
  pm10: number // PM10
  tvoc: number // TVOC
  hcho: number // 甲醛
  co2: number // 二氧化碳
  zd: number // 照度
  zs: number // 噪声
  reportTime: number
  reportTimeList: any[] | null
  hourTime: string | null
  timeWhere: string | null
}

/**
 * 空气质量监测响应
 */
export interface IndoorAirQualityResponse {
  msg: string
  code: number
  data: IndoorAirQualityData
}

/**
 * 趋势数据项
 */
export interface IndoorTrendDataItem {
  wd: number // 温度
  sd: number // 湿度
  ph2_5: number // PM2.5
  ph10: number // PM10
  tvoc: number | null // TVOC
  hcho: number | null // 甲醛
  co2: number | null // 二氧化碳
  zd: number | null // 照度
  zs: number | null // 噪声
}

/**
 * 趋势数据
 */
export interface IndoorTrendData {
  timeList: string[]
  dataList: IndoorTrendDataItem[]
}

/**
 * 趋势数据响应
 */
export interface IndoorTrendDataResponse {
  msg: string
  code: number
  data: IndoorTrendData
}

/**
 * 历史数据查询参数
 */
export interface IndoorHistoryDataParams {
  projectId: number | string
  equipmentId?: number | string
  beginTime?: string
  endTime?: string
  pageSize: number
  pageNum: number
}

/**
 * 历史数据响应
 */
export interface IndoorHistoryDataResponse {
  total: number
  rows: IndoorAirQualityData[]
  code: number
  msg: string
}

/**
 * 获取设备列表
 * @param params 查询参数
 * @returns 设备列表
 */
export function getEquipmentList(params: EquipmentListParams): Promise<EquipmentListResponse> {
  return api.get({
    url: '/search/getEquipmentList',
    params
  })
}

/**
 * 获取室内空气质量监测数据
 * @param params 查询参数
 * @returns 空气质量数据
 */
export function getIndoorAirQualityMonitoring(params: {
  projectId: number | string
  equipmentId?: number | string
}): Promise<IndoorAirQualityResponse> {
  return api.get({
    url: '/envmonitor/indoor/getAirQualityMonitoring',
    params
  })
}

/**
 * 获取室内温度湿度趋势数据
 * @param params 查询参数
 * @returns 趋势数据
 */
export function getIndoorTemperatureAndTemperatureTrend(params: {
  projectId: number | string
  equipmentId?: number | string
  timeType: number // 0:日, 1:周, 2:月
}): Promise<IndoorTrendDataResponse> {
  return api.get({
    url: '/envmonitor/indoor/getTemperatureAndTemperatureTrend',
    params
  })
}

/**
 * 获取室内历史监测数据
 * @param params 查询参数
 * @returns 历史数据
 */
export function getIndoorHistoryList(
  params: IndoorHistoryDataParams
): Promise<IndoorHistoryDataResponse> {
  return api.get({
    url: '/envmonitor/indoor/getHistoryList',
    params
  })
}

/**
 * 室内导出参数
 */
export interface IndoorExportParams {
  projectId: number | string
  equipmentId?: number | string
  timeType?: number // 时间类型 0：日，1：周，2：月
  beginTime?: string
  endTime?: string
}

/**
 * 导出室内空气质量监测数据
 * @param params 导出参数
 * @returns 文件流
 */
export function exportIndoorData(params: IndoorExportParams): Promise<Blob> {
  return api.post({
    url: '/envmonitor/indoor/export',
    data: params,
    responseType: 'blob'
  })
}
