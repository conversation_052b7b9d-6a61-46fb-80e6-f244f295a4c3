<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>外部登录页面</title>
</head>
<body>
    <div>
        <h2>外部登录</h2>
        <button id="loginBtn" onclick="login()">登录</button>
        <div id="debugInfo" style="margin-top: 20px; padding: 10px; background: #f5f5f5; font-family: monospace; white-space: pre-wrap;"></div>
    </div>

    <script>
        function debugLog(message) {
            const debugDiv = document.getElementById('debugInfo');
            debugDiv.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            console.log(message);
        }

        async function login() {
            const loginBtn = document.getElementById('loginBtn');

            // 禁用按钮防止重复点击
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';

            try {
                debugLog('开始登录请求...');

                // 发送登录请求（使用代理路径）
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });

                const data = await response.json();
                debugLog('登录响应: ' + JSON.stringify(data));
                
                // 检查响应状态
                if (data.code === 200 && data.token) {
                    debugLog('登录成功，开始设置本地存储...');

                    // 1. 清理旧的存储数据，确保干净的状态
                    localStorage.removeItem('accessToken');
                    const version = '2.2.64';
                    localStorage.removeItem(`sys-v${version}`);
                    localStorage.removeItem('version');
                    debugLog('清理旧存储数据完成');

                    // 2. 设置版本号
                    localStorage.setItem('version', version);
                    debugLog('设置 version: ' + version);

                    // 3. 设置token
                    localStorage.setItem('accessToken', data.token);
                    debugLog('设置 accessToken: ' + data.token.substring(0, 20) + '...');

                    // 4. 构建完整的用户状态数据结构（与项目结构完全一致）
                    const userData = {
                        user: {
                            info: data.userInfo || {
                                userId: 'external_user',
                                userName: 'admin',
                                nickName: '管理员',
                                email: '',
                                phonenumber: '',
                                sex: '0',
                                avatar: '',
                                admin: true,
                                createTime: new Date().toISOString(),
                                remark: '外部登录用户'
                            },
                            isLogin: true,
                            language: 'zh',
                            isLock: false,
                            lockPassword: '',
                            searchHistory: [],
                            refreshToken: '',
                            worktab: {
                                current: {
                                    title: '',
                                    path: '',
                                    name: '',
                                    params: {},
                                    query: {}
                                },
                                opened: []
                            },
                            setting: {
                                systemThemeType: 'light',
                                systemThemeMode: 'light',
                                menuThemeType: 'design',
                                boxBorderMode: true,
                                uniqueOpened: true,
                                systemThemeColor: '#5D87FF',
                                showMenuButton: true,
                                showRefreshButton: true,
                                showCrumbs: true,
                                autoClose: false,
                                showWorkTab: true,
                                showLanguage: true,
                                showNprogress: true,
                                colorWeak: false,
                                showSettingGuide: true,
                                refresh: false
                            }
                        }
                    };

                    // 5. 保存到localStorage
                    const storageKey = `sys-v${version}`;
                    localStorage.setItem(storageKey, JSON.stringify(userData));
                    debugLog('设置 ' + storageKey + ': 用户数据已保存');

                    // 6. 设置外部登录标记
                    localStorage.setItem('externalLogin', 'true');
                    localStorage.setItem('externalLoginTime', Date.now().toString());
                    debugLog('设置外部登录标记');

                    // 7. 验证存储是否成功
                    const storedToken = localStorage.getItem('accessToken');
                    const storedData = localStorage.getItem(storageKey);
                    const storedVersion = localStorage.getItem('version');
                    
                    debugLog('=== 存储验证 ===');
                    debugLog('accessToken: ' + (storedToken ? '✓ 存在' : '✗ 不存在'));
                    debugLog('version: ' + (storedVersion ? '✓ ' + storedVersion : '✗ 不存在'));
                    debugLog('userData: ' + (storedData ? '✓ 存在' : '✗ 不存在'));

                    if (storedData) {
                        try {
                            const parsedData = JSON.parse(storedData);
                            debugLog('isLogin状态: ' + (parsedData.user?.isLogin ? '✓ true' : '✗ false'));
                            debugLog('用户信息: ' + (parsedData.user?.info?.userName || '未知'));
                        } catch (e) {
                            debugLog('✗ 解析用户数据失败: ' + e.message);
                        }
                    }

                    // 8. 等待一下确保数据写入完成
                    await new Promise(resolve => setTimeout(resolve, 500));

                    alert('登录成功！即将跳转到主应用...');
                    debugLog('准备跳转到目标页面...');

                    // 9. 跳转到主应用
                    setTimeout(() => {
                        debugLog('执行跳转...');
                        // 使用replace而不是href，避免在历史记录中留下外部登录页面
                        window.location.replace('http://localhost:3006/#/comprehensive-overview/project-overview');
                    }, 1000);
                } else {
                    debugLog('登录失败: ' + (data.msg || '未知错误'));
                    alert('登录失败：' + (data.msg || '未知错误'));
                }
            } catch (error) {
                debugLog('登录请求异常: ' + error.message);
                console.error('登录请求失败:', error);
                alert('登录请求失败，请检查网络连接');
            } finally {
                // 恢复按钮状态
                loginBtn.disabled = false;
                loginBtn.textContent = '登录';
            }
        }

        // 页面加载时检查当前存储状态
        window.onload = function() {
            debugLog('=== 页面加载时的存储状态检查 ===');
            
            const accessToken = localStorage.getItem('accessToken');
            const version = localStorage.getItem('version') || '2.2.64';
            const userData = localStorage.getItem(`sys-v${version}`);
            
            debugLog('accessToken: ' + (accessToken ? '存在 (' + accessToken.substring(0, 20) + '...)' : '不存在'));
            debugLog('version: ' + (version || '不存在'));
            debugLog('sys-v' + version + ': ' + (userData ? '存在' : '不存在'));

            if (userData) {
                try {
                    const parsed = JSON.parse(userData);
                    debugLog('解析结果:');
                    debugLog('  - isLogin: ' + parsed.user?.isLogin);
                    debugLog('  - userName: ' + parsed.user?.info?.userName);
                    debugLog('  - language: ' + parsed.user?.language);
                } catch (e) {
                    debugLog('解析用户数据失败: ' + e.message);
                }
            }

            // 检查是否已经登录，如果是则提供快速跳转选项
            if (accessToken && userData) {
                try {
                    const parsed = JSON.parse(userData);
                    if (parsed.user?.isLogin) {
                        debugLog('检测到已有登录状态，可直接跳转');
                        const quickJumpBtn = document.createElement('button');
                        quickJumpBtn.textContent = '直接跳转到主应用';
                        quickJumpBtn.style.marginLeft = '10px';
                        quickJumpBtn.onclick = function() {
                            debugLog('执行快速跳转...');
                            window.location.replace('http://localhost:3006/#/comprehensive-overview/project-overview');
                        };
                        document.getElementById('loginBtn').parentNode.appendChild(quickJumpBtn);
                    }
                } catch (e) {
                    debugLog('检查登录状态时出错: ' + e.message);
                }
            }
        };
    </script>
</body>
</html>
