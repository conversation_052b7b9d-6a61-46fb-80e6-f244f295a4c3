<template>
  <div class="realtime-container page-content">
    <!-- 筛选查询区域 -->
    <div class="filter-container">
      <div class="filter-row">
        <!-- 项目和楼栋选择器 -->
        <ProjectBuildingSelector
          ref="projectBuildingSelectorRef"
          v-model="projectBuildingSelection"
          @change="handleProjectBuildingChange"
          @project-loaded="handleProjectListLoaded"
        />

        <div class="filter-item">
          <span class="filter-label">能源类型:</span>
          <el-select
            v-model="energyType"
            placeholder="请选择能源类型"
            clearable
            class="filter-select"
            @change="handleEnergyTypeChange"
          >
            <el-option
              v-for="item in energyTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>

        <div class="filter-buttons">
          <el-button type="primary" @click="handleSearch" :disabled="queryButtonDisabled"
            >查询</el-button
          >
          <el-button @click="handleReset" :disabled="resetButtonDisabled">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 表计列表区域 -->
    <div class="meter-list">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="meter in meterList" :key="meter.id">
          <el-card class="meter-card" :class="{ offline: !meter.isOnline }">
            <div class="meter-header">
              <div class="meter-title">
                <span class="building-name">{{ meter.buildingName }}</span>
                <span class="building-location">{{ meter.location }}</span>
              </div>
              <el-button
                size="small"
                type="primary"
                plain
                class="trend-btn"
                @click="viewTrend(meter)"
                >曲线</el-button
              >
            </div>

            <div class="meter-content">
              <!-- 左侧列 -->
              <div class="content-left">
                <div class="meter-icon">
                  <!-- 根据表计类型显示对应图标 -->
                  <div class="meter-type-icon">
                    <!-- 统一使用SVG图片 -->
                    <img
                      :src="getMeterIcon(meter.meterType)"
                      width="46"
                      height="46"
                      alt="表计图标"
                    />
                  </div>
                  <div class="meter-type-text">
                    {{ getMeterTypeText(meter.meterType) }}
                  </div>
                </div>
              </div>

              <!-- 右侧列 -->
              <div class="content-right">
                <div
                  class="status-tag"
                  :class="{ online: meter.isOnline, offline: !meter.isOnline }"
                >
                  {{ meter.isOnline ? '在线' : '离线' }}
                </div>
                <div class="meter-data">
                  <div class="data-item">
                    <span class="data-label">累计读数:</span>
                    <span class="data-value"
                      >{{ meter.realTimeData }} {{ getMeterUnit(meter.meterType) }}</span
                    >
                  </div>
                  <div class="data-item">
                    <span class="data-label">今日能耗:</span>
                    <span class="data-value"
                      >{{ meter.lastCollectionEnergy }} {{ getMeterUnit(meter.meterType) }}</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[12, 24, 36, 48]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 趋势图对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="今日能耗曲线"
      width="80%"
      :before-close="handleDialogClose"
    >
      <div class="trend-chart-container" v-loading="chartLoading">
        <div id="trendChart" class="trend-chart"></div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, onMounted, nextTick, computed, watch } from 'vue'

  import { ElMessage } from 'element-plus'
  import * as echarts from 'echarts'
  import { useSettingStore } from '@/store/modules/setting'
  import { storeToRefs } from 'pinia'
  import {
    getRealEnergyList,
    getMeterTrend,
    selectRealEnergyDayList
  } from '@/api/energy-monitoring/realTimeApi'
  import { getEnergyTypeDict } from '@/api/dictApi'
  import ProjectBuildingSelector from '@/components/ProjectBuildingSelector.vue'

  // 获取当前主题
  const settingStore = useSettingStore()
  const { isDark } = storeToRefs(settingStore)

  // 图表主题 - 根据系统主题自动切换
  const getChartTheme = computed(() => {
    return isDark.value ? 'dark' : 'light'
  })

  // 项目和楼栋选择器相关数据
  const projectBuildingSelectorRef = ref()
  const projectBuildingSelection = ref({
    projectId: null,
    buildingId: null
  })
  const selectedProjectInfo = ref(null)
  const selectedBuildingInfo = ref(null)

  const energyType = ref('')
  const energyTypeOptions = ref([])

  // 获取能源类型字典数据
  const fetchEnergyTypeOptions = async () => {
    try {
      const response = await getEnergyTypeDict()
      if (response.code === 200) {
        // 将字典数据转换为下拉框选项格式
        energyTypeOptions.value = response.data.map((item) => ({
          value: item.key,
          label: item.label
        }))
      } else {
        console.error('获取能源类型失败:', response.msg)
        // 使用默认数据作为备选
        energyTypeOptions.value = [
          { value: '0', label: '耗水' },
          { value: '1', label: '耗电' },
          { value: '2', label: '耗气' },
          { value: '3', label: '光伏' }
        ]
      }
    } catch (error) {
      console.error('获取能源类型失败:', error)
      // 使用默认数据作为备选
      energyTypeOptions.value = [
        { value: '0', label: '耗水' },
        { value: '1', label: '耗电' },
        { value: '2', label: '耗气' },
        { value: '3', label: '光伏' }
      ]
    }
  }

  const timeUnit = ref('month')
  const dateRange = ref(['2025-04', '2025-04'])

  // 表计类型选项
  const meterTypeOptions = ref([
    { value: 'electricity', label: '电表' },
    { value: 'water', label: '水表' },
    { value: 'gas', label: '气表' },
    { value: 'heat', label: '热表' }
  ])

  // 建筑区域选项
  const buildingAreaOptions = ref([
    { value: 'xinbei', label: '新北区政府' },
    { value: 'tianning', label: '天宁区政府' },
    { value: 'zhonglou', label: '钟楼区政府' }
  ])

  // 表计分类选项
  const meterCategoryOptions = ref([
    { value: 'lighting', label: '照明用电' },
    { value: 'aircon', label: '空调用电' },
    { value: 'power', label: '动力用电' },
    { value: 'special', label: '特殊用电' }
  ])

  // 表计分项选项
  const meterItemOptions = ref([
    { value: 'office', label: '办公区' },
    { value: 'meeting', label: '会议室' },
    { value: 'public', label: '公共区域' },
    { value: 'others', label: '其他' }
  ])

  // 根据energyType获取对应图标
  const getMeterIcon = (energyType) => {
    // 将energyType转换为数字进行判断
    const type = parseInt(energyType)
    switch (type) {
      case 0: // 水资源
        return new URL('@/assets/icons/system/shuibiao.svg', import.meta.url).href
      case 1: // 电力
        return new URL('@/assets/icons/system/dianbiao.svg', import.meta.url).href
      case 2: // 燃气
        return new URL('@/assets/icons/system/qibiao.svg', import.meta.url).href
      case 3: // 光伏（使用电表图标）
        return new URL('@/assets/icons/system/dianbiao.svg', import.meta.url).href
      default:
        return new URL('@/assets/icons/system/dianbiao.svg', import.meta.url).href // 默认使用电表图标
    }
  }

  // 获取表计类型文本
  const getMeterTypeText = (energyType) => {
    // 将energyType转换为数字进行判断
    const type = parseInt(energyType)
    switch (type) {
      case 0: // 水资源
        return '水表'
      case 1: // 电力
        return '电表'
      case 2: // 燃气
        return '气表'
      case 3: // 光伏
        return '光伏'
      default:
        return '电表'
    }
  }

  // 获取表计单位
  const getMeterUnit = (energyType) => {
    // 将energyType转换为数字进行判断
    const type = parseInt(energyType)
    switch (type) {
      case 0: // 水资源
        return 'm³'
      case 1: // 电力
        return 'kWh'
      case 2: // 燃气
        return 'm³'
      case 3: // 光伏
        return 'kWh'
      default:
        return 'kWh'
    }
  }

  // 筛选参数
  const meterType = ref('')
  const buildingArea = ref('')
  const meterCategory = ref('')
  const meterItem = ref('')

  // 分页参数
  const currentPage = ref(1)
  const pageSize = ref(12)
  const total = ref(0)

  // 表计列表数据
  const meterList = ref([])

  // 趋势图相关
  const dialogVisible = ref(false)
  const chartLoading = ref(false)
  const currentMeter = ref(null)
  let trendChart = null

  // 查询状态管理
  const lastQueryParams = ref(null)
  const queryButtonDisabled = ref(false)
  const resetButtonDisabled = ref(true) // 初始状态下重置按钮禁用

  // 比较查询参数是否相同
  const isSameQueryParams = (params1, params2) => {
    if (!params1 || !params2) return false
    return (
      params1.projectId === params2.projectId &&
      params1.buildingId === params2.buildingId &&
      params1.energyType === params2.energyType &&
      params1.pageSize === params2.pageSize &&
      params1.pageNum === params2.pageNum
    )
  }

  // 获取当前查询参数
  const getCurrentQueryParams = () => {
    return {
      projectId: projectBuildingSelection.value.projectId,
      buildingId: projectBuildingSelection.value.buildingId || undefined,
      energyType: energyType.value || undefined,
      pageSize: pageSize.value,
      pageNum: currentPage.value
    }
  }

  // 初始化
  onMounted(() => {
    // 页面加载时获取能源类型数据
    fetchEnergyTypeOptions()
    // 页面加载时不自动获取数据，等待项目列表加载完成后自动选择第一个项目
    console.log('页面已加载，等待项目列表加载完成')
  })

  // 数据转换函数：将接口数据转换为页面需要的格式
  const transformMeterData = (apiData) => {
    // 调试信息：检查数据字段
    console.log('transformMeterData 原始数据:', apiData)
    console.log('字段检查:', {
      id: apiData.id,
      energyId: apiData.energyId,
      equipmentId: apiData.equipmentId,
      energyType: apiData.energyType
    })

    return {
      id: apiData.energyId, // 使用energyId作为主要ID，用于调用selectRealEnergyDayList接口
      equipmentId: apiData.equipmentId, // 保留原始equipmentId
      energyId: apiData.energyId, // 保留原始energyId
      buildingName: apiData.projectName || '未知项目', // building-name 显示项目名称
      location: apiData.equipmentName || '未知设备', // building-location 显示设备名称
      carbon: 0, // 接口暂无此字段，使用默认值
      realTimeData: apiData.totalEnergy?.toString() || '0',
      lastCollectionEnergy: apiData.todayEnergy?.toString() || '0',
      collectionTime: new Date().toLocaleString(), // 接口暂无此字段，使用当前时间
      isOnline: apiData.equipmentStatus !== '' && apiData.equipmentStatus !== null, // 转换设备状态为布尔值
      meterType: apiData.energyType !== undefined ? apiData.energyType : 1, // 保持原始energyType数值，默认为1（电力）
      meterCategory: 'lighting', // 接口暂无此字段，使用默认值
      meterItem: 'office', // 接口暂无此字段，使用默认值
      unitValue: apiData.unitValue || 'kWh' // 单位值
    }
  }

  // 获取表计列表
  const fetchMeterList = async () => {
    try {
      // 构建API请求参数
      const params = {
        projectId: projectBuildingSelection.value.projectId, // 使用选中的项目ID
        buildingId: projectBuildingSelection.value.buildingId || undefined, // 使用选中的楼栋ID
        energyType: energyType.value || undefined,
        pageSize: pageSize.value,
        pageNum: currentPage.value
      }

      // 如果没有选择项目，使用默认参数发请求
      if (!projectBuildingSelection.value.projectId) {
        console.log('没有选择项目，使用默认参数发请求')
        // 移除projectId参数，让后端返回默认数据
        delete params.projectId
      }

      console.log('发送查询请求，参数:', params)

      // 调用真实API
      const response = await getRealEnergyList(params)

      if (response.code === 200) {
        // 调试信息：打印原始API数据
        console.log('getRealEnergyList 原始数据:', response.rows)
        if (response.rows.length > 0) {
          console.log('第一条数据示例:', response.rows[0])
          console.log('第一条数据的id:', response.rows[0].id)
        }

        // 转换数据格式
        const transformedList = response.rows.map(transformMeterData)

        // 调试信息：打印转换后的数据
        console.log('转换后的数据:', transformedList)
        if (transformedList.length > 0) {
          console.log('转换后第一条数据示例:', transformedList[0])
          console.log('转换后第一条数据的id:', transformedList[0].id)
        }

        // 应用本地筛选逻辑（因为API可能不支持所有筛选条件）
        let filteredList = transformedList
        // 按表计类型筛选
        if (meterType.value) {
          filteredList = filteredList.filter((item) => item.meterType === meterType.value)
        }

        // 按建筑区域筛选
        if (buildingArea.value) {
          const selectedAreaLabel =
            buildingAreaOptions.value.find((option) => option.value === buildingArea.value)
              ?.label || ''
          filteredList = filteredList.filter((item) => item.buildingName === selectedAreaLabel)
        }

        // 按表计分类筛选
        if (meterCategory.value) {
          filteredList = filteredList.filter((item) => item.meterCategory === meterCategory.value)
        }

        // 按表计分项筛选
        if (meterItem.value) {
          filteredList = filteredList.filter((item) => item.meterItem === meterItem.value)
        }

        // 设置数据
        meterList.value = filteredList
        total.value = response.total

        // 更新查询状态
        const currentParams = getCurrentQueryParams()
        lastQueryParams.value = { ...currentParams }
        queryButtonDisabled.value = true
        resetButtonDisabled.value = false

        // 显示成功信息
        if (filteredList.length === 0) {
          ElMessage.info('没有找到符合条件的表计数据')
        } else if (
          meterType.value ||
          buildingArea.value ||
          meterCategory.value ||
          meterItem.value
        ) {
          ElMessage.success(`筛选成功，共找到 ${filteredList.length} 条符合条件的数据`)
        }
      } else {
        ElMessage.error(response.msg || '获取数据失败')
        meterList.value = []
        total.value = 0
      }
    } catch (error) {
      ElMessage.error('获取表计数据失败')
      console.error('获取表计数据失败:', error)
      meterList.value = []
      total.value = 0
    }
  }

  // 处理项目和楼栋选择变化
  const handleProjectBuildingChange = (data) => {
    selectedProjectInfo.value = data.projectInfo
    selectedBuildingInfo.value = data.buildingInfo
    console.log('项目楼栋选择变化:', data)

    // 自动触发查询
    autoQuery()
  }

  // 监听项目列表加载完成
  const handleProjectListLoaded = (projectList) => {
    // 当项目列表加载完成后，自动发请求获取数据
    if (projectList && projectList.length > 0) {
      console.log('项目列表加载完成，自动获取数据')

      // 自动发请求获取数据
      nextTick(() => {
        fetchMeterList()
      })
    }
  }

  // 自动查询函数
  const autoQuery = () => {
    // 重置页码到第一页
    currentPage.value = 1

    // 清空查询参数缓存，允许重新查询
    lastQueryParams.value = null
    queryButtonDisabled.value = false
    resetButtonDisabled.value = false

    // 延迟执行查询，避免频繁触发
    nextTick(() => {
      fetchMeterList()
    })
  }

  // 处理能源类型变化
  const handleEnergyTypeChange = (value) => {
    console.log('能源类型变化:', value)
    // 自动触发查询
    autoQuery()
  }

  // 搜索处理
  const handleSearch = () => {
    // 如果查询按钮已禁用，不执行查询
    if (queryButtonDisabled.value) {
      ElMessage.info('查询条件未变化，无需重复查询')
      return
    }

    // 获取当前查询参数
    const currentParams = getCurrentQueryParams()

    // 检查查询参数是否与上次相同
    if (isSameQueryParams(currentParams, lastQueryParams.value)) {
      ElMessage.info('查询条件未变化')
      queryButtonDisabled.value = true
      return
    }

    currentPage.value = 1
    // 这里可以将新增的筛选条件应用到搜索中
    // 实际项目中应该根据筛选条件调用API
    console.log('搜索参数:', {
      projectBuildingSelection: projectBuildingSelection.value,
      selectedProjectInfo: selectedProjectInfo.value,
      selectedBuildingInfo: selectedBuildingInfo.value,
      energyType: energyType.value,
      timeUnit: timeUnit.value,
      dateRange: dateRange.value
    })

    // 保存当前查询参数
    lastQueryParams.value = { ...currentParams }

    // 禁用查询按钮，启用重置按钮
    queryButtonDisabled.value = true
    resetButtonDisabled.value = false

    fetchMeterList()
  }

  // 重置筛选
  const handleReset = () => {
    // 如果重置按钮已禁用，不执行重置
    if (resetButtonDisabled.value) {
      ElMessage.info('没有需要重置的内容')
      return
    }

    // 重置项目和楼栋选择器
    if (projectBuildingSelectorRef.value) {
      projectBuildingSelectorRef.value.reset()
    }

    energyType.value = ''
    timeUnit.value = 'month'
    dateRange.value = ['2025-04', '2025-04']

    // 保留原有重置逻辑
    meterType.value = ''
    buildingArea.value = ''
    meterCategory.value = ''
    meterItem.value = ''
    currentPage.value = 1

    // 清空查询参数缓存
    lastQueryParams.value = null

    // 启用查询按钮，禁用重置按钮
    queryButtonDisabled.value = false
    resetButtonDisabled.value = true

    fetchMeterList()
  }

  // 分页大小变化
  const handleSizeChange = (val) => {
    pageSize.value = val
    fetchMeterList()
  }

  // 分页页码变化
  const handleCurrentChange = (val) => {
    currentPage.value = val
    fetchMeterList()
  }

  // 查看趋势
  const viewTrend = async (meter) => {
    console.log('点击查看趋势，meter数据:', meter)
    console.log('meter.id:', meter.id)
    currentMeter.value = meter
    dialogVisible.value = true
    chartLoading.value = true

    try {
      // 等待DOM更新
      await nextTick()
      // 初始化趋势图
      initTrendChart()
      // 获取今日能耗数据
      await fetchTodayEnergyData()
    } catch (error) {
      ElMessage.error('加载今日能耗数据失败')
      console.error('加载今日能耗数据失败:', error)
    } finally {
      chartLoading.value = false
    }
  }

  // 初始化趋势图
  const initTrendChart = () => {
    const chartDom = document.getElementById('trendChart')
    if (!chartDom) return

    // 如果已存在图表实例，销毁它
    if (trendChart) {
      trendChart.dispose()
    }

    // 创建新的图表实例，应用主题
    trendChart = echarts.init(chartDom, getChartTheme.value)

    // 设置图表加载中状态
    trendChart.showLoading()
  }

  // 获取今日能耗数据
  const fetchTodayEnergyData = async () => {
    if (!currentMeter.value || !trendChart) return

    try {
      // 调试信息：打印当前表计数据
      console.log('当前表计数据:', currentMeter.value)
      console.log('表计各种ID:', {
        id: currentMeter.value.id,
        equipmentId: currentMeter.value.equipmentId,
        energyId: currentMeter.value.energyId
      })

      // 确保energyId存在且为有效值
      if (!currentMeter.value.id && !currentMeter.value.energyId) {
        console.error('energyId为空，无法获取能耗数据')
        console.error('当前表计数据:', currentMeter.value)
        console.error('后端返回的energyId字段为null，请检查后端数据')
        ElMessage.error('energyId为空，无法获取能耗曲线数据。请联系管理员检查设备配置。')
        trendChart && trendChart.hideLoading()
        return
      }

      // 构建请求参数 - 使用energyId作为id参数
      const requestParams = {
        id: currentMeter.value.energyId || currentMeter.value.id // 优先使用energyId
      }
      console.log('即将发送的请求参数:', requestParams)
      console.log('使用的energyId值:', requestParams.id)

      // 调用真实API获取今日能耗数据
      const response = await selectRealEnergyDayList(requestParams)

      // 停止加载中状态
      trendChart.hideLoading()

      if (response.code === 200 && response.data) {
        // 调试信息：查看响应数据结构
        console.log('selectRealEnergyDayList 响应数据:', response)
        console.log('response.data:', response.data)

        // 处理实际的数据结构
        const { timeList, dataList } = response.data

        if (!Array.isArray(timeList) || !Array.isArray(dataList)) {
          console.error('数据格式错误: timeList或dataList不是数组')
          ElMessage.error('数据格式错误')
          return
        }

        if (timeList.length !== dataList.length) {
          console.error('数据长度不匹配: timeList和dataList长度不一致')
          ElMessage.error('数据长度不匹配')
          return
        }

        // 转换数据格式
        const times = timeList.map((time) => `${time}:00`) // 将 "00" 转换为 "00:00"
        const energyValues = dataList.map((item) => item.energyValue || 0)
        const unit = 'kWh' // 默认单位

        console.log('处理后的数据:', {
          times,
          energyValues,
          unit
        })

        // 使用历史趋势的echarts模板，但只显示能耗数据
        const option = {
          title: {
            text: `${currentMeter.value.buildingName} ${currentMeter.value.location} 今日能耗曲线`,
            left: 'center'
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              label: {
                backgroundColor: '#6a7985'
              }
            }
          },
          legend: {
            data: [`能耗 (${unit})`],
            top: 30
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: times
          },
          yAxis: [
            {
              type: 'value',
              name: `能耗 (${unit})`,
              position: 'left',
              axisLine: {
                show: true,
                lineStyle: {
                  color: '#5470C6'
                }
              },
              axisLabel: {
                formatter: `{value} ${unit}`
              }
            }
          ],
          series: [
            {
              name: `能耗 (${unit})`,
              type: 'line',
              smooth: true,
              emphasis: {
                focus: 'series'
              },
              data: energyValues
            }
          ]
        }

        // 设置图表
        trendChart.setOption(option)

        // 自适应窗口大小变化
        window.addEventListener('resize', () => {
          trendChart && trendChart.resize()
        })
      } else {
        ElMessage.error(response.msg || '获取今日能耗数据失败')
      }
    } catch (error) {
      trendChart && trendChart.hideLoading()
      ElMessage.error('获取今日能耗数据失败')
      console.error('获取今日能耗数据失败:', error)
    }
  }

  // 关闭对话框
  const handleDialogClose = () => {
    dialogVisible.value = false
    currentMeter.value = null

    // 销毁图表实例
    if (trendChart) {
      trendChart.dispose()
      trendChart = null
    }
  }

  // 导出数据
  const exportData = () => {
    ElMessage.success('数据导出成功')
    console.log('导出数据', {
      projectName: projectName.value,
      searchType: searchType.value,
      energyType: energyType.value,
      timeUnit: timeUnit.value,
      dateRange: dateRange.value
    })
  }

  // 下载报表
  const downloadReport = () => {
    ElMessage.success('报表下载成功')
    console.log('下载报表', {
      projectName: projectName.value,
      searchType: searchType.value,
      energyType: energyType.value,
      timeUnit: timeUnit.value,
      dateRange: dateRange.value
    })
  }

  // 监听主题变化，重新渲染图表
  watch(
    () => isDark.value,
    () => {
      if (trendChart) {
        const newChart = echarts.init(document.getElementById('trendChart'), getChartTheme.value)
        const option = trendChart.getOption()
        trendChart.dispose()
        trendChart = newChart
        trendChart.setOption(option)
      }
    }
  )
</script>

<style lang="scss" scoped>
  .realtime-container {
    padding: 20px;
    // 适配深色主题
    --card-bg-color: var(--el-bg-color);
    --card-text-color: var(--el-text-color-primary);
    --card-border-color: var(--el-border-color-light);
    --meter-title-color: var(--el-text-color-primary);
    --meter-location-color: var(--el-text-color-secondary);
    --label-color: var(--el-text-color-secondary);
    --value-color: var(--el-text-color-primary);
    --filter-label-color: var(--el-text-color-secondary);
    --hover-shadow: 0 6px 16px rgb(0 0 0 / 10%);

    html.dark & {
      --card-bg-color: var(--el-bg-color-overlay);
      --card-text-color: var(--el-text-color-primary);
      --card-border-color: var(--el-border-color-darker);
      --meter-title-color: var(--el-text-color-primary);
      --meter-location-color: var(--el-text-color-secondary);
      --label-color: var(--el-text-color-secondary);
      --value-color: var(--el-text-color-primary);
      --filter-label-color: var(--el-text-color-secondary);
      --hover-shadow: 0 6px 16px rgb(0 0 0 / 30%);
    }

    .filter-container {
      padding: 15px;
      margin-bottom: 20px;
      background-color: var(--card-bg-color);
      border: 1px solid var(--card-border-color);
      border-radius: 4px;
      box-shadow: 0 0 10px rgb(0 0 0 / 5%);

      .filter-row {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        margin-bottom: 15px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .filter-item {
        display: flex;
        align-items: center;
        margin-right: 20px;
        margin-bottom: 10px;
        margin-left: 20px;

        &.time-unit {
          margin-left: 10px;
        }
      }

      .filter-label {
        margin-right: 8px;
        font-size: 14px;
        color: var(--filter-label-color);
        white-space: nowrap;
      }

      .filter-select {
        width: 180px;
      }

      .filter-buttons {
        display: flex;
        gap: 10px;
        margin-bottom: 10px;
      }

      :deep(.el-input__inner) {
        height: 32px;
        line-height: 32px;
      }

      :deep(.el-button) {
        padding: 8px 16px;
        font-size: 14px;
      }
    }

    .meter-list {
      margin-bottom: 20px;

      .meter-card {
        margin-bottom: 20px;
        background-color: var(--card-bg-color);
        border: 1px solid var(--card-border-color);
        border-radius: 8px;
        transition: all 0.3s;

        &:hover {
          box-shadow: var(--hover-shadow);
          transform: translateY(-5px);
        }

        &.offline {
          opacity: 0.7;
        }

        .meter-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 15px;

          .meter-title {
            display: flex;
            flex-direction: column;

            .building-name {
              font-size: 16px;
              font-weight: bold;
              color: var(--meter-title-color);
            }

            .building-location {
              font-size: 14px;
              color: var(--meter-location-color);
            }
          }

          .trend-btn {
            padding: 6px 10px;
            font-size: 12px;
          }
        }

        .meter-content {
          position: relative;
          display: flex;
          gap: 20px;

          .content-left {
            display: flex;
            flex: 0 0 100px;
            flex-direction: column;
            align-items: center;

            .meter-icon {
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              width: 80px;
              height: 80px;
              margin-bottom: 10px;

              .meter-type-icon {
                margin-bottom: 5px;
                color: var(--el-color-primary);
              }

              .meter-type-text {
                font-size: 12px;
                color: var(--label-color);
              }
            }
          }

          .content-right {
            position: relative;
            flex: 1;

            .meter-data {
              .data-item {
                display: flex;
                margin-bottom: 8px;
                font-size: 13px;
                color: var(--card-text-color);

                .data-label {
                  flex-shrink: 0;
                  width: 70px;
                  padding-right: 8px;
                  color: var(--label-color);
                  text-align: right;
                }

                .data-value {
                  font-weight: 500;
                  color: var(--value-color);
                  text-align: left;
                }
              }
            }

            .status-tag {
              position: absolute;
              top: -5px;
              right: 0;
              padding: 3px 8px;
              font-size: 12px;
              border-radius: 4px;

              &.online {
                color: var(--el-color-success);
                background-color: var(--el-color-success-light-9);
                border: 1px solid var(--el-color-success-light-8);
              }

              &.offline {
                color: var(--el-color-danger);
                background-color: var(--el-color-danger-light-9);
                border: 1px solid var(--el-color-danger-light-8);
              }
            }
          }
        }
      }
    }

    .pagination-container {
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;
    }

    .trend-chart-container {
      width: 100%;
      height: 500px;
      background-color: var(--card-bg-color);

      .trend-chart {
        width: 100%;
        height: 100%;
      }
    }
  }
</style>
