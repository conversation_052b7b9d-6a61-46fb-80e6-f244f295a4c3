import api from '@/utils/http'

// 碳排放数据报表相关接口

/**
 * 碳排放数据报表请求参数接口
 */
export interface CarbonReportParams {
  projectId: number
  beginTime: string
  endTime: string
}

/**
 * 碳排放数据报表响应数据接口
 */
export interface CarbonReportDataItem {
  createBy?: string | null
  createTime?: string | null
  updateBy?: string | null
  updateTime?: string | null
  remark?: string | null
  dayTime?: string | null
  elecQuantity: number // 电量
  waterQuantity: number // 水量
  gasQuantity: number // 气量
  reableQuantity: number // 可再生能源
  ceQuantity: number // 标准煤合计
  co2Quantity: number // 碳排放合计
}

/**
 * 分项用电年报数据接口
 */
export interface CarbonItemReportDataItem {
  itemName: string // 项目名称（如：空调、照明等）
  elecQuantity: number // 电量
  ceQuantity: number // 标准煤
  co2Quantity: number // 碳排放量
}

/**
 * 碳排放数据报表响应接口
 */
export interface CarbonReportResponse {
  msg: string
  code: number
  data: CarbonReportDataItem[]
}

/**
 * 分项用电年报响应接口
 */
export interface CarbonItemReportResponse {
  msg: string
  code: number
  data: CarbonItemReportDataItem[]
}

/**
 * 获取历年能耗数据
 * @param params 查询参数
 * @returns 历年能耗数据
 */
export function getCarbonHistoryReport(params: CarbonReportParams): Promise<CarbonReportResponse> {
  return api.post({
    url: '/carbonemission/carbonfactor/getCarbonHistoryReport',
    data: params
  })
}

/**
 * 获取能耗年报数据
 * @param params 查询参数
 * @returns 能耗年报数据
 */
export function getCarbonYearReport(params: CarbonReportParams): Promise<CarbonReportResponse> {
  return api.post({
    url: '/carbonemission/carbonfactor/getCarbonYearReport',
    data: params
  })
}

/**
 * 获取分项用电年报数据
 * @param params 查询参数
 * @returns 分项用电年报数据
 */
export function getCarbonItemReport(params: CarbonReportParams): Promise<CarbonItemReportResponse> {
  return api.post({
    url: '/carbonemission/carbonfactor/getCarbonItemReport',
    data: params
  })
}

/**
 * 获取累计年报数据
 * @param params 查询参数
 * @returns 累计年报数据
 */
export function getCarbonQuarterReport(params: CarbonReportParams): Promise<CarbonReportResponse> {
  return api.post({
    url: '/carbonemission/carbonfactor/getCarbonQuarterReport',
    data: params
  })
}
