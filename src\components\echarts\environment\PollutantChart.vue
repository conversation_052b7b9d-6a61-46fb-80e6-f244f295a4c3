<template>
  <div class="chart-container" ref="chartContainer"></div>
</template>

<script setup>
  import { ref, onMounted, watch, nextTick } from 'vue'
  import * as echarts from 'echarts'

  const props = defineProps({
    timeRange: {
      type: String,
      default: 'day'
    },
    chartData: {
      type: Object,
      default: () => ({
        xAxisData: [],
        pm25Data: [],
        pm10Data: [],
        tvocData: [],
        formaldehydeData: []
      })
    }
  })

  const chartContainer = ref(null)
  let chart = null

  // 初始化图表
  const initChart = () => {
    if (!chartContainer.value) return

    // 如果图表已经存在，销毁它以避免重复创建
    if (chart) {
      chart.dispose()
    }

    // 创建新的图表实例
    chart = echarts.init(chartContainer.value)

    // 设置图表选项
    const option = {
      tooltip: {
        trigger: 'axis',
        formatter: function (params) {
          let result = params[0].axisValue + '<br/>'
          params.forEach((param) => {
            let marker = `<span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${param.color};"></span>`
            let value = param.value

            // 为不同类型的数据添加适当的单位
            if (param.seriesName === 'PM2.5' || param.seriesName === 'PM10') {
              value = value + 'μg/m³'
            } else if (param.seriesName === 'TVOC' || param.seriesName === '甲醛') {
              value = value + 'μg/m³'
            }

            result += marker + param.seriesName + ': ' + value + '<br/>'
          })
          return result
        }
      },
      legend: {
        data: ['PM2.5', 'PM10'],
        bottom: 0
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: props.chartData.xAxisData,
        axisLine: {
          lineStyle: {
            color: '#999'
          }
        },
        axisLabel: {
          color: '#666'
        }
      },
      yAxis: [
        {
          type: 'value',
          name: 'PM2.5(μg/m³)',
          nameTextStyle: {
            color: '#666'
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#E6A23C'
            }
          },
          axisLabel: {
            formatter: '{value}',
            color: '#666'
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(224, 230, 241, 0.8)'
            }
          }
        },
        {
          type: 'value',
          name: 'PM10(μg/m³)',
          nameTextStyle: {
            color: '#666'
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#F56C6C'
            }
          },
          axisLabel: {
            formatter: '{value}',
            color: '#666'
          },
          splitLine: {
            show: false
          },
          position: 'right'
        }
      ],
      series: [
        {
          name: 'PM2.5',
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          showSymbol: false,
          data: props.chartData.pm25Data,
          itemStyle: {
            color: '#E6A23C'
          },
          lineStyle: {
            width: 2
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(230, 162, 60, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(230, 162, 60, 0.1)'
              }
            ])
          }
        },
        {
          name: 'PM10',
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          showSymbol: false,
          yAxisIndex: 1, // 使用右侧Y轴
          data: props.chartData.pm10Data,
          itemStyle: {
            color: '#F56C6C'
          },
          lineStyle: {
            width: 2
          }
        }
      ]
    }

    // 应用选项
    chart.setOption(option)

    // 添加响应式调整
    window.addEventListener('resize', () => {
      if (chart) {
        chart.resize()
      }
    })
  }

  // 更新图表
  const updateChart = () => {
    if (!chart) {
      initChart()
      return
    }

    chart.setOption({
      xAxis: {
        data: props.chartData.xAxisData
      },
      series: [
        {
          data: props.chartData.pm25Data
        },
        {
          data: props.chartData.pm10Data
        }
      ]
    })
  }

  // 监听图表数据变化
  watch(
    () => props.chartData,
    () => {
      nextTick(() => {
        updateChart()
      })
    },
    { deep: true }
  )

  // 监听时间范围变化
  watch(
    () => props.timeRange,
    () => {
      nextTick(() => {
        updateChart()
      })
    }
  )

  // 组件挂载时初始化图表
  onMounted(() => {
    initChart()
  })
</script>

<style scoped lang="scss">
  .chart-container {
    width: 100%;
    height: 350px;
  }
</style>
