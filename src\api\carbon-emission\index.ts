import request from '@/utils/http'

// 碳排放总览相关接口

/**
 * 碳排放总览查询参数
 */
export interface CarbonReportParams {
  projectIdList: number[] // 项目ID集合
  beginTime: string // 开始时间，格式：YYYY-MM-DD
  endTime: string // 结束时间，格式：YYYY-MM-DD
  timeType: number // 时间类型：0-日，1-月，2-年
}

/**
 * 碳排放数据项
 */
export interface CarbonDataItem {
  carbonemissionQuantity: number // 碳排放总量
  emissionQuantity: number // 光伏碳减排
  quantity: number // 实际碳排放量
}

/**
 * 能源消耗数据项
 */
export interface EnergyDataItem {
  elecQuantity: number // 电
  waterQuantity: number // 水
  gasQuantity: number // 气
  photovoltaicQuantity: number // 光伏
}

/**
 * 碳排放报告数据
 */
export interface CarbonReportData {
  timeList: string[] // 时间列表
  dataList: CarbonDataItem[] // 碳排放数据列表
  itemTimeList: string[] // 分项时间列表
  itemDataList: EnergyDataItem[] // 分项数据列表
}

/**
 * 碳排放总览响应
 */
export interface CarbonReportResponse {
  msg: string
  code: number
  data: CarbonReportData[]
}

/**
 * 图表总体数据格式
 */
export interface ChartTotalData {
  xAxis: string[]
  values: number[] // 碳排放总量
  reductionData: number[] // 光伏碳减排
  actualData: number[] // 实际碳排放量
  average: string
}

/**
 * 图表分项数据格式
 */
export interface ChartDetailData {
  xAxis: string[]
  legend: string[]
  series: Array<{
    name: string
    data: number[]
  }>
}

/**
 * 转换后的图表数据
 */
export interface ChartData {
  total: ChartTotalData
  detail: ChartDetailData
}

/**
 * 获取碳排放总览数据
 * @param params 查询参数
 * @returns 碳排放数据
 */
export function getCarbonReport(params: CarbonReportParams): Promise<CarbonReportResponse> {
  return request.post({
    url: '/carbonemission/carbonfactor/getCarbonReport',
    data: params
  })
}

/**
 * 时间粒度映射
 */
export const timeTypeMap = {
  day: 0,
  month: 1,
  year: 2
} as const

/**
 * 将API返回的数据转换为图表组件需要的格式
 * @param apiData API返回的原始数据
 * @returns 转换后的图表数据
 */
export function transformCarbonDataToChart(apiData: CarbonReportData): ChartData {
  // 转换总体数据
  const totalData: ChartTotalData = {
    xAxis: apiData.timeList,
    values: apiData.dataList.map((item) => item.carbonemissionQuantity), // 使用总碳排放量
    reductionData: apiData.dataList.map((item) => item.emissionQuantity),
    actualData: apiData.dataList.map((item) => item.quantity),
    average: calculateAverage(apiData.dataList.map((item) => item.carbonemissionQuantity))
  }

  // 转换分项数据
  const detailData: ChartDetailData = {
    xAxis: apiData.itemTimeList,
    legend: ['用电', '用水', '用气', '光伏'],
    series: [
      {
        name: '用电',
        data: apiData.itemDataList.map((item) => item.elecQuantity)
      },
      {
        name: '用水',
        data: apiData.itemDataList.map((item) => item.waterQuantity)
      },
      {
        name: '用气',
        data: apiData.itemDataList.map((item) => item.gasQuantity)
      },
      {
        name: '光伏',
        data: apiData.itemDataList.map((item) => item.photovoltaicQuantity)
      }
    ]
  }

  return {
    total: totalData,
    detail: detailData
  }
}

/**
 * 计算平均值
 * @param values 数值数组
 * @returns 平均值字符串
 */
function calculateAverage(values: number[]): string {
  if (values.length === 0) return '0'
  const sum = values.reduce((acc, val) => acc + val, 0)
  const average = sum / values.length
  return average.toFixed(2)
}

/**
 * 碳排放环比查询参数
 */
export interface CarbonMonthOnMonthParams {
  projectId: number // 项目ID
  buildingId?: number // 楼栋ID
  beginTime: string // 开始时间，格式：YYYY-MM
  endTime: string // 结束时间，格式：YYYY-MM
  timeType: number // 时间类型：1-月，2-年
}

/**
 * 碳排放同比查询参数
 */
export interface CarbonYearOnYearParams {
  projectId: number // 项目ID
  buildingId?: number // 楼栋ID
  beginTime: string // 开始时间，格式：YYYY-MM
  endTime: string // 结束时间，格式：YYYY-MM
  timeType: number // 时间类型：1-月，2-年
}

/**
 * 环比/同比数据项
 */
export interface ComparisonDataItem {
  data: number // 当前值
  preData: number // 对比值
  increment: number // 增量
  incrementPercent: number // 增长百分比
  dataResult: string // 增长/下降状态
}

/**
 * 环比/同比响应数据
 */
export interface ComparisonResponse {
  msg: string
  code: number
  data: {
    timeList: string[] // 时间列表
    dataList: ComparisonDataItem[] // 对比数据列表
  }
}

/**
 * 获取碳排放环比数据
 * @param params 查询参数
 * @returns 环比数据
 */
export function getCarbonMonthOnMonthReport(
  params: CarbonMonthOnMonthParams
): Promise<ComparisonResponse> {
  return request.post({
    url: '/carbonemission/carbonfactor/getCarbonMonthOnMonthReport',
    data: params
  })
}

/**
 * 获取碳排放同比数据
 * @param params 查询参数
 * @returns 同比数据
 */
export function getCarbonYearOnYearReport(
  params: CarbonYearOnYearParams
): Promise<ComparisonResponse> {
  return request.post({
    url: '/carbonemission/carbonfactor/getCarbonYearOnYearReport',
    data: params
  })
}

/**
 * 兼容旧版本的函数名（用于现有代码的兼容性）
 * @deprecated 请使用 getCarbonReport 替代
 */
export const getCarbonEmissionData = getCarbonReport

// ========== 碳排放对比相关接口 ==========

/**
 * 碳排放对比查询参数
 */
export interface CarbonComparisonParams {
  projectIdList: number[] // 项目ID集合
  beginTime: string // 开始时间，格式：YYYY-MM-DD
  endTime: string // 结束时间，格式：YYYY-MM-DD
  timeType: number // 时间类型：0-日，1-月，2-年
  dataType: string // 数据类型：0-碳排放总量，1-碳排放强度
}

/**
 * 碳排放对比数据项
 */
export interface CarbonComparisonDataItem {
  projectName: string // 项目名称
  quantity: number // 碳排放总量
  strength: number // 碳排放强度
}

/**
 * 碳排放对比响应数据
 */
export interface CarbonComparisonResponse {
  msg: string
  code: number
  data: {
    timeList: string[] // 时间列表
    dataList: CarbonComparisonDataItem[][] // 对比数据列表，每个时间点对应一个项目数组
  }
}

/**
 * 获取碳排放对比数据
 * @param params 查询参数
 * @returns 碳排放对比数据
 */
export function getCarbonComparisonReport(
  params: CarbonComparisonParams
): Promise<CarbonComparisonResponse> {
  return request.post({
    url: '/carbonemission/carbonfactor/getCarbonComparisonReport',
    data: params
  })
}

// ========== 碳排放数据查询相关接口 ==========

/**
 * 碳排放数据查询参数
 */
export interface CarbonSearchParams {
  projectId: number // 项目ID
  buildingId?: number // 楼栋ID
  energyType?: string // 能源类型
  beginTime?: string // 开始时间，格式：YYYY-MM-DD
  endTime?: string // 结束时间，格式：YYYY-MM-DD
  pageSize: number // 页大小
  pageNum: number // 当前页
}

/**
 * 碳排放数据查询项
 */
export interface CarbonSearchItem {
  createBy?: string
  createTime?: string
  updateBy?: string
  updateTime?: string
  remark?: string
  projectName: string // 项目名称
  buildingName: string // 楼栋名称
  buildingType?: string // 楼栋类型
  waterQuantity: number // 耗水量
  elecQuantity: number // 耗电量
  gasQuantity?: number // 耗气量
  co2Quantity: number // 碳排放量(kgCO₂)
  ceQuantity?: number // CE量
  reportTime: string | number // 报告时间，格式：YYYYMMDD 或时间戳
}

/**
 * 碳排放数据查询响应
 */
export interface CarbonSearchResponse {
  total: number
  rows: CarbonSearchItem[]
  code: number
  msg: string
}

/**
 * 获取碳排放数据查询列表
 * @param params 查询参数
 * @returns 碳排放数据列表
 */
export function getCarbonSearchReport(params: CarbonSearchParams): Promise<CarbonSearchResponse> {
  return request.get({
    url: '/carbonemission/carbonfactor/getCarbonSearchReport',
    params
  })
}

// ========== 碳足迹相关接口 ==========

/**
 * 碳足迹查询参数
 */
export interface CarbonFootprintParams {
  projectId: number // 项目ID
  beginTime: string // 开始时间，格式：YYYY-MM-DD
  endTime: string // 结束时间，格式：YYYY-MM-DD
}

/**
 * 碳足迹数据项
 */
export interface CarbonFootprintDataItem {
  itemName: string // 设备名称
  quantity: number // 数量
}

/**
 * 碳足迹能源类型
 */
export interface CarbonFootprintEnergyType {
  name: string // 能源类型名称（如：耗水、耗电、耗气、光伏）
  totalQuantity: number // 总量
  dataList: CarbonFootprintDataItem[] // 详细项目列表
}

/**
 * 碳足迹响应数据
 */
export interface CarbonFootprintResponse {
  msg: string
  code: number
  data: CarbonFootprintEnergyType[]
}

/**
 * 获取碳足迹数据
 * @param params 查询参数
 * @returns 碳足迹数据
 */
export function getCarbonFootprintReport(
  params: CarbonFootprintParams
): Promise<CarbonFootprintResponse> {
  return request.post({
    url: '/carbonemission/carbonfactor/getCarbonFootprintReport',
    data: params
  })
}
