/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ArtBackToTop: typeof import('./src/components/core/base/ArtBackToTop.vue')['default']
    ArtBarChart: typeof import('./src/components/core/charts/ArtBarChart.vue')['default']
    ArtBarChartCard: typeof import('./src/components/core/cards/ArtBarChartCard.vue')['default']
    ArtBasicBanner: typeof import('./src/components/core/banners/ArtBasicBanner.vue')['default']
    ArtBreadcrumb: typeof import('./src/components/core/layouts/art-breadcrumb/index.vue')['default']
    ArtButtonMore: typeof import('./src/components/core/forms/ArtButtonMore.vue')['default']
    ArtButtonTable: typeof import('./src/components/core/forms/ArtButtonTable.vue')['default']
    ArtCardBanner: typeof import('./src/components/core/banners/ArtCardBanner.vue')['default']
    ArtChatWindow: typeof import('./src/components/core/layouts/art-chat-window/index.vue')['default']
    ArtCutterImg: typeof import('./src/components/core/media/ArtCutterImg.vue')['default']
    ArtDataListCard: typeof import('./src/components/core/cards/ArtDataListCard.vue')['default']
    ArtDonutChartCard: typeof import('./src/components/core/cards/ArtDonutChartCard.vue')['default']
    ArtDragVerify: typeof import('./src/components/core/forms/ArtDragVerify.vue')['default']
    ArtDualBarCompareChart: typeof import('./src/components/core/charts/ArtDualBarCompareChart.vue')['default']
    ArtExcelExport: typeof import('./src/components/core/forms/ArtExcelExport.vue')['default']
    ArtExcelImport: typeof import('./src/components/core/forms/ArtExcelImport.vue')['default']
    ArtException: typeof import('./src/components/core/views/exception/ArtException.vue')['default']
    ArtFastEnter: typeof import('./src/components/core/layouts/art-fast-enter/index.vue')['default']
    ArtFestivalTextScroll: typeof import('./src/components/core/text-effect/ArtFestivalTextScroll.vue')['default']
    ArtFireworksEffect: typeof import('./src/components/core/layouts/art-fireworks-effect/index.vue')['default']
    ArtFormInput: typeof import('./src/components/core/forms/ArtFormInput.vue')['default']
    ArtFormSelect: typeof import('./src/components/core/forms/ArtFormSelect.vue')['default']
    ArtGlobalSearch: typeof import('./src/components/core/layouts/art-global-search/index.vue')['default']
    ArtHBarChart: typeof import('./src/components/core/charts/ArtHBarChart.vue')['default']
    ArtHeaderBar: typeof import('./src/components/core/layouts/art-header-bar/index.vue')['default']
    ArtHorizontalMenu: typeof import('./src/components/core/layouts/art-menus/art-horizontal-menu/index.vue')['default']
    ArtIconSelector: typeof import('./src/components/core/base/ArtIconSelector.vue')['default']
    ArtImageCard: typeof import('./src/components/core/cards/ArtImageCard.vue')['default']
    ArtKLineChart: typeof import('./src/components/core/charts/ArtKLineChart.vue')['default']
    ArtLineChart: typeof import('./src/components/core/charts/ArtLineChart.vue')['default']
    ArtLineChartCard: typeof import('./src/components/core/cards/ArtLineChartCard.vue')['default']
    ArtMapChart: typeof import('./src/components/core/charts/ArtMapChart.vue')['default']
    ArtMenuRight: typeof import('./src/components/core/others/ArtMenuRight.vue')['default']
    ArtMixedMenu: typeof import('./src/components/core/layouts/art-menus/art-mixed-menu/index.vue')['default']
    ArtNetwork: typeof import('./src/components/core/base/ArtNetwork.vue')['default']

    ArtPageContent: typeof import('./src/components/core/layouts/art-page-content/index.vue')['default']
    ArtProgressCard: typeof import('./src/components/core/cards/ArtProgressCard.vue')['default']
    ArtRadarChart: typeof import('./src/components/core/charts/ArtRadarChart.vue')['default']
    ArtRingChart: typeof import('./src/components/core/charts/ArtRingChart.vue')['default']
    ArtScatterChart: typeof import('./src/components/core/charts/ArtScatterChart.vue')['default']
    ArtScreenLock: typeof import('./src/components/core/layouts/art-screen-lock/index.vue')['default']
    ArtSettingsPanel: typeof import('./src/components/core/layouts/art-settings-panel/index.vue')['default']
    ArtSidebarMenu: typeof import('./src/components/core/layouts/art-menus/art-sidebar-menu/index.vue')['default']
    ArtStatsCard: typeof import('./src/components/core/cards/ArtStatsCard.vue')['default']
    ArtTable: typeof import('./src/components/core/tables/ArtTable.vue')['default']
    ArtTableBar: typeof import('./src/components/core/tables/ArtTableBar.vue')['default']
    ArtTextScroll: typeof import('./src/components/core/text-effect/ArtTextScroll.vue')['default']
    ArtTimelineListCard: typeof import('./src/components/core/cards/ArtTimelineListCard.vue')['default']
    ArtVideoPlayer: typeof import('./src/components/core/media/ArtVideoPlayer.vue')['default']
    ArtWangEditor: typeof import('./src/components/core/forms/ArtWangEditor.vue')['default']
    ArtWatermark: typeof import('./src/components/core/others/ArtWatermark.vue')['default']
    ArtWorkTab: typeof import('./src/components/core/layouts/art-work-tab/index.vue')['default']
    BannerWidget: typeof import('./src/components/core/BannerWidget.vue')['default']
    HorizontalSubmenu: typeof import('./src/components/core/layouts/art-menus/art-horizontal-menu/widget/HorizontalSubmenu.vue')['default']
    LoginLeftView: typeof import('./src/components/core/views/login/LoginLeftView.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SidebarSubmenu: typeof import('./src/components/core/layouts/art-menus/art-sidebar-menu/widget/SidebarSubmenu.vue')['default']
  }
}
