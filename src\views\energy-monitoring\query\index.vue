<template>
  <div class="query-container page-content">
    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <div class="filter-container">
        <div class="filter-row">
          <!-- 项目和楼栋选择器 -->
          <div class="filter-item">
            <ProjectBuildingSelector
              ref="projectBuildingSelectorRef"
              v-model="projectBuildingSelection"
              :auto-select-first="true"
              @change="handleProjectBuildingChange"
              @project-loaded="handleProjectLoaded"
            />
          </div>

          <div class="filter-item">
            <span class="filter-label">设备类型:</span>
            <el-select
              v-model="filterForm.equipmentType"
              placeholder="请选择设备类型"
              clearable
              :loading="equipmentTypeLoading"
              class="filter-select"
              @change="handleEquipmentTypeChange"
            >
              <el-option
                v-for="item in equipmentTypeOptions"
                :key="item.key"
                :label="item.label"
                :value="item.key"
              />
            </el-select>
          </div>

          <div class="filter-item">
            <span class="filter-label">时间粒度:</span>
            <el-radio-group
              v-model="filterForm.timeGranularity"
              @change="handleTimeGranularityChange"
              class="time-granularity-buttons"
            >
              <el-radio-button label="day">日</el-radio-button>
              <el-radio-button label="month">月</el-radio-button>
              <el-radio-button label="year">年</el-radio-button>
            </el-radio-group>
          </div>

          <div class="filter-item date-filter">
            <span class="filter-label">选择时间:</span>
            <div class="date-picker-group">
              <!-- 日期范围选择器 -->
              <el-date-picker
                v-if="filterForm.timeGranularity === 'day'"
                v-model="filterForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                class="date-picker"
                @change="handleDateRangeChange"
              />
              <!-- 月份范围选择器 -->
              <el-date-picker
                v-else-if="filterForm.timeGranularity === 'month'"
                v-model="filterForm.dateRange"
                type="monthrange"
                range-separator="至"
                start-placeholder="开始月份"
                end-placeholder="结束月份"
                value-format="YYYY-MM"
                class="date-picker"
                @change="handleDateRangeChange"
              />
              <!-- 年份单选选择器 -->
              <el-date-picker
                v-else-if="filterForm.timeGranularity === 'year'"
                v-model="filterForm.yearValue"
                type="year"
                placeholder="选择年份"
                value-format="YYYY"
                class="date-picker"
                @change="handleYearChange"
              />
            </div>
          </div>

          <div class="filter-actions">
            <el-button type="primary" @click="handleQuery" :disabled="queryButtonDisabled">
              <el-icon><Search /></el-icon> 查询
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon> 重置
            </el-button>
            <el-button type="success" @click="handleExport">
              <el-icon><Download /></el-icon> 导出列表
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 表格和操作区域 -->
    <el-card class="table-card">
      <!-- 使用 ArtTable 替代 el-table 和 el-pagination -->
      <ArtTable
        :data="tableData"
        :loading="loading"
        row-key="id"
        :border="false"
        :highlight-current-row="false"
        :total="totalCount"
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        index
        class="query-table"
      >
        <el-table-column prop="projectName" label="项目名称" width="380" align="center" />
        <el-table-column
          v-if="showBuildingColumn"
          prop="buildingName"
          label="楼栋名称"
          align="center"
        />
        <el-table-column v-if="showEquipmentTypeColumn" label="设备类型" align="center">
          <template #default="{ row }">
            {{ getEquipmentTypeName(filterForm.equipmentType) }}
          </template>
        </el-table-column>
        <el-table-column prop="reportTime" label="时间" align="center">
          <template #default="{ row }">
            {{ formatDate(row.reportTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="wQuantity" label="耗水量(m³)" align="center" />
        <el-table-column prop="eQuantity" label="耗电量(kWh)" align="center" />
        <el-table-column prop="gQuantity" label="耗气量(m³)" align="center" />
        <el-table-column prop="cQuantity" label="折标煤(kgce)" align="center" />
        <el-table-column prop="co2Quantity" label="碳排放量(kgCO₂)" align="center" />
      </ArtTable>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted, watch, nextTick } from 'vue'
  import { ElMessage, FormInstance } from 'element-plus'
  import { Search, Refresh, Download } from '@element-plus/icons-vue' // 移除了 View, Edit, Delete 图标导入
  import ProjectBuildingSelector from '@/components/ProjectBuildingSelector.vue'
  import { getEnergySearch, getEquipmentTypeDict } from '@/api/energy-monitoring/queryApi'
  // ArtTable组件已在全局注册，无需导入

  // 定义接口或类型
  interface TableRow {
    id?: number
    projectName: string
    buildingName: string
    reportTime: string
    wQuantity: number // 耗水量
    eQuantity: number // 耗电量
    gQuantity: number // 耗气量
    cQuantity: number // 折标煤
    co2Quantity: number // 碳排放量
  }

  interface FilterForm {
    equipmentType: string
    timeGranularity: 'day' | 'month' | 'year'
    dateRange: [string, string] | undefined
    yearValue: string | undefined
  }

  interface Option {
    key: string
    label: string
  }

  // 筛选表单的引用
  const filterFormRef = ref<FormInstance>()
  const loading = ref(false) // 表格加载状态

  // 项目楼栋选择相关数据
  const projectBuildingSelection = ref({
    projectId: null, // 初始为空，等待项目列表加载后设置第一个
    buildingId: null
  })
  const projectBuildingSelectorRef = ref()

  // 控制楼栋列显示
  const showBuildingColumn = ref(false)

  // 控制设备类型列显示
  const showEquipmentTypeColumn = ref(false)

  // 设置默认日期为当前日期往前推一个月
  const setDefaultDateRange = (): [string, string] => {
    const today = new Date()
    const oneMonthAgo = new Date()
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1)

    const todayStr = today.toISOString().split('T')[0]
    const oneMonthAgoStr = oneMonthAgo.toISOString().split('T')[0]

    console.log('设置默认日期范围（前一个月）:', {
      开始时间: oneMonthAgoStr,
      结束时间: todayStr
    })

    return [oneMonthAgoStr, todayStr]
  }

  // 设置默认月份范围为当前月份往前推一个月
  const setDefaultMonthRange = (): [string, string] => {
    const today = new Date()
    const oneMonthAgo = new Date()
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1)

    const todayStr = `${today.getFullYear()}-${(today.getMonth() + 1).toString().padStart(2, '0')}`
    const oneMonthAgoStr = `${oneMonthAgo.getFullYear()}-${(oneMonthAgo.getMonth() + 1).toString().padStart(2, '0')}`

    console.log('设置默认月份范围（前一个月）:', {
      开始月份: oneMonthAgoStr,
      结束月份: todayStr
    })

    return [oneMonthAgoStr, todayStr]
  }

  // 筛选表单数据
  const filterForm = reactive<FilterForm>({
    equipmentType: '',
    timeGranularity: 'day', // 默认按日查询，以便设置默认日期
    dateRange: setDefaultDateRange(), // 设置默认日期范围
    yearValue: undefined // 年份模式下才使用
  })

  // 设备类型选项
  const equipmentTypeOptions = ref<Option[]>([])
  const equipmentTypeLoading = ref(false)

  // 表格数据
  const tableData = ref<TableRow[]>([])
  const totalCount = ref(0)

  // 获取设备类型选项
  const fetchEquipmentTypeOptions = async () => {
    try {
      equipmentTypeLoading.value = true
      const response = await getEquipmentTypeDict()

      if (response.code === 200) {
        equipmentTypeOptions.value = response.data || []
        console.log('获取设备类型选项成功:', equipmentTypeOptions.value)
      } else {
        ElMessage.error(response.msg || '获取设备类型选项失败')
        equipmentTypeOptions.value = []
      }
    } catch (error) {
      console.error('获取设备类型选项失败:', error)
      ElMessage.error('获取设备类型选项失败，请稍后重试')
      equipmentTypeOptions.value = []
    } finally {
      equipmentTypeLoading.value = false
    }
  }

  // 分页配置
  const pagination = reactive({
    currentPage: 1,
    pageSize: 10
  })

  // 处理项目楼栋选择变化
  const handleProjectBuildingChange = (data: any) => {
    console.log('项目楼栋选择变化:', data)

    // 控制楼栋列显示：当选择了具体楼栋时显示楼栋列，否则隐藏
    showBuildingColumn.value = !!data.buildingId

    // 重置分页到第一页
    if (data.projectId) {
      pagination.currentPage = 1
    }
    // 不需要在这里手动触发查询，watch监听器会自动处理
  }

  // 处理设备类型变化
  const handleEquipmentTypeChange = (value: string | null) => {
    console.log('设备类型变化:', value)

    // 控制设备类型列显示：当选择了具体设备类型时显示设备类型列，否则隐藏
    showEquipmentTypeColumn.value = !!value

    // 不需要在这里手动触发查询，watch监听器会自动处理
  }

  // 处理时间粒度变化
  const handleTimeGranularityChange = (value: string | number | boolean | undefined) => {
    const granularity = value as string
    console.log('时间粒度变化:', granularity)
    // 清空之前的时间选择
    filterForm.dateRange = undefined
    filterForm.yearValue = undefined

    // 根据时间粒度设置默认值
    if (granularity === 'day') {
      filterForm.dateRange = setDefaultDateRange()
    } else if (granularity === 'month') {
      filterForm.dateRange = setDefaultMonthRange()
    } else if (granularity === 'year') {
      // 年份默认选择当前年份
      const currentYear = new Date().getFullYear().toString()
      filterForm.yearValue = currentYear
    }

    // 不需要在这里手动触发查询，watch监听器会自动处理
  }

  // 处理日期范围变化
  const handleDateRangeChange = (value: [string, string] | null) => {
    console.log('日期范围变化:', value)
    // 不需要在这里手动触发查询，watch监听器会自动处理
  }

  // 处理年份变化
  const handleYearChange = (value: string | null) => {
    console.log('年份变化:', value)
    // 不需要在这里手动触发查询，watch监听器会自动处理
  }

  // 监听项目列表加载完成
  const handleProjectLoaded = (projectList: any[]) => {
    console.log('项目列表加载完成:', projectList)
    // 自动选择第一个项目
    if (projectList && projectList.length > 0 && !projectBuildingSelection.value.projectId) {
      const firstProject = projectList[0]
      projectBuildingSelection.value.projectId = firstProject.id
      console.log('自动选择第一个项目:', firstProject)

      // 设置初始化完成标记，启用自动查询
      setTimeout(() => {
        isInitialized.value = true
        // 自动执行查询
        fetchEnergyData()
      }, 100) // 稍微延迟确保组件完全初始化
    } else {
      // 即使没有项目，也要设置初始化完成标记
      setTimeout(() => {
        isInitialized.value = true
      }, 100)
    }
  }

  // 获取能耗数据
  const fetchEnergyData = async () => {
    // 如果没有选择项目，不发请求
    if (!projectBuildingSelection.value.projectId) {
      console.log('没有选择项目，不发请求')
      return
    }

    try {
      loading.value = true

      // 构建查询参数
      let beginTime: string | undefined
      let endTime: string | undefined

      // 根据时间粒度设置时间参数
      if (filterForm.timeGranularity === 'year' && filterForm.yearValue) {
        // 年份选择：设置为该年的第一天和最后一天
        beginTime = `${filterForm.yearValue}-01-01`
        endTime = `${filterForm.yearValue}-12-31`
      } else if (filterForm.dateRange) {
        // 日期范围或月份范围选择
        beginTime = filterForm.dateRange[0]
        endTime = filterForm.dateRange[1]

        // 如果是月份选择，需要转换为具体的日期
        if (filterForm.timeGranularity === 'month') {
          if (beginTime) {
            beginTime = `${beginTime}-01` // 月份开始日期
          }
          if (endTime) {
            // 计算月份的最后一天
            const [year, month] = endTime.split('-')
            const lastDay = new Date(parseInt(year), parseInt(month), 0).getDate()
            endTime = `${endTime}-${lastDay.toString().padStart(2, '0')}`
          }
        }
      }

      const params = {
        projectId: projectBuildingSelection.value.projectId,
        buildingId: projectBuildingSelection.value.buildingId || undefined,
        beginTime,
        endTime,
        equipmentType: filterForm.equipmentType || undefined,
        pageSize: pagination.pageSize,
        pageNum: pagination.currentPage
      }

      console.log('查询参数:', params)

      // 调用API
      const response = await getEnergySearch(params)

      if (response.code === 200) {
        tableData.value = response.rows || []
        totalCount.value = response.total || 0
        ElMessage.success('查询成功！')

        // 保存当前查询参数
        lastQueryParams.value = { ...params }
        // 禁用查询按钮（因为已经查询过相同条件）
        queryButtonDisabled.value = true
      } else {
        ElMessage.error(response.msg || '查询失败')
        tableData.value = []
        totalCount.value = 0
      }
    } catch (error) {
      console.error('查询失败:', error)
      ElMessage.error('查询失败，请稍后重试')
      tableData.value = []
      totalCount.value = 0
    } finally {
      loading.value = false
    }
  }

  // 查询状态管理 - 防重复查询
  const lastQueryParams = ref<any>(null)
  const queryButtonDisabled = ref(false) // 查询按钮状态

  // 比较查询参数是否相同
  const isSameQueryParams = (params1: any, params2: any) => {
    if (!params1 || !params2) return false
    return (
      params1.projectId === params2.projectId &&
      params1.buildingId === params2.buildingId &&
      params1.beginTime === params2.beginTime &&
      params1.endTime === params2.endTime &&
      params1.equipmentType === params2.equipmentType &&
      params1.pageSize === params2.pageSize &&
      params1.pageNum === params2.pageNum
    )
  }

  // 查询处理
  const handleQuery = () => {
    // 如果查询按钮已禁用，不执行查询
    if (queryButtonDisabled.value) {
      ElMessage.info('查询条件未变化，无需重复查询')
      return
    }

    // 构建当前查询参数
    let beginTime: string | undefined
    let endTime: string | undefined

    // 根据时间粒度设置时间参数
    if (filterForm.timeGranularity === 'year' && filterForm.yearValue) {
      beginTime = `${filterForm.yearValue}-01-01`
      endTime = `${filterForm.yearValue}-12-31`
    } else if (filterForm.dateRange) {
      beginTime = filterForm.dateRange[0]
      endTime = filterForm.dateRange[1]

      if (filterForm.timeGranularity === 'month') {
        if (beginTime) {
          beginTime = `${beginTime}-01`
        }
        if (endTime) {
          const [year, month] = endTime.split('-')
          const lastDay = new Date(parseInt(year), parseInt(month), 0).getDate()
          endTime = `${endTime}-${lastDay.toString().padStart(2, '0')}`
        }
      }
    }

    const currentParams = {
      projectId: projectBuildingSelection.value.projectId,
      buildingId: projectBuildingSelection.value.buildingId || undefined,
      beginTime,
      endTime,
      equipmentType: filterForm.equipmentType || undefined,
      pageSize: pagination.pageSize,
      pageNum: 1 // 查询时重置为第一页
    }

    // 检查查询参数是否与上次相同
    if (isSameQueryParams(currentParams, lastQueryParams.value)) {
      ElMessage.info('查询条件未变化')
      queryButtonDisabled.value = true
      return
    }

    pagination.currentPage = 1
    fetchEnergyData()
  }

  // 重置处理
  const handleReset = () => {
    // 重置项目楼栋选择
    if (projectBuildingSelectorRef.value) {
      projectBuildingSelectorRef.value.reset()
    }

    // 重置筛选表单
    filterFormRef.value?.resetFields()
    filterForm.equipmentType = ''
    filterForm.timeGranularity = 'day' // 重置为日期模式
    filterForm.dateRange = setDefaultDateRange() // 设置默认日期范围
    filterForm.yearValue = undefined // 清空年份值

    // 重置列显示状态
    showBuildingColumn.value = false
    showEquipmentTypeColumn.value = false

    // 重置分页
    pagination.currentPage = 1

    // 清空查询缓存，允许重新查询
    lastQueryParams.value = null
    queryButtonDisabled.value = false

    // 清空表格数据
    tableData.value = []
    totalCount.value = 0

    ElMessage.info('筛选条件已重置。')
  }

  // 导出列表处理 - 暂时占位
  const handleExport = () => {
    ElMessage.info('导出列表功能待实现。')
  }

  // 分页大小改变处理
  const handleSizeChange = (val: number) => {
    pagination.pageSize = val
    pagination.currentPage = 1
    fetchEnergyData()
  }

  // 当前页改变处理
  const handleCurrentChange = (val: number) => {
    pagination.currentPage = val
    fetchEnergyData()
  }

  // 标记是否已初始化完成
  const isInitialized = ref(false)

  // 格式化日期显示
  const formatDate = (dateStr: string) => {
    if (!dateStr) return ''

    // 如果是8位数字格式(如20250530)，转换为YYYY-MM-DD格式
    if (/^\d{8}$/.test(dateStr)) {
      const year = dateStr.substring(0, 4)
      const month = dateStr.substring(4, 6)
      const day = dateStr.substring(6, 8)
      return `${year}-${month}-${day}`
    }

    // 如果是6位数字格式(如202505)，转换为YYYY-MM格式
    if (/^\d{6}$/.test(dateStr)) {
      const year = dateStr.substring(0, 4)
      const month = dateStr.substring(4, 6)
      return `${year}-${month}`
    }

    // 如果是4位数字格式(如2025)，直接返回
    if (/^\d{4}$/.test(dateStr)) {
      return dateStr
    }

    // 如果已经是正确格式或其他格式，直接返回
    return dateStr
  }

  // 获取设备类型名称
  const getEquipmentTypeName = (equipmentTypeKey: string) => {
    if (!equipmentTypeKey) return '-'
    const equipmentType = equipmentTypeOptions.value.find((item) => item.key === equipmentTypeKey)
    return equipmentType ? equipmentType.label : equipmentTypeKey
  }

  // 监听查询条件变化，自动触发查询
  watch(
    [
      () => projectBuildingSelection.value,
      () => filterForm.equipmentType,
      () => filterForm.timeGranularity,
      () => filterForm.dateRange,
      () => filterForm.yearValue
    ],
    (newVal, oldVal) => {
      // 只有在初始化完成后才自动触发查询
      if (!isInitialized.value) {
        return
      }

      // 当查询条件发生变化时，自动触发查询
      if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
        console.log('查询条件变化，自动触发查询:', {
          项目选择: projectBuildingSelection.value,
          设备类型: filterForm.equipmentType,
          时间粒度: filterForm.timeGranularity,
          日期范围: filterForm.dateRange,
          年份值: filterForm.yearValue
        })

        // 清空上次查询参数，允许重新查询
        lastQueryParams.value = null
        queryButtonDisabled.value = false

        // 如果有项目选择，自动触发查询
        if (projectBuildingSelection.value.projectId) {
          nextTick(() => {
            fetchEnergyData()
          })
        }
      }
    },
    { deep: true }
  )

  onMounted(() => {
    // 获取设备类型选项
    fetchEquipmentTypeOptions()

    // 页面加载完成后，如果有默认日期，等待项目列表加载完成后自动查询
    console.log('页面初始化完成，默认日期范围:', filterForm.dateRange)
  })
</script>

<style scoped>
  .query-container {
    padding: 20px;
    background-color: #f0f2f5;
  }

  .filter-card {
    margin-bottom: 20px;
    border-radius: 4px;
  }

  .table-card {
    min-height: 400px;
    border-radius: 4px;
  }

  /* 确保ArtTable组件有足够的显示空间 */
  .table-card :deep(.art-table) {
    height: auto;
    min-height: 400px;
  }

  .table-actions {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 15px;
  }

  /* 筛选容器布局 - 参考可再生能源管理页面 */
  .filter-container {
    padding: 16px;
    margin-bottom: 0;

    .filter-row {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      align-items: center;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .filter-item {
      display: flex;
      align-items: center;
      margin-right: 0;

      .filter-label {
        margin-right: 8px;
        font-size: 14px;
        color: var(--el-text-color-regular);
        white-space: nowrap;
      }

      .filter-select {
        width: 200px;
      }

      .time-granularity-buttons {
        margin-left: 8px;
      }
    }

    .date-filter {
      .date-picker-group {
        display: flex;

        .date-picker {
          width: 100%;
          max-width: 300px;
        }
      }
    }

    .filter-actions {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      margin-left: auto;
    }
  }

  /* 修复el-table__empty-block高度不断增长问题 */
  .query-table :deep(.el-table__empty-block) {
    height: auto !important;
    min-height: 60px;
    max-height: 400px;
    overflow: hidden;
  }

  /* 确保空表格状态下的布局稳定 */
  .query-table :deep(.el-table__body-wrapper) {
    height: auto !important;
    min-height: 200px;
  }

  .el-button .el-icon + span {
    margin-left: 5px;
  }

  /* 响应式布局 */
  @media (width <= 1200px) {
    .filter-container .filter-row {
      gap: 12px;
    }

    .filter-item .filter-select {
      width: 180px;
    }

    .date-filter .date-picker-group .date-picker {
      max-width: 250px;
    }
  }

  @media (width <= 768px) {
    .filter-container .filter-row {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      .filter-item {
        justify-content: space-between;
        margin-right: 0;

        .filter-label {
          margin-right: 12px;
        }

        .filter-select {
          width: 100%;
          max-width: 300px;
        }

        .time-granularity-buttons {
          width: 100%;
          margin-left: 0;
        }
      }

      .date-filter {
        .date-picker-group .date-picker {
          width: 100%;
          max-width: none;
        }
      }

      .filter-actions {
        justify-content: center;
        margin-top: 10px;
        margin-left: 0;
      }
    }
  }

  @media (width <= 480px) {
    .filter-container {
      padding: 12px;
    }

    .filter-actions {
      flex-direction: column;
      gap: 8px;
      width: 100%;

      .el-button {
        width: 100%;
      }
    }
  }
</style>
