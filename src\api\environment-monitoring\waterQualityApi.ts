import api from '@/utils/http'

// 水质监测相关接口

/**
 * 设备列表查询参数
 */
export interface EquipmentListParams {
  projectId: number | string
  buildingId?: number | string
  floorId?: number | string
  equipmentCategory?: number | string
  equipmentCategorytype?: number | string
}

/**
 * 设备信息
 */
export interface EquipmentInfo {
  id: number
  name: string
  code: string
  equipmentType: string
  // 其他设备字段...
}

/**
 * 设备列表响应
 */
export interface EquipmentListResponse {
  msg: string
  code: number
  data: EquipmentInfo[]
}

/**
 * 水质监测数据
 */
export interface WaterQualityData {
  createBy: string
  createTime: string
  updateBy: string
  updateTime: string | null
  remark: string | null
  id: number
  projectId: number
  buildingId: number
  equipmentId: number
  sw: number // 水温
  cod: number // COD
  rjy: number // 溶解氧
  adlz: number // 氨氮离子
  zd: number // 浊度
  ph: number // pH值
  reportTime: number
  reportTimeList: any | null
  hourTime: any | null
  timeWhere: any | null
}

/**
 * 水质监测数据响应
 */
export interface WaterQualityResponse {
  msg: string
  code: number
  data: WaterQualityData
}

/**
 * 趋势数据项
 */
export interface TrendDataItem {
  sw: number // 水温
  ph: number // pH值
  cod: number // COD
  rjy: number // 溶解氧
  adlz: number // 氨氮离子
  zd: number // 浊度
}

/**
 * 趋势数据
 */
export interface TrendData {
  timeList: string[]
  dataList: TrendDataItem[]
}

/**
 * 趋势数据响应
 */
export interface TrendDataResponse {
  msg: string
  code: number
  data: TrendData
}

/**
 * 历史数据查询参数
 */
export interface HistoryDataParams {
  projectId: number | string
  equipmentId?: number | string
  beginTime?: string
  endTime?: string
  pageSize: number
  pageNum: number
}

/**
 * 历史数据响应
 */
export interface HistoryDataResponse {
  total: number
  rows: WaterQualityData[]
  code: number
  msg: string
}

/**
 * 获取设备列表
 * @param params 查询参数
 * @returns 设备列表
 */
export function getEquipmentList(params: EquipmentListParams): Promise<EquipmentListResponse> {
  return api.get({
    url: '/search/getEquipmentList',
    params
  })
}

/**
 * 获取水质监测数据
 * @param params 查询参数
 * @returns 水质监测数据
 */
export function getWaterQualityMonitoring(params: {
  projectId: number | string
  equipmentId?: number | string
}): Promise<WaterQualityResponse> {
  return api.get({
    url: '/envmonitor/water/getWaterQualityMonitoring',
    params
  })
}

/**
 * 获取水质趋势数据
 * @param params 查询参数
 * @returns 趋势数据
 */
export function getWaterQualityTrend(params: {
  projectId: number | string
  equipmentId?: number | string
  timeType: number // 0：日，1：周，2：月
}): Promise<TrendDataResponse> {
  return api.get({
    url: '/envmonitor/water/getTemperatureAndTemperatureTrend',
    params
  })
}

/**
 * 获取水质历史数据
 * @param params 查询参数
 * @returns 历史数据
 */
export function getWaterQualityHistory(params: HistoryDataParams): Promise<HistoryDataResponse> {
  return api.get({
    url: '/envmonitor/water/getHistoryList',
    params
  })
}

/**
 * 水质导出参数
 */
export interface WaterExportParams {
  projectId: number | string
  equipmentId?: number | string
  timeType?: number // 时间类型 0：日，1：周，2：月
  beginTime?: string
  endTime?: string
}

/**
 * 导出水质监测数据
 * @param params 导出参数
 * @returns 文件流
 */
export function exportWaterQualityData(params: WaterExportParams): Promise<Blob> {
  return api.post({
    url: '/envmonitor/water/export',
    data: params,
    responseType: 'blob'
  })
}
