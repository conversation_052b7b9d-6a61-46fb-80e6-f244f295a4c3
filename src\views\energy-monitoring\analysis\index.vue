<template>
  <div class="energy-analysis page-content">
    <!-- 头部筛选区域 -->
    <div class="filter-container">
      <div class="filter-row">
        <!-- 项目和楼栋选择器 -->
        <ProjectBuildingSelector
          ref="projectBuildingSelectorRef"
          v-model="projectBuildingSelection"
          @change="handleProjectBuildingChange"
          @project-loaded="handleProjectListLoaded"
        />
        <div class="filter-item">
          <span class="filter-label">时间</span>
          <el-radio-group
            v-model="queryParams.timeGranularity"
            class="time-granularity-buttons"
            @change="handleTimeGranularityChange"
          >
            <el-radio-button
              v-for="item in timeGranularityOptions"
              :key="item.value"
              :label="item.value"
            >
              {{ item.label }}
            </el-radio-button>
          </el-radio-group>
        </div>
        <div class="filter-item date-filter">
          <span class="filter-label">日期选择：</span>
          <div class="date-picker-group">
            <!-- 年份范围选择器 -->
            <el-date-picker
              v-if="isYearMode"
              v-model="queryParams.yearRange"
              type="yearrange"
              range-separator="至"
              start-placeholder="开始年份"
              end-placeholder="结束年份"
              value-format="YYYY"
              :disabled-date="disabledDate"
              class="date-picker"
              @change="handleYearRangeChange"
            />
            <!-- 日期/月份范围选择器 -->
            <el-date-picker
              v-else
              v-model="queryParams.dateRange"
              :type="datePickerType"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :shortcuts="dateShortcuts"
              :disabled-date="disabledDate"
              class="date-picker"
              @change="handleDateRangeChange"
            />
          </div>
        </div>
        <div class="filter-actions">
          <el-button
            type="primary"
            @click="handleSearch"
            :loading="loading"
            :disabled="queryButtonDisabled"
          >
            查询
          </el-button>
          <el-button @click="resetForm" :disabled="resetButtonDisabled">重置</el-button>
        </div>
      </div>
    </div>

    <el-row :gutter="24">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>能耗类型分类</span>
            </div>
          </template>
          <energy-distribution-pie-chart
            class="chart-container"
            :inner-data="innerPieData"
            :outer-data="outerPieData"
          />
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>用电分项</span>
            </div>
          </template>
          <energy-trend-chart
            class="chart-container"
            :energy-data="energyTypeData"
            :time-type="queryParams.timeGranularity"
          />
        </el-card>
      </el-col>
    </el-row>

    <el-card class="analysis-card">
      <template #header>
        <div class="card-header">
          <span>能耗详细数据</span>
        </div>
      </template>

      <monthly-trend-line-chart
        class="chart-container"
        :x-axis-data="months"
        :series-data="lineChartData"
      />
    </el-card>
  </div>
</template>

<script setup>
  import { ref, computed, reactive, onMounted, watch } from 'vue'
  import { ElMessage } from 'element-plus'

  import EnergyDistributionPieChart from '@/components/echarts/EnergyAnalysis/EnergyDistributionPieChart.vue'
  import MonthlyTrendLineChart from '@/components/echarts/EnergyAnalysis/MonthlyTrendLineChart.vue'
  import EnergyTrendChart from '@/components/echarts/EnergyAnalysis/EnergyTrendChart.vue'
  import ProjectBuildingSelector from '@/components/ProjectBuildingSelector.vue'
  import { getEnergyAnalysis } from '@/api/energy-monitoring/analysisApi'
  import { nextTick } from 'vue'

  // 查询参数
  const queryParams = reactive({
    timeGranularity: 'day', // 默认按日查询
    dateRange: '',
    yearRange: [] // 年份范围选择值
  })

  // 项目和楼栋选择器相关数据
  const projectBuildingSelectorRef = ref()
  const projectBuildingSelection = ref({
    projectId: null,
    buildingId: null
  })
  const selectedProjectInfo = ref(null)
  const selectedBuildingInfo = ref(null)

  // API返回的数据
  const apiData = ref(null)

  // 界面状态变量
  const loading = ref(false)

  // 查询状态管理
  const lastQueryParams = ref(null)
  const queryButtonDisabled = ref(false)
  const resetButtonDisabled = ref(true) // 初始状态下重置按钮禁用

  // 比较查询参数是否相同
  const isSameQueryParams = (params1, params2) => {
    if (!params1 || !params2) return false
    return (
      params1.projectId === params2.projectId &&
      params1.buildingId === params2.buildingId &&
      params1.beginTime === params2.beginTime &&
      params1.endTime === params2.endTime &&
      params1.timeType === params2.timeType
    )
  }

  // 获取当前查询参数
  const getCurrentQueryParams = () => {
    let beginTime, endTime

    if (
      queryParams.timeGranularity === 'year' &&
      queryParams.yearRange &&
      queryParams.yearRange.length === 2
    ) {
      // 年份范围选择：如果开始和结束年份相同，只发送一个年份值
      if (queryParams.yearRange[0] === queryParams.yearRange[1]) {
        // 相同年份时，只发送一个值
        beginTime = queryParams.yearRange[0]
        endTime = queryParams.yearRange[0]
      } else {
        // 不同年份时，发送范围
        beginTime = queryParams.yearRange[0]
        endTime = queryParams.yearRange[1]
      }
    } else if (queryParams.dateRange && queryParams.dateRange.length === 2) {
      // 日期或月份选择：根据时间粒度格式化
      beginTime = formatDate(queryParams.dateRange[0], queryParams.timeGranularity)
      endTime = formatDate(queryParams.dateRange[1], queryParams.timeGranularity)
    }

    return {
      projectId: projectBuildingSelection.value.projectId,
      buildingId: projectBuildingSelection.value.buildingId || undefined,
      beginTime,
      endTime,
      timeType: getTimeType(queryParams.timeGranularity)
    }
  }

  // 时间粒度选项
  const timeGranularityOptions = [
    { value: 'day', label: '日' },
    { value: 'month', label: '月' },
    { value: 'year', label: '年' }
  ]

  // 时间类型映射 (API需要的timeType参数)
  const getTimeType = (timeGranularity) => {
    const timeTypeMap = {
      day: 0,
      month: 1,
      year: 2
    }
    return timeTypeMap[timeGranularity] || 0
  }

  // 根据选择的时间粒度动态设置日期选择器类型
  const datePickerType = computed(() => {
    switch (queryParams.timeGranularity) {
      case 'year':
        return 'year'
      case 'month':
        return 'monthrange'
      case 'day':
        return 'daterange'
      default:
        return 'daterange'
    }
  })

  // 判断是否为年份选择模式
  const isYearMode = computed(() => queryParams.timeGranularity === 'year')

  // 禁用日期选择（不能选择未来日期）
  const disabledDate = (time) => {
    return time.getTime() > Date.now()
  }

  // 日期快捷选项
  const dateShortcuts = [
    {
      text: '最近一周',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
        return [start, end]
      }
    },
    {
      text: '最近一个月',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        return [start, end]
      }
    },
    {
      text: '最近三个月',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
        return [start, end]
      }
    }
  ]

  // 格式化日期的辅助函数
  const formatDate = (date, timeGranularity = 'day') => {
    if (!date) return ''

    const d = new Date(date)

    // 修复时区问题：使用本地时间而不是UTC时间
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')

    switch (timeGranularity) {
      case 'year':
        return String(year)
      case 'month':
        return `${year}-${month}`
      case 'day':
        return `${year}-${month}-${day}`
      default:
        return `${year}-${month}-${day}`
    }
  }

  // 能源分项数据（专门为能源趋势图提供）
  const energyTypeData = computed(() => {
    if (!apiData.value?.energyItemList) return []
    return apiData.value.energyItemList.map((item) => ({
      type: item.name,
      value: item.value.toLocaleString(),
      unit: getElectricityUnit(), // 用电分项使用 kWh 单位
      cost: (item.value * 0.8).toFixed(2),
      percentage: 0 // 可以根据需要计算百分比
    }))
  })

  // 根据数据类型获取对应单位
  const getUnitByName = () => {
    // 能耗类型分类统一使用 用电量（标煤） 单位
    return '用电量（标煤）'
  }

  // 用电分项单位
  const getElectricityUnit = () => {
    // 用电分项统一使用 kWh 单位
    return 'kWh'
  }

  // 饼图数据 - 使用API返回的能耗类型数据
  const innerPieData = computed(() => {
    // 内圈显示用电总量和光伏发电数据
    if (!apiData.value?.energyTypeList) {
      return [
        { name: '用电总量', value: 0, unit: '用电量（标煤）', transparent: true },
        { name: '光伏发电', value: 0, unit: '用电量（标煤）' }
      ]
    }

    // 计算用电总量
    const totalConsumption = apiData.value.energyTypeList.reduce((sum, item) => {
      return sum + (parseFloat(item.value) || 0)
    }, 0)

    // 从能耗类型列表中查找光伏相关数据
    const photovoltaicItem = apiData.value.energyTypeList.find(
      (item) =>
        item.name.includes('光伏') ||
        item.name.includes('太阳能') ||
        item.name.includes('PV') ||
        item.name.toLowerCase().includes('photovoltaic')
    )

    const photovoltaicValue = photovoltaicItem ? photovoltaicItem.value : 0
    const photovoltaicName = photovoltaicItem ? photovoltaicItem.name : '光伏发电'

    return [
      { name: '用电总量', value: totalConsumption, unit: '用电量（标煤）', transparent: true },
      { name: photovoltaicName, value: photovoltaicValue, unit: '用电量（标煤）' }
    ]
  })

  const outerPieData = computed(() => {
    if (!apiData.value?.energyTypeList) return []

    // 过滤掉光伏相关数据，只显示其他能耗类型
    return apiData.value.energyTypeList
      .filter(
        (item) =>
          !item.name.includes('光伏') &&
          !item.name.includes('太阳能') &&
          !item.name.includes('PV') &&
          !item.name.toLowerCase().includes('photovoltaic')
      )
      .map((item) => ({
        name: item.name,
        value: item.value,
        unit: getUnitByName() // 统一使用 用电量（标煤） 单位
      }))
  })

  // 折线图数据 - 使用API返回的趋势数据
  const lineChartData = computed(() => {
    if (!apiData.value?.energyLineDataList) return []
    return apiData.value.energyLineDataList.map((item) => ({
      name: item.name,
      data: item.dataList
    }))
  })

  // 时间轴数据
  const months = computed(() => {
    if (!apiData.value?.timeList) return []
    return apiData.value.timeList
  })

  // 处理项目楼栋选择变化
  const handleProjectBuildingChange = (data) => {
    selectedProjectInfo.value = data.projectInfo
    selectedBuildingInfo.value = data.buildingInfo
    console.log('项目楼栋选择变化:', data)
    // 自动触发查询
    autoQuery()
  }

  // 监听项目选择器组件的项目列表加载完成
  const handleProjectListLoaded = (projectList) => {
    // 当项目列表加载完成后，自动选择第一个项目并发请求
    if (projectList && projectList.length > 0) {
      const firstProject = projectList[0]
      projectBuildingSelection.value.projectId = firstProject.id
      selectedProjectInfo.value = firstProject
      console.log('自动选择第一个项目:', firstProject)
      // 自动发请求获取数据
      nextTick(() => {
        handleSearch()
      })
    }
  }

  // 自动查询函数
  const autoQuery = () => {
    // 清空查询参数缓存，允许重新查询
    lastQueryParams.value = null
    queryButtonDisabled.value = false
    resetButtonDisabled.value = false

    // 延迟执行查询，避免频繁触发
    nextTick(() => {
      handleSearch()
    })
  }

  // 处理时间粒度变化
  const handleTimeGranularityChange = (value) => {
    console.log('时间粒度变化:', value)

    if (value === 'year') {
      // 年份范围选择：设置默认为当前年份
      const currentYear = new Date().getFullYear().toString()
      queryParams.yearRange = [currentYear, currentYear]
      queryParams.dateRange = '' // 清空日期范围
    } else {
      // 日期或月份选择：根据时间粒度重置日期范围
      const end = new Date()
      const start = new Date()

      if (value === 'day') {
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7) // 最近一周
      } else if (value === 'month') {
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30) // 最近一个月
      }

      queryParams.dateRange = [start, end]
      queryParams.yearRange = [] // 清空年份范围
    }

    // 自动触发查询
    autoQuery()
  }

  // 处理日期范围变化
  const handleDateRangeChange = (value) => {
    console.log('日期范围变化:', value)
    if (value && value.length === 2) {
      // 自动触发查询
      autoQuery()
    }
  }

  // 处理年份范围变化
  const handleYearRangeChange = (value) => {
    console.log('年份范围变化:', value)
    if (value && value.length === 2) {
      // 自动触发查询
      autoQuery()
    }
  }

  // 数据查询方法
  const handleSearch = async () => {
    // 如果没有选择项目，不发请求
    if (!projectBuildingSelection.value.projectId) {
      console.log('没有选择项目，不发请求')
      return
    }

    // 检查时间选择
    if (queryParams.timeGranularity === 'year') {
      if (!queryParams.yearRange || queryParams.yearRange.length !== 2) {
        ElMessage.warning('请选择年份范围')
        return
      }
    } else {
      if (!queryParams.dateRange || queryParams.dateRange.length !== 2) {
        ElMessage.warning('请选择日期范围')
        return
      }
    }

    // 获取当前查询参数
    const currentParams = getCurrentQueryParams()

    // 检查查询参数是否与上次相同（仅在手动点击查询按钮时检查）
    if (queryButtonDisabled.value && isSameQueryParams(currentParams, lastQueryParams.value)) {
      ElMessage.info('查询条件未变化')
      return
    }

    loading.value = true

    try {
      console.log('查询参数:', currentParams)

      // 调用API
      const response = await getEnergyAnalysis(currentParams)

      if (response.code === 200) {
        apiData.value = response.data

        // 保存当前查询参数
        lastQueryParams.value = { ...currentParams }

        // 禁用查询按钮，启用重置按钮
        queryButtonDisabled.value = true
        resetButtonDisabled.value = false

        console.log('查询成功，数据已更新')
      } else {
        ElMessage.error(response.msg || '查询失败')
      }
    } catch (error) {
      console.error('查询失败:', error)
      ElMessage.error('查询失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  // 重置筛选条件
  const resetForm = () => {
    // 如果重置按钮已禁用，不执行重置
    if (resetButtonDisabled.value) {
      ElMessage.info('没有需要重置的内容')
      return
    }

    // 重置项目楼栋选择
    if (projectBuildingSelectorRef.value) {
      projectBuildingSelectorRef.value.reset()
    }

    // 重置查询参数
    queryParams.timeGranularity = 'day'
    queryParams.dateRange = ''
    queryParams.yearRange = []

    // 设置默认时间范围为最近一周
    const end = new Date()
    const start = new Date()
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
    queryParams.dateRange = [start, end]

    // 清空查询参数缓存
    lastQueryParams.value = null

    // 启用查询按钮，禁用重置按钮
    queryButtonDisabled.value = false
    resetButtonDisabled.value = true

    // 清空API数据
    apiData.value = null

    ElMessage.success('已重置查询条件')
  }

  // 监听年份范围选择变化，自动触发查询
  watch(
    () => queryParams.yearRange,
    (newVal, oldVal) => {
      if (JSON.stringify(newVal) !== JSON.stringify(oldVal) && newVal && newVal.length === 2) {
        console.log('年份范围选择变化:', newVal)
        // 年份范围变化时自动触发查询
        autoQuery()
      }
    },
    { deep: true }
  )

  // 组件挂载时初始化数据
  onMounted(() => {
    // 设置默认时间范围为最近一周
    const end = new Date()
    const start = new Date()
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
    queryParams.dateRange = [start, end]
  })
</script>

<style lang="scss" scoped>
  .energy-analysis {
    min-height: calc(100vh - 60px);
    padding: 16px;

    .filter-container {
      padding: 16px;
      margin-bottom: 16px;
      background-color: var(--el-bg-color);
      border-radius: 8px;
      box-shadow: var(--el-box-shadow-light);

      .filter-row {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        align-items: center;
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .filter-item {
        display: flex;
        align-items: center;
        margin-right: 0;

        .filter-label {
          margin-right: 8px;
          font-size: 14px;
          color: var(--el-text-color-regular);
          white-space: nowrap;
        }

        .filter-select {
          width: 200px;
        }

        .time-granularity-buttons {
          margin-left: 8px;
        }
      }

      .date-filter {
        .date-picker-group {
          display: flex;

          .date-picker {
            width: 100%;
            max-width: 300px;
          }
        }
      }

      .filter-actions {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
        margin-left: auto;
      }
    }

    .chart-card {
      margin-bottom: 24px;
      background-color: var(--el-bg-color);
      border-color: var(--el-border-color-light);

      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: var(--el-text-color-primary);
      }

      .chart-container {
        width: 100%;
        height: 400px;
      }
    }

    .analysis-card {
      margin-bottom: 24px;
      background-color: var(--el-bg-color);
      border-color: var(--el-border-color-light);

      :deep(.el-progress-bar__outer) {
        background-color: var(--el-fill-color-lighter);
      }

      :deep(.el-table) {
        margin-top: 10px;

        --el-table-border-color: var(--el-border-color-lighter);
        --el-table-header-bg-color: var(--el-fill-color-light);
        --el-table-row-hover-bg-color: var(--el-fill-color);
      }

      .chart-container {
        width: 100%;
        height: 400px;
      }
    }
  }
</style>
