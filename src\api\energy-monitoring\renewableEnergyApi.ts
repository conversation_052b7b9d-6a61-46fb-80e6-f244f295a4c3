import request from '@/utils/http'

// 可再生能源监测相关接口

/**
 * 可再生能源监测数据查询参数
 */
export interface RenewableEnergyDataParams {
  projectId?: string | number
}

/**
 * 可再生能源监测数据
 */
export interface RenewableEnergyData {
  createBy: string
  createTime: string
  updateBy: string
  updateTime: string
  remark: string | null
  id: number
  projectId: number
  tyngf: string // 太阳能光伏装机容量
  tynrsjrq: string // 太阳能热水集热器面积
  dyrb: string // 地源热泵制冷/热量
  kqyrb: string // 空气源热泵制热量
  status: string | null
  delFlag: string
}

/**
 * 可再生能源监测数据响应
 */
export interface RenewableEnergyDataResponse {
  msg: string
  code: number
  data: RenewableEnergyData
}

/**
 * 光伏发电量统计查询参数
 */
export interface PhotovoltaicDataParams {
  projectId: string | number
  beginTime: string
  endTime: string
  timeType: number // 0-日 1-月 2-年
}

/**
 * 光伏发电量统计数据
 */
export interface PhotovoltaicData {
  todayData: number
  monthData: number
  yearData: number
  timeList: string[]
  dataList: number[]
}

/**
 * 光伏发电量统计响应
 */
export interface PhotovoltaicDataResponse {
  msg: string
  code: number
  data: PhotovoltaicData
}

/**
 * 获取可再生能源监测数据
 */
export function getRenewableEnergyData(
  params: RenewableEnergyDataParams
): Promise<RenewableEnergyDataResponse> {
  return request.get({
    url: '/energy/energyconsume/renewableEnergyData',
    params
  })
}

/**
 * 获取光伏发电量统计数据
 */
export function getPhotovoltaicData(
  params: PhotovoltaicDataParams
): Promise<PhotovoltaicDataResponse> {
  return request.get({
    url: '/energy/energyconsume/getPhotovoltaicData',
    params
  })
}
