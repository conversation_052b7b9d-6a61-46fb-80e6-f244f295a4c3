<template>
  <div class="chart-container" ref="chartContainer"></div>
</template>

<script setup>
  import { ref, onMounted, watch, inject } from 'vue'
  import * as echarts from 'echarts'

  // 接收父组件传递的时间范围参数
  const props = defineProps({
    timeRange: {
      type: String,
      default: 'day'
    }
  })

  // 注入数据
  const waterQualityData = inject('waterQualityData')
  const codTrendData = inject('codTrendData')

  // 图表容器引用
  const chartContainer = ref(null)
  let chart = null

  // 处理真实API数据
  const processApiData = () => {
    if (!codTrendData.value || !codTrendData.value.timeList || !codTrendData.value.dataList) {
      return {
        xAxis: [],
        cod: [],
        ammonia: []
      }
    }

    const codData = []
    const ammoniaData = []
    const xAxisData = codTrendData.value.timeList

    // 从API数据中提取COD和氨氮数据
    codTrendData.value.dataList.forEach((item) => {
      codData.push(item.cod || 0)
      ammoniaData.push(item.adlz || 0) // adlz是氨氮离子
    })

    return {
      xAxis: xAxisData,
      cod: codData,
      ammonia: ammoniaData
    }
  }

  // 生成示例数据（作为备用）
  const generateChartData = (range) => {
    // 如果有真实数据，优先使用真实数据
    if (
      codTrendData.value &&
      codTrendData.value.timeList &&
      codTrendData.value.timeList.length > 0
    ) {
      return processApiData()
    }

    // 否则使用模拟数据
    const codData = []
    const ammoniaData = []
    const xAxisData = []

    const codBase = parseInt(waterQualityData.find((item) => item.name === 'COD').value)
    const ammoniaBase = parseFloat(waterQualityData.find((item) => item.name === '氨氮离子').value)

    let pointCount = 24 // 默认值

    if (range === 'day') {
      // 生成24小时数据
      pointCount = 24

      for (let i = 0; i < pointCount; i++) {
        const hour = i
        xAxisData.push(`${hour}:00`)

        // COD小时波动（考虑工业生产和日常活动）
        const timeEffect = Math.sin((i / 24) * Math.PI * 2) * 0.15 + 1
        // 假设工作时间(8-18点)排放较高
        const workHourFactor = i >= 8 && i <= 18 ? 1.15 : 0.9
        const cod = Math.floor(codBase * timeEffect * workHourFactor * (0.9 + Math.random() * 0.2))
        codData.push(cod)

        // 氨氮小时波动
        const ammoniaHourFactor = 0.95 + Math.random() * 0.1
        // 生活用水高峰期排放高（早晨7-9点，晚上18-22点）
        const peakHourFactor = (i >= 7 && i <= 9) || (i >= 18 && i <= 22) ? 1.2 : 0.95
        const ammonia = (ammoniaBase * ammoniaHourFactor * peakHourFactor).toFixed(2)
        ammoniaData.push(ammonia)
      }
    } else if (range === 'week') {
      // 生成7天数据（周一到周日）
      pointCount = 7
      const weekDays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']

      weekDays.forEach((day, index) => {
        xAxisData.push(day)

        // COD日波动（工作日排放高）
        const isWeekend = index >= 5 // 周六日
        const weekdayFactor = isWeekend ? 0.7 : 1.2
        const codRandomFactor = 0.85 + Math.random() * 0.3
        const cod = Math.floor(codBase * weekdayFactor * codRandomFactor)
        codData.push(cod)

        // 氨氮日波动
        const ammoniaRandomFactor = 0.9 + Math.random() * 0.2
        // 周末生活废水可能增加
        const weekendLifeFactor = isWeekend ? 1.1 : 0.95
        const ammonia = (ammoniaBase * ammoniaRandomFactor * weekendLifeFactor).toFixed(2)
        ammoniaData.push(ammonia)
      })
    } else if (range === 'month') {
      // 生成30天数据
      pointCount = 30
      const today = new Date()

      for (let i = 0; i < pointCount; i++) {
        const date = new Date(today)
        date.setDate(today.getDate() - (pointCount - 1) + i)
        xAxisData.push(`${date.getMonth() + 1}-${date.getDate()}`)

        // COD日波动
        const codRandomFactor = 0.8 + Math.random() * 0.4
        // 工作日排放较高（周一到周五）
        const dayOfWeek = date.getDay()
        const workdayFactor = dayOfWeek >= 1 && dayOfWeek <= 5 ? 1.2 : 0.8
        const cod = Math.floor(codBase * codRandomFactor * workdayFactor)
        codData.push(cod)

        // 氨氮日波动
        const ammoniaRandomFactor = 0.85 + Math.random() * 0.3
        // 随机模拟降水影响
        const rainEffect = Math.random() > 0.8 ? 0.7 : 1
        const ammonia = (ammoniaBase * ammoniaRandomFactor * rainEffect).toFixed(2)
        ammoniaData.push(ammonia)
      }
    }

    return {
      xAxis: xAxisData,
      cod: codData,
      ammonia: ammoniaData
    }
  }

  // 初始化图表
  const initChart = () => {
    if (chart) {
      chart.dispose()
    }

    // 创建图表实例
    chart = echarts.init(chartContainer.value)

    // 更新图表数据
    updateChart()

    // 响应窗口大小变化
    window.addEventListener('resize', () => {
      chart.resize()
    })
  }

  // 计算Y轴范围
  const calculateYAxisRange = (data, padding = 0.1) => {
    if (!data || data.length === 0) return { min: 0, max: 10 }

    const values = data.map((v) => parseFloat(v)).filter((v) => !isNaN(v))
    if (values.length === 0) return { min: 0, max: 10 }

    const min = Math.min(...values)
    const max = Math.max(...values)
    const range = max - min
    const paddingValue = range * padding

    return {
      min: Math.max(0, min - paddingValue),
      max: max + paddingValue
    }
  }

  // 更新图表数据
  const updateChart = () => {
    const data = generateChartData(props.timeRange)

    // 动态计算Y轴范围
    const codRange = calculateYAxisRange(data.cod)
    const ammoniaRange = calculateYAxisRange(data.ammonia)

    // 设置图表配置
    const option = {
      tooltip: {
        trigger: 'axis',
        formatter: function (params) {
          return params
            .map((param) => {
              return `${param.seriesName}: ${param.value} mg/l`
            })
            .join('<br>')
        }
      },
      legend: {
        data: ['COD', '氨氮'],
        bottom: '0%'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: true,
        data: data.xAxis
      },
      yAxis: [
        {
          type: 'value',
          name: 'COD(mg/l)',
          min: codRange.min,
          max: codRange.max,
          axisLabel: {
            formatter: '{value}'
          }
        },
        {
          type: 'value',
          name: '氨氮(mg/l)',
          min: ammoniaRange.min,
          max: ammoniaRange.max,
          axisLabel: {
            formatter: '{value}'
          }
        }
      ],
      series: [
        {
          name: 'COD',
          type: 'bar',
          data: data.cod,
          barWidth: '60%',
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#e67e22' },
              { offset: 1, color: '#f39c12' }
            ])
          }
        },
        {
          name: '氨氮',
          type: 'line',
          smooth: true,
          yAxisIndex: 1,
          data: data.ammonia,
          lineStyle: {
            width: 3,
            color: '#9b59b6'
          },
          itemStyle: {
            color: '#9b59b6'
          }
        }
      ]
    }

    // 应用配置
    chart.setOption(option)
  }

  // 监听时间范围变化
  watch(
    () => props.timeRange,
    () => {
      updateChart()
    }
  )

  // 监听趋势数据变化
  watch(
    () => codTrendData.value,
    () => {
      if (chart) {
        updateChart()
      }
    },
    { deep: true }
  )

  // 组件挂载时初始化图表
  onMounted(() => {
    initChart()
  })
</script>

<style lang="scss" scoped>
  .chart-container {
    width: 100%;
    height: 350px;
  }
</style>
