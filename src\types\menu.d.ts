export type MenuListType = {
  id?: number
  path: string // 路由
  name: string // 组件名
  component?: string // 改为字符串类型，表示组件路径
  hidden?: boolean // 是否隐藏
  redirect?: string // 重定向
  alwaysShow?: boolean // 是否总是显示
  meta: {
    title: string // 菜单名称
    icon?: string | null // 菜单图标，允许null
    showBadge?: boolean // 是否显示徽标
    showTextBadge?: string // 是否显示新徽标
    isHide?: boolean // 是否在菜单中隐藏
    isHideTab?: boolean // 是否在标签页中隐藏
    link?: string | null // 链接，允许null
    isIframe?: boolean // 是否是 iframe
    keepAlive: boolean // 是否缓存
    noCache?: boolean // 是否不缓存
    authList?: Array<any> // 可操作权限
    inMainContainer?: boolean // 是否在主容器中
  }
  children?: MenuListType[] // 子菜单
}
