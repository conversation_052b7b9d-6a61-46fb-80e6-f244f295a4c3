<template>
  <div ref="chartRef" class="carbon-chart"></div>
</template>

<script setup>
  import { ref, onMounted, watch, defineProps } from 'vue'
  import * as echarts from 'echarts/core'
  import { LineChart } from 'echarts/charts'
  import {
    TitleComponent,
    TooltipComponent,
    GridComponent,
    LegendComponent
  } from 'echarts/components'
  import { CanvasRenderer } from 'echarts/renderers'

  // 注册必要的组件
  echarts.use([
    TitleComponent,
    TooltipComponent,
    GridComponent,
    LegendComponent,
    LineChart,
    CanvasRenderer
  ])

  const props = defineProps({
    activeScenario: {
      type: Number,
      default: 0
    }
  })

  const chartRef = ref(null)
  let chart = null

  const scenarios = [
    {
      name: '基准情景',
      reduction: '0%',
      color: '#5470c6'
    },
    {
      name: '乐观情景',
      reduction: '30-40%',
      color: '#91cc75'
    },
    {
      name: '保守情景',
      reduction: '10-15%',
      color: '#fac858'
    },
    {
      name: '激进情景',
      reduction: '50-60%',
      color: '#ee6666'
    }
  ]

  // 生成数据
  const generateData = () => {
    const years = Array.from({ length: 11 }, (_, i) => 2025 + i)
    const baselineData = Array.from({ length: 11 }, () => 100)

    // 基于减排比例生成数据
    const optimisticData = baselineData.map((value, index) => {
      const reductionPercent = 35 * (index / 10) // 0% to 35% over time (mid of 30-40%)
      return value * (1 - reductionPercent / 100)
    })

    const conservativeData = baselineData.map((value, index) => {
      const reductionPercent = 12.5 * (index / 10) // 0% to 12.5% over time (mid of 10-15%)
      return value * (1 - reductionPercent / 100)
    })

    const radicalData = baselineData.map((value, index) => {
      const reductionPercent = 55 * (index / 10) // 0% to 55% over time (mid of 50-60%)
      return value * (1 - reductionPercent / 100)
    })

    return {
      years,
      baselineData,
      optimisticData,
      conservativeData,
      radicalData
    }
  }

  const updateChart = () => {
    if (!chart) return

    chart.setOption({
      series: scenarios.map((scenario, index) => ({
        lineStyle: {
          width: props.activeScenario === index ? 4 : 2
        }
      }))
    })
  }

  const initChart = () => {
    if (chart) {
      chart.dispose()
    }

    const { years, baselineData, optimisticData, conservativeData, radicalData } = generateData()

    chart = echarts.init(chartRef.value)
    const option = {
      title: {
        text: '情景对比分析图表',
        subtext: '不同情景下碳排放趋势对比',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: function (params) {
          const year = params[0].axisValue
          let result = `${year}年<br/>`
          params.forEach((param) => {
            result += `${param.seriesName}: ${param.value.toFixed(1)}%<br/>`
          })
          return result
        }
      },
      legend: {
        data: scenarios.map((s) => s.name),
        bottom: 10
      },
      grid: {
        left: '10%',
        right: '4%',
        bottom: '15%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: years,
        name: '年份',
        nameLocation: 'middle',
        nameGap: 30
      },
      yAxis: {
        type: 'value',
        name: '相对排放量(%)',
        nameLocation: 'middle',
        nameGap: 50,
        axisLabel: {
          formatter: '{value}%'
        }
      },
      series: [
        {
          name: scenarios[0].name,
          type: 'line',
          data: baselineData,
          lineStyle: {
            color: scenarios[0].color,
            width: props.activeScenario === 0 ? 4 : 2
          },
          itemStyle: {
            color: scenarios[0].color
          },
          emphasis: {
            lineStyle: {
              width: 4
            }
          }
        },
        {
          name: scenarios[1].name,
          type: 'line',
          data: optimisticData,
          lineStyle: {
            color: scenarios[1].color,
            width: props.activeScenario === 1 ? 4 : 2
          },
          itemStyle: {
            color: scenarios[1].color
          },
          emphasis: {
            lineStyle: {
              width: 4
            }
          }
        },
        {
          name: scenarios[2].name,
          type: 'line',
          data: conservativeData,
          lineStyle: {
            color: scenarios[2].color,
            width: props.activeScenario === 2 ? 4 : 2
          },
          itemStyle: {
            color: scenarios[2].color
          },
          emphasis: {
            lineStyle: {
              width: 4
            }
          }
        },
        {
          name: scenarios[3].name,
          type: 'line',
          data: radicalData,
          lineStyle: {
            color: scenarios[3].color,
            width: props.activeScenario === 3 ? 4 : 2
          },
          itemStyle: {
            color: scenarios[3].color
          },
          emphasis: {
            lineStyle: {
              width: 4
            }
          }
        }
      ]
    }

    chart.setOption(option)
  }

  watch(
    () => props.activeScenario,
    () => {
      updateChart()
    }
  )

  onMounted(() => {
    initChart()
    window.addEventListener('resize', () => {
      chart && chart.resize()
    })
  })
</script>

<style scoped>
  .carbon-chart {
    width: 100%;
    height: 400px;
  }
</style>
