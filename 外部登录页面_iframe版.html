<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>外部登录页面 - 新窗口版</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
            overflow: hidden;
        }

        /* 初始状态容器 */
        .initial-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        /* 跳转按钮样式 */
        .jump-btn {
            padding: 15px 30px;
            font-size: 18px;
            background: #67c23a;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(103, 194, 58, 0.3);
            transition: all 0.3s ease;
        }

        .jump-btn:hover {
            background: #529b2e;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(103, 194, 58, 0.4);
        }

        /* 全屏iframe容器 */
        .fullscreen-container {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: white;
            z-index: 1000;
        }

        .fullscreen-container.active {
            display: block;
        }

        /* 关闭按钮 */
        .close-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            background: #ff4757;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 20px;
            font-weight: bold;
            z-index: 1001;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 10px rgba(255, 71, 87, 0.3);
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: #ff3742;
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(255, 71, 87, 0.4);
        }

        /* iframe样式 */
        .fullscreen-iframe {
            width: 100%;
            height: 100%;
            border: none;
        }

        /* 隐藏原有的容器 */
        .container {
            display: none;
        }
    </style>
</head>
<body>
    <!-- 初始状态：只显示跳转按钮 -->
    <div class="initial-container" id="initialContainer">
        <button class="jump-btn" onclick="openFullscreen()">进入应用</button>
    </div>

    <!-- 全屏iframe容器 -->
    <div class="fullscreen-container" id="fullscreenContainer">
        <button class="close-btn" onclick="closeFullscreen()">×</button>
        <iframe id="mainAppIframe" class="fullscreen-iframe" src="" sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation"></iframe>
    </div>

    <!-- 隐藏的原有容器（保留用于调试） -->
    <div class="container">
        <h2>外部登录页面 - iframe版</h2>

        <div class="controls">
            <button id="autoLoginBtn" class="login-btn" onclick="requestAutoLogin()">请求自动登录</button>
            <span id="loginStatus" class="status"></span>
        </div>

        <div id="debugInfo" class="debug-info">等待操作...</div>
    </div>

    <script>
        // 配置项 - 父组件只处理URL和路径
        const CONFIG = {
            // 登录成功后跳转的路径
            targetPath: '/comprehensive-overview/project-overview',
            // iframe地址
            iframeUrl: 'http://localhost:3006',
            // 是否自动跳转到目标页面
            autoNavigate: true,
            // 自动跳转延时（毫秒）
            autoNavigateDelay: 500,
            // 是否在新窗口打开（true: 新窗口打开, false: iframe内跳转）
            openInNewWindow: false
        };

        let isFullscreen = false;
        let iframeReady = false;

        // 全屏控制函数
        function openFullscreen() {
            const initialContainer = document.getElementById('initialContainer');
            const fullscreenContainer = document.getElementById('fullscreenContainer');
            const iframe = document.getElementById('mainAppIframe');

            // 隐藏初始容器，显示全屏容器
            initialContainer.style.display = 'none';
            fullscreenContainer.classList.add('active');
            isFullscreen = true;

            // 设置iframe地址并等待子组件准备就绪
            iframe.src = CONFIG.iframeUrl;
            debugLog('进入全屏模式，开始加载应用...');

            // 等待iframe加载完成
            iframe.onload = () => {
                debugLog('iframe加载完成，等待子组件准备就绪...');
            };
        }

        function closeFullscreen() {
            const initialContainer = document.getElementById('initialContainer');
            const fullscreenContainer = document.getElementById('fullscreenContainer');
            const iframe = document.getElementById('mainAppIframe');

            // 显示初始容器，隐藏全屏容器
            initialContainer.style.display = 'flex';
            fullscreenContainer.classList.remove('active');
            isFullscreen = false;
            iframeReady = false;

            // 清空iframe
            iframe.src = '';
            debugLog('退出全屏模式');
        }

        function debugLog(message) {
            const debugDiv = document.getElementById('debugInfo');
            const timestamp = new Date().toLocaleTimeString();
            debugDiv.textContent += `[${timestamp}] ${message}\n`;
            debugDiv.scrollTop = debugDiv.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function updateLoginStatus(status, isSuccess = false) {
            const statusEl = document.getElementById('loginStatus');
            statusEl.textContent = status;
            statusEl.className = `status ${isSuccess ? 'success' : 'error'}`;
        }

        // 请求子组件执行自动登录
        function requestAutoLogin() {
            if (!isFullscreen || !iframeReady) {
                debugLog('iframe未准备就绪，无法请求自动登录');
                return;
            }

            try {
                debugLog('请求子组件执行自动登录...');

                const iframe = document.getElementById('mainAppIframe');
                const autoLoginMessage = {
                    type: 'AUTO_LOGIN_REQUEST',
                    data: {
                        targetPath: CONFIG.targetPath,
                        autoNavigate: CONFIG.autoNavigate,
                        timestamp: Date.now()
                    }
                };

                debugLog('发送自动登录请求: ' + JSON.stringify(autoLoginMessage, null, 2));

                iframe.contentWindow.postMessage(autoLoginMessage, CONFIG.iframeUrl);
                updateLoginStatus('已请求子组件自动登录...');

            } catch (error) {
                debugLog('✗ 请求自动登录失败: ' + error.message);
                updateLoginStatus('请求自动登录失败: ' + error.message);
                console.error('请求自动登录失败:', error);
            }
        }



        // 请求导航到指定页面
        function requestNavigation(targetPath) {
            if (!iframeReady) {
                debugLog('✗ 导航失败：iframe未准备就绪');
                return;
            }

            if (CONFIG.openInNewWindow) {
                // 在新窗口打开目标页面
                const targetUrl = CONFIG.iframeUrl + '#' + targetPath;
                debugLog('在新窗口打开目标页面: ' + targetUrl);

                try {
                    window.open(targetUrl, '_blank');
                    updateLoginStatus('已在新窗口打开目标页面', true);
                    debugLog('✓ 新窗口打开成功');
                } catch (error) {
                    debugLog('✗ 新窗口打开失败: ' + error.message);
                    updateLoginStatus('新窗口打开失败: ' + error.message);
                }
            } else {
                // 向iframe发送跳转消息
                const iframe = document.getElementById('mainAppIframe');
                const navigationMessage = {
                    type: 'NAVIGATE_TO_PAGE',
                    data: {
                        path: targetPath,
                        timestamp: Date.now()
                    }
                };

                debugLog('发送导航请求: ' + JSON.stringify(navigationMessage, null, 2));
                iframe.contentWindow.postMessage(navigationMessage, CONFIG.iframeUrl);
                updateLoginStatus('正在导航到目标页面...', true);
            }
        }

        // 监听来自iframe的消息
        window.addEventListener('message', function(event) {
            // 验证消息来源
            if (event.origin !== CONFIG.iframeUrl) {
                return;
            }

            debugLog('收到iframe消息: ' + JSON.stringify(event.data, null, 2));

            const { type, data } = event.data;

            switch (type) {
                case 'IFRAME_READY':
                    debugLog('✓ 子组件已准备就绪');
                    iframeReady = true;
                    updateLoginStatus('子组件已准备就绪');

                    // 子组件准备就绪后，自动请求登录
                    setTimeout(() => {
                        requestAutoLogin();
                    }, 500);
                    break;

                case 'AUTO_LOGIN_SUCCESS':
                    debugLog('✓ 子组件自动登录成功');
                    updateLoginStatus('自动登录成功', true);

                    // 如果配置了自动导航，则导航到目标页面
                    if (CONFIG.autoNavigate && data.targetPath) {
                        debugLog('准备自动导航到目标页面，延时: ' + CONFIG.autoNavigateDelay + 'ms');
                        updateLoginStatus('登录成功，即将自动导航...', true);

                        setTimeout(() => {
                            requestNavigation(data.targetPath);
                        }, CONFIG.autoNavigateDelay);
                    }
                    break;

                case 'AUTO_LOGIN_ERROR':
                    debugLog('✗ 子组件自动登录失败: ' + data.error);
                    updateLoginStatus('自动登录失败: ' + data.error);
                    break;

                case 'NAVIGATION_SUCCESS':
                    debugLog('✓ 页面导航成功');
                    updateLoginStatus('页面导航成功！', true);

                    if (CONFIG.autoNavigate) {
                        debugLog('✓ 自动导航流程完成');
                        setTimeout(() => {
                            updateLoginStatus('自动导航完成，用户已进入目标页面', true);
                        }, 1000);
                    }
                    break;

                case 'NAVIGATION_ERROR':
                    debugLog('✗ 页面导航失败: ' + data.error);
                    updateLoginStatus('页面导航失败: ' + data.error);
                    break;

                default:
                    debugLog('收到未知消息类型: ' + type);
            }
        });

        // 键盘事件监听
        document.addEventListener('keydown', function(event) {
            // ESC键关闭全屏
            if (event.key === 'Escape' && isFullscreen) {
                closeFullscreen();
            }
        });

        // 页面加载完成后的初始化
        window.onload = function() {
            debugLog('=== 外部登录页面初始化完成 ===');
            debugLog('iframe地址: ' + CONFIG.iframeUrl);
            debugLog('目标导航路径: ' + CONFIG.targetPath);
            debugLog('自动导航: ' + (CONFIG.autoNavigate ? '启用 (延时' + CONFIG.autoNavigateDelay + 'ms)' : '禁用'));
            debugLog('打开方式: ' + (CONFIG.openInNewWindow ? '新窗口' : 'iframe内导航'));
            debugLog('等待用户点击进入应用按钮...');
            debugLog('提示：全屏模式下可按ESC键退出');
            debugLog('架构：父组件只负责URL和路径管理，子组件完全负责登录和token管理');
        };

        // 检查iframe加载状态（仅在全屏模式下）
        function checkIframeReady() {
            if (!isFullscreen) return;

            const iframe = document.getElementById('mainAppIframe');
            try {
                // 发送准备检查消息
                iframe.contentWindow.postMessage({
                    type: 'EXTERNAL_PAGE_READY',
                    data: { timestamp: Date.now() }
                }, CONFIG.iframeUrl);
                debugLog('已发送外部页面准备就绪消息');
            } catch (error) {
                debugLog('无法与iframe通信，可能存在跨域限制: ' + error.message);
            }
        }
    </script>
</body>
</html>
