<template>
  <div></div>
</template>

<script>
  import * as echarts from 'echarts'

  export default {
    name: 'EnvironmentGaugeChart',
    methods: {
      /**
       * 初始化环境监测仪表盘
       * @param {HTMLElement} element - DOM元素
       * @param {number} value - 仪表盘值
       * @param {string} name - 状态名称
       * @returns {Object} 图表实例
       */
      initGauge(element, value, name) {
        if (!element) return null

        const chart = echarts.init(element)
        const option = {
          series: [
            {
              type: 'gauge',
              startAngle: 180,
              endAngle: 0,
              min: 0,
              max: 100,
              splitNumber: 3,
              animation: false,
              axisLine: {
                lineStyle: {
                  width: 20,
                  color: [
                    [0.3, '#FF6B6B'],
                    [0.7, '#FFD93D'],
                    [1, '#6BCB77']
                  ]
                }
              },
              pointer: {
                itemStyle: {
                  color: '#303133'
                }
              },
              axisTick: {
                show: false
              },
              splitLine: {
                show: false
              },
              axisLabel: {
                show: false
              },
              detail: {
                valueAnimation: false,
                fontSize: 16,
                offsetCenter: [0, '20%'],
                formatter: '{value}',
                color: '#303133'
              },
              title: {
                offsetCenter: [0, '-20%'],
                fontSize: 14,
                color: '#303133'
              },
              data: [
                {
                  value: value,
                  name: name
                }
              ]
            }
          ]
        }
        chart.setOption(option)

        // 添加窗口大小变化时的自适应
        window.addEventListener('resize', () => {
          chart.resize()
        })

        return chart
      }
    }
  }
</script>
