/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ArtBackToTop: typeof import('./../components/core/base/ArtBackToTop.vue')['default']
    ArtBarChart: typeof import('./../components/core/charts/ArtBarChart.vue')['default']
    ArtBarChartCard: typeof import('./../components/core/cards/ArtBarChartCard.vue')['default']
    ArtBasicBanner: typeof import('./../components/core/banners/ArtBasicBanner.vue')['default']
    ArtBreadcrumb: typeof import('./../components/core/layouts/art-breadcrumb/index.vue')['default']
    ArtButtonMore: typeof import('./../components/core/forms/ArtButtonMore.vue')['default']
    ArtButtonTable: typeof import('./../components/core/forms/ArtButtonTable.vue')['default']
    ArtCardBanner: typeof import('./../components/core/banners/ArtCardBanner.vue')['default']
    ArtChatWindow: typeof import('./../components/core/layouts/art-chat-window/index.vue')['default']
    ArtCutterImg: typeof import('./../components/core/media/ArtCutterImg.vue')['default']
    ArtDataListCard: typeof import('./../components/core/cards/ArtDataListCard.vue')['default']
    ArtDonutChartCard: typeof import('./../components/core/cards/ArtDonutChartCard.vue')['default']
    ArtDragVerify: typeof import('./../components/core/forms/ArtDragVerify.vue')['default']
    ArtDualBarCompareChart: typeof import('./../components/core/charts/ArtDualBarCompareChart.vue')['default']
    ArtExcelExport: typeof import('./../components/core/forms/ArtExcelExport.vue')['default']
    ArtExcelImport: typeof import('./../components/core/forms/ArtExcelImport.vue')['default']
    ArtException: typeof import('./../components/core/views/exception/ArtException.vue')['default']
    ArtFastEnter: typeof import('./../components/core/layouts/art-fast-enter/index.vue')['default']
    ArtFestivalTextScroll: typeof import('./../components/core/text-effect/ArtFestivalTextScroll.vue')['default']
    ArtFireworksEffect: typeof import('./../components/core/layouts/art-fireworks-effect/index.vue')['default']
    ArtGlobalSearch: typeof import('./../components/core/layouts/art-global-search/index.vue')['default']
    ArtHBarChart: typeof import('./../components/core/charts/ArtHBarChart.vue')['default']
    ArtHeaderBar: typeof import('./../components/core/layouts/art-header-bar/index.vue')['default']
    ArtHorizontalMenu: typeof import('./../components/core/layouts/art-menus/art-horizontal-menu/index.vue')['default']
    ArtIconSelector: typeof import('./../components/core/base/ArtIconSelector.vue')['default']
    ArtImageCard: typeof import('./../components/core/cards/ArtImageCard.vue')['default']
    ArtKLineChart: typeof import('./../components/core/charts/ArtKLineChart.vue')['default']
    ArtLineChart: typeof import('./../components/core/charts/ArtLineChart.vue')['default']
    ArtLineChartCard: typeof import('./../components/core/cards/ArtLineChartCard.vue')['default']
    ArtMapChart: typeof import('./../components/core/charts/ArtMapChart.vue')['default']
    ArtMenuRight: typeof import('./../components/core/others/ArtMenuRight.vue')['default']
    ArtMixedMenu: typeof import('./../components/core/layouts/art-menus/art-mixed-menu/index.vue')['default']
    ArtNetwork: typeof import('./../components/core/base/ArtNetwork.vue')['default']
    ArtPageContent: typeof import('./../components/core/layouts/art-page-content/index.vue')['default']
    ArtProgressCard: typeof import('./../components/core/cards/ArtProgressCard.vue')['default']
    ArtRadarChart: typeof import('./../components/core/charts/ArtRadarChart.vue')['default']
    ArtRingChart: typeof import('./../components/core/charts/ArtRingChart.vue')['default']
    ArtScatterChart: typeof import('./../components/core/charts/ArtScatterChart.vue')['default']
    ArtScreenLock: typeof import('./../components/core/layouts/art-screen-lock/index.vue')['default']
    ArtSearchBar: typeof import('./../components/core/forms/art-search-bar/index.vue')['default']
    ArtSearchInput: typeof import('./../components/core/forms/art-search-bar/widget/ArtSearchInput.vue')['default']
    ArtSearchRadio: typeof import('./../components/core/forms/art-search-bar/widget/ArtSearchRadio.vue')['default']
    ArtSearchSelect: typeof import('./../components/core/forms/art-search-bar/widget/ArtSearchSelect.vue')['default']
    ArtSettingsPanel: typeof import('./../components/core/layouts/art-settings-panel/index.vue')['default']
    ArtSidebarMenu: typeof import('./../components/core/layouts/art-menus/art-sidebar-menu/index.vue')['default']
    ArtStatsCard: typeof import('./../components/core/cards/ArtStatsCard.vue')['default']
    ArtTable: typeof import('./../components/core/tables/ArtTable.vue')['default']
    ArtTableFullScreen: typeof import('./../components/core/tables/ArtTableFullScreen.vue')['default']
    ArtTableHeader: typeof import('./../components/core/tables/ArtTableHeader.vue')['default']
    ArtTextScroll: typeof import('./../components/core/text-effect/ArtTextScroll.vue')['default']
    ArtTimelineListCard: typeof import('./../components/core/cards/ArtTimelineListCard.vue')['default']
    ArtVideoPlayer: typeof import('./../components/core/media/ArtVideoPlayer.vue')['default']
    ArtWangEditor: typeof import('./../components/core/forms/ArtWangEditor.vue')['default']
    ArtWatermark: typeof import('./../components/core/others/ArtWatermark.vue')['default']
    ArtWorkTab: typeof import('./../components/core/layouts/art-work-tab/index.vue')['default']
    BarChart: typeof import('./../components/charts/BarChart.vue')['default']
    BuildingEnergyChart: typeof import('./../components/charts/BuildingEnergyChart.vue')['default']
    CarbonEmissionChart: typeof import('./../components/echarts/CarbonEmissionChart.vue')['default']
    CarbonEmissionData: typeof import('./../components/echarts/CarbonFootprint/CarbonEmissionData.vue')['default']
    CarbonInfoPanel: typeof import('./../components/echarts/CarbonFootprint/CarbonInfoPanel.vue')['default']
    CarbonSankeyChart: typeof import('./../components/echarts/CarbonFootprint/CarbonSankeyChart.vue')['default']
    CarbonScenariosChart: typeof import('./../components/echarts/CarbonScenariosChart.vue')['default']
    CarbonSinkChart: typeof import('./../components/echarts/CarbonSinkChart.vue')['default']
    Co2: typeof import('./../components/echarts/environment/co2.vue')['default']
    CO2Chart: typeof import('./../components/echarts/environment/CO2Chart.vue')['default']
    CODChart: typeof import('./../components/echarts/WaterQuality/CODChart.vue')['default']
    CodeBlock: typeof import('./../components/chat/CodeBlock.vue')['default']
    ControlLevelChart: typeof import('./../components/echarts/ControlLevelChart.vue')['default']
    DataAnalysis: typeof import('./../components/echarts/CarbonEmission/DataAnalysis.vue')['default']
    Echartsbiage: typeof import('./../components/charts/echartsbiage.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElMenuItemGroup: typeof import('element-plus/es')['ElMenuItemGroup']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopconfirm: typeof import('element-plus/es')['ElPopconfirm']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTreeSelect: typeof import('element-plus/es')['ElTreeSelect']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    ElWatermark: typeof import('element-plus/es')['ElWatermark']
    EmissionChart: typeof import('./../components/echarts/EmissionChart.vue')['default']
    EnergyDistributionPieChart: typeof import('./../components/echarts/EnergyAnalysis/EnergyDistributionPieChart.vue')['default']
    EnergyEmissionChart: typeof import('./../components/echarts/EnergyEmissionChart.vue')['default']
    EnergyTargetChart: typeof import('./../components/echarts/RenewableEnergy/EnergyTargetChart.vue')['default']
    EnergyTrendChart: typeof import('./../components/echarts/EnergyAnalysis/EnergyTrendChart.vue')['default']
    EnvironmentGaugeChart: typeof import('./../components/echarts/environment/environmentGaugeChart.vue')['default']
    GenericDetailChart: typeof import('./../components/echarts/CarbonEmission/GenericDetailChart.vue')['default']
    GenericTotalChart: typeof import('./../components/echarts/CarbonEmission/GenericTotalChart.vue')['default']
    HorizontalSubmenu: typeof import('./../components/core/layouts/art-menus/art-horizontal-menu/widget/HorizontalSubmenu.vue')['default']
    LoginBgView: typeof import('./../components/core/views/login/temp/LoginBgView.vue')['default']
    LoginLeftView: typeof import('./../components/core/views/login/LoginLeftView.vue')['default']
    MarkdownRenderer: typeof import('./../components/chat/MarkdownRenderer.vue')['default']
    MonthlyTrendLineChart: typeof import('./../components/echarts/EnergyAnalysis/MonthlyTrendLineChart.vue')['default']
    MultiProjectBuildingSelector: typeof import('./../components/MultiProjectBuildingSelector.vue')['default']
    MultiProjectSelector: typeof import('./../components/MultiProjectSelector.vue')['default']
    NewLoginLeftView: typeof import('./../components/core/views/login/temp/NewLoginLeftView.vue')['default']
    NoiseChart: typeof import('./../components/echarts/OutdoorEnvironment/NoiseChart.vue')['default']
    OtherIndexChart: typeof import('./../components/echarts/environment/OtherIndexChart.vue')['default']
    PHChart: typeof import('./../components/echarts/WaterQuality/PHChart.vue')['default']
    PM25Chart: typeof import('./../components/echarts/environment/PM25Chart.vue')['default']
    PollutantChart: typeof import('./../components/echarts/environment/PollutantChart.vue')['default']
    PowerGenerationChart: typeof import('./../components/echarts/RenewableEnergy/PowerGenerationChart.vue')['default']
    ProjectBuildingSelector: typeof import('./../components/ProjectBuildingSelector.vue')['default']
    ProjectCard: typeof import('./../components/ProjectCard.vue')['default']
    ProjectDetail: typeof import('./../components/ProjectDetail.vue')['default']
    ProjectSelector: typeof import('./../components/ProjectSelector.vue')['default']
    RenewableEnergyOverview: typeof import('./../components/echarts/RenewableEnergy/RenewableEnergyOverview.vue')['default']
    RenewableEnergyTypes: typeof import('./../components/echarts/RenewableEnergy/RenewableEnergyTypes.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SidebarSubmenu: typeof import('./../components/core/layouts/art-menus/art-sidebar-menu/widget/SidebarSubmenu.vue')['default']
    TemperatureHumidityChart: typeof import('./../components/echarts/environment/TemperatureHumidityChart.vue')['default']
    TempHumidChart: typeof import('./../components/echarts/OutdoorEnvironment/TempHumidChart.vue')['default']
    TransportEmissionChart: typeof import('./../components/echarts/TransportEmissionChart.vue')['default']
    WasteEmissionChart: typeof import('./../components/echarts/WasteEmissionChart.vue')['default']
    WaterEmissionChart: typeof import('./../components/echarts/WaterEmissionChart.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
