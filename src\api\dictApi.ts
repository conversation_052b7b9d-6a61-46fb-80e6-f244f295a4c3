import api from '@/utils/http'

// ========== 字典类型相关接口 ==========
// 字典类型接口
export interface DictType {
  dictId: number
  dictName: string
  dictType: string
  remark?: string
  createTime?: string
  updateTime?: string
}

// 字典类型列表响应接口
export interface DictTypeListResponse {
  msg: string
  code: number
  total: number
  rows: DictType[]
}

// 字典类型查询参数
export interface DictTypeQueryParams {
  dictName?: string
  dictType?: string
  pageSize: number
  pageNum: number
}

/**
 * 获取字典类型列表
 * @param params 查询参数
 * @returns 字典类型列表
 */
export function getDictTypeList(params: DictTypeQueryParams) {
  return api.get<DictTypeListResponse>({
    url: '/system/dict/type/list',
    params
  })
}

/**
 * 获取字典类型详情
 * @param dictId 字典类型ID
 * @returns 字典类型详情
 */
export function getDictTypeDetail(dictId: number) {
  return api.get<{
    code: number
    msg: string
    data: DictType
  }>({
    url: `/system/dict/type/${dictId}`
  })
}

/**
 * 新增字典类型
 * @param data 字典类型数据
 * @returns 操作结果
 */
export function createDictType(data: { dictName: string; dictType: string; remark?: string }) {
  return api.post<{
    code: number
    msg: string
  }>({
    url: '/system/dict/type/',
    data
  })
}

/**
 * 修改字典类型
 * @param data 字典类型数据
 * @returns 操作结果
 */
export function updateDictType(data: {
  dictId: number
  dictName: string
  dictType: string
  remark?: string
}) {
  return api.put<{
    code: number
    msg: string
  }>({
    url: '/system/dict/type/',
    data
  })
}

/**
 * 删除字典类型
 * @param dictId 字典类型ID
 * @returns 操作结果
 */
export function deleteDictType(dictId: number) {
  return api.del<{
    code: number
    msg: string
  }>({
    url: `/system/dict/type/${dictId}`
  })
}

// ========== 字典数据相关接口 ==========
// 字典数据接口
export interface DictData {
  dictCode: number
  dictCodeId?: number // 添加 dictCodeId 字段用于删除接口
  dictSort: number
  dictLabel: string
  dictValue: string
  dictType: string
  remark?: string
  createTime?: string
  updateTime?: string
}

// 字典数据列表响应接口
export interface DictDataListResponse {
  msg: string
  code: number
  total: number
  rows: DictData[]
}

// 字典数据查询参数
export interface DictDataQueryParams {
  dictLabel?: string
  dictValue?: string
  dictType?: string
  pageSize: number
  pageNum: number
}

/**
 * 获取字典数据列表
 * @param params 查询参数
 * @returns 字典数据列表
 */
export function getDictDataList(params: DictDataQueryParams) {
  return api.get<DictDataListResponse>({
    url: '/system/dict/data/list',
    params
  })
}

/**
 * 获取字典数据详情
 * @param dictCode 字典数据编码
 * @returns 字典数据详情
 */
export function getDictDataDetail(dictCode: number) {
  return api.get<{
    code: number
    msg: string
    data: DictData
  }>({
    url: `/system/dict/data/${dictCode}`
  })
}

/**
 * 新增字典数据
 * @param data 字典数据
 * @returns 操作结果
 */
export function createDictData(data: {
  dictSort: number
  dictLabel: string
  dictValue: string
  dictType: string
  remark?: string
}) {
  return api.post<{
    code: number
    msg: string
  }>({
    url: '/system/dict/data/',
    data
  })
}

/**
 * 修改字典数据
 * @param data 字典数据
 * @returns 操作结果
 */
export function updateDictData(data: {
  dictCode: number
  dictSort: number
  dictLabel: string
  dictValue: string
  dictType: string
  remark?: string
}) {
  return api.put<{
    code: number
    msg: string
  }>({
    url: '/system/dict/data/',
    data
  })
}

/**
 * 删除字典数据
 * @param dictCodeId 字典数据编码ID
 * @returns 操作结果
 */
export function deleteDictData(dictCodeId: number) {
  return api.del<{
    code: number
    msg: string
  }>({
    url: `/system/dict/data/${dictCodeId}`
  })
}

// ========== 兼容旧接口 ==========
// 字典数据项接口（兼容旧版本）
export interface DictDataItem {
  id: number
  key: string
  label: string
}

// 字典数据响应接口（兼容旧版本）
export interface DictDataResponse {
  msg: string
  code: number
  data: DictDataItem[]
}

/**
 * 获取字典数据列表（兼容旧版本）
 * @param dictType 字典类型
 * @returns 字典数据列表
 */
export function getDictDataListByType(dictType: string) {
  return api.get<DictDataResponse>({
    url: '/search/getDictDataList',
    params: { dictType }
  })
}

/**
 * 获取能源类型字典数据
 * @returns 能源类型列表
 */
export function getEnergyTypeDict() {
  return getDictDataListByType('yw_nhlx')
}

/**
 * 获取区域字典数据
 * @returns 区域列表
 */
export function getAreaDict() {
  return getDictDataListByType('yw_area')
}
