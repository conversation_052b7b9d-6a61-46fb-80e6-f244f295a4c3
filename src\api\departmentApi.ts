import api from '@/utils/http'

// 部门信息接口
export interface DepartmentInfo {
  deptId?: number
  parentId: number
  ancestors?: string
  deptName: string
  orderNum: number
  leader?: string
  phone?: string
  email?: string
  status: string
  delFlag?: string
  createBy?: string
  createTime?: string
  updateBy?: string
  updateTime?: string
  remark?: string
  parentName?: string
  children?: DepartmentInfo[]
}

// 部门列表响应接口
export interface DepartmentListResponse {
  msg: string
  code: number
  data: DepartmentInfo[]
}

// 部门查询参数
export interface DepartmentQueryParams {
  deptName?: string
  pageSize?: number
  pageNum?: number
}

// 新增部门请求参数
export interface CreateDepartmentRequest {
  parentId: number
  deptName: string
  orderNum: number
  leader?: string
  phone?: string
  email?: string
  status: string
}

// 修改部门请求参数
export interface UpdateDepartmentRequest {
  deptId: number
  parentId: number
  deptName: string
  orderNum: number
  leader?: string
  phone?: string
  email?: string
  status: string
}

// 通用响应接口
export interface CommonResponse {
  msg: string
  code: number
  data?: any
}

/**
 * 获取部门列表
 * @param params 查询参数
 * @returns 部门列表
 */
export function getDepartmentList(params?: DepartmentQueryParams) {
  return api.get<DepartmentListResponse>({
    url: '/system/dept/list',
    params
  })
}

/**
 * 获取部门详细信息
 * @param deptId 部门ID
 * @returns 部门详情
 */
export function getDepartmentDetail(deptId: number) {
  return api.get<{
    code: number
    msg: string
    data: DepartmentInfo
  }>({
    url: `/system/dept/${deptId}`
  })
}

/**
 * 新增部门
 * @param data 部门数据
 * @returns 新增结果
 */
export function createDepartment(data: CreateDepartmentRequest) {
  return api.post<CommonResponse>({
    url: '/system/dept/',
    data
  })
}

/**
 * 修改部门
 * @param data 部门数据
 * @returns 修改结果
 */
export function updateDepartment(data: UpdateDepartmentRequest) {
  return api.put<CommonResponse>({
    url: '/system/dept/',
    data
  })
}

/**
 * 删除部门
 * @param deptId 部门ID
 * @returns 删除结果
 */
export function deleteDepartment(deptId: number) {
  return api.del<CommonResponse>({
    url: `/system/dept/${deptId}`
  })
}
