<template>
  <div class="test-container">
    <h2>ProjectBuildingSelector 组件测试</h2>

    <div class="test-section">
      <h3>基本使用</h3>
      <ProjectBuildingSelector
        v-model="selection1"
        @change="handleChange1"
        @project-loaded="handleProjectLoaded"
      />
      <div class="result">
        <p>选中值: {{ JSON.stringify(selection1, null, 2) }}</p>
        <p>项目信息: {{ selectedProjectInfo1 ? selectedProjectInfo1.name : '未选择' }}</p>
        <p>楼栋信息: {{ selectedBuildingInfo1 ? selectedBuildingInfo1.name : '未选择' }}</p>
      </div>
    </div>

    <div class="test-section">
      <h3>禁用状态</h3>
      <ProjectBuildingSelector v-model="selection2" :disabled="true" @change="handleChange2" />
    </div>

    <div class="test-section">
      <h3>不自动选择第一个项目</h3>
      <ProjectBuildingSelector
        v-model="selection3"
        :auto-select-first="false"
        @change="handleChange3"
      />
    </div>

    <div class="test-section">
      <h3>自定义样式</h3>
      <ProjectBuildingSelector
        v-model="selection4"
        custom-class="custom-selector"
        @change="handleChange4"
      />
    </div>

    <div class="test-section">
      <h3>操作按钮</h3>
      <el-button @click="resetSelector">重置选择器</el-button>
      <el-button @click="logSelections">打印所有选择</el-button>
    </div>
  </div>
</template>

<script setup>
  import { ref } from 'vue'
  import ProjectBuildingSelector from '@/components/ProjectBuildingSelector.vue'

  // 测试数据
  const selection1 = ref({ projectId: null, buildingId: null })
  const selection2 = ref({ projectId: null, buildingId: null })
  const selection3 = ref({ projectId: null, buildingId: null })
  const selection4 = ref({ projectId: null, buildingId: null })

  // 选中的项目和楼栋信息
  const selectedProjectInfo1 = ref(null)
  const selectedBuildingInfo1 = ref(null)

  // 事件处理
  const handleChange1 = (data) => {
    console.log('选择器1变化:', data)
    selectedProjectInfo1.value = data.projectInfo
    selectedBuildingInfo1.value = data.buildingInfo
  }

  const handleChange2 = (data) => {
    console.log('选择器2变化:', data)
  }

  const handleChange3 = (data) => {
    console.log('选择器3变化:', data)
  }

  const handleChange4 = (data) => {
    console.log('选择器4变化:', data)
  }

  const handleProjectLoaded = (projectList) => {
    console.log('项目列表加载完成:', projectList)
  }

  // 操作方法
  const resetSelector = () => {
    selection1.value = { projectId: null, buildingId: null }
    selection2.value = { projectId: null, buildingId: null }
    selection3.value = { projectId: null, buildingId: null }
    selection4.value = { projectId: null, buildingId: null }
  }

  const logSelections = () => {
    console.log('所有选择:', {
      selection1: selection1.value,
      selection2: selection2.value,
      selection3: selection3.value,
      selection4: selection4.value
    })
  }
</script>

<style lang="scss" scoped>
  .test-container {
    max-width: 1200px;
    padding: 20px;
    margin: 0 auto;
  }

  .test-section {
    padding: 20px;
    margin-bottom: 30px;
    background-color: var(--el-bg-color);
    border: 1px solid var(--el-border-color);
    border-radius: 8px;

    h3 {
      margin-top: 0;
      margin-bottom: 15px;
      color: var(--el-text-color-primary);
    }

    .result {
      padding: 10px;
      margin-top: 15px;
      font-family: monospace;
      font-size: 12px;
      background-color: var(--el-fill-color-light);
      border-radius: 4px;
    }
  }

  :deep(.custom-selector) {
    .filter-item {
      margin-bottom: 15px;
    }

    .filter-label {
      font-weight: bold;
      color: var(--el-color-primary);
    }

    .filter-select {
      width: 220px;
    }
  }
</style>
