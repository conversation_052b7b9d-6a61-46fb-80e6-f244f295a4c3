import request from '@/utils/http'

/**
 * 设备参数配置查询参数
 */
export interface DeviceConfigQueryParams {
  configName?: string
  configKey?: string
  configValue?: string
  businessType?: string
  pageSize: number
  pageNum: number
}

/**
 * 设备参数配置数据项
 */
export interface DeviceConfigItem {
  createBy?: string
  createTime?: string
  updateBy?: string
  updateTime?: string | null
  remark?: string
  configId?: number
  configName: string
  configKey: string
  configValue: string
  configType?: string
  businessType?: string
  businessName?: string
  unitName?: string
}

/**
 * 设备参数配置查询响应
 */
export interface DeviceConfigListResponse {
  total: number
  rows: DeviceConfigItem[]
  code: number
  msg: string
}

/**
 * 设备参数配置详情响应
 */
export interface DeviceConfigDetailResponse {
  code: number
  msg: string
  data: DeviceConfigItem
}

/**
 * 通用响应接口
 */
export interface CommonResponse {
  code: number
  msg: string
}

/**
 * 新增设备参数配置请求参数
 */
export interface CreateDeviceConfigRequest {
  configName: string
  configKey: string
  configValue: string
  businessType?: string
  unitName?: string
  remark?: string
}

/**
 * 修改设备参数配置请求参数
 */
export interface UpdateDeviceConfigRequest extends CreateDeviceConfigRequest {
  configId: number
}

/**
 * 查询设备参数配置列表
 * @param params 查询参数
 * @returns 设备参数配置列表
 */
export function getDeviceConfigList(
  params: DeviceConfigQueryParams
): Promise<DeviceConfigListResponse> {
  return request.get({
    url: '/system/config/list',
    params
  })
}

/**
 * 获取设备参数配置详细信息
 * @param configId 配置ID
 * @returns 设备参数配置详情
 */
export function getDeviceConfigDetail(configId: number): Promise<DeviceConfigDetailResponse> {
  return request.get({
    url: `/system/config/${configId}`
  })
}

/**
 * 新增设备参数配置
 * @param data 配置数据
 * @returns 操作结果
 */
export function createDeviceConfig(data: CreateDeviceConfigRequest): Promise<CommonResponse> {
  return request.post({
    url: '/system/config/',
    data
  })
}

/**
 * 修改设备参数配置
 * @param data 配置数据
 * @returns 操作结果
 */
export function updateDeviceConfig(data: UpdateDeviceConfigRequest): Promise<CommonResponse> {
  return request.put({
    url: '/system/config/',
    data
  })
}

/**
 * 删除设备参数配置
 * @param configId 配置ID
 * @returns 操作结果
 */
export function deleteDeviceConfig(configId: number): Promise<CommonResponse> {
  return request.del({
    url: `/system/config/${configId}`
  })
}
