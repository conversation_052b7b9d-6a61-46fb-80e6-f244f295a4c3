<template>
  <ArtTableFullScreen>
    <div class="account-page" id="table-full-screen">
      <!-- 搜索栏 -->
      <ArtSearchBar
        :filter="formFilters"
        :items="formItems"
        @reset="handleReset"
        @search="handleSearch"
        @update:filter="(newFilter: any) => Object.assign(formFilters, newFilter)"
      ></ArtSearchBar>

      <ElCard shadow="never" class="art-table-card">
        <!-- 表格头部 -->
        <div class="table-header">
          <div class="header-left">
            <!-- 空白区域 -->
          </div>
          <div class="header-right">
            <ElButton type="primary" @click="showDialog('add')" v-ripple>
              <ElIcon><Plus /></ElIcon>
              新增用户
            </ElButton>
          </div>
        </div>

        <!-- 表格 -->
        <ElTable :data="tableData" :loading="loading" stripe style="width: 100%">
          <ElTableColumn prop="userId" label="用户ID" width="80" />
          <ElTableColumn prop="userName" label="用户名" />
          <ElTableColumn prop="nickName" label="昵称" />
          <ElTableColumn prop="roleName" label="角色">
            <template #default="scope">
              <ElTag
                v-if="getRoleNames(scope.row).length > 0"
                type="primary"
                v-for="roleName in getRoleNames(scope.row)"
                :key="roleName"
                style="margin-right: 4px"
              >
                {{ roleName }}
              </ElTag>
              <span v-else class="text-gray-400">暂无角色</span>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="projectId" label="关联项目">
            <template #default="scope">
              <div class="project-tags-container">
                <template v-if="getProjectNames(scope.row).length > 0">
                  <template v-if="getProjectNames(scope.row).length <= 2">
                    <ElTag
                      v-for="projectName in getProjectNames(scope.row)"
                      :key="projectName"
                      type="success"
                      effect="light"
                      class="project-tag"
                    >
                      {{ projectName }}
                    </ElTag>
                  </template>
                  <template v-else>
                    <ElTag
                      v-for="(projectName, index) in getProjectNames(scope.row).slice(0, 2)"
                      :key="projectName"
                      type="success"
                      effect="light"
                      class="project-tag"
                    >
                      {{ projectName }}
                    </ElTag>
                    <ElPopover
                      placement="top"
                      :width="300"
                      trigger="hover"
                      popper-class="project-popover"
                    >
                      <template #reference>
                        <ElTag type="info" effect="light" class="project-tag"
                          >+{{ getProjectNames(scope.row).length - 2 }}</ElTag
                        >
                      </template>
                      <template #default>
                        <div class="project-popover-content">
                          <p class="project-popover-title">全部项目</p>
                          <div class="project-popover-tags">
                            <ElTag
                              v-for="projectName in getProjectNames(scope.row)"
                              :key="projectName"
                              type="success"
                              effect="light"
                              class="project-popover-tag"
                            >
                              {{ projectName }}
                            </ElTag>
                          </div>
                        </div>
                      </template>
                    </ElPopover>
                  </template>
                </template>
                <span v-else class="text-gray-400">暂无项目</span>
              </div>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="createTime" label="创建时间" />
          <ElTableColumn label="操作" width="150">
            <template #default="scope">
              <ElButton size="small" type="primary" link @click="handleEdit(scope.row)">
                编辑
              </ElButton>
              <ElButton size="small" type="danger" link @click="handleDelete(scope.row)">
                删除
              </ElButton>
            </template>
          </ElTableColumn>
        </ElTable>

        <!-- 分页 -->
        <div class="pagination-container">
          <ElPagination
            v-model:current-page="pagination.pageNum"
            v-model:page-size="pagination.pageSize"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
          />
        </div>

        <!-- 新增/编辑对话框 -->
        <ElDialog
          v-model="dialogVisible"
          :title="dialogType === 'add' ? '添加用户' : '编辑用户'"
          width="500px"
          :close-on-click-modal="false"
        >
          <ElForm ref="formRef" :model="formData" :rules="rules" label-width="80px">
            <ElFormItem label="用户名" prop="userName">
              <ElInput v-model="formData.userName" placeholder="请输入用户名" />
            </ElFormItem>
            <ElFormItem label="昵称" prop="nickName">
              <ElInput v-model="formData.nickName" placeholder="请输入昵称" />
            </ElFormItem>
            <ElFormItem label="密码" prop="password" v-if="dialogType === 'add'">
              <ElInput
                v-model="formData.password"
                type="password"
                placeholder="请输入密码"
                show-password
              />
            </ElFormItem>
            <ElFormItem label="密码" v-if="dialogType === 'edit'">
              <ElInput
                v-model="formData.password"
                type="password"
                placeholder="不修改请留空"
                show-password
              />
            </ElFormItem>
            <ElFormItem label="角色" prop="roleId">
              <ElSelect v-model="formData.roleId" placeholder="请选择角色" style="width: 100%">
                <ElOption
                  v-for="role in roleList"
                  :key="role.roleId"
                  :label="role.roleName"
                  :value="role.roleId"
                />
              </ElSelect>
            </ElFormItem>
            <ElFormItem label="关联项目" prop="projectId">
              <ElSelect
                v-model="formData.projectId"
                placeholder="请选择项目"
                style="width: 100%"
                multiple
                collapse-tags
                collapse-tags-tooltip
              >
                <ElOption
                  v-for="project in projectList"
                  :key="project.id"
                  :label="project.name"
                  :value="project.id"
                />
              </ElSelect>
            </ElFormItem>
          </ElForm>
          <template #footer>
            <div class="dialog-footer">
              <ElButton @click="dialogVisible = false">取消</ElButton>
              <ElButton type="primary" :loading="submitLoading" @click="handleSubmit">
                确定
              </ElButton>
            </div>
          </template>
        </ElDialog>
      </ElCard>
    </div>
  </ArtTableFullScreen>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, nextTick } from 'vue'
  import {
    ElButton,
    ElIcon,
    ElTag,
    ElMessageBox,
    ElMessage,
    ElTable,
    ElTableColumn,
    ElPagination,
    ElPopover,
    FormInstance
  } from 'element-plus'
  import { Plus } from '@element-plus/icons-vue'
  import type { FormRules } from 'element-plus'
  import { SearchChangeParams, SearchFormItem } from '@/types/search-form'
  import { getUserList, addUser, updateUser, deleteUser, getRoleList } from '@/api/usersApi'
  import { getProjectSelectList, type ProjectOption } from '@/api/projectApi'

  // 类型定义
  interface ApiResponse {
    code: number
    msg?: string
    rows?: any[]
    total?: number
  }

  interface UserData {
    userId?: number
    userName?: string
    nickName?: string
    roleName?: string
    roleId?: number
    projectId?: string
    createTime?: string
    roleList?: Array<{
      roleId: number
      roleName: string
    }>
    projectList?: Array<{
      projectId: number
      projectName: string
    }> | null
  }

  // 响应式数据
  const dialogType = ref<'add' | 'edit'>('add')
  const dialogVisible = ref(false)
  const loading = ref(false)
  const submitLoading = ref(false)
  const formRef = ref<FormInstance>()

  // 分页数据
  const pagination = reactive({
    pageNum: 1,
    pageSize: 10,
    total: 0
  })

  // 表格数据
  const tableData = ref<UserData[]>([])
  const roleList = ref<any[]>([])
  const projectList = ref<ProjectOption[]>([])

  // 定义表单搜索初始值
  const initialSearchState = {
    userName: '',
    roleId: ''
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  // 对话框表单数据
  const formData = reactive({
    userId: '',
    userName: '',
    nickName: '',
    password: '',
    roleId: '',
    projectId: [] as number[]
  })

  // 表单验证规则
  const rules: FormRules = {
    userName: [
      { required: true, message: '请输入用户名', trigger: 'blur' },
      { min: 2, max: 20, message: '用户名长度在 2 到 20 个字符', trigger: 'blur' }
    ],
    nickName: [
      { required: true, message: '请输入昵称', trigger: 'blur' },
      { min: 2, max: 20, message: '昵称长度在 2 到 20 个字符', trigger: 'blur' }
    ],
    password: [
      { required: true, message: '请输入密码', trigger: 'blur' },
      { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
    ],
    roleId: [{ required: true, message: '请选择角色', trigger: 'change' }]
  }

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    pagination.pageNum = 1
    getTableData()
  }

  // 搜索处理
  const handleSearch = () => {
    pagination.pageNum = 1
    getTableData()
  }

  // 获取用户角色名称列表
  const getRoleNames = (user: UserData): string[] => {
    // 优先从 roleList 数组中获取角色名称
    if (user.roleList && user.roleList.length > 0) {
      return user.roleList.map((role) => role.roleName).filter((name) => name)
    }
    // 如果 roleList 为空，但有 roleName，则使用 roleName
    if (user.roleName) {
      return [user.roleName]
    }
    // 都没有则返回空数组
    return []
  }

  // 获取用户关联项目名称列表
  const getProjectNames = (user: UserData): string[] => {
    // 优先使用 projectList 数据，如果存在
    if (user.projectList && user.projectList.length > 0) {
      return user.projectList.map((project) => project.projectName)
    }

    // 备选：从 projectId 字符串解析
    if (!user.projectId) {
      return []
    }

    // 将逗号分隔的项目ID字符串转换为数组
    const projectIds = user.projectId
      .split(',')
      .map((id) => parseInt(id.trim()))
      .filter((id) => !isNaN(id))

    // 根据ID查找项目名称
    return projectIds
      .map((id) => {
        const project = projectList.value.find((p) => p.id === id)
        return project ? project.name : `项目${id}`
      })
      .filter((name) => name)
  }

  // 表单项变更处理
  const handleFormChange = (params: SearchChangeParams): void => {
    console.log('表单项变更:', params)
  }

  // 表单配置项
  const formItems: SearchFormItem[] = [
    {
      label: '用户名',
      prop: 'userName',
      type: 'input',
      config: {
        placeholder: '请输入用户名',
        clearable: true
      },
      onChange: handleFormChange
    },
    {
      label: '角色',
      prop: 'roleId',
      type: 'select',
      config: {
        placeholder: '请选择角色',
        clearable: true
      },
      options: () =>
        roleList.value.map((role) => ({
          label: role.roleName,
          value: role.roleId
        })),
      onChange: handleFormChange
    }
  ]

  // 分页变化处理
  const handlePageChange = (page: number) => {
    pagination.pageNum = page
    getTableData()
  }

  const handleSizeChange = (size: number) => {
    pagination.pageSize = size
    pagination.pageNum = 1
    getTableData()
  }

  // 获取表格数据
  const getTableData = async () => {
    loading.value = true
    try {
      const params = {
        pageSize: pagination.pageSize,
        pageNum: pagination.pageNum,
        userName: formFilters.userName || undefined,
        roleId: formFilters.roleId || undefined
      }

      const response = (await getUserList(params)) as ApiResponse
      if (response.code === 200 || response.code === 1) {
        tableData.value = response.rows || []
        pagination.total = response.total || 0
      } else {
        ElMessage.error(response.msg || '获取用户列表失败')
      }
    } catch (error) {
      console.error('获取用户列表失败:', error)
      ElMessage.error('获取用户列表失败')
    } finally {
      loading.value = false
    }
  }

  // 获取角色列表
  const getRoles = async () => {
    try {
      const response = (await getRoleList({ pageSize: 100, pageNum: 1 })) as ApiResponse
      if (response.code === 200 || response.code === 1) {
        roleList.value = response.rows || []
      }

      // 如果接口没有返回角色数据，记录日志
      if (roleList.value.length === 0) {
        console.warn('未获取到角色列表数据')
      }
    } catch (error) {
      console.error('获取角色列表失败:', error)
      ElMessage.error('获取角色列表失败')
    }
  }

  // 获取项目列表
  const getProjects = async () => {
    try {
      const response = await getProjectSelectList()
      if (response.code === 200) {
        projectList.value = response.data || []
      } else {
        ElMessage.error(response.msg || '获取项目列表失败')
      }
    } catch (error) {
      console.error('获取项目列表失败:', error)
      ElMessage.error('获取项目列表失败')
    }
  }

  // 获取用户的角色ID（优先从roleList获取第一个角色）
  const getUserRoleId = (user: UserData): string => {
    // 优先从 roleList 中获取第一个角色的ID
    if (user.roleList && user.roleList.length > 0) {
      return user.roleList[0].roleId.toString()
    }
    // 如果 roleList 为空，使用 roleId
    if (user.roleId) {
      return user.roleId.toString()
    }
    return ''
  }

  // 显示对话框
  const showDialog = (type: 'add' | 'edit', row?: UserData) => {
    dialogType.value = type
    if (type === 'add') {
      Object.assign(formData, {
        userId: '',
        userName: '',
        nickName: '',
        password: '',
        roleId: '',
        projectId: []
      })
    } else if (row) {
      // 优先使用 projectList 数据，如果存在
      let projectIds: number[] = []

      if (row.projectList && row.projectList.length > 0) {
        // 从 projectList 获取项目ID
        projectIds = row.projectList.map((project) => project.projectId)
      } else if (row.projectId) {
        // 备选：从 projectId 字符串解析
        projectIds = row.projectId
          .split(',')
          .map((id) => parseInt(id.trim()))
          .filter((id) => !isNaN(id))
      }

      Object.assign(formData, {
        userId: row.userId ? row.userId.toString() : '',
        userName: row.userName || '',
        nickName: row.nickName || '',
        password: '',
        roleId: getUserRoleId(row),
        projectId: projectIds
      })
    }
    dialogVisible.value = true
    nextTick(() => {
      formRef.value?.clearValidate()
    })
  }

  // 编辑处理
  const handleEdit = (row: UserData) => {
    console.log('编辑用户数据:', row)
    showDialog('edit', row)
  }

  // 删除处理
  const handleDelete = async (row: UserData) => {
    try {
      await ElMessageBox.confirm(`确定要删除用户"${row.userName}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      const response = (await deleteUser(row.userId ? row.userId.toString() : '')) as ApiResponse
      if (response.code === 200) {
        ElMessage.success('删除成功')
        getTableData()
      } else {
        ElMessage.error(response.msg || '删除失败')
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除用户失败:', error)
        ElMessage.error('删除失败')
      }
    }
  }

  // 提交处理
  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()
      submitLoading.value = true

      // 将项目ID数组转换为逗号分隔的字符串
      const projectIdString = formData.projectId.length > 0 ? formData.projectId.join(',') : ''

      let response: ApiResponse
      if (dialogType.value === 'add') {
        response = (await addUser({
          userName: formData.userName,
          nickName: formData.nickName,
          password: formData.password,
          roleId: Number(formData.roleId),
          projectId: projectIdString
        })) as ApiResponse
      } else {
        const updateData: any = {
          userId: formData.userId,
          userName: formData.userName,
          nickName: formData.nickName,
          roleId: Number(formData.roleId),
          projectId: projectIdString
        }
        if (formData.password) {
          updateData.password = formData.password
        }
        response = (await updateUser(updateData)) as ApiResponse
      }

      if (response.code === 200) {
        ElMessage.success(dialogType.value === 'add' ? '添加成功' : '修改成功')
        dialogVisible.value = false
        getTableData()
      } else {
        ElMessage.error(response.msg || (dialogType.value === 'add' ? '添加失败' : '修改失败'))
      }
    } catch (error) {
      console.error('提交失败:', error)
    } finally {
      submitLoading.value = false
    }
  }

  // 初始化
  onMounted(() => {
    getRoles()
    getProjects()
    getTableData()
  })
</script>

<style scoped>
  .account-page {
    height: 100%;
  }

  .art-table-card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 140px);
  }

  .pagination-container {
    display: flex;
    justify-content: flex-end;
    padding: 16px;
  }

  .dialog-footer {
    text-align: right;
  }

  .table-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 0;
    margin-bottom: 16px;
    border-bottom: 1px solid #ebeef5;
  }

  .header-left {
    flex: 1;
  }

  .header-right {
    display: flex;
    align-items: center;
  }

  /* 项目标签相关样式 */
  .project-tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }

  .project-tag {
    max-width: 100%;
    margin: 2px 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .project-popover-content {
    padding: 4px;
  }

  .project-popover-title {
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #606266;
  }

  .project-popover-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
  }

  .project-popover-tag {
    margin: 0;
  }

  /* 自定义弹出框样式 */
  :deep(.project-popover) {
    max-width: 300px;
  }
</style>
