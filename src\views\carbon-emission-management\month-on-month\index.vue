<template>
  <div class="carbon-emission-month-comparison">
    <!-- 头部筛选区域 -->
    <div class="filter-container">
      <div class="filter-row">
        <ProjectBuildingSelector
          v-model="projectBuildingSelection"
          :auto-select-first="true"
          @change="handleProjectBuildingChange"
          @project-loaded="handleProjectListLoaded"
        />

        <div class="filter-item">
          <span class="filter-label">时间</span>
          <el-radio-group
            v-model="timeGranularity"
            class="time-granularity-buttons"
            @change="handleTimeGranularityChange"
          >
            <el-radio-button
              v-for="item in timeGranularityOptions"
              :key="item.value"
              :label="item.value"
            >
              {{ item.label }}
            </el-radio-button>
          </el-radio-group>
        </div>
        <div class="filter-item date-filter">
          <span class="filter-label">日期选择：</span>
          <div class="date-picker-group">
            <el-date-picker
              v-model="selectedDate"
              :type="datePickerType"
              placeholder="选择日期"
              class="date-picker"
              :disabled-date="disabledDate"
              @change="handleDateChange"
            />
          </div>
        </div>
        <div class="filter-actions">
          <el-button
            type="primary"
            @click="handleSearch"
            :loading="loading"
            :disabled="queryButtonDisabled"
          >
            查询
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="success" @click="exportData" :loading="isExporting">
            导出数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 月度对比图表 -->
    <el-card class="chart-card" shadow="hover">
      <div class="chart-header">
        <h3>{{ chartTitle }}</h3>
      </div>
      <div ref="chartRef" class="chart-container"></div>
    </el-card>

    <!-- 环比数据表格 -->
    <el-card class="table-card" shadow="hover">
      <div class="card-header">
        <h3>{{ tableTitle }}</h3>
      </div>

      <el-table :data="paginatedTableData" border style="width: 100%" v-loading="loading">
        <el-table-column :prop="dateColumnProp" :label="dateColumnLabel"></el-table-column>
        <el-table-column prop="current" :label="`${currentPeriodName}碳排放(kgCO₂)`">
          <template #default="scope">
            {{ scope.row.current.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="last" :label="`${lastPeriodName}碳排放(kgCO₂)`">
          <template #default="scope">
            {{ scope.row.last.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="change" label="增减量">
          <template #default="scope">
            {{ scope.row.change.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="rate" label="环比变化率">
          <template #default="scope">
            <span v-if="scope.row.rate === null" class="text-gray">--</span>
            <span v-else :class="scope.row.rate > 0 ? 'text-red' : 'text-green'">
              {{ scope.row.rate > 0 ? '+' : '' }}{{ scope.row.rate.toFixed(2) }}%
              <el-icon v-if="scope.row.rate > 0"><ArrowUp /></el-icon>
              <el-icon v-else><ArrowDown /></el-icon>
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="analysis" label="分析"></el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-sizes="[10, 20, 30]"
          :page-size="pageSize"
          :current-page="currentPage"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </el-card>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
  import * as echarts from 'echarts'
  import { ElMessage } from 'element-plus'
  import ProjectBuildingSelector from '@/components/ProjectBuildingSelector.vue'
  import { getCarbonMonthOnMonthReport } from '@/api/carbon-emission'
  import { useSettingStore } from '@/store/modules/setting'
  import { SystemThemeEnum } from '@/enums/appEnum'

  import { getMonthComparisonOption } from '@/components/echarts/CarbonEmission/monthComparison.js'

  // 获取主题设置
  const settingStore = useSettingStore()
  const isDark = computed(() => settingStore.systemThemeType === SystemThemeEnum.DARK)

  // 项目楼栋选择相关数据
  const projectBuildingSelection = ref({ projectId: null, buildingId: null })
  const selectedProjectInfo = ref(null)
  const selectedBuildingInfo = ref(null)

  // 状态管理
  const loading = ref(false)
  const isExporting = ref(false)
  const timeGranularity = ref('month')
  const selectedDate = ref(new Date())
  const total = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(10)
  const tableData = ref([])
  const isFullScreen = ref(false)

  // 查询状态管理 - 防重复查询
  const lastQueryParams = ref(null)
  const queryButtonDisabled = ref(false) // 查询按钮状态

  // 比较查询参数是否相同
  const isSameQueryParams = (params1, params2) => {
    if (!params1 || !params2) return false
    return (
      params1.projectId === params2.projectId &&
      params1.buildingId === params2.buildingId &&
      params1.timeType === params2.timeType &&
      params1.beginTime === params2.beginTime &&
      params1.endTime === params2.endTime
    )
  }

  // 时间粒度选项
  const timeGranularityOptions = [
    { value: 'month', label: '月' },
    { value: 'year', label: '年' }
  ]

  // 禁用日期选择
  const disabledDate = (time) => {
    return time.getTime() > Date.now()
  }

  // 图表相关
  const chartRef = ref(null)
  let chart = null

  // 根据选择的时间粒度，动态设置日期选择器类型
  const datePickerType = computed(() => {
    switch (timeGranularity.value) {
      case 'month':
        return 'month'
      case 'year':
        return 'year'
      default:
        return 'month'
    }
  })

  // 表格标题
  const tableTitle = computed(() => {
    switch (timeGranularity.value) {
      case 'year':
        return '年度环比详细数据'
      case 'month':
        return '月度环比详细数据'
      case 'day':
        return '日环比详细数据'
      default:
        return '环比详细数据'
    }
  })

  // 表格日期列名称和属性
  const dateColumnLabel = computed(() => {
    switch (timeGranularity.value) {
      case 'year':
        return '年份'
      case 'month':
        return '月份'
      case 'day':
        return '日期'
      default:
        return '日期'
    }
  })

  const dateColumnProp = computed(() => {
    return 'day' // 所有日期类型都存储在day属性中
  })

  // 根据选择的时间粒度和日期计算当前周期和上一周期的名称
  const currentPeriodName = ref('')
  const lastPeriodName = ref('')

  // 图表标题
  const chartTitle = computed(() => {
    let periodText = ''

    switch (timeGranularity.value) {
      case 'year':
        periodText = '年度'
        break
      case 'month':
        periodText = '月度'
        break
      case 'day':
        periodText = '日'
        break
      default:
        periodText = '月度'
    }

    // 如果有选择项目，则显示项目名称
    const projectName = selectedProjectInfo.value?.name || ''

    return `${projectName}${periodText}碳排放环比变化分析`
  })

  // 分页后的表格数据
  const paginatedTableData = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value
    const end = start + pageSize.value
    return tableData.value.slice(start, end)
  })

  // 防抖处理函数
  let searchTimeout = null
  const debouncedAutoSearch = () => {
    if (searchTimeout) {
      clearTimeout(searchTimeout)
    }
    searchTimeout = setTimeout(() => {
      autoSearch()
    }, 500) // 500ms防抖延迟
  }

  // 自动查询方法
  const autoSearch = async () => {
    // 如果没有选择项目，不发请求
    if (!projectBuildingSelection.value.projectId) {
      console.log('没有选择项目，不发请求')
      return
    }

    // 如果没有选择日期，不发请求
    if (!selectedDate.value) {
      console.log('没有选择日期，不发请求')
      return
    }

    console.log('自动查询参数:', {
      projectId: projectBuildingSelection.value.projectId,
      buildingId: projectBuildingSelection.value.buildingId,
      timeGranularity: timeGranularity.value,
      selectedDate: selectedDate.value
    })
    await fetchData()
  }

  // 设置默认日期为上个月
  const setDefaultDate = () => {
    const now = new Date()
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
    selectedDate.value = lastMonth
    console.log('设置默认日期为上个月:', lastMonth)
  }

  // 初始化
  onMounted(() => {
    initChart()

    // 设置默认日期为上个月
    setDefaultDate()
    // 默认选择月粒度
    timeGranularity.value = 'month'

    updatePeriodNames()

    window.addEventListener('resize', handleResize)
  })

  // 组件卸载时清理
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    if (chart) {
      chart.dispose()
      chart = null
    }
  })

  // 监听时间粒度变化
  watch(timeGranularity, () => {
    updatePeriodNames()
  })

  // 监听选择日期变化
  watch(selectedDate, () => {
    updatePeriodNames()
  })

  // 监听主题变化，更新图表
  watch(isDark, () => {
    nextTick(() => {
      if (chart) {
        chart.dispose()
        chart = null
        initChart()
      }
    })
  })

  // 监听查询条件变化，启用查询按钮
  watch(
    [projectBuildingSelection, timeGranularity, selectedDate],
    (newVal, oldVal) => {
      // 当查询条件发生变化时，启用查询按钮
      if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
        console.log('查询条件变化，启用查询按钮:', {
          projectBuildingSelection: projectBuildingSelection.value,
          timeGranularity: timeGranularity.value,
          selectedDate: selectedDate.value
        })
        queryButtonDisabled.value = false
        // 清空上次查询参数，允许重新查询
        lastQueryParams.value = null
      }
    },
    { deep: true }
  )

  // 处理项目楼栋变化
  const handleProjectBuildingChange = (data) => {
    selectedProjectInfo.value = data.projectInfo
    selectedBuildingInfo.value = data.buildingInfo
    console.log('项目楼栋选择变化:', data)
    // 清空上次查询参数，允许重新查询
    lastQueryParams.value = null
    queryButtonDisabled.value = false
    // 项目楼栋变化时自动发送请求（使用防抖）
    if (data.projectId) {
      debouncedAutoSearch()
    }
  }

  // 监听项目列表加载完成
  const handleProjectListLoaded = (projectList) => {
    console.log('项目列表加载完成:', projectList)
    // 当项目列表加载完成后，自动选择第一个项目并发送查询请求
    if (projectList && projectList.length > 0) {
      const firstProject = projectList[0]
      console.log('自动选择第一个项目:', firstProject)

      // 自动执行查询
      nextTick(() => {
        if (projectBuildingSelection.value.projectId) {
          console.log('自动发送环比查询请求')
          fetchData()
        }
      })
    }
  }

  // 重置表单
  const handleReset = () => {
    // 重置项目楼栋选择
    projectBuildingSelection.value = { projectId: null, buildingId: null }
    selectedProjectInfo.value = null
    selectedBuildingInfo.value = null

    timeGranularity.value = 'month'
    // 设置默认日期为上个月
    setDefaultDate()

    // 清空表格数据
    tableData.value = []
    total.value = 0
    updateChart()

    // 清空查询缓存，允许重新查询
    lastQueryParams.value = null
    queryButtonDisabled.value = false

    ElMessage.success('已重置查询条件')
  }

  // 处理时间粒度变化
  const handleTimeGranularityChange = () => {
    console.log('时间粒度变化:', timeGranularity.value)
    // 更新周期名称
    updatePeriodNames()
    // 清空上次查询参数，允许重新查询
    lastQueryParams.value = null
    queryButtonDisabled.value = false

    // 如果已选择项目，则自动重新获取数据（使用防抖）
    if (projectBuildingSelection.value.projectId) {
      debouncedAutoSearch()
    } else {
      // 清空数据并更新图表
      tableData.value = []
      total.value = 0
      updateChart()
    }
  }

  // 处理日期变化
  const handleDateChange = () => {
    if (selectedDate.value) {
      console.log('日期变化:', selectedDate.value)
      // 更新周期名称
      updatePeriodNames()
      // 清空上次查询参数，允许重新查询
      lastQueryParams.value = null
      queryButtonDisabled.value = false
      // 日期变化时自动发送请求（使用防抖）
      if (projectBuildingSelection.value.projectId) {
        debouncedAutoSearch()
      }
    }
  }

  // 更新周期名称
  const updatePeriodNames = () => {
    // 如果没有选择日期，使用默认日期
    if (!selectedDate.value) {
      selectedDate.value = new Date()
    }

    const currentDate = selectedDate.value
    let previousDate

    if (timeGranularity.value === 'year') {
      // 年份展示逻辑
      const year = currentDate.getFullYear()
      currentPeriodName.value = year.toString()
      lastPeriodName.value = (year - 1).toString()
      return
    }

    // 月份展示逻辑
    previousDate = new Date(currentDate)
    previousDate.setMonth(previousDate.getMonth() - 1)

    // 格式化为"yyyy年MM月"
    currentPeriodName.value = `${currentDate.getFullYear()}年${currentDate.getMonth() + 1}月`
    lastPeriodName.value = `${previousDate.getFullYear()}年${previousDate.getMonth() + 1}月`
  }

  // 切换全屏显示
  const toggleFullScreen = () => {
    const chartContainer = chartRef.value

    if (!isFullScreen.value) {
      if (chartContainer.requestFullscreen) {
        chartContainer.requestFullscreen()
      } else if (chartContainer.webkitRequestFullscreen) {
        chartContainer.webkitRequestFullscreen()
      } else if (chartContainer.msRequestFullscreen) {
        chartContainer.msRequestFullscreen()
      }
      isFullScreen.value = true
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen()
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen()
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen()
      }
      isFullScreen.value = false
    }

    // 延迟resize以适应全屏后的尺寸
    setTimeout(() => {
      handleResize()
    }, 200)
  }

  // 监听全屏变化事件
  document.addEventListener('fullscreenchange', () => {
    isFullScreen.value = !!document.fullscreenElement
    handleResize()
  })

  // 监听窗口大小变化
  const handleResize = () => {
    if (chart) {
      chart.resize()
    }
  }

  // 初始化图表
  const initChart = () => {
    if (chartRef.value) {
      chart = echarts.init(chartRef.value)
      updateChart()
    }
  }

  // 更新图表数据
  const updateChart = () => {
    if (!chart) return

    // 如果没有真实数据，显示空图表
    if (!tableData.value || tableData.value.length === 0) {
      const emptyOption = {
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value',
          name: '碳排放量(kgCO₂)'
        },
        series: []
      }
      chart.setOption(emptyOption)
      return
    }

    // 使用真实API数据更新图表（这部分由 updateChartWithApiData 处理）
  }

  // 获取数据
  const fetchData = async () => {
    // 检查是否选择了项目
    if (!projectBuildingSelection.value.projectId) {
      ElMessage.warning('请先选择项目')
      return
    }

    // 检查是否选择了日期
    if (!selectedDate.value) {
      ElMessage.warning('请选择日期')
      return
    }

    loading.value = true

    try {
      // 构建API请求参数
      const params = {
        projectId: projectBuildingSelection.value.projectId,
        buildingId: projectBuildingSelection.value.buildingId,
        timeType: timeGranularity.value === 'year' ? 2 : 1, // 1-月，2-年
        beginTime: '',
        endTime: ''
      }

      // 根据时间粒度设置时间参数
      if (timeGranularity.value === 'year') {
        const year = selectedDate.value.getFullYear()
        params.beginTime = `${year}`
        params.endTime = `${year}`
      } else {
        const year = selectedDate.value.getFullYear()
        const month = (selectedDate.value.getMonth() + 1).toString().padStart(2, '0')
        params.beginTime = `${year}-${month}`
        params.endTime = `${year}-${month}`
      }

      console.log('发送环比API请求，参数:', params)

      // 调用环比API
      const response = await getCarbonMonthOnMonthReport(params)

      console.log('环比API响应:', response)

      if (response.code === 200 && response.data) {
        const { timeList, dataList } = response.data

        // 转换API数据为表格数据
        const data = timeList.map((time, index) => {
          const item = dataList[index] || {}
          const current = item.data || 0
          const last = item.preData || 0
          const change = item.increment || 0

          // 修复环比变化率计算：API返回的incrementPercent是小数形式，直接乘以100即可
          // 当API返回的incrementPercent为null/undefined时，表示无法计算环比，显示null
          let rate = null
          if (item.incrementPercent !== null && item.incrementPercent !== undefined) {
            rate = item.incrementPercent * 100
          } else {
            // API返回null时，无论数据如何都显示为无数据
            rate = null
          }

          return {
            day: time,
            current,
            last,
            change,
            rate,
            analysis:
              rate === null
                ? '无数据'
                : item.dataResult === '增长'
                  ? rate > 10
                    ? '环比大幅上升，需关注碳排放使用情况'
                    : '环比上升'
                  : rate < -10
                    ? '环比大幅下降，碳排放效率提高'
                    : '环比下降'
          }
        })

        tableData.value = data
        total.value = data.length

        // 更新图表数据
        updateChartWithApiData(timeList, dataList)

        // 保存当前查询参数
        lastQueryParams.value = { ...params }
        // 禁用查询按钮（因为已经查询过相同条件）
        queryButtonDisabled.value = true

        console.log('✅ 环比数据处理完成:', data)
      } else {
        throw new Error(response.msg || 'API返回数据异常')
      }
    } catch (error) {
      console.error('❌ 环比数据API调用失败:', error)
      ElMessage.error(`数据获取失败: ${error.message}`)

      // 清空数据
      tableData.value = []
      total.value = 0

      // 显示空图表
      updateChart()
    } finally {
      loading.value = false
    }
  }

  // 使用API数据更新图表
  const updateChartWithApiData = (timeList, dataList) => {
    if (!chart) return

    const currentData = dataList.map((item) => item.data || 0)
    const lastData = dataList.map((item) => item.preData || 0)

    const option = getMonthComparisonOption(
      currentPeriodName.value,
      lastPeriodName.value,
      currentData,
      lastData,
      timeList
    )

    // 设置Y轴名称为碳排放量(kgCO₂)
    if (option.yAxis && option.yAxis[0]) {
      option.yAxis[0].name = '碳排放量(kgCO₂)'
    }

    // 适配主题颜色
    if (isDark.value) {
      // 黑夜模式颜色适配 - 安全地设置series颜色
      if (option.series && option.series[0] && option.series[0].itemStyle) {
        option.series[0].itemStyle.color = '#409EFF'
      }
      if (option.series && option.series[1] && option.series[1].itemStyle) {
        option.series[1].itemStyle.color = '#E6A23C'
      }

      // 文字颜色适配 - 安全地设置文字颜色
      if (option.legend) {
        option.legend.textStyle = option.legend.textStyle || {}
        option.legend.textStyle.color = '#E5EAF3'
      }

      if (option.xAxis && option.xAxis[0]) {
        option.xAxis[0].axisLabel = option.xAxis[0].axisLabel || {}
        option.xAxis[0].axisLabel.color = '#E5EAF3'
      }

      if (option.yAxis && option.yAxis[0]) {
        option.yAxis[0].axisLabel = option.yAxis[0].axisLabel || {}
        option.yAxis[0].axisLabel.color = '#E5EAF3'
        option.yAxis[0].nameTextStyle = option.yAxis[0].nameTextStyle || {}
        option.yAxis[0].nameTextStyle.color = '#E5EAF3'
      }

      // 网格线颜色适配 - 安全地设置网格线颜色
      if (option.yAxis && option.yAxis[0]) {
        option.yAxis[0].splitLine = option.yAxis[0].splitLine || {}
        option.yAxis[0].splitLine.lineStyle = option.yAxis[0].splitLine.lineStyle || {}
        option.yAxis[0].splitLine.lineStyle.color = '#363637'

        option.yAxis[0].axisLine = option.yAxis[0].axisLine || {}
        option.yAxis[0].axisLine.lineStyle = option.yAxis[0].axisLine.lineStyle || {}
        option.yAxis[0].axisLine.lineStyle.color = '#363637'
      }

      if (option.xAxis && option.xAxis[0]) {
        option.xAxis[0].axisLine = option.xAxis[0].axisLine || {}
        option.xAxis[0].axisLine.lineStyle = option.xAxis[0].axisLine.lineStyle || {}
        option.xAxis[0].axisLine.lineStyle.color = '#363637'
      }
    }

    chart.setOption(option)
  }

  // 处理筛选查询
  const handleSearch = () => {
    // 检查是否选择了项目
    if (!projectBuildingSelection.value.projectId) {
      ElMessage.warning('请先选择项目')
      return
    }

    // 检查是否选择了日期
    if (!selectedDate.value) {
      ElMessage.warning('请选择日期')
      return
    }

    // 如果查询按钮已禁用，不执行查询
    if (queryButtonDisabled.value) {
      ElMessage.info('查询条件未变化，无需重复查询')
      return
    }

    // 构建当前查询参数用于比较
    const currentParams = {
      projectId: projectBuildingSelection.value.projectId,
      buildingId: projectBuildingSelection.value.buildingId,
      timeType: timeGranularity.value === 'year' ? 2 : 1,
      beginTime: '',
      endTime: ''
    }

    // 根据时间粒度设置时间参数
    if (timeGranularity.value === 'year') {
      const year = selectedDate.value.getFullYear()
      currentParams.beginTime = `${year}`
      currentParams.endTime = `${year}`
    } else {
      const year = selectedDate.value.getFullYear()
      const month = (selectedDate.value.getMonth() + 1).toString().padStart(2, '0')
      currentParams.beginTime = `${year}-${month}`
      currentParams.endTime = `${year}-${month}`
    }

    // 检查查询参数是否与上次相同
    if (isSameQueryParams(currentParams, lastQueryParams.value)) {
      ElMessage.info('查询条件未变化')
      queryButtonDisabled.value = true
      return
    }

    console.log('查询参数:', {
      projectId: projectBuildingSelection.value.projectId,
      buildingId: projectBuildingSelection.value.buildingId,
      timeGranularity: timeGranularity.value,
      selectedDate: selectedDate.value
    })

    updatePeriodNames()
    fetchData()
  }

  // 分页处理
  const handleSizeChange = (size) => {
    pageSize.value = size
    // 分页不需要重新请求API，只是改变显示的数据
  }

  const handleCurrentChange = (page) => {
    currentPage.value = page
    // 分页不需要重新请求API，只是改变显示的数据
  }

  // 导出图表
  const exportChart = () => {
    if (!chart) return

    const dataURL = chart.getDataURL({
      type: 'png',
      pixelRatio: 2,
      backgroundColor: isDark.value ? '#1d1e1f' : '#fff'
    })

    const filename = `${chartTitle.value}_${new Date().getTime()}.png`

    const link = document.createElement('a')
    link.download = filename
    link.href = dataURL
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success('图表导出成功')
  }

  // 导出数据（CSV格式）
  const exportData = () => {
    isExporting.value = true

    try {
      // 生成CSV内容
      let csvContent =
        `${dateColumnLabel.value},` +
        `${currentPeriodName.value}碳排放(kgCO₂),` +
        `${lastPeriodName.value}碳排放(kgCO₂),` +
        '增减量,环比变化率(%),分析\n'

      tableData.value.forEach((row) => {
        const rateDisplay = row.rate === null ? '--' : `${row.rate.toFixed(2)}%`
        csvContent +=
          `${row.day},${row.current.toFixed(2)},${row.last.toFixed(2)},` +
          `${row.change.toFixed(2)},${rateDisplay},${row.analysis}\n`
      })

      // 创建并下载CSV文件
      const encodedUri = encodeURI('data:text/csv;charset=utf-8,' + csvContent)

      // 获取所选项目名称
      const projectName = selectedProjectInfo.value?.name || ''

      const filename = `${projectName}_${
        timeGranularity.value === 'year' ? '年度' : '月度'
      }碳排放_环比数据_${new Date().getTime()}.csv`

      const link = document.createElement('a')
      link.setAttribute('href', encodedUri)
      link.setAttribute('download', filename)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      ElMessage.success('数据导出成功')
    } catch (error) {
      console.error('导出数据错误:', error)
      ElMessage.error('导出数据失败')
    } finally {
      isExporting.value = false
    }
  }
</script>

<style lang="scss" scoped>
  .carbon-emission-month-comparison {
    min-height: calc(100vh - 60px);
    padding: 16px;
    color: var(--el-text-color-primary);
    background-color: var(--el-bg-color);

    .filter-container {
      padding: 16px;
      margin-bottom: 16px;
      border-radius: 8px;
      box-shadow: var(--el-box-shadow-light);

      .filter-row {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        justify-content: flex-start;
        width: 100%;
      }

      .filter-item {
        display: flex;
        flex-shrink: 0;
        align-items: center;
        margin-right: 16px;

        .filter-label {
          margin-right: 16px;
          margin-left: 16px;
          font-size: 14px;
          color: var(--el-text-color-regular);
          white-space: nowrap;
        }

        .filter-select {
          width: 180px;
        }

        .time-granularity-buttons {
          margin-left: 8px;
        }
      }

      .date-filter {
        .date-picker-group {
          display: flex;

          .date-picker {
            width: 180px;
          }
        }
      }

      .filter-actions {
        display: flex;
        gap: 10px;
        margin-left: auto;
      }
    }

    .chart-card {
      margin-bottom: 20px;
      border-radius: 8px;
      box-shadow: var(--el-box-shadow-light);

      .chart-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 16px;
        margin-bottom: 20px;
        border-bottom: 1px solid var(--el-border-color-lighter);

        h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: var(--el-text-color-primary);
        }

        .chart-actions {
          display: flex;
          gap: 10px;
        }
      }

      .chart-container {
        width: 100%;
        height: 400px;

        &:fullscreen {
          padding: 20px;
        }
      }
    }

    .table-card {
      padding: 16px;
      border-radius: 8px;
      box-shadow: var(--el-box-shadow-light);

      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 16px;
        margin-bottom: 20px;
        border-bottom: 1px solid var(--el-border-color-lighter);

        h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: var(--el-text-color-primary);
        }

        .header-actions {
          display: flex;
          gap: 10px;
        }
      }

      .text-red {
        color: var(--el-color-danger);
      }

      .text-green {
        color: var(--el-color-success);
      }

      .text-gray {
        color: var(--el-text-color-placeholder);
      }

      .pagination-container {
        display: flex;
        justify-content: flex-end;
        margin-top: 20px;
      }
    }

    @media screen and (width <= 1200px) {
      .filter-container {
        .filter-item {
          margin-right: 12px;

          .filter-select {
            width: 150px;
          }
        }

        .date-filter {
          .date-picker {
            width: 280px;
          }
        }
      }
    }

    /* 黑夜模式特殊样式 */
    :global(.dark) {
      .carbon-emission-month-comparison {
        background-color: var(--el-bg-color);

        .filter-container,
        .chart-card,
        .table-card {
          background-color: var(--el-bg-color-overlay);
          border: 1px solid var(--el-border-color);
        }

        .chart-container {
          &:fullscreen {
            background-color: var(--el-bg-color-overlay);
          }
        }
      }
    }
  }
</style>
