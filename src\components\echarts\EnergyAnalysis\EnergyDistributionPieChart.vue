<template>
  <div ref="chartRef" :style="{ height: height, width: width }"></div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted, watch, nextTick, computed } from 'vue'
  import * as echarts from 'echarts/core'
  import { <PERSON><PERSON><PERSON> } from 'echarts/charts'
  import {
    TitleComponent,
    TooltipComponent
    // LegendComponent, // 移除 LegendComponent 因为不再使用图例
  } from 'echarts/components'
  import { CanvasRenderer } from 'echarts/renderers'
  import { ElMessage } from 'element-plus'
  import { useSettingStore } from '@/store/modules/setting'
  import { SystemThemeEnum } from '@/enums/appEnum'

  echarts.use([
    TitleComponent,
    TooltipComponent,
    // LegendComponent,
    PieChart,
    CanvasRenderer
  ])

  const props = defineProps({
    height: {
      type: String,
      default: '400px'
    },
    width: {
      type: String,
      default: '100%'
    },
    outerData: {
      type: Array,
      required: true,
      // 示例: [{ name: '空调', value: 64600, unit: '用电量（标煤）' }, ...]
      default: () => []
    },
    innerData: {
      type: Array,
      // 示例: [{ name: '用电总量', value: 380, unit: '用电量（标煤）', transparent: true }, { name: '光伏发电', value: 19.2, unit: '用电量（标煤）' }]
      default: () => [{ name: '光伏发电', value: 19.2, unit: '用电量（标煤）' }]
    }
  })

  const chartRef = ref(null)
  let chartInstance = null

  // 获取主题状态
  const settingStore = useSettingStore()
  const isDark = computed(() => settingStore.systemThemeType === SystemThemeEnum.DARK)

  const initChart = () => {
    if (!chartRef.value) {
      console.warn('图表容器未找到 (EnergyDistribution)')
      return
    }
    try {
      if (chartInstance) {
        chartInstance.dispose()
      }
      chartInstance = echarts.init(chartRef.value)
      setOptions()
      console.log('环形图初始化/更新完成 (EnergyDistribution)')
    } catch (error) {
      console.error('初始化/更新环形图失败 (EnergyDistribution):', error)
      ElMessage.error('环形图加载失败')
    }
  }

  const setOptions = () => {
    if (!chartInstance || !props.outerData || props.outerData.length === 0) {
      // 如果没有数据，可以清空图表或显示提示信息
      chartInstance?.clear() // 清空图表
      // console.warn("数据为空，无法渲染环形图 (EnergyDistribution)");
      // 可以选择在这里显示一个占位提示，或者在模板中处理
      return
    }

    // 准备饼图数据，并确保value是数字
    const pieData = props.outerData.map((item) => ({
      name: item.name,
      value: parseFloat(String(item.value).replace(/,/g, '')) || 0, // 确保value是数字，并处理可能的逗号
      unit: item.unit || '用电量（标煤）' // 如果数据中没有单位，提供一个默认单位
    }))

    // 准备内圈数据
    const innerPieData = props.innerData.map((item) => ({
      name: item.name,
      value: parseFloat(String(item.value).replace(/,/g, '')) || 0,
      unit: item.unit || '用电量（标煤）',
      transparent: item.transparent || false
    }))

    // 计算总用电量和确定单位
    let totalValue = 0
    let commonUnit = ''
    if (pieData.length > 0) {
      commonUnit = pieData[0].unit // 假设所有数据项单位一致，或使用第一个作为代表
      pieData.forEach((item) => {
        totalValue += item.value
      })
    }

    const option = {
      tooltip: {
        trigger: 'item',
        backgroundColor: isDark.value ? 'rgba(50, 50, 50, 0.9)' : 'rgba(255, 255, 255, 0.9)',
        borderColor: isDark.value ? '#555' : '#eee',
        borderWidth: 1,
        formatter: (params) => {
          const textColor = isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#666'
          const titleColor = isDark.value ? 'rgba(255, 255, 255, 0.95)' : '#333'
          return `
          <div style="font-size:14px;color:${textColor};line-height:22px">
            <div style="font-weight:bold;color:${titleColor}">${params.name}</div>
            <div>用电量（标煤）: ${params.value.toLocaleString()} kgce</div>
            <div>占比: ${params.percent}%</div>
          </div>
        `
        }
      },
      // 移除图例配置
      // legend: { ... }

      // 隐藏中心标题文字
      // title: {
      //   text: '用电总量',
      //   subtext: `${totalValue.toLocaleString()} ${commonUnit}`,
      //   left: 'center',
      //   top: 'center',
      //   textStyle: {
      //     fontSize: 20,
      //     fontWeight: 'bold',
      //     color: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#333'
      //   },
      //   subtextStyle: {
      //     fontSize: 16,
      //     color: isDark.value ? 'rgba(255, 255, 255, 0.75)' : '#333'
      //   },
      //   itemGap: 5
      // },
      color: ['#4169E1', '#5B7FFF', '#6495ED', '#87CEFA', '#ADD8E6'], // 蓝色系
      series: [
        {
          name: '能耗类型分布',
          type: 'pie',
          radius: ['55%', '75%'], // 外圈半径
          avoidLabelOverlap: true,
          itemStyle: {
            borderRadius: 0,
            borderColor: isDark.value ? '#333' : '#fff',
            borderWidth: 1
          },
          label: {
            show: true,
            position: 'outside',
            formatter: (params) => {
              return `{a|${params.name}}\n{b|${params.value.toLocaleString()} kgce}\n{c|${params.percent}%}`
            },
            rich: {
              a: {
                color: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#333',
                fontSize: 12,
                lineHeight: 18,
                align: 'center'
              },
              b: {
                color: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#333',
                fontSize: 12,
                lineHeight: 18,
                align: 'center'
              },
              c: {
                color: isDark.value ? 'rgba(255, 255, 255, 0.65)' : '#555',
                fontSize: 12,
                lineHeight: 18,
                align: 'center'
              }
            }
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          labelLine: {
            show: true,
            length: 8,
            length2: 10,
            smooth: false
          },
          data: pieData // 使用处理后的 outerData
        },
        // 添加内圈 - 用电总量和光伏发电
        {
          name: '内圈数据',
          type: 'pie',
          radius: ['40%', '52%'], // 内圈半径
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 0,
            borderColor: isDark.value ? '#333' : '#fff',
            borderWidth: 1
          },
          label: {
            show: false // 不显示标签文字，避免与中心的用电总量冲突
          },
          labelLine: {
            show: false
          },
          emphasis: {
            scale: false
          },
          data: innerPieData.map((item) => ({
            ...item,
            itemStyle: {
              color: item.transparent
                ? 'transparent' // 透明颜色用于用电总量
                : '#2E8B57' // 绿色表示光伏发电
            }
          }))
        }
      ]
    }

    chartInstance.setOption(option)
  }

  const resizeChart = () => {
    chartInstance?.resize()
  }

  watch(
    [() => props.outerData, () => props.innerData], // 监听 outerData 和 innerData
    () => {
      nextTick(() => {
        // 确保DOM更新后再尝试初始化或更新图表
        initChart() // 调用initChart以处理数据变化，它内部会调用setOptions
      })
    },
    { deep: true, immediate: true } // immediate: true 可以在组件初始加载时就执行一次
  )

  // 监听主题变化，更新图表
  watch(
    () => isDark.value,
    () => {
      nextTick(() => {
        if (chartInstance) {
          setOptions()
        }
      })
    }
  )

  onMounted(() => {
    nextTick(() => {
      initChart() // 初始加载时也调用initChart
    })
    window.addEventListener('resize', resizeChart)
  })

  onUnmounted(() => {
    chartInstance?.dispose()
    window.removeEventListener('resize', resizeChart)
    console.log('环形图实例已清理 (EnergyDistribution)')
  })

  defineExpose({ resizeChart })
</script>

<style lang="scss" scoped>
  /* 图表容器样式 */
</style>
