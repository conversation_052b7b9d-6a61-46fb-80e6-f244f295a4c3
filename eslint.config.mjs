// ESLint 配置文件 - 全局禁用所有规则
import fs from 'fs'
import path, { dirname } from 'path'
import { fileURLToPath } from 'url'
import pluginJs from '@eslint/js'
import pluginVue from 'eslint-plugin-vue'
import globals from 'globals'
import tseslint from 'typescript-eslint'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

const autoImportConfig = JSON.parse(
  fs.readFileSync(path.resolve(__dirname, '.auto-import.json'), 'utf-8')
)

export default [
  {
    files: ['**/*.{js,mjs,cjs,ts,vue}']
  },
  {
    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.node,
        ...autoImportConfig.globals
      }
    }
  },
  pluginJs.configs.recommended,
  ...tseslint.configs.recommended,
  ...pluginVue.configs['flat/essential'],
  {
    files: ['**/*.vue'],
    languageOptions: {
      parserOptions: { parser: tseslint.parser }
    }
  },
  {
    ignores: [
      'node_modules',
      'dist',
      'public',
      '.vscode/**',
      'src/assets/**'
    ]
  },
  // 全局禁用所有规则
  {
    files: ['**/*.{js,mjs,cjs,ts,vue}'],
    rules: {
      // 禁用所有规则
      '@typescript-eslint/no-unused-vars': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/ban-ts-comment': 'off',
      '@typescript-eslint/no-unused-expressions': 'off',
      'vue/multi-word-component-names': 'off',
      'no-unused-vars': 'off',
      'no-undef': 'off',
      'no-empty': 'off',
      'no-prototype-builtins': 'off',
      'no-case-declarations': 'off',
      'prefer-const': 'off',
      'no-var': 'off',
      'quotes': 'off',
      'semi': 'off',
      'no-multiple-empty-lines': 'off',
      'no-unexpected-multiline': 'off'
    }
  }
]
