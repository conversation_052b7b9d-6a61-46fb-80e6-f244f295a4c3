// 辅助函数：获取CSS变量值
function getCssVariableValue(variableName) {
  if (typeof window === 'undefined' || typeof document === 'undefined') return null
  try {
    const value = getComputedStyle(document.documentElement).getPropertyValue(variableName).trim()
    return value || null //确保返回 null 而不是空字符串
  } catch (e) {
    console.error(`Error getting CSS variable ${variableName}:`, e)
    return null
  }
}

// 辅助函数：将CSS变量（RGB字符串）转换为 'rgb(r,g,b)'
function getRgbColor(variableName, fallbackColor) {
  const rgbString = getCssVariableValue(variableName)
  if (rgbString && /^\d{1,3},\s*\d{1,3},\s*\d{1,3}$/.test(rgbString)) {
    return `rgb(${rgbString})`
  }
  return fallbackColor
}

// 辅助函数：将CSS变量（RGB字符串）和alpha转换为 'rgba(r,g,b,a)'
function getRgbaColor(variableName, alpha, fallbackColor) {
  const rgbString = getCssVariableValue(variableName)
  if (rgbString && /^\d{1,3},\s*\d{1,3},\s*\d{1,3}$/.test(rgbString)) {
    return `rgba(${rgbString}, ${alpha})`
  }
  return fallbackColor
}

// 定义月度碳排放同比图表配置
export function getMonthComparisonOption(
  currentMonth,
  lastMonth,
  currentData,
  lastData,
  xAxisData
) {
  return {
    title: {
      left: 'center',
      textStyle: { color: getCssVariableValue('--art-text-gray-900') || '#333333' }
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: getCssVariableValue('--art-main-bg-color') || '#ffffff',
      borderColor: getCssVariableValue('--art-border-color') || '#e7e7e7',
      textStyle: {
        color: getCssVariableValue('--art-text-gray-800') || '#333333'
      },
      axisPointer: {
        type: 'shadow',
        shadowStyle: {
          color: getRgbaColor('--art-primary', 0.1, 'rgba(93,135,255,0.1)')
        }
      },
      formatter: function (params) {
        // params 可能为空或长度不足，需要添加保护
        if (!params || params.length < 2) {
          return ''
        }
        // 使用参数索引获取数据
        const day = params[0].axisValue
        const current = params[0].data.toFixed(2)
        const last = params[1].data.toFixed(2)
        const diff = (params[0].data - params[1].data).toFixed(2)
        const rate = (((params[0].data - params[1].data) / params[1].data) * 100).toFixed(2)

        const sign = diff >= 0 ? '+' : ''
        const rateSymbol = rate >= 0 ? '↑' : '↓'

        return `${day}<br/>
                    ${currentMonth}: ${current} 吨<br/>
                    ${lastMonth}: ${last} 吨<br/>
                    同比变化: ${sign}${diff} 吨<br/>
                    同比增长率: ${sign}${rate}% ${rateSymbol}`
      }
    },
    legend: {
      data: [currentMonth, lastMonth],
      top: '40px',
      textStyle: {
        color: getCssVariableValue('--art-text-gray-700') || '#666666'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '80px',
      containLabel: true
    },
    toolbox: {
      feature: {
        saveAsImage: { title: '保存为图片' },
        dataZoom: { title: { zoom: '区域缩放', back: '区域还原' } },
        magicType: { type: ['line', 'bar'], title: { line: '切换为折线图', bar: '切换为柱状图' } },
        restore: { title: '还原' }
      },
      iconStyle: {
        borderColor: getCssVariableValue('--art-text-gray-600') || '#666666'
      },
      emphasis: {
        iconStyle: {
          borderColor: getRgbColor('--art-primary', '#5470c6')
        }
      }
    },
    dataZoom: [
      {
        type: 'slider',
        show: true,
        start: 0,
        end: 100,
        backgroundColor: getCssVariableValue('--art-gray-100') || 'rgba(255,255,255,0)',
        dataBackground: {
          lineStyle: { color: getCssVariableValue('--art-gray-300') || '#d3d3d3' },
          areaStyle: { color: getCssVariableValue('--art-gray-200') || '#f0f0f0' }
        },
        selectedDataBackground: {
          lineStyle: { color: getRgbColor('--art-primary', '#5470C6') },
          areaStyle: { color: getRgbaColor('--art-primary', 0.2, 'rgba(93,135,255,0.2)') }
        },
        fillerColor: getRgbaColor('--art-primary', 0.15, 'rgba(93,135,255,0.15)'),
        borderColor: getCssVariableValue('--art-border-color') || '#ddd',
        handleStyle: {
          color: getRgbColor('--art-primary', '#5D87FF'),
          borderWidth: 1,
          borderColor: getRgbColor('--art-primary', '#5D87FF')
        },
        moveHandleStyle: {
          color: getRgbColor('--art-primary', '#5D87FF'),
          opacity: 0.7
        },
        textStyle: {
          color: getCssVariableValue('--art-text-gray-700') || '#333'
        }
      },
      {
        type: 'inside',
        start: 0,
        end: 100
      }
    ],
    xAxis: [
      {
        type: 'category',
        data: xAxisData,
        axisTick: {
          alignWithLabel: true,
          lineStyle: {
            color: getCssVariableValue('--art-border-color') || '#cccccc'
          }
        },
        axisLine: {
          lineStyle: {
            color: getCssVariableValue('--art-border-color') || '#cccccc'
          }
        },
        axisLabel: {
          rotate: xAxisData.length > 12 ? 45 : 0,
          color: getCssVariableValue('--art-text-gray-600') || '#666666'
        },
        splitLine: { show: false }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '碳排放量(kgCO₂)', // 这个单位也可能需要根据 isCarbon 和 isUnitArea 动态变化
        nameTextStyle: {
          color: getCssVariableValue('--art-text-gray-700') || '#666666'
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: getCssVariableValue('--art-border-color') || '#cccccc'
          }
        },
        axisTick: {
          show: true,
          lineStyle: {
            color: getCssVariableValue('--art-border-color') || '#cccccc'
          }
        },
        axisLabel: {
          color: getCssVariableValue('--art-text-gray-600') || '#666666'
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: getCssVariableValue('--art-border-dashed-color') || '#eeeeee'
          }
        }
      }
    ],
    series: [
      {
        name: currentMonth,
        type: 'bar',
        barWidth: '40%',
        data: currentData,
        itemStyle: {
          color: getRgbColor('--art-primary', '#5470c6') // 使用主题主色
        },
        markPoint: {
          data: [
            { type: 'max', name: '最大值' },
            { type: 'min', name: '最小值' }
          ]
        }
      },
      {
        // 添加 lastMonth 的系列数据
        name: lastMonth,
        type: 'bar',
        barWidth: '40%',
        data: lastData, // 确保 lastData 被传入和使用
        itemStyle: {
          color: getRgbColor('--art-warning', '#fac858') // 使用主题警告色
        },
        markPoint: {
          data: [
            { type: 'max', name: '最大值' },
            { type: 'min', name: '最小值' }
          ]
        }
      }
    ]
  }
}
