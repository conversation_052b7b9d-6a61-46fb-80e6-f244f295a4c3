<template>
  <div class="security-params-container page-content">
    <div class="content-box">
      <!-- 安全状态概览卡片 -->
      <el-card class="status-card" shadow="hover">
        <template #header>
          <div class="status-header">
            <span class="status-title">安全状态概览</span>
            <el-button type="primary" size="small" @click="refreshStatus" :icon="Refresh"
              >刷新状态</el-button
            >
          </div>
        </template>
        <div class="status-content">
          <div class="status-item" v-for="(item, index) in securityStatusList" :key="index">
            <div class="device-icon" :class="item.status">
              <el-icon :size="24">
                <component :is="getSecurityIcon(item.type)" />
              </el-icon>
            </div>
            <div class="device-info">
              <div class="device-name">{{ item.name }}</div>
              <div class="device-address">{{ item.description }}</div>
              <div class="device-status">
                <span class="status-dot" :class="item.status"></span>
                <span class="status-text" :class="item.status">{{
                  getStatusText(item.status)
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <el-card class="param-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span class="card-title">信息安全配置</span>
            <el-button type="primary" size="small" @click="handleAddParameter" :icon="Plus"
              >添加配置</el-button
            >
          </div>
        </template>

        <!-- 搜索表单 -->
        <div class="search-form">
          <el-form :model="searchForm" inline>
            <el-form-item label="配置名称">
              <el-input
                v-model="searchForm.configName"
                placeholder="请输入配置名称"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="配置编码">
              <el-input
                v-model="searchForm.configKey"
                placeholder="请输入配置编码"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="配置类型">
              <el-select
                v-model="searchForm.configType"
                placeholder="请选择配置类型"
                clearable
                style="width: 200px"
              >
                <el-option label="加密策略" value="encryption" />
                <el-option label="认证策略" value="authentication" />
                <el-option label="访问控制" value="access" />
                <el-option label="防火墙规则" value="firewall" />
                <el-option label="其他安全规则" value="other" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" :icon="Search">搜索</el-button>
              <el-button @click="handleReset" :icon="Refresh">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 参数列表 -->
        <ArtTable
          :data="securityParamsList"
          :loading="loading"
          row-key="id"
          :border="false"
          :highlight-current-row="false"
          :total="total"
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          index
          class="param-table"
        >
          <el-table-column label="配置名称" prop="configName" min-width="150">
            <template #default="scope">
              <div class="security-name-cell">
                <div class="icon-wrapper">
                  <el-icon :size="16" v-if="scope.row.configType">
                    <component :is="getSecurityIcon(scope.row.configType)" />
                  </el-icon>
                </div>
                <span>{{ scope.row.configName }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="configKey" label="配置编码" min-width="150" />
          <el-table-column label="配置类型" align="center" min-width="120">
            <template #default="scope">
              <el-tag
                :type="getSecurityTagType(scope.row.configType)"
                effect="light"
                size="small"
                class="security-tag"
              >
                {{ getConfigTypeLabel(scope.row.configType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="configValue" label="配置值" min-width="120" />
          <el-table-column prop="ipAddr" label="IP地址" min-width="120" />
          <el-table-column prop="port" label="端口" min-width="80" />
          <el-table-column prop="remark" label="描述" show-overflow-tooltip min-width="150" />
          <el-table-column label="操作" width="180" align="center" fixed="right">
            <template #default="scope">
              <div class="operation-btns">
                <el-button
                  link
                  size="small"
                  @click="handleEdit(scope.row)"
                  title="编辑"
                  class="operation-btn edit-btn"
                >
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-button
                  link
                  size="small"
                  @click="handleDelete(scope.row)"
                  title="删除"
                  class="operation-btn delete-btn"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </template>
          </el-table-column>
        </ArtTable>
      </el-card>
    </div>

    <!-- 添加/编辑参数对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEditing ? '编辑安全配置' : '添加安全配置'"
      width="650px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-form
        :model="paramForm"
        :rules="rules"
        ref="paramFormRef"
        label-width="120px"
        label-position="right"
        class="param-form"
      >
        <el-form-item label="配置名称" prop="configName">
          <el-input v-model="paramForm.configName" placeholder="请输入配置名称" />
        </el-form-item>
        <el-form-item label="配置编码" prop="configKey">
          <el-input v-model="paramForm.configKey" placeholder="请输入配置编码" />
        </el-form-item>
        <el-form-item label="配置类型" prop="configType">
          <el-select
            v-model="paramForm.configType"
            placeholder="请选择配置类型"
            style="width: 100%"
            @change="handleTypeChange"
          >
            <el-option label="加密策略" value="encryption">
              <div class="security-option">
                <el-icon :size="16">
                  <component :is="getSecurityIcon('encryption')" />
                </el-icon>
                <span>加密策略</span>
              </div>
            </el-option>
            <el-option label="认证策略" value="authentication">
              <div class="security-option">
                <el-icon :size="16">
                  <component :is="getSecurityIcon('authentication')" />
                </el-icon>
                <span>认证策略</span>
              </div>
            </el-option>
            <el-option label="访问控制" value="access">
              <div class="security-option">
                <el-icon :size="16">
                  <component :is="getSecurityIcon('access')" />
                </el-icon>
                <span>访问控制</span>
              </div>
            </el-option>
            <el-option label="防火墙规则" value="firewall">
              <div class="security-option">
                <el-icon :size="16">
                  <component :is="getSecurityIcon('firewall')" />
                </el-icon>
                <span>防火墙规则</span>
              </div>
            </el-option>
            <el-option label="其他安全规则" value="other">
              <div class="security-option">
                <el-icon :size="16">
                  <component :is="getSecurityIcon('other')" />
                </el-icon>
                <span>其他安全规则</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="配置值" prop="configValue">
          <el-input v-model="paramForm.configValue" placeholder="请输入配置值" />
        </el-form-item>
        <el-form-item label="IP地址" prop="ipAddr">
          <el-input v-model="paramForm.ipAddr" placeholder="请输入IP地址" />
        </el-form-item>
        <el-form-item label="端口" prop="port">
          <el-input v-model="paramForm.port" placeholder="请输入端口" />
        </el-form-item>
        <el-form-item label="描述" prop="remark">
          <el-input
            v-model="paramForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入配置描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import {
    Edit,
    Delete,
    Refresh,
    Plus,
    Lock,
    Key,
    UserFilled,
    Switch,
    SetUp,
    Search
  } from '@element-plus/icons-vue'
  import {
    getSecurityConfigList,
    addSecurityConfig,
    updateSecurityConfig,
    deleteSecurityConfig
  } from '@/api/securityConfigApi'

  // 搜索表单
  const searchForm = reactive({
    configName: '',
    configKey: '',
    configValue: '',
    configType: ''
  })

  // 加载状态
  const loading = ref(false)

  // 安全状态列表
  const securityStatusList = ref([
    {
      id: 1,
      name: '数据加密状态',
      type: 'encryption',
      description: '用户数据AES-256加密状态',
      status: 'online'
    },
    {
      id: 2,
      name: '双因素认证',
      type: 'authentication',
      description: '管理员两步验证状态',
      status: 'online'
    },
    {
      id: 3,
      name: '防火墙状态',
      type: 'firewall',
      description: '系统防火墙运行状态',
      status: 'warning'
    },
    {
      id: 4,
      name: '访问控制系统',
      type: 'access',
      description: '权限管理系统状态',
      status: 'offline'
    }
  ])

  // 表格行样式
  const tableRowClassName = ({ row, rowIndex }) => {
    const type = row.type
    return `row-${type}`
  }

  // 状态文本映射
  const getStatusText = (status) => {
    const statusMap = {
      online: '正常',
      offline: '异常',
      warning: '警告'
    }
    return statusMap[status] || '未知'
  }

  // 安全类型图标映射
  const getSecurityIcon = (type) => {
    const iconMap = {
      encryption: Lock,
      authentication: Key,
      access: UserFilled,
      firewall: Switch,
      other: SetUp
    }
    return iconMap[type] || iconMap.other
  }

  // 安全标签类型
  const getSecurityTagType = (type) => {
    const typeMap = {
      encryption: 'success',
      authentication: 'primary',
      access: 'warning',
      firewall: 'info',
      other: ''
    }
    return typeMap[type] || ''
  }

  // 刷新安全状态
  const refreshStatus = () => {
    // 这里可以调用API刷新安全状态
    ElMessage.success('安全状态已刷新')
  }

  // 参数列表数据
  const securityParamsList = ref([])

  // 分页相关
  const currentPage = ref(1)
  const pageSize = ref(10)
  const total = ref(0)

  // 表单相关
  const dialogVisible = ref(false)
  const isEditing = ref(false)
  const paramFormRef = ref(null)
  const paramForm = reactive({
    id: null,
    configName: '',
    configKey: '',
    configValue: '',
    configType: '',
    ipAddr: '',
    port: '',
    remark: ''
  })

  // 处理类型变更
  const handleTypeChange = (type) => {
    // 可以根据配置类型设置默认值
    console.log('配置类型变更:', type)
  }

  // 表单验证规则
  const rules = {
    configName: [{ required: true, message: '请输入配置名称', trigger: 'blur' }],
    configKey: [{ required: true, message: '请输入配置编码', trigger: 'blur' }],
    configType: [{ required: true, message: '请选择配置类型', trigger: 'change' }],
    configValue: [{ required: true, message: '请输入配置值', trigger: 'blur' }]
  }

  // 获取配置类型标签
  const getConfigTypeLabel = (type) => {
    const typeMap = {
      encryption: '加密策略',
      authentication: '认证策略',
      access: '访问控制',
      firewall: '防火墙规则',
      other: '其他安全规则'
    }
    return typeMap[type] || type
  }

  // 获取安全配置列表
  const fetchSecurityParamsList = async () => {
    try {
      loading.value = true
      const params = {
        pageSize: pageSize.value,
        pageNum: currentPage.value,
        ...searchForm
      }
      const response = await getSecurityConfigList(params)
      if (response.code === 200) {
        securityParamsList.value = response.rows || []
        total.value = response.total || 0
      } else {
        ElMessage.error(response.msg || '获取数据失败')
      }
    } catch (error) {
      console.error('获取安全配置列表失败:', error)
      ElMessage.error('获取数据失败')
    } finally {
      loading.value = false
    }
  }

  // 搜索
  const handleSearch = () => {
    currentPage.value = 1
    fetchSecurityParamsList()
  }

  // 重置搜索
  const handleReset = () => {
    Object.keys(searchForm).forEach((key) => {
      searchForm[key] = ''
    })
    currentPage.value = 1
    fetchSecurityParamsList()
  }

  // 生命周期钩子
  onMounted(() => {
    fetchSecurityParamsList()
  })

  // 分页方法
  const handleSizeChange = (size) => {
    pageSize.value = size
    fetchSecurityParamsList()
  }

  const handleCurrentChange = (page) => {
    currentPage.value = page
    fetchSecurityParamsList()
  }

  // 添加参数
  const handleAddParameter = () => {
    isEditing.value = false
    resetForm()
    dialogVisible.value = true
  }

  // 编辑参数
  const handleEdit = (row) => {
    isEditing.value = true
    resetForm()

    // 设置表单字段
    Object.keys(paramForm).forEach((key) => {
      if (key in row) {
        paramForm[key] = row[key]
      }
    })

    dialogVisible.value = true
  }

  // 删除参数
  const handleDelete = async (row) => {
    try {
      await ElMessageBox.confirm('确定要删除此配置吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      const response = await deleteSecurityConfig(row.id)
      if (response.code === 200) {
        ElMessage.success('删除成功')
        fetchSecurityParamsList()
      } else {
        ElMessage.error(response.msg || '删除失败')
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除安全配置失败:', error)
        ElMessage.error('删除失败')
      }
    }
  }

  // 提交表单
  const submitForm = async () => {
    try {
      const valid = await paramFormRef.value.validate()
      if (!valid) return

      const formData = { ...paramForm }

      if (isEditing.value) {
        // 编辑模式
        const response = await updateSecurityConfig(formData)
        if (response.code === 200) {
          ElMessage.success('更新成功')
          dialogVisible.value = false
          fetchSecurityParamsList()
        } else {
          ElMessage.error(response.msg || '更新失败')
        }
      } else {
        // 添加模式
        const response = await addSecurityConfig(formData)
        if (response.code === 200) {
          ElMessage.success('添加成功')
          dialogVisible.value = false
          fetchSecurityParamsList()
        } else {
          ElMessage.error(response.msg || '添加失败')
        }
      }
    } catch (error) {
      console.error('提交表单失败:', error)
      ElMessage.error('操作失败')
    }
  }

  // 重置表单
  const resetForm = () => {
    Object.keys(paramForm).forEach((key) => {
      if (key === 'id') {
        paramForm[key] = null
      } else {
        paramForm[key] = ''
      }
    })
    if (paramFormRef.value) {
      paramFormRef.value.resetFields()
    }
  }
</script>

<style scoped>
  .security-params-container {
    min-height: calc(100vh - 60px);
    padding: 20px;
    background-color: var(--art-bg-color);
  }

  .page-title {
    margin-bottom: 20px;
    font-size: 24px;
    font-weight: 600;
    color: var(--art-text-gray-800);
  }

  .content-box {
    border-radius: 4px;
  }

  .status-card {
    margin-bottom: 20px;
    background-color: var(--art-main-bg-color);
    border: 1px solid var(--art-border-color);
    border-radius: 8px;
    box-shadow: var(--art-box-shadow-xs);
    transition: all 0.3s ease;
  }

  .status-card:hover {
    box-shadow: var(--art-box-shadow-sm);
  }

  .status-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .status-title,
  .card-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--art-text-gray-800);
  }

  .status-content {
    display: flex;
    gap: 15px;
    padding: 8px 0;
  }

  .status-item {
    display: flex;
    align-items: center;
    width: calc(25% - 12px);
    min-width: 250px;
    padding: 15px;
    background-color: var(--art-main-bg-color);
    border: 1px solid var(--art-border-color);
    border-radius: 8px;
    box-shadow: var(--art-box-shadow-xs);
    transition: all 0.3s ease;
  }

  .status-item:hover {
    box-shadow: var(--art-box-shadow-sm);
    transform: translateY(-3px);
  }

  .device-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 46px;
    height: 46px;
    margin-right: 15px;
    color: var(--art-text-gray-600);
    border-radius: 50%;
    transition: all 0.3s ease;
  }

  .device-icon.online {
    color: var(--el-color-success);
    background-color: var(--art-bg-success);
    border: 1px solid var(--el-color-success);
  }

  .device-icon.offline {
    color: var(--el-color-danger);
    background-color: var(--art-bg-danger);
    border: 1px solid var(--el-color-danger);
  }

  .device-icon.warning {
    color: var(--el-color-warning);
    background-color: var(--art-bg-warning);
    border: 1px solid var(--el-color-warning);
  }

  .device-info {
    flex: 1;
  }

  .device-name {
    margin-bottom: 8px;
    font-size: 15px;
    font-weight: 600;
    color: var(--art-text-gray-800);
  }

  .device-address {
    margin-bottom: 10px;
    font-size: 13px;
    color: var(--art-text-gray-600);
  }

  .device-status {
    display: flex;
    align-items: center;
  }

  .status-dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    margin-right: 6px;
    border-radius: 50%;
  }

  .status-dot.online {
    background-color: var(--el-color-success);
  }

  .status-dot.offline {
    background-color: var(--el-color-danger);
  }

  .status-dot.warning {
    background-color: var(--el-color-warning);
  }

  .status-text {
    font-size: 13px;
    font-weight: 500;
  }

  .status-text.online {
    color: var(--el-color-success);
  }

  .status-text.offline {
    color: var(--el-color-danger);
  }

  .status-text.warning {
    color: var(--el-color-warning);
  }

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .param-card {
    margin-bottom: 20px;
    background-color: var(--art-main-bg-color);
    border: 1px solid var(--art-border-color);
    border-radius: 8px;
    box-shadow: var(--art-box-shadow-xs);
    transition: all 0.3s ease;
  }

  .param-card:hover {
    box-shadow: var(--art-box-shadow-sm);
  }

  .search-form {
    padding: 20px;
    margin-bottom: 20px;
    background-color: var(--art-main-bg-color);
    border: 1px solid var(--art-border-color);
    border-radius: 8px;
  }

  /* 确保ArtTable组件有足够的显示空间 */
  .param-table {
    height: auto;
    min-height: 400px;
  }

  /* 修复el-table__empty-block高度不断增长问题 */
  .param-table :deep(.el-table__empty-block) {
    height: auto !important;
    min-height: 60px;
    max-height: 400px;
    overflow: hidden;
  }

  /* 确保空表格状态下的布局稳定 */
  .param-table :deep(.el-table__body-wrapper) {
    height: auto !important;
    min-height: 200px;
  }

  .icon-wrapper {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    margin-right: 8px;
    color: var(--art-text-gray-600);
  }

  .security-name-cell {
    display: flex;
    align-items: center;
  }

  .security-tag {
    padding: 4px 8px;
    font-weight: 500;
    letter-spacing: 0.5px;
    border-radius: 4px;
  }

  /* 自定义操作按钮样式 */
  .operation-btns {
    display: flex;
    gap: 12px;
    justify-content: center;
  }

  .operation-btn {
    width: 32px !important;
    height: 32px !important;
    padding: 6px !important;
    margin: 0 !important;
    line-height: 1 !important; /* 确保图标垂直居中 */
    border: none !important;
    border-radius: 4px !important; /* 方形边框 */
    box-shadow: 0 2px 4px rgb(0 0 0 / 10%) !important; /* 添加阴影效果 */
    transition: all 0.3s ease !important; /* 添加过渡效果 */
  }

  .operation-btn:hover {
    box-shadow: 0 4px 8px rgb(0 0 0 / 15%) !important; /* 悬停时增强阴影 */
    transform: translateY(-2px) !important; /* 悬停时上移效果 */
  }

  .edit-btn {
    background-color: #e6f7ff !important; /* 浅蓝色背景 */
  }

  .edit-btn .el-icon {
    font-size: 16px;
    color: #409eff !important; /* 蓝色图标 */
  }

  .delete-btn {
    background-color: #fff1f0 !important; /* 浅红色背景 */
  }

  .delete-btn .el-icon {
    font-size: 16px;
    color: #f56c6c !important; /* 红色图标 */
  }

  .pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 25px;
  }

  .security-option,
  .level-option {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .level {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    font-size: 12px;
    font-weight: bold;
    color: white;
    border-radius: 50%;
  }

  .level-low {
    background-color: var(--el-color-success);
  }

  .level-medium {
    background-color: var(--el-color-warning);
  }

  .level-high {
    background-color: var(--el-color-danger);
  }

  .param-form {
    padding: 0 20px;
  }

  /* 表格行样式 */
  :deep(.row-encryption) {
    background-color: rgba(var(--art-success), 0.05);
  }

  :deep(.row-encryption:hover) {
    background-color: rgba(var(--art-success), 0.1) !important;
  }

  :deep(.row-authentication) {
    background-color: rgba(var(--art-primary), 0.05);
  }

  :deep(.row-authentication:hover) {
    background-color: rgba(var(--art-primary), 0.1) !important;
  }

  :deep(.row-access) {
    background-color: rgba(var(--art-warning), 0.05);
  }

  :deep(.row-access:hover) {
    background-color: rgba(var(--art-warning), 0.1) !important;
  }

  :deep(.row-firewall) {
    background-color: rgba(var(--art-info), 0.05);
  }

  :deep(.row-firewall:hover) {
    background-color: rgba(var(--art-info), 0.1) !important;
  }

  /* 响应式设计 */
  @media screen and (width <= 1200px) {
    .status-item {
      width: calc(33.33% - 10px);
    }
  }

  @media screen and (width <= 992px) {
    .status-item {
      width: calc(50% - 10px);
    }
  }

  @media screen and (width <= 768px) {
    .status-item {
      width: 100%;
    }
  }
</style>
