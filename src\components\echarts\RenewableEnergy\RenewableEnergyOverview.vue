<template>
  <div class="chart-container" ref="chartContainer"></div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted } from 'vue'
  import * as echarts from 'echarts'

  const chartContainer = ref(null)
  let chart = null

  // 图表数据
  const energyData = {
    // 全球可再生能源装机容量数据（单位：吉瓦）
    globalCapacity: {
      years: ['2018', '2019', '2020', '2021', '2022', '2023'],
      solar: [480, 580, 710, 840, 1050, 1250],
      wind: [590, 650, 740, 830, 900, 1010],
      hydro: [1150, 1170, 1200, 1230, 1240, 1250],
      other: [150, 160, 170, 180, 190, 200]
    },
    // 中国可再生能源装机容量（单位：吉瓦）
    chinaCapacity: {
      years: ['2018', '2019', '2020', '2021', '2022', '2023'],
      solar: [175, 205, 253, 306, 392, 609],
      wind: [184, 210, 281, 328, 365, 420],
      hydro: [352, 356, 370, 391, 401, 415],
      other: [35, 38, 42, 47, 52, 59]
    },
    // 中国可再生能源在总发电量中的占比（单位：%）
    chinaPercentage: {
      years: ['2018', '2019', '2020', '2021', '2022', '2023'],
      values: [26.7, 27.8, 29.5, 31.7, 33.6, 35.9]
    }
  }

  // 初始化图表
  const initChart = () => {
    if (chartContainer.value) {
      chart = echarts.init(chartContainer.value)
      updateChart('globalCapacity')

      // 监听窗口大小变化，调整图表大小
      window.addEventListener('resize', () => {
        chart.resize()
      })
    }
  }

  // 更新图表数据
  const updateChart = (dataType) => {
    if (!chart) return

    let option = {}

    if (dataType === 'globalCapacity' || dataType === 'chinaCapacity') {
      const data =
        dataType === 'globalCapacity' ? energyData.globalCapacity : energyData.chinaCapacity
      const title =
        dataType === 'globalCapacity' ? '全球可再生能源装机容量' : '中国可再生能源装机容量'

      option = {
        title: {
          text: title,
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['太阳能', '风能', '水能', '其他'],
          bottom: 10
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: data.years
        },
        yAxis: {
          type: 'value',
          name: '装机容量 (吉瓦)'
        },
        series: [
          {
            name: '太阳能',
            type: 'bar',
            stack: 'total',
            emphasis: {
              focus: 'series'
            },
            data: data.solar,
            itemStyle: {
              color: '#FFA500'
            }
          },
          {
            name: '风能',
            type: 'bar',
            stack: 'total',
            emphasis: {
              focus: 'series'
            },
            data: data.wind,
            itemStyle: {
              color: '#1E90FF'
            }
          },
          {
            name: '水能',
            type: 'bar',
            stack: 'total',
            emphasis: {
              focus: 'series'
            },
            data: data.hydro,
            itemStyle: {
              color: '#32CD32'
            }
          },
          {
            name: '其他',
            type: 'bar',
            stack: 'total',
            emphasis: {
              focus: 'series'
            },
            data: data.other,
            itemStyle: {
              color: '#9370DB'
            }
          }
        ]
      }
    } else if (dataType === 'chinaPercentage') {
      option = {
        title: {
          text: '中国可再生能源在总发电量中的占比',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          formatter: '{b}: {c}%'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: energyData.chinaPercentage.years
        },
        yAxis: {
          type: 'value',
          name: '占比 (%)',
          min: 0,
          max: 50
        },
        series: [
          {
            data: energyData.chinaPercentage.values,
            type: 'line',
            symbol: 'circle',
            symbolSize: 8,
            lineStyle: {
              width: 4,
              color: '#4CAF50'
            },
            itemStyle: {
              color: '#4CAF50'
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(76, 175, 80, 0.6)' },
                { offset: 1, color: 'rgba(76, 175, 80, 0.1)' }
              ])
            }
          }
        ]
      }
    }

    chart.setOption(option)
  }

  // 导出更新图表的方法，供父组件使用
  const changeChartType = (type) => {
    updateChart(type)
  }

  // 组件挂载时初始化图表
  onMounted(() => {
    initChart()
  })

  // 组件卸载时销毁图表，释放资源
  onUnmounted(() => {
    if (chart) {
      chart.dispose()
      chart = null
    }
    window.removeEventListener('resize', () => {
      if (chart) {
        chart.resize()
      }
    })
  })

  // 导出给父组件使用的方法
  defineExpose({
    changeChartType
  })
</script>

<style scoped lang="scss">
  .chart-container {
    width: 100%;
    height: 400px;
  }
</style>
