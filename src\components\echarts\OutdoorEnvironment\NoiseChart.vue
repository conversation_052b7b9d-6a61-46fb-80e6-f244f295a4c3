<template>
  <div ref="chartRef" class="chart-container"></div>
</template>

<script setup>
  import { ref, onMounted, watch } from 'vue'
  import * as echarts from 'echarts'

  const props = defineProps({
    timeRange: {
      type: String,
      default: 'day'
    }
  })

  const chartRef = ref(null)
  let chartInstance = null

  // 初始化图表
  onMounted(() => {
    chartInstance = echarts.init(chartRef.value)
    updateChart()

    // 监听窗口大小变化，调整图表大小
    window.addEventListener('resize', () => {
      chartInstance.resize()
    })
  })

  // 监听时间范围变化
  watch(
    () => props.timeRange,
    () => {
      updateChart()
    }
  )

  // 更新图表数据
  const updateChart = () => {
    let xAxisData = []
    let noiseData = []
    let standardLine = []

    // 根据时间范围设置数据
    if (props.timeRange === 'day') {
      xAxisData = ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00']
      noiseData = [42.5, 41.8, 50.2, 55.6, 52.3, 49.8, 45.6]
      standardLine = [55, 55, 55, 55, 55, 55, 55]
    } else if (props.timeRange === 'week') {
      xAxisData = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
      noiseData = [48.5, 50.2, 52.6, 49.8, 53.4, 57.2, 46.5]
      standardLine = [55, 55, 55, 55, 55, 55, 55]
    } else if (props.timeRange === 'month') {
      xAxisData = ['5日', '10日', '15日', '20日', '25日', '30日', '5日']
      noiseData = [47.8, 51.5, 49.2, 48.7, 50.6, 52.3, 48.5]
      standardLine = [55, 55, 55, 55, 55, 55, 55]
    }

    const option = {
      tooltip: {
        trigger: 'axis',
        formatter: function (params) {
          let result = params[0].name + '<br/>'
          for (let i = 0; i < params.length; i++) {
            let marker =
              '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' +
              params[i].color +
              '"></span>'
            if (params[i].seriesName === '标准限值') {
              result += marker + params[i].seriesName + ': ' + params[i].value + ' dB(A)<br/>'
            } else {
              result += marker + params[i].seriesName + ': ' + params[i].value + ' dB(A)<br/>'
            }
          }
          return result
        }
      },
      legend: {
        data: ['噪声值', '标准限值'],
        right: '10%'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: xAxisData
      },
      yAxis: {
        type: 'value',
        name: 'dB(A)'
      },
      series: [
        {
          name: '噪声值',
          type: 'line',
          smooth: true,
          data: noiseData,
          itemStyle: {
            color: '#67C23A'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(103, 194, 58, 0.5)'
                },
                {
                  offset: 1,
                  color: 'rgba(103, 194, 58, 0.1)'
                }
              ]
            }
          }
        },
        {
          name: '标准限值',
          type: 'line',
          smooth: true,
          data: standardLine,
          itemStyle: {
            color: '#F56C6C'
          },
          lineStyle: {
            type: 'dashed'
          },
          symbol: 'none'
        }
      ]
    }

    chartInstance.setOption(option)
  }
</script>

<style scoped>
  .chart-container {
    width: 100%;
    height: 300px;
  }
</style>
